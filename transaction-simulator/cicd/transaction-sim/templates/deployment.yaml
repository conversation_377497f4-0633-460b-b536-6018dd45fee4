apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "transaction-sim.shortname" . }}
  labels:
{{ include "transaction-sim.labels" . | indent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "transaction-sim.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "transaction-sim.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      imagePullSecrets:
        - name: harbor-pull-secret
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repo }}/{{ .Values.image.imageName }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: "brokerUrl"
              value: "tcp://{{ .Release.Name }}-{{ .Values.global.mq_type }}:61616"
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
