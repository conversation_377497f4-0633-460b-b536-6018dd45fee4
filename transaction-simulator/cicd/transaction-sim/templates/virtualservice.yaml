{{- if .Values.istio.enabled -}}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: "{{ .Release.Name }}-{{ .Chart.Name }}"
  labels:
{{ include "transaction-sim.labels" . | indent 4 }}
spec:
  gateways:
  - global-gateway.istio-system.svc.cluster.local
  hosts:
  - "{{ .Release.Name }}-{{ .Chart.Name }}.{{ .Values.global.psci_devops_domain }}"
  http:
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: "{{ .Release.Name }}-{{ .Chart.Name }}"
        port:
          number: {{ .Values.service.port }}
{{- end }}
