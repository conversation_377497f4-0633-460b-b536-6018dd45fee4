apiVersion: v1
kind: Service
metadata:
  name: "{{ .Release.Name }}-{{ .Chart.Name }}"
  labels:
{{ include "transaction-sim.labels" . | indent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.port }}
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "transaction-sim.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
