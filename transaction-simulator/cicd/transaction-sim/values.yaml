# Default values for core.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

nameOverride: ""
fullnameOverride: ""
replicaCount: 1

image:
  tag:          "kuwait"
  repo:         "harbor.progressoft.io"
  pullPolicy:   "Always"
  javaOpts:     "-Xmx1500m"
  imageName:    "acacia/dms-transaction-sim"

service:
  type: ClusterIP
  port: 9090

istio:
  enabled: true
  annotations: {}

resources:
  limits:
    cpu: 1000m
    memory: 2048Mi
  requests:
    cpu: 100m
    memory: 1024Mi

nodeSelector: {}
#  apptype: backend

tolerations: []
#- key: "apptype"
#  operator: "Equal"
#  value: "backend"
#  effect: "NoSchedule"

affinity: {}
