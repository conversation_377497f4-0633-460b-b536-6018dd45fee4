#!/bin/bash

export PSCI_CHART_USERNAME=developer
export PSCI_CHART_PASSWORD=FBvAgfFHV8k8eHUh
export PSCI_CHART_URL=harbor.progressoft.io

unzip artifacts.zip

echo "Make sure you are loged in to your private registry"
docker login -u $PSCI_CHART_USERNAME -p $PSCI_CHART_PASSWORD $PSCI_CHART_URL

echo "build dms-transaction-sim:kuwait"
docker build -t $PSCI_CHART_URL/acacia/dms-transaction-sim:kuwait -f Dockerfile ./artifacts
docker push $PSCI_CHART_URL/acacia/dms-transaction-sim:kuwait

rm -rf artifacts