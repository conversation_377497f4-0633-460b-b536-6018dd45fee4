{"transactions": [{"transactionReference": 44444, "transactionDate": "2024-11-11", "transactionCurrency": "KWD", "transactionAmount": 500, "senderParticipant": "RJHIKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 101011, "transactionDate": "2024-11-10", "transactionCurrency": "KWD", "transactionAmount": 190, "senderParticipant": "RJHIKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 101020, "transactionDate": "2024-12-11", "transactionCurrency": "KWD", "transactionAmount": 190, "senderParticipant": "RJHIKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 101012, "transactionDate": "2024-11-14", "transactionCurrency": "KWD", "transactionAmount": 190, "senderParticipant": "RJHIKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 111010, "transactionDate": "2024-11-15", "transactionCurrency": "KWD", "transactionAmount": 190, "senderParticipant": "RJHIKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 201012, "transactionDate": "2024-12-14", "transactionCurrency": "KWD", "transactionAmount": 190, "senderParticipant": "RJHIKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 55555, "transactionDate": "2024-11-11", "transactionCurrency": "KWD", "transactionAmount": 200, "senderParticipant": "NBOKKWK0", "receiverParticipant": "RJHIKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 11111, "transactionDate": "2024-11-11", "transactionCurrency": "KWD", "transactionAmount": 4000, "senderParticipant": "RJHIKWKW", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 22222, "transactionDate": "2022-01-11", "transactionCurrency": "KWD", "transactionAmount": 1000, "senderParticipant": "NBOKKWK0", "receiverParticipant": "RJHIKWKW", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 33333, "transactionDate": "2024-12-12", "transactionCurrency": "KWD", "transactionAmount": 100, "senderParticipant": "BRGNKWK0", "receiverParticipant": "RJHIKWKW", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 88888, "transactionDate": "2024-12-12", "transactionCurrency": "KWD", "transactionAmount": 100, "senderParticipant": "BRGNKWK0", "receiverParticipant": "RJHIKWKW", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 99999, "transactionDate": "2024-12-12", "transactionCurrency": "KWD", "transactionAmount": 100, "senderParticipant": "BRGNKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 101010, "transactionDate": "2024-12-12", "transactionCurrency": "KWD", "transactionAmount": 300, "senderParticipant": "BRGNKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}, {"transactionReference": 202020, "transactionDate": "2024-12-12", "transactionCurrency": "KWD", "transactionAmount": 300, "senderParticipant": "BRGNKWK0", "receiverParticipant": "NBOKKWK0", "additionalInfo": [{"key": "instgBranch", "value": "branchCode1 - branchName1"}, {"key": "instdBranch", "value": "branchCode2 - branchName2"}, {"key": "creditorIban", "value": "TEST"}, {"key": "creditorRIB", "value": "12345"}, {"key": "creditorCountry", "value": "KWT"}, {"key": "creditorCity", "value": "KWT"}, {"key": "creditorPhone", "value": "12345"}, {"key": "creditorEmail", "value": "<EMAIL>"}, {"key": "debtorIban", "value": "TEST"}, {"key": "debtorRIB", "value": "12345"}, {"key": "debtorCountry", "value": "KWT"}, {"key": "debtorCity", "value": "KWT"}, {"key": "debtorPhone", "value": "12345"}, {"key": "debtorEmail", "value": "<EMAIL>"}]}]}