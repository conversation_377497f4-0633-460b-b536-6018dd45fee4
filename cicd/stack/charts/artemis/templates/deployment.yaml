apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "artemis.fullname" . }}
  labels:
{{ include "artemis.labels" . | indent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      app.kubernetes.io/name: {{ include "artemis.name" . }}
      app.kubernetes.io/instance: {{ .Release.Name }}
  template:
    metadata:
      labels:
        app.kubernetes.io/name: {{ include "artemis.name" . }}
        app.kubernetes.io/instance: {{ .Release.Name }}
    spec:
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: ARTEMIS_USERNAME
            value: "{{ .Values.config.username }}"
          - name: ARTEMIS_PASSWORD
            value: "{{ .Values.config.password }}"
          - name: DISABLE_SECURITY
            value: "{{ .Values.config.disableSecurity }}"
          - name: BROKER_CONFIGS
            value: "{{ .Values.config.brokerConfigs }}"
          ports:
          - name: http-amq-ui
            containerPort: 8161
          - name: tcp-amq-broker
            containerPort: 61616
            protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: 8161
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 3
            failureThreshold: 15
            initialDelaySeconds: 15
          readinessProbe:
            httpGet:
              path: /
              port: 8161
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 3
            failureThreshold: 15
            initialDelaySeconds: 15
          resources:
{{- toYaml .Values.resources | nindent 12 }}
    {{- with .Values.nodeSelector }}
      nodeSelector:
{{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
{{- toYaml . | nindent 8 }}
    {{- end }}
