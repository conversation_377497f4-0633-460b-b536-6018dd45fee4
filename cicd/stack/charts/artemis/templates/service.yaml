apiVersion: v1
kind: Service
metadata:
  name: {{ include "artemis.fullname" . }}
  labels:
{{ include "artemis.labels" . | indent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: 61616
      targetPort: 61616
      protocol: TCP
      name: tcp-amq-broker
    - port: 8161
      targetPort: 8161
      protocol: TCP
      name: http-amq-ui
  selector:
    app.kubernetes.io/name: {{ include "artemis.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
