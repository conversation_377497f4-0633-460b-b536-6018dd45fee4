{{- if .Values.istio.enabled -}}
{{- $fullName := include "artemis.fullname" . -}}
{{- $domain   := include "devopsDomain" . -}}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: {{ $fullName }}
  labels:
{{ include "artemis.labels" . | indent 4 }}
spec:
  gateways:
  - global-gateway.istio-system.svc.cluster.local
  hosts:
  - {{ $fullName }}.{{ $domain }}
  http:
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: {{ $fullName }}
        port:
          number: {{ .Values.service.port }}
{{- end }}
