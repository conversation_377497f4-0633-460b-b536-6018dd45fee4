# Default values for artemis.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  tag: 2.8-alpine-latest
  repository: vromero/activemq-artemis
  pullPolicy: IfNotPresent

config:
  username: admin
  password: admin
  disableSecurity: "true"
  brokerConfigs: "-Dbrokerconfig.journalMaxIo=500"

nameOverride: ""
fullnameOverride: ""

service:
  type: ClusterIP
  port: 8161


istio:
  enabled: true
  annotations: {}

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []

  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 200m
    memory: 256Mi

nodeSelector: {}

tolerations: []

affinity: {}
