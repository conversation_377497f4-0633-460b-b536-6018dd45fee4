apiVersion: v1
kind: Service
metadata:
  name: "{{ .Release.Name }}-{{ .Chart.Name }}"
  labels:
{{ include "dms-kuwait-ws.labels" . | indent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/name: {{ include "dms-kuwait-ws.name" . }}
    app.kubernetes.io/instance: {{ .Release.Name }}
