# Default values for core.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

nameOverride: ""
fullnameOverride: ""
replicaCount: 1

image:
  tag:          ""
  repo:         ""
  pullPolicy:   "Always"
  javaOpts:     "-Xmx2500m"
  imageName:    "acacia/dms-kuwait-ws"

service:
  type: ClusterIP
  port: 8080

istio:
  enabled: true
  host: "{{ .Release.Name }}-{{ .Chart.Name }}.{{ .Values.global.psci_devops_domain }}"
  annotations: {}

resources:
  limits:
    cpu: 2000m
    memory: 3096Mi
  requests:
    cpu: 100m
    memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}
