{{- if .Values.istio.enabled -}}
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: "{{ .Release.Name }}-{{ .Chart.Name }}"
  labels:
{{ include "dms-kuwait.labels" . | indent 4 }}
spec:
  gateways:
  - global-gateway.istio-system.svc.cluster.local
  hosts:
  - "{{ tpl .Values.istio.host . }}"
  http:
  - match:
    - uri:
        prefix: "/"
    route:
    - destination:
        host: "{{ .Release.Name }}-{{ .Chart.Name }}"
        port:
          number: {{ .Values.service.port }}
{{- end }}
