{{- define "environment" }}
#-------------------------------------------------------------------------------
# defaults
#-------------------------------------------------------------------------------
{{- $engine   := .Values.global.database_engine }}
{{- $username := .Values.global.database_username }}
{{- $password := .Values.global.database_password }}
#-------------------------------------------------------------------------------
# Common Variables
#-------------------------------------------------------------------------------
- name: "db_user"
  value: "{{ $username }}"
- name: "db_password"
  value: "{{ $password }}"
- name: "db_schema"
  value: "{{ $username }}"
- name: "db_engine"
  value: "{{ $engine }}"
- name: "db_external"
  value: "{{ .Values.global.database_external | default false }}"
- name: "env_broker_url"
  value: "tcp://{{ .Release.Name }}-artemis:61616"
- name: "IIPS_ACTIVEMQ_URL"
  value: "{{.Values.environments.iips_activemq_url}}"
- name: "dms_app_url"
  value: "https://{{ .Release.Name }}-dms-beac.{{ .Values.global.psci_devops_domain }}"
- name: "reporting_base_url"
  value: "http://{{ .Release.Name }}-reporting:80/api/v1"
- name: "OTEL_EXPORTER_OTLP_INSECURE"
  value: "{{ .Values.global.otel_exporter_otlp_insecure }}"
- name: "OTEL_EXPORTER_OTLP_ENDPOINT"
  value: "{{ .Values.global.otel_exporter_otlp_endpoint }}"
- name: "OTEL_RESOURCE_ATTRIBUTES"
  value: "{{ .Values.global.otel_resource_attributes }}"
- name: "OTEL_LOGS_EXPORTER"
  value: "{{ .Values.global.otel_logs_exporter }}"
- name: "OTEL_METRICS_EXPORTER"
  value: "{{ .Values.global.otel_metrics_exporter }}"
- name: "JAVA_TOOL_OPTIONS"
  value: "{{ .Values.global.java_tool_options }}"
- name: "OTEL_EXPORTER_OTLP_PROTOCOL"
  value: "{{ .Values.global.otel_exporter_otlp_protocol }}"
- name: "TZ" 
  value: "GMT+1" 
- name: "csp_upgrade_insecure_requests"
  value: "{{ .Values.global.csp_upgrade_insecure_requests }}"
- name: "cors_origin"
  value: "https://{{ tpl .Values.istio.host . }}"
- name: "base_protocol"
  value: "{{ .Values.global.base_protocol }}"
- name: "base_uri"
  value: "{{ tpl .Values.istio.host . }}"
- name: "bank_names"
  value: "{{ .Values.environments.banks.names }}"
- name: "bank_shortnames"
  value: "{{ .Values.environments.banks.shortnames }}"
- name: "bank_bic"
  value: "{{ .Values.environments.banks.bic }}"
- name: "bank_ncb"
  value: "{{ .Values.environments.banks.ncb }}"
- name: "bank_email"
  value: "{{ .Values.environments.banks.email }}"
- name: "bank_countries"
  value: "{{ .Values.environments.banks.country }}"
- name: "ncb_names"
  value: "{{ .Values.environments.ncb.names }}"
- name: "ncb_shortnames"
  value: "{{ .Values.environments.ncb.shortnames }}"
- name: "ncb_bic"
  value: "{{ .Values.environments.ncb.bic }}"
- name: "ncb_country"
  value: "{{ .Values.environments.ncb.country }}"
- name: "ncb_email"
  value: "{{ .Values.environments.ncb.email }}"

#-------------------------------------------------------------------------------
# Oracle Varables
#-------------------------------------------------------------------------------
{{- if eq $engine "oracle" }}
{{- $instance :=  .Values.global.database_instance | default "xe" }}
{{- $port     :=  .Values.global.database_port     | default 1521 }}
{{- $host     :=  .Values.global.database_host     | default (printf "%s-oracle" .Release.Name) }}
- name: "db_host"
  value: "{{ $host }}"
- name: "db_port"
  value: "{{ $port }}"
- name: "db_instance"
  value: "{{ $instance }}"
- name: "db_url"
  value: "jdbc:oracle:thin:@{{ $host }}:{{ $port }}/{{ $instance }}"
- name: "db_type"
  value: "ORACLE10"
- name: "quartz_dialect"
  value: "org.quartz.impl.jdbcjobstore.oracle.OracleDelegate"
- name: "db_driver"
  value: "oracle.jdbc.OracleDriver"
- name: "db_dialect"
  value: "org.hibernate.dialect.OracleDialect"
- name: "db_validation_query"
  value: "SELECT 1 FROM DUAL"
- name: "db_check_query"
  value: "SELECT COUNT(*) FROM USER_TABLES"
#-------------------------------------------------------------------------------
# Postgres Varables# 
#-------------------------------------------------------------------------------
{{- else if eq $engine  "postgres" }}
{{- $instance :=  .Values.global.database_instance | default "postgres" }}
{{- $port     :=  .Values.global.database_port     | default 5432 }}
{{- $host     :=  .Values.global.database_host     | default (printf "%s-postgres" .Release.Name) }}
- name: "db_host"
  value: "{{ $host }}"
- name: "db_port"
  value: "{{ $port }}"
- name: "db_instance"
  value: "{{ $instance }}"
- name: "db_url"
  value: "jdbc:postgresql://{{ $host }}:{{ $port }}/{{ $instance }}"
- name: "db_type"
  value: "POSTGRESSQL"
- name: "quartz_dialect"
  value: "org.quartz.impl.jdbcjobstore.PostgreSQLDelegate"
- name: "db_driver"
  value: "org.postgresql.Driver"
- name: "db_dialect"
  value: "com.progressoft.jfw.JfwPostgreSQLDialect"
- name: "db_validation_query"
  value: "SELECT 1"
- name: "db_check_query"
  value: "SELECT 1"
#-------------------------------------------------------------------------------
# MSSQL Varables
#-------------------------------------------------------------------------------
{{- else if eq $engine "mssql" }}
{{- $instance :=  .Values.global.database_instance | default "" }}
{{- $port     :=  .Values.global.database_port     | default 1433 }}
{{- $host     :=  .Values.global.database_host     | default (printf "%s-mssql" .Release.Name) }}
- name: "db_host"
  value: "{{ $host }}"
- name: "db_port"
  value: "{{ $port }}"
- name: "db_instance"
  value: "{{ $instance }}"
- name: "db_url"
  value: "jdbc:jtds:sqlserver://{{ $host }}:{{ $port }}/{{ $username }}"
- name: "db_type"
  value: "MSSQL2008"
- name: "db_driver"
  value: "net.sourceforge.jtds.jdbc.Driver"
- name: "db_dialect"
  value: "org.hibernate.dialect.SQLServer2008Dialect"
- name: "db_validation_query"
  value: "SELECT 1"
- name: "db_check_query"
  value: "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES"
#-------------------------------------------------------------------------------
# Default H2
#-------------------------------------------------------------------------------
{{- else }}
{{- $instance := "" }}
{{- $host     := "" }}
{{- $port     := "" }}
- name: "db_host"
  value: "{{ $host }}"
- name: "db_port"
  value: "{{ $port }}"
- name: "db_instance"
  value: "{{ $instance }}"
- name: "db_url"
  value: "jdbc:h2:file:/init/data/h2db;MODE=Oracle;AUTO_SERVER=TRUE;MVCC=true;LOCK_TIMEOUT=5000"
- name: "db_type"
  value: "H2"
- name: "db_driver"
  value: "org.h2.Driver"
- name: "db_dialect"
  value: "org.hibernate.dialect.H2Dialect"
- name: "db_validation_query"
  value: "SELECT 1 FROM DUAL"
- name: "db_check_query"
  value: "SELECT 1 FROM DUAL"
{{- end }}
#-------------------------------------------------------------------------------
# Environment Variables
#-------------------------------------------------------------------------------
{{- if .Values.javaOpts }}
- name: "JAVA_OPTS"
  value: "{{ .Values.javaOpts }}"
{{- end }}
#-------------------------------------------------------------------------------
{{- end }}
