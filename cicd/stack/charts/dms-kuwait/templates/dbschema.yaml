{{- if .Values.global.dbschema.enabled -}}
apiVersion: progressoft.com/v1
kind: DBSchema
metadata:
  name: "{{ .Release.Name }}-{{ .Chart.Name }}"
  labels:
{{ include "dms-kuwait.labels" . | indent 4 }}
spec:
  engine: {{ .Values.global.database_engine }}
  host: {{ .Values.global.database_host     | default (printf "%s-oracle" .Release.Name) }}
  inst: {{ .Values.global.database_instance | default "xe" }}
  pass: {{ .Values.global.database_password }}
  port: {{ .Values.global.database_port     | default 1521 }}
  test: JFW_USERS
  user: {{ .Values.global.database_username }}
{{- end }}