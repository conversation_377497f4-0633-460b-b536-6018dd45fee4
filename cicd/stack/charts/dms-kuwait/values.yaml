# Default values for core.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

nameOverride: ""
fullnameOverride: ""
replicaCount: 1

image:
  tag:          ""
  repo:         ""
  pullPolicy:   "Always"
  javaOpts:     "-Xmx2500m"
  imageName:    "acacia/dms-kuwait"

service:
  type: ClusterIP
  port: 7070

istio:
  enabled: true
  host: "{{ .Release.Name }}-{{ .Chart.Name }}.{{ .Values.global.psci_devops_domain }}"
  annotations: {}

resources:
  limits:
    cpu: 2000m
    memory: 3096Mi
  requests:
    cpu: 100m
    memory: 256Mi
environments:
  dms_env_broker_url: "tcp://{{ .Release.Name }}-artemis:61616"
  iips_activemq_url: "failover:(tcp://artemis.test.svc.cluster.local:61616?keepAlive=true)?randomize=false&maxReconnectAttempts=-1&jms.prefetchPolicy.all=1&timeout=10000"
  banks:
    names: "Al Ahli Bank of Kuwait K.S.C.P,Boubyan Bank (K.S.C)',Ahli United Bank K.S.C.P.,Burgan Bank K.P.S.C,Kuwait Finance House (K.S.C.),National Bank of Kuwait S.A.K.P.,Kuwait International Bank,Al Rajhi Bank Kuwait"
    shortnames: "ABK,BBY,BKM,BRG,KFH,NBK,KWI,RJH"
    bic: "ABKKKWK0,BBYNKWK0,BKMEKWK0,BRGNKWK0,KFHOKWK0,NBOKKWK0,KWIBKWK0,RJHIKWK0"
    ncb: "ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0"
    email: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
    country: "KWT,KWT,KWT,KWT,KWT,KWT,KWT,KWT"
  ncb:
    names: "Central Bank of Kuwait"
    shortnames: "CBK"
    bic: "ZYAAKWK0"
    country: "KWT"
    email: "<EMAIL>"


nodeSelector: {}

tolerations: []

affinity: {}
