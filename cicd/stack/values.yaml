# This is the global configuration file for your stack

global:
  # PSCI Variables
  #
  psci_devops_domain:   "${PSCI_DEVOPS_DOMAIN}"
  reporting_enabled: false
  simulator_enabled: true

  # Database settings
  #
  database_external:    "true"
  database_engine:      "oracle"
  database_username:    "${PSCI_DBUSER_NAME}"
  database_password:    "${PSCI_DBUSER_NAME}"
  database_instance:    "EE.oracle.docker"
  database_host:        "acacia-oracle-db"
  database_port:        "1521"

  systemPullSecrets:
    - name: harbor-pull-secret
      
  systemRepo: harbor.progressoft.io/acacia
  tag: "${PSCI_VERSION}"
  dbschema: 
    enabled: true


dms-kuwait:
  image:
    tag: "${PSCI_VERSION}"
    repo: "${PSCI_REGISTRY_URL}"

  environments:
    dms_env_broker_url: "tcp://{{ .Release.Name }}-artemis:61616"
    iips_activemq_url: "failover:(tcp://artemis.test.svc.cluster.local:61616?keepAlive=true)?randomize=false&maxReconnectAttempts=-1&jms.prefetchPolicy.all=1&timeout=10000"
    banks:
      names: "Al Ahli Bank of Kuwait K.S.C.P,Boubyan Bank (K.S.C)',Ahli United Bank K.S.C.P.,Burgan Bank K.P.S.C,Kuwait Finance House (K.S.C.),National Bank of Kuwait S.A.K.P.,Kuwait International Bank,Al Rajhi Bank Kuwait"
      shortnames: "ABK,BBY,BKM,BRG,KFH,NBK,KWI,RJH"
      bic: "ABKKKWK0,BBYNKWK0,BKMEKWK0,BRGNKWK0,KFHOKWK0,NBOKKWK0,KWIBKWK0,RJHIKWK0"
      ncb: "ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0,ZYAAKWK0"
      email: "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
      country: "KWT,KWT,KWT,KWT,KWT,KWT,KWT,KWT"
    ncb:
      names: "Central Bank of Kuwait"
      shortnames: "CBK"
      bic: "ZYAAKWK0"
      country: "KWT"
      email: "<EMAIL>"

dms-kuwait-ws:
  image:
    tag: "${PSCI_VERSION}"
    repo: "${PSCI_REGISTRY_URL}"