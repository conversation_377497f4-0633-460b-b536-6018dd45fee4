global:
  database:
    dbVendor: "oracle"
    host: "central-oracle"
    port: "1521"
    instance: "oracle"
    dbDriver: "oracle.jdbc.OracleDriver"
    type: "oracle"
    driver: "org.postgresql.Driver"
    dialect: ""
    dialect_ui: ""
    validation_query: "SELECT 1"
    check_query: "SELECT COUNT(*) FROM information_schema.tables"
    createSchemas: true

  reporting_tag: v3.6.0


reporting:
#pod replicas
  replicaCount: 1
#resource control
  resources:
    limits:
      cpu: 1
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 1Gi
