FROM postman/newman:latest
COPY ../../rsc/reports/templates /app/reporting
COPY ../../newman-collections/newman/collections/reporting /app/reporting

WORKDIR /app

ENTRYPOINT sh -c "echo '*********** report image is ready ************' && \
    cd /app/reporting && \
    echo '********** Start Running Newman Scripts **********' && \
    echo REPORTING_SVC_URL=${REPORTING_SVC_URL} && \
    echo '********** Start execute Newman Collections **********' && \
    newman run Reports-Collection.json --global-var \"REPORTING_SVC_URL=${REPORTING_SVC_URL//\'/}\" \
    --global-var \"JRXML_FILE_PATH=dispute_details_report/disputes_details-en.jrxml\" \
    --global-var \"JSON_FILE_PATH=dispute_details_report/disputesDetailsReportRequest-en.json\" && \
    echo '********** EXECUTED DISPUTE DETAILS REPORT - ENGLISH COLLECTION **********' && \
    newman run Reports-Collection.json --global-var \"REPORTING_SVC_URL=${REPORTING_SVC_URL//\'/}\" \
    --global-var \"JRXML_FILE_PATH=disputes_summary_report/disputes_summary-en.jrxml\" \
    --global-var \"JSON_FILE_PATH=disputes_summary_report/disputesSummaryReportRequest-en.json\" && \
    echo '********** EXECUTED DISPUTE SUMMARY REPORT - ENGLISH COLLECTION **********' && \
    newman run Reports-Collection.json --global-var \"REPORTING_SVC_URL=${REPORTING_SVC_URL//\'/}\" \
    --global-var \"JRXML_FILE_PATH=disputes_summary_report/disputes_summary-fr.jrxml\" \
    --global-var \"JSON_FILE_PATH=disputes_summary_report/disputesSummaryReportRequest-fr.json\" && \
    echo '********** EXECUTED DISPUTE SUMMARY REPORT - FRENCH COLLECTION **********' && \
    newman run Reports-Collection.json --global-var \"REPORTING_SVC_URL=${REPORTING_SVC_URL//\'/}\" \
    --global-var \"JRXML_FILE_PATH=dispute_details_report/disputes_details-fr.jrxml\" \
    --global-var \"JSON_FILE_PATH=dispute_details_report/disputesDetailsReportRequest-fr.json\" && \
    echo '********** EXECUTED DISPUTE DETAILS REPORT - FRENCH COLLECTION **********'"
