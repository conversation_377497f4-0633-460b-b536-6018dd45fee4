ARG  PSCI_UBI_VERSION=250223
FROM us.gcr.io/ps-public/amazoncorretto:17-alpine-jdk-${PSCI_UBI_VERSION}
ARG  PSCI_VERSION=1.0
ENV  VERSION=${PSCI_VERSION}
USER root

RUN  printf "║ %-25s │ %-25s ║\n" "core" ${VERSION} >> /VERSION_INFO; \
     chown -R nobody:nobody /VERSION_INFO

COPY --chown=nobody:nobody artifacts/web-services-*.jar /app/app.jar
COPY --chown=nobody:nobody cicd/docker/ws-entrypoint.sh /app/

RUN chown -R nobody:nobody /app \
    && chmod +x /app/*.sh
    
EXPOSE 8080

WORKDIR /app/
USER  nobody
CMD  sh /app/ws-entrypoint.sh