ARG  PSCI_UBI_VERSION=250223
FROM us.gcr.io/ps-public/amazoncorretto:17-alpine-jdk-${PSCI_UBI_VERSION}
ARG  PSCI_VERSION=1.0
ENV  VERSION=${PSCI_VERSION}
ENV  JAVA_TOOL_OPTIONS="--add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED"
USER root

RUN  printf "║ %-25s │ %-25s ║\n" "core" ${VERSION} >> /VERSION_INFO; \
     chown -R nobody:nobody /VERSION_INFO

RUN mkdir app
COPY --chown=nobody:nobody artifacts/dms-app*.jar /app/app.jar
COPY --chown=nobody:nobody cicd/docker/entrypoint.sh /app

RUN chown -R nobody:nobody /app \
    && chmod +x /app/*.sh

EXPOSE 7070

WORKDIR /app/
USER  nobody
CMD   sh /app/entrypoint.sh
