#------------------------------------------
# Autogenerated gitlab-ci file
#------------------------------------------
variables:
  TEMPLATE:             "jfw17"
  MAVEN_TEST_SKIP:      "false"
  MAVEN_EXTRA_ARGS:     ""
  MAVEN_DEPLOY_SKIP:    ""
  LOGGING_ARGS:         ""
  DIR_DOCKER:           ""
  DIR_CHART:            ""
  DIR_STACK:            ""
  DIR_SCAN:             ""
  DIR_ARTIFACTS:        ""
  ARTIFACTS_BASH:       ""
  PS_SECURITY_ANALYZERS: "java17"
  PSCI_APP_URL:             "https://${PSCI_RELEASE_NAME}-dms-kuwait.${PSCI_DEVOPS_DOMAIN}"
  PSCI_AUTH_URL:            "https://${PSCI_RELEASE_NAME}-dms-kuwait.${PSCI_DEVOPS_DOMAIN}/SSO/#/login"
  PSCI_FINDSECBUGSPACKAGE:  "com.progressoft.dms.-,com.progressoft.dmskuwait.-,com.progressoft.psdms.-"
  PROJECT_IMAGES:       "dms-kuwait"

  HELM_TIMEOUT_SECONDS:     "1800"
  DUMPSCHEMA:           ""
  LIQUIBASE_ENABLE:     ""

#------------------------------------------
stages:
  - build
  - deploy
  - security
  - promote
  - airgap
  - release
  - cleanup

#------------------------------------------
include:
  - project: 'progressoft/cicd5/gitlab'
    file:    'ci-build-17.yml'
  - project: 'progressoft/cicd5/gitlab'
    file:    'ci-deploy-17.yml'
  - project: 'progressoft/cicd5/gitlab'
    file:    'ci-promote.yml'
  - project: 'progressoft/cicd5/gitlab'
    file:    'ci-release-17.yml'
  - project: 'progressoft/cicd5/gitlab'
    file:    'ci-cleanup.yml'
  - project: 'progressoft/cicd5/gitlab'
    file:    'ci-security-scanners.yml'
  - project: "progressoft/cicd5/gitlab"
    file:    "ci-airgap.yml"
