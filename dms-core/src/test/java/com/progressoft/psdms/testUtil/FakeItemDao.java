package com.progressoft.psdms.testUtil;

import com.progressoft.jfw.AppUnhandledException;
import com.progressoft.jfw.JfwEntityUtils;
import com.progressoft.jfw.model.bussinessobject.Filter;
import com.progressoft.jfw.model.bussinessobject.changeHistory.ChangeDictionary;
import com.progressoft.jfw.model.bussinessobject.core.IJFWEntity;
import com.progressoft.jfw.model.bussinessobject.security.User;
import com.progressoft.jfw.model.bussinessobject.workflow.WorkflowAction;
import com.progressoft.jfw.model.bussinessobject.workflow.WorkflowActionFunc;
import com.progressoft.jfw.model.bussinessobject.workflow.WorkflowStatus;
import com.progressoft.jfw.model.dao.item.ItemDao;
import com.progressoft.jfw.model.dao.utils.JFWQuery;
import com.progressoft.jfw.model.messaging.entities.NotificationScheme;
import com.progressoft.jfw.shared.Operator;
import com.progressoft.jfw.shared.filter.operator.Parameter;
import org.apache.commons.lang3.NotImplementedException;
import org.apache.commons.lang3.Validate;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.persistence.EntityManager;
import jakarta.persistence.LockModeType;
import jakarta.persistence.metamodel.Attribute;
import jakarta.persistence.metamodel.EntityType;
import jakarta.persistence.metamodel.SingularAttribute;
import java.util.*;
import java.util.function.Predicate;

import static com.progressoft.jfw.JfwEntityUtils.getId;
import static com.progressoft.jfw.shared.Operator.*;
import static java.lang.Boolean.FALSE;
import static java.util.Arrays.asList;

public class FakeItemDao implements ItemDao {
    private boolean flushCalled = false;
    private boolean hasWatchers = false;
    private boolean isUserWatching = false;
    private boolean affectItems = false;
    private boolean filterItems = false;

    protected final Logger LOG = LoggerFactory.getLogger(getClass());

    private List<Object> mergedItems = new ArrayList<>();
    private ArrayList<Object> detachedItems = new ArrayList<>();
    //private List<NotificationScheme> interestedSchemes = new ArrayList<>();
    private List<Object> persistedItems = new ArrayList<>();
    private Map<Class<? extends IJFWEntity>, ArrayList<IJFWEntity>> items = new HashMap<>();

    public <T extends IJFWEntity> T add(T entity) {
        return add(entity, false);
    }

    public <T extends IJFWEntity> T add(T entity, boolean overwrite) {
        Validate.notNull(entity, "entity is null");

        if (getItem(entity.getClass(), getIdentifier(entity)) != null && !overwrite)
            return entity;

        if (!items.containsKey(entity.getClass()))
            items.put(entity.getClass(), new ArrayList<IJFWEntity>());

        removeItem(entity);
        items.get(entity.getClass()).add(entity);

        return entity;
    }

    public void affectItems() {
        affectItems = true;
    }

    public void clear() {
        items.clear();
        mergedItems.clear();
        detachedItems.clear();
        //interestedSchemes.clear();
        persistedItems.clear();
        affectItems = false;
        filterItems = false;
        flushCalled = false;
        isUserWatching = false;
        hasWatchers = false;
    }

    public void clearChanges() {
        persistedItems.clear();
        mergedItems.clear();
    }

    @Override
    public void detachEntity(Object entity) {
        Validate.notNull(entity, "entity is null");
        removeItem(entity);
        detachedItems.add(entity);
    }

    public void filterItems() {
        filterItems = true;
    }

    @Override
    public <T> T find(Class<T> entityClass, Object primaryKey) {
        return getItem(entityClass, primaryKey);
    }

    @Override
    public void flush() {
        flushCalled = true;
    }

    @Override
    public List<User> listAllUsersNotTenantAware() {
        return null;
    }

    @Override
    public <T> List<Object> getAggregates(Class<T> entityClass, List<String> projections, List<Filter> filters,
                                          List<Map<String, String>> aggregates, List<String> groups, String whereClause) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public Long getChangeDictionaryIdByViewId(Long viewId) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public String getConfigValue(String configKey) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public Set<EntityType<?>> getEntitiesNames() {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public <X> Set<Attribute<? super X, ?>> getEntityAttributes(Class<X> entityClass) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public ChangeDictionary getEntityChangeHistory(Class<?> entityClass) {
        throw new NotImplementedException("Not implemented");
    }

    public EntityManager getEntityManager() {
        throw new AppUnhandledException();
    }

    @Override
    public <T> T getEntityReference(Class<T> entityClass, Map<String, Object> entityProperties) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public <T> T getEntityReference(Class<T> entityClass, Object idValue) {
        throw new NotImplementedException("Not Implemented");
    }

    @Override
    public Object getEntityReference(Object parent, String name, Map<String, Object> idValue) {
        throw new NotImplementedException("Not Implemented");
    }

    public EntityType<?> getEntityType(Class<?> arg0) {
        throw new AppUnhandledException();
    }

    @Override
    public Object getIdentifier(Object entity) {
        Object identifier = JfwEntityUtils.getId(entity);
        if (identifier instanceof Long && ((Long) identifier == 0L)) {
            identifier = new Random().nextLong();
            JfwEntityUtils.setId(entity, identifier);
        }
        return identifier;
    }

    @Override
    public SingularAttribute<?, ?> getIdProperty(Class<?> entityClass) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public String getIdPropertyName(Class<?> entityClass) {
        return JfwEntityUtils.getIdPropertyName(entityClass);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getItem(Class<T> entityClass, Object idValue) {
        Validate.notNull(entityClass, "entityClass is null");
        Validate.notNull(idValue, "idValue is null");
        if (!items.containsKey(entityClass))
            return null;
        ArrayList<IJFWEntity> list = items.get(entityClass);
        for (IJFWEntity entity : list)
            if (getIdentifier(entity).equals(idValue))
                return (T) entity;
        return null;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getItem(Class<T> entityClass, String propertyName, Object propertyValue) {
        Validate.notNull(entityClass, "entityClass is null");
        Validate.notNull(propertyName, "propertyName is null");
        Validate.notNull(propertyValue, "propertyValue is null");

        if (!items.containsKey(entityClass))
            return null;
        ArrayList<IJFWEntity> list = items.get(entityClass);
        for (IJFWEntity entity : list)
            try {
                if (FieldUtils.readField(entity, propertyName, true).equals(propertyValue))
                    return (T) entity;
            } catch (IllegalAccessException e) {
                LOG.error(e.getMessage(), e);
                return null;
            }
        return null;
    }

    @Override
    public Map<String, Object> getItemCount(Class<?> entityClass, List<Filter> filters,
                                            List<Map<String, String>> aggregates, List<String> groups, String whereClause) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public Map<String, Object> getItemCount(Class<?> entityClass, List<Filter> filters,
                                            List<Map<String, String>> aggregates, String whereClause) {
        throw new NotImplementedException("Not Implemented");
    }

    public Map<String, Object> getItemCount(Class<?> entityClass, List<Filter> filters, List<Map<String, String>> arg2,
                                            String arg3, String arg4, Map<String, Parameter> arg5) {
        throw new AppUnhandledException();
    }

    @Override
    public Long getItemCount(Class<?> entityClass, List<Filter> filters, String whereClause) {
        return (long) filter(getItems(entityClass), filters).size();
    }

    public Long getItemCount(Class<?> entityClass, List<Filter> filters, String arg2, String arg3,
                             Map<String, Parameter> arg4) {
        return (long) filter(getItems(entityClass), filters).size();
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> List<T> getItems(Class<T> entityClass) {
        Validate.notNull(entityClass, "entityClass is null");

        if (!items.containsKey(entityClass))
            return new ArrayList<>();
        return (List<T>) items.get(entityClass);
    }

    @Override
    public <T> List<T> getItems(Class<T> entityClass, int startPosition, int maxCount, Map<String, Boolean> sortMap,
                                List<Filter> filters, String whereClause, List<String> joins) {
        return filter(getItems(entityClass), filters);
    }

    public <T> List<T> getItems(Class<T> arg0, int arg1, int arg2, Map<String, Boolean> arg3, List<Filter> arg4,
                                String arg5, List<String> arg6, String arg7, Map<String, Parameter> arg8) {
        return getItems(arg0);
    }

    public <T> List<T> getItems(Class<T> arg0, int arg1, int arg2, Map<String, Boolean> arg3, List<Filter> arg4,
                                String arg5, List<String> arg6, String arg7, Map<String, Parameter> arg8, String arg9) {
        return getItems(arg0);
    }

    @Override
    public <T> List<T> getItems(Class<T> entityClass, Map<String, Boolean> sortMap, List<Filter> filters,
                                String whereClause, List<String> joins) {
        return filter(getItems(entityClass), filters);
    }

    @Override
    public <T> List<T> getItems(Class<T> entityClass, String authorityField, int startPosition, int maxCount,
                                Map<String, Boolean> sortMap, List<Filter> filters, String whereClause, List<String> joins,
                                LockModeType lockModeType) {
        return filter(getItems(entityClass), filters);
    }

    @Override
    public <T> List<T> getItemIds(Class<T> aClass, int i, int i1, Map<String, Boolean> map, List<Filter> list, String s, String s1, List<String> list1, String s2, Map<String, Parameter> map1, String s3, String s4) {
        return List.of();
    }

    @Override
    public <T> List<T> getItems(Class<T> entityClass, String authorityField, int startPosition, int maxCount,
                                Map<String, Boolean> sortMap, List<Filter> filters, String whereClause, List<String> joins, long lockPeriod,
                                boolean includeLockItems) {
        return filter(getItems(entityClass), filters);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public <T> T getItemWhere(Class<T> entity, String propertyName, Object propertyValue, String whereClause) {
        List<? extends Class> items = getItems(entity.getClass());
        return !items.isEmpty() ? (T) items.get(0) : null;
    }

    @Override
    public JFWQuery getJfwQuery(Class<?> entityClass, EntityManager em) {
        throw new NotImplementedException("Not implemented");
    }

    public List<Object> getMergedItems() {
        return new ArrayList<>(mergedItems);
    }

    public List<Object> getPersistedItems() {
        return new ArrayList<>(persistedItems);
    }

    @Override
    public List<?> getReportSummaryResults(Class<?> entityClass, List<Map<String, String>> aggregates,
                                           Map<String, Boolean> groups, List<Filter> filters) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public Set<String> getSubEntitiesNames(Class<?> entityClass) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public WorkflowAction getViewAction(int actionKey, long viewId) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public WorkflowStatus getWorkflowStatusById(Long statusId) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public WorkflowStatus getWorkflowStatusByCode(String s) {
        return null;
    }

    @Override
    public List<WorkflowAction> listViewActions(String s) {
        return null;
    }

    @Override
    public String getUsernameById(long l) {
        return null;
    }

    @Override
    public boolean hasWatchers(String entityName, String entityId) {
        return hasWatchers;
    }

    public boolean isEntityDetached(Object entity) {
        Validate.notNull(entity, "entity is null");
        return detachedItems.contains(entity);
    }

    public boolean isFlushCalled() {
        return flushCalled;
    }

    public boolean isMerged(Object entity) {
        Validate.notNull(entity, "entity is null");
        return mergedItems.contains(entity);
    }

    public boolean isPersisted(Object entity) {
        Validate.notNull(entity, "entity is null");
        return persistedItems.contains(entity);
    }

    @Override
    public boolean isUserWatching(Long userId, String entityName, String entityId) {
        return isUserWatching;
    }

    @Override
    public List<WorkflowActionFunc> listCreatePrefunctions(String workflowName) {
        throw new NotImplementedException("Not implemented");
    }

    @Override
    public <T> T merge(T entity) {
        Validate.notNull(entity, "entity is null");
        mergedItems.add(entity);
        updateItemIfNeeded(entity);
        return (T) entity;
    }

    @Override
    public void persist(Object entity) {
        Validate.notNull(entity, "entity is null");
        persistedItems.add(entity);
        updateItemIfNeeded(entity);
    }

    @Override
    public void refresh(Object entity) {
    }

    @Override
    public void removeItem(Object entity) {
        Validate.notNull(entity, "entity is null");
        remove(entity.getClass(), o -> Objects.equals(getId(o), getId(entity)));
    }

    @Override
    public void removeItemById(Class<?> entityClass, long id) {
        remove(entityClass, o -> Objects.equals(getId(o), id));
    }

    @Override
    public List<NotificationScheme> listInterestedSchemes(Long aLong) {
        return null;
    }

    private void remove(Class<? extends Object> classOfT, Predicate<? super IJFWEntity> filter) {
        if (items.containsKey(classOfT))
            items.get(classOfT).removeIf(filter);
    }

    public void setHasWatchers(boolean hasWatchers) {
        this.hasWatchers = hasWatchers;
    }

    public void setUserWatching(boolean isUserWatching) {
        this.isUserWatching = isUserWatching;
    }

    @Override
    public void setWorkflowRecord(Class<?> entityClass, String workflowIdPropery, Long workflowIdValue,
                                  String recordIdProperty, Object recordIdValue) {
        throw new NotImplementedException("Not Implemented");
    }

    private <T> Object field(T item, String fieldName) {
        if (item == null)
            return null;
        if (!fieldName.contains("."))
            return readField(item, fieldName);
        String[] parts = fieldName.split("\\.", 2);
        return field(readField(item, parts[0]), parts[1]);
    }

    private <T> List<T> filter(List<T> items, List<Filter> filters) {
        if (!filterItems)
            return items;
        List<T> result = new ArrayList<>();
        for (T item : items)
            if (matches(item, filters))
                result.add(item);
        return result;
    }

    private boolean matches(Object fieldValue, Operator operator, Object filterValue) {
        if (asList(EQ, ON, LIKE).contains(operator))
            return Objects.equals(fieldValue, filterValue);
        if (ISNULL.equals(operator))
            return Objects.isNull(fieldValue) || asList(0L, 0, FALSE).contains(fieldValue);
        if (ISNOTNULL.equals(operator))
            return Objects.nonNull(fieldValue) && !asList(0L, 0, FALSE).contains(fieldValue);
        if (NE.equals(operator))
            return !Objects.equals(fieldValue, filterValue);
        return true;
    }

    private <T> boolean matches(T item, List<Filter> filters) {
        if (filters != null)
            for (Filter filter : filters)
                if (!matches(field(item, filter.getProperty()), filter.getOperator(), filter.getFirstOperand()))
                    return false;
        return true;
    }

    private <T> Object readField(T item, String fieldName) {
        try {
            return FieldUtils.readField(item, fieldName, true);
        } catch (IllegalAccessException e) {
            throw new AppUnhandledException(e, e.getMessage());
        }
    }

    private void updateItemIfNeeded(Object entity) {
        if (affectItems && entity instanceof IJFWEntity)
            add((IJFWEntity) entity, true);
    }

    @Override
    public <T> List<T> getItems(Class<T> arg0, int arg1, int arg2, Map<String, Boolean> arg3, List<Filter> arg4,
                                String arg5, String arg6, List<String> arg7, String arg8, Map<String, Parameter> arg9, String arg10) {
        throw new NotImplementedException("Not Impelemented");
    }
}
