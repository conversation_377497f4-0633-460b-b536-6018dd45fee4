package testUtil

import org.apache.camel.*
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ExecutorService
import java.util.concurrent.Future
import java.util.concurrent.TimeUnit

class FakeProducerTemplate : ProducerTemplate {

    val messages = mutableMapOf<String, String>()
    override fun start() {
    }

    override fun stop() {
    }

    override fun getCamelContext(): CamelContext {
        TODO("Not yet implemented")
    }

    override fun getMaximumCacheSize(): Int {
        TODO("Not yet implemented")
    }

    override fun setMaximumCacheSize(maximumCacheSize: Int) {
        TODO("Not yet implemented")
    }

    override fun getCurrentCacheSize(): Int {
        TODO("Not yet implemented")
    }

    override fun isThreadedAsyncMode(): Bo<PERSON>an {
        TODO("Not yet implemented")
    }

    override fun setThreadedAsyncMode(useExecutor: Boolean) {
        TODO("Not yet implemented")
    }

    override fun getDefaultEndpoint(): Endpoint {
        TODO("Not yet implemented")
    }

    override fun setDefaultEndpoint(defaultEndpoint: Endpoint?) {
        TODO("Not yet implemented")
    }

    override fun setDefaultEndpointUri(endpointUri: String?) {
        TODO("Not yet implemented")
    }

    override fun setEventNotifierEnabled(enabled: Boolean) {
        TODO("Not yet implemented")
    }

    override fun isEventNotifierEnabled(): Boolean {
        TODO("Not yet implemented")
    }

    override fun cleanUp() {
        TODO("Not yet implemented")
    }

    override fun send(exchange: Exchange?): Exchange {
        TODO("Not yet implemented")
    }

    override fun send(processor: Processor?): Exchange {
        TODO("Not yet implemented")
    }

    override fun send(endpointUri: String?, exchange: Exchange?): Exchange {
        TODO("Not yet implemented")
    }

    override fun send(endpointUri: String?, processor: Processor?): Exchange {
        TODO("Not yet implemented")
    }

    override fun send(endpointUri: String?, pattern: ExchangePattern?, processor: Processor?): Exchange {
        TODO("Not yet implemented")
    }

    override fun send(endpoint: Endpoint?, exchange: Exchange?): Exchange {
        TODO("Not yet implemented")
    }

    override fun send(endpoint: Endpoint?, processor: Processor?): Exchange {
        TODO("Not yet implemented")
    }

    override fun send(endpoint: Endpoint?, pattern: ExchangePattern?, processor: Processor?): Exchange {
        TODO("Not yet implemented")
    }

    override fun send(
        endpoint: Endpoint?,
        pattern: ExchangePattern?,
        processor: Processor?,
        resultProcessor: Processor?
    ): Exchange {
        TODO("Not yet implemented")
    }

    override fun sendBody(body: Any?) {
        TODO("Not yet implemented")
    }

    override fun sendBody(endpoint: Endpoint?, body: Any?) {
        TODO("Not yet implemented")
    }

    override fun sendBody(endpointUri: String, body: Any) {
        messages[endpointUri] = (body as String)
    }

    override fun sendBody(endpoint: Endpoint?, pattern: ExchangePattern?, body: Any?): Any {
        TODO("Not yet implemented")
    }

    override fun sendBody(endpointUri: String?, pattern: ExchangePattern?, body: Any?): Any {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeader(body: Any?, header: String?, headerValue: Any?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeader(endpointUri: String?, body: Any?, header: String?, headerValue: Any?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeader(endpoint: Endpoint?, body: Any?, header: String?, headerValue: Any?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeader(
        endpoint: Endpoint?,
        pattern: ExchangePattern?,
        body: Any?,
        header: String?,
        headerValue: Any?
    ): Any {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeader(
        endpoint: String?,
        pattern: ExchangePattern?,
        body: Any?,
        header: String?,
        headerValue: Any?
    ): Any {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndProperty(body: Any?, property: String?, propertyValue: Any?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndProperty(endpointUri: String?, body: Any?, property: String?, propertyValue: Any?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndProperty(endpoint: Endpoint?, body: Any?, property: String?, propertyValue: Any?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndProperty(
        endpoint: Endpoint?,
        pattern: ExchangePattern?,
        body: Any?,
        property: String?,
        propertyValue: Any?
    ): Any {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndProperty(
        endpoint: String?,
        pattern: ExchangePattern?,
        body: Any?,
        property: String?,
        propertyValue: Any?
    ): Any {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeaders(body: Any?, headers: MutableMap<String, Any>?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeaders(endpointUri: String?, body: Any?, headers: MutableMap<String, Any>?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeaders(endpoint: Endpoint?, body: Any?, headers: MutableMap<String, Any>?) {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeaders(
        endpointUri: String?,
        pattern: ExchangePattern?,
        body: Any?,
        headers: MutableMap<String, Any>?
    ): Any {
        TODO("Not yet implemented")
    }

    override fun sendBodyAndHeaders(
        endpoint: Endpoint?,
        pattern: ExchangePattern?,
        body: Any?,
        headers: MutableMap<String, Any>?
    ): Any {
        TODO("Not yet implemented")
    }

    override fun request(endpoint: Endpoint?, processor: Processor?): Exchange {
        TODO("Not yet implemented")
    }

    override fun request(endpointUri: String?, processor: Processor?): Exchange {
        TODO("Not yet implemented")
    }

    override fun requestBody(body: Any?): Any {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> requestBody(body: Any?, type: Class<T>?): T {
        TODO("Not yet implemented")
    }

    override fun requestBody(endpoint: Endpoint?, body: Any?): Any {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> requestBody(endpoint: Endpoint?, body: Any?, type: Class<T>?): T {
        TODO("Not yet implemented")
    }

    override fun requestBody(endpointUri: String?, body: Any?): Any {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> requestBody(endpointUri: String?, body: Any?, type: Class<T>?): T {
        TODO("Not yet implemented")
    }

    override fun requestBodyAndHeader(body: Any?, header: String?, headerValue: Any?): Any {
        TODO("Not yet implemented")
    }

    override fun requestBodyAndHeader(endpoint: Endpoint?, body: Any?, header: String?, headerValue: Any?): Any {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> requestBodyAndHeader(
        endpoint: Endpoint?,
        body: Any?,
        header: String?,
        headerValue: Any?,
        type: Class<T>?
    ): T {
        TODO("Not yet implemented")
    }

    override fun requestBodyAndHeader(endpointUri: String?, body: Any?, header: String?, headerValue: Any?): Any {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> requestBodyAndHeader(
        endpointUri: String?,
        body: Any?,
        header: String?,
        headerValue: Any?,
        type: Class<T>?
    ): T {
        TODO("Not yet implemented")
    }

    override fun requestBodyAndHeaders(endpointUri: String?, body: Any?, headers: MutableMap<String, Any>?): Any {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> requestBodyAndHeaders(
        endpointUri: String?,
        body: Any?,
        headers: MutableMap<String, Any>?,
        type: Class<T>?
    ): T {
        TODO("Not yet implemented")
    }

    override fun requestBodyAndHeaders(endpoint: Endpoint?, body: Any?, headers: MutableMap<String, Any>?): Any {
        TODO("Not yet implemented")
    }

    override fun requestBodyAndHeaders(body: Any?, headers: MutableMap<String, Any>?): Any {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> requestBodyAndHeaders(
        endpoint: Endpoint?,
        body: Any?,
        headers: MutableMap<String, Any>?,
        type: Class<T>?
    ): T {
        TODO("Not yet implemented")
    }

    override fun setExecutorService(executorService: ExecutorService?) {
        TODO("Not yet implemented")
    }

    override fun asyncSend(endpointUri: String?, exchange: Exchange?): CompletableFuture<Exchange> {
        TODO("Not yet implemented")
    }

    override fun asyncSend(endpointUri: String?, processor: Processor?): CompletableFuture<Exchange> {
        TODO("Not yet implemented")
    }

    override fun asyncSend(endpoint: Endpoint?, exchange: Exchange?): CompletableFuture<Exchange> {
        TODO("Not yet implemented")
    }

    override fun asyncSend(endpoint: Endpoint?, processor: Processor?): CompletableFuture<Exchange> {
        TODO("Not yet implemented")
    }

    override fun asyncSendBody(endpointUri: String?, body: Any?): CompletableFuture<Any> {
        TODO("Not yet implemented")
    }

    override fun asyncSendBody(endpoint: Endpoint?, body: Any?): CompletableFuture<Any> {
        TODO("Not yet implemented")
    }

    override fun asyncRequestBody(endpointUri: String?, body: Any?): CompletableFuture<Any> {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> asyncRequestBody(endpointUri: String?, body: Any?, type: Class<T>?): CompletableFuture<T> {
        TODO("Not yet implemented")
    }

    override fun asyncRequestBody(endpoint: Endpoint?, body: Any?): CompletableFuture<Any> {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> asyncRequestBody(endpoint: Endpoint?, body: Any?, type: Class<T>?): CompletableFuture<T> {
        TODO("Not yet implemented")
    }

    override fun asyncRequestBodyAndHeader(
        endpointUri: String?,
        body: Any?,
        header: String?,
        headerValue: Any?
    ): CompletableFuture<Any> {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> asyncRequestBodyAndHeader(
        endpointUri: String?,
        body: Any?,
        header: String?,
        headerValue: Any?,
        type: Class<T>?
    ): CompletableFuture<T> {
        TODO("Not yet implemented")
    }

    override fun asyncRequestBodyAndHeader(
        endpoint: Endpoint?,
        body: Any?,
        header: String?,
        headerValue: Any?
    ): CompletableFuture<Any> {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> asyncRequestBodyAndHeader(
        endpoint: Endpoint?,
        body: Any?,
        header: String?,
        headerValue: Any?,
        type: Class<T>?
    ): CompletableFuture<T> {
        TODO("Not yet implemented")
    }

    override fun asyncRequestBodyAndHeaders(
        endpointUri: String?,
        body: Any?,
        headers: MutableMap<String, Any>?
    ): CompletableFuture<Any> {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> asyncRequestBodyAndHeaders(
        endpointUri: String?,
        body: Any?,
        headers: MutableMap<String, Any>?,
        type: Class<T>?
    ): CompletableFuture<T> {
        TODO("Not yet implemented")
    }

    override fun asyncRequestBodyAndHeaders(
        endpoint: Endpoint?,
        body: Any?,
        headers: MutableMap<String, Any>?
    ): CompletableFuture<Any> {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> asyncRequestBodyAndHeaders(
        endpoint: Endpoint?,
        body: Any?,
        headers: MutableMap<String, Any>?,
        type: Class<T>?
    ): CompletableFuture<T> {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> extractFutureBody(future: Future<*>?, type: Class<T>?): T {
        TODO("Not yet implemented")
    }

    override fun <T : Any?> extractFutureBody(future: Future<*>?, timeout: Long, unit: TimeUnit?, type: Class<T>?): T {
        TODO("Not yet implemented")
    }
}