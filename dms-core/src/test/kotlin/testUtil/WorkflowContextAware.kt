package testUtil

import com.progressoft.jfw.model.bussinessobject.core.IJFWEntity
import com.progressoft.jfw.model.service.workflow.WorkflowService
import com.progressoft.jfw.shared.UIFieldAttributes
import com.progressoft.jfw.shared.UIPortletAttributes
import com.progressoft.jfw.shared.UITabAttributes
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import com.progressoft.jfw.workflow.WfChangeHandlerContextBuilder
import com.progressoft.jfw.workflow.WfContext


class WorkflowContextAware {
    fun <T : IJFWEntity?> createWfContext(entity: T): WfContext<T> {
        return createWfContext(entity, false)
    }

    fun <T : IJFWEntity?> createWfContext(entity: T, isDraft: Boolean): WfContext<T> {
        val variables: MutableMap<String?, Any?> = HashMap()
        val arguments: Map<String?, Any?> = HashMap()
        variables[if (isDraft) WorkflowService.WF_ARG_BEAN_DRAFT else WorkflowService.WF_ARG_BEAN] = entity
        return WfContext(variables, arguments, null)
    }

    fun <T : IJFWEntity?> createWfChangeHandlerContext(
        entity: T,
        fieldsAttrNames: Array<String?>,
        portletsAttrNames: Array<String?>,
        tabsAttrNames: Array<String?>
    ): WfChangeHandlerContext<T> {
        val builder = WfChangeHandlerContextBuilder(entity)

        for (fieldName in fieldsAttrNames) {
            builder.context.fieldsAttributes[fieldName] = UIFieldAttributes()
        }

        for (fieldName in portletsAttrNames) {
            builder.context.portletsAttributes[fieldName] = UIPortletAttributes()
        }

        for (fieldName in tabsAttrNames) {
            builder.context.tabsAttributes[fieldName] = UITabAttributes()
        }

        return WfChangeHandlerContext(builder.context)
    }
}
