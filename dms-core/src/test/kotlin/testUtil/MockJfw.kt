package testUtil

import com.progressoft.jfw.Jfw
import com.progressoft.jfw.ServiceUserEnabled
import com.progressoft.jfw.integration.JfwFacade
import com.progressoft.jfw.model.bussinessobject.core.IJFWEntity
import com.progressoft.jfw.model.bussinessobject.security.Org
import org.springframework.context.ApplicationContext
import org.springframework.core.io.Resource
import java.util.*

class MockJfw(private val messages: HashMap<String, String>) : Jfw() {
    override fun createEntity(
        s: String,
        ijfwEntity: IJFWEntity,
        map: Map<String, Any>,
        map1: Map<JfwFacade.Option, Any>,
        emitLogs: EmitLogs
    ) {
    }

    override fun disableServiceUser() {
    }

    override fun getResource(s: String): Resource? {
        return null
    }

    override fun enableServiceUser(s: String): ServiceUserEnabled? {
        return null
    }

    override fun <T : IJFWEntity?> executeAction(
        s: String,
        t: T,
        s1: String,
        b: Boolean,
        hashtable: Hashtable<String, Any>,
        emitLogs: EmitLogs
    ): T? {
        return null
    }

    override fun getApplicationContext(): ApplicationContext? {
        return null
    }

    override fun <T> getBean(aClass: Class<T>): T? {
        return null
    }

    override fun <T> getBean(s: String, aClass: Class<T>): T? {
        return null
    }

    override fun getCurrentPrefOrg(): Org? {
        return Org().apply {
            shortName = "TEST"
        }
    }

    override fun getCurrentTenant(): String? {
        return null
    }

    override fun getMessage(s: String, vararg objects: Any): String {
        return messages.getOrDefault(s, "MESSAGE NOT FOUND")
    }

    override fun isServiceUser(): Boolean {
        return false
    }
}
