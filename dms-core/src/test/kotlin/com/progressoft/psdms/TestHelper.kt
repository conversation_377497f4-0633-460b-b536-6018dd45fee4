package com.progressoft.psdms

import com.progressoft.dms.entities.*
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.DISPUTE_AMOUNT
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.REF_DISPUTE_REFERENCE_NUM
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.TRANSACTION_REF
import com.progressoft.jfw.model.bussinessobject.core.IJFWEntity
import com.progressoft.jfw.model.bussinessobject.core.JfwCountry
import com.progressoft.jfw.model.bussinessobject.workflow.WorkflowStatus
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_REF_INFO_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.FETCH_DISPUTE_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.FETCH_TRANSACTION_POPUP_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.MODIFICATION_WF_STATUSES
import com.progressoft.psdms.application.utils.Constants.Companion.TRANSACTION_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.TRANSACTION_INFO
import com.progressoft.psdms.testUtil.FakeItemDao
import testUtil.WorkflowContextAware

open class TestHelper {
    protected var itemDao: FakeItemDao = FakeItemDao()
    protected val contextAware = WorkflowContextAware()
    protected val disputeCaseViewPortlets: Array<String?> = arrayOf(
        FETCH_TRANSACTION_POPUP_PORTLET,
        TRANSACTION_ADDITIONAL_INFO,
        TRANSACTION_INFO,
        DISPUTE_REF_INFO_PORTLET,
        FETCH_DISPUTE_PORTLET
    )
    protected val disputeCaseViewFields: Array<String?> = arrayOf(
        DISPUTE_AMOUNT, TRANSACTION_REF,
        REF_DISPUTE_REFERENCE_NUM
    )


    protected companion object {
        const val REASON_CODE_UNIQUENESS_ERROR_MSG: String = "Reason code should be unique"
        const val REASON_TYPE_ERROR_MSG: String = "At least one reason type should be selected"
    }

    protected fun createNewParticipant(id: Long, code: String): DMS_Participant {
        val participant = DMS_Participant()
        participant.id = id
        participant.name = code
        participant.fullName = code
        participant.code = code
        itemDao.add(participant)
        return participant
    }

    protected fun createNewNcb(id: Long, code: String, country: JfwCountry): DMS_NationalCentralBank {
        val ncb = DMS_NationalCentralBank()
        ncb.id = id
        ncb.name = code
        ncb.code = code
        ncb.country = country
        itemDao.add(ncb)
        return ncb
    }

    protected fun createCountry(id: Long, code: String): JfwCountry {
        val country = JfwCountry()
        country.id = id
        country.name = code
        country.code = code
        itemDao.add(country)
        return country
    }

    protected fun createPaymentSystem(id: Long, code: String): DMS_PaymentSystems {
        val paymentSystems = DMS_PaymentSystems()
        paymentSystems.id = id
        paymentSystems.name = code
        paymentSystems.code = code
        paymentSystems.description = code
        itemDao.add(paymentSystems)
        return paymentSystems
    }

    protected fun createDisputeCase(
        id: Long,
        refNo: String,
        isDisputeOverDispute: Boolean,
        claimant: DMS_Participant,
        defendant: DMS_Participant,
        paymentSystems: DMS_PaymentSystems
    ): DMS_DisputeCase {
        val disputeCase = DMS_DisputeCase()
        disputeCase.id = id
        disputeCase.caseReferenceNumber = refNo
        disputeCase.disputeOverDispute = isDisputeOverDispute
        disputeCase.claimantBank = claimant
        disputeCase.defendantBank = defendant
        disputeCase.paymentSystem = paymentSystems
        disputeCase.statusId = getWorkflowStatus(MODIFICATION_WF_STATUSES[0])
        itemDao.add(disputeCase)
        return disputeCase
    }

    protected fun getWorkflowStatus(statusCode: String): WorkflowStatus {
        val workflowStatus = WorkflowStatus()
        workflowStatus.id = 1
        workflowStatus.code = statusCode
        workflowStatus.active = true
        itemDao.add(workflowStatus)
        return workflowStatus
    }

    protected fun createNewReason(id: Long, code: String, name: String): DMS_ReasonManagement {
        val reason = DMS_ReasonManagement()
        reason.id = id
        reason.code = code
        reason.name = name
        return reason
    }

    protected fun <T : IJFWEntity?> createWfContext(entity: T): WfContext<T> {
        return contextAware.createWfContext(entity)
    }
}