package com.progressoft.psdms.processor

import com.progressoft.dms.entities.*
import com.progressoft.jfw.Jfw
import com.progressoft.jfw.ServiceUserEnabled
import com.progressoft.jfw.TenantProvider
import com.progressoft.psdms.application.processor.NotifyArbitrationExceedingProcessor
import com.progressoft.psdms.application.utils.Constants.Companion.ARBITRATION_NOTIFICATION_THRESHOLD_IN_DAYS
import com.progressoft.psdms.email.EmailSenderService
import com.progressoft.psdms.testUtil.FakeItemDao
import com.progressoft.psdms.util.EmailUtils
import io.mockk.MockKAnnotations.init
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.apache.camel.impl.DefaultCamelContext
import org.apache.camel.support.DefaultExchange
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.sql.Timestamp
import java.time.LocalDateTime
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class NotifyArbitrationExceedingProcessorTest {
    @MockK
    lateinit var emailUtil: EmailUtils

    @MockK
    lateinit var emailService: EmailSenderService

    @MockK
    lateinit var jfw: Jfw

    private val itemDao = FakeItemDao()

    @MockK
    lateinit var tenantProvider: TenantProvider

    private lateinit var processor: NotifyArbitrationExceedingProcessor

    @BeforeEach
    fun setUp() {
        init(this, relaxUnitFun = true)
        itemDao.add(getClaimantBankCaseManagement())
        itemDao.add(getClaimantNCBCaseManagement())
        processor = NotifyArbitrationExceedingProcessor(emailService, emailUtil)
        processor.setJfw(jfw)
        processor.setItemDao(itemDao)
        processor.setTenantProvider(tenantProvider)
    }


    @Test
    fun givenDisputeExceededArbitration_whenProcess_thenTheEmailWillBeCreatedAndSentSuccessfully() {
        every { jfw.enableServiceUser("DMS") } returns ServiceUserEnabled(jfw, true)
        every { tenantProvider.tenants() } returns listOf("DMS")
        every { emailUtil.getConfigValue(ARBITRATION_NOTIFICATION_THRESHOLD_IN_DAYS) } returns "1"
        processor.process(DefaultExchange(DefaultCamelContext()))
        verify(exactly = 1) { emailUtil.getConfigValue(ARBITRATION_NOTIFICATION_THRESHOLD_IN_DAYS) }
        verify(exactly = 2) { emailService.sendEmail(any()) }
        assertEquals(2, itemDao.mergedItems.size)
        assertTrue((itemDao.mergedItems[0] as DMS_DisputeCase).bankExceedArbitrationNotified)
        assertTrue((itemDao.mergedItems[1] as DMS_DisputeCase).ncbExceedArbitrationNotified)
    }

    private fun getClaimantBankCaseManagement() = DMS_ClaimantBankCaseManagement().apply {
        id = 123
        disputeCase = getDisputeCase()
    }

    private fun getClaimantNCBCaseManagement() = DMS_ClaimantNCBCaseManagement().apply {
        id = 321
        disputeCase = getDisputeCase()
    }

    private fun getDisputeCase() = DMS_DisputeCase().apply {
        id = 1
        caseReferenceNumber = "12345"
        creationDateTime = Timestamp.valueOf(LocalDateTime.of(2024, 5, 28, 5, 1, 2))
        paymentSystem = getPaymentSystem()
        claimantBank = getClaimantBank()
        defendantBank = getDefendantBank()
        transactionReference = "1234567"
        disputedAmount = BigDecimal.valueOf(120)
        bankExceedArbitrationNotified = false
        ncbExceedArbitrationNotified = false
    }

    private fun getPaymentSystem() = DMS_PaymentSystems().apply {
        code = "payment system code"
        name = "payment system name"
    }

    private fun getClaimantBank() = DMS_Participant().apply {
        code = "claimant bank code"
        name = "claimant bank name"
    }

    private fun getDefendantBank() = DMS_Participant().apply {
        code = "defendant bank code"
        name = "defendant bank name"
    }
}