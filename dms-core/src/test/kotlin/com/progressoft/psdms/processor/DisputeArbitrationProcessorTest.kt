package com.progressoft.psdms.processor

import com.progressoft.dms.entities.*
import com.progressoft.jfw.Jfw
import com.progressoft.jfw.ServiceUserEnabled
import com.progressoft.psdms.application.processor.DisputeArbitrationProcessor
import com.progressoft.psdms.email.ArbitrationEmailSenderService
import com.progressoft.psdms.testUtil.FakeItemDao
import io.mockk.*
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.sql.Timestamp
import java.time.LocalDateTime

class DisputeArbitrationProcessorTest  {
    @MockK
    lateinit var arbitrationNotifierSender: ArbitrationEmailSenderService

    @MockK
    lateinit var jfw: Jfw

    var itemDao  = FakeItemDao()

    lateinit var  processor: DisputeArbitrationProcessor
    @BeforeEach
    fun setUp() {
        MockKAnnotations.init(this, relaxUnitFun = true)
        itemDao  = FakeItemDao()
        processor = DisputeArbitrationProcessor(arbitrationNotifierSender)
        processor.setJfw(jfw)
        processor.setItemDao(itemDao)
    }

    @Test
    fun givenNoActionPerformedByClaimantOrDefendantNCB_whenProcess_thenArbitrateToOperator(){
        every { jfw.enableServiceUser("DMS") } returns ServiceUserEnabled(jfw, true)
         every{ jfw.executeAction(any<DMS_DefendantNCBCaseManagement>(), "SVC_SLA_Arbitrate") } returns DMS_DefendantNCBCaseManagement()
        every{ jfw.executeAction(any<DMS_ClaimantNCBCaseManagement>(), "SVC_SLA_Arbitrate") } returns DMS_ClaimantNCBCaseManagement()
        justRun{ arbitrationNotifierSender.sendEmail(getRecipients(), getDisputeCase(creationDateTime)) }
        itemDao.add(getDefendantSlaConfiguration())
        itemDao.add(getClaimantSlaConfiguration())
        itemDao.add(getDefendantNCBCaseManagement())
        itemDao.add(getClaimantNCBCaseManagement())

        processor.process(null)
        verify(atLeast=1) { jfw.executeAction(any<DMS_DefendantNCBCaseManagement>(), "SVC_SLA_Arbitrate") }
        verify(atLeast =1) { jfw.executeAction(any<DMS_ClaimantNCBCaseManagement>(), "SVC_SLA_Arbitrate") }
    }

    fun getDefendantSlaConfiguration()= DMS_SLAConfiguration().apply {
        slaConfigurationParty = getDefendantNcbPartyCode()
        maxDays = 3
     }
    private fun getDefendantNcbPartyCode():DMS_SLAConfigurationParty {
        val party = DMS_SLAConfigurationParty()
        party.code = "DefendantNCB"
        return party
    }
    private fun getClaimantSlaConfiguration()= DMS_SLAConfiguration().apply {
        slaConfigurationParty = getClaimantNcbPartyCode()
        maxDays =2
    }
    private fun getClaimantNcbPartyCode():DMS_SLAConfigurationParty {
        val party = DMS_SLAConfigurationParty()
        party.code = "ClaimantNCB"
        return party
    }
    private fun getDefendantNCBCaseManagement() = DMS_DefendantNCBCaseManagement().apply {
        id = 321
        disputeCase = getDisputeCase(creationDateTime)
    }
    private fun getClaimantNCBCaseManagement() = DMS_ClaimantNCBCaseManagement().apply {
        id = 321
        disputeCase = getDisputeCase(creationDateTime)
    }
    private fun getDisputeCase(dateTime: Timestamp) = DMS_DisputeCase().apply {
        id = 1
        caseReferenceNumber = "12345"
        creationDateTime = dateTime
        paymentSystem = getPaymentSystem()
        claimantBank = getClaimantBank()
        defendantBank = getDefendantBank()
        transactionReference = "1234567"
        disputedAmount = BigDecimal.valueOf(120)
        bankExceedArbitrationNotified = false
        ncbExceedArbitrationNotified = false
    }
    private fun getPaymentSystem() = DMS_PaymentSystems().apply {
        code = "payment system code"
        name = "payment system name"
    }
    private fun getClaimantBank() = DMS_Participant().apply {
        code = "claimant bank code"
        name = "claimant bank name"
        email = "<EMAIL>"
    }
    private fun getDefendantBank() = DMS_Participant().apply {
        code = "defendant bank code"
        name = "defendant bank name"
        email = "<EMAIL>"
    }
    private fun getRecipients(): List<String?> {
        val disputeCase = getDisputeCase(creationDateTime)
        return listOf(
            disputeCase.claimantBank?.email,
            disputeCase.defendantBank?.email,
            disputeCase.claimantBank?.nationalCentralBank?.email,
            disputeCase.defendantBank?.nationalCentralBank?.email
        )
    }
    companion object{
        private val creationDateTime = Timestamp.valueOf(LocalDateTime.of(2024, 5, 5, 5, 1, 2))
    }
}
