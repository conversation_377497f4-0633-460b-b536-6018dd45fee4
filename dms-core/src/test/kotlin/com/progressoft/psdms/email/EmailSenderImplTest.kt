package com.progressoft.psdms.email

import com.progressoft.psdms.exception.FailedToSendEmailException
import io.mockk.MockKAnnotations.init
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertThrows
import javax.mail.Message
import javax.mail.MessagingException
import javax.mail.Session
import javax.mail.Transport
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals


internal class EmailSenderImplTest {

    @MockK
    lateinit var message: Message

    @MockK
    lateinit var session: Session

    @MockK
    lateinit var emailCredentials: EmailCredentials

    @MockK
    lateinit var emailProperties: EmailProperties

    @MockK
    lateinit var emailRecipients: EmailRecipients

    @MockK
    lateinit var email: Email

    @BeforeTest
    fun setUp() {
        init(this, relaxUnitFun = true)
    }

    @Test
    fun givenValidEmailWithEnabledSendingEmailFlag_whenSendEmail_thenSentSuccessfully() {
        every { email.recipients } returns emailRecipients
        every { email.subject } returns "subject"
        every { email.body } returns "body"
        every { email.message("<EMAIL>", session) } returns message
        mockkStatic(Session::class)
        every { Session.getInstance(emailProperties.properties(), emailCredentials.authenticator()) } returns session
        mockkStatic(Transport::class)
        every { Transport.send(message) } returns Unit

        val emailSenderImpl = EmailSenderImpl(true, "<EMAIL>", emailCredentials, emailProperties)
        emailSenderImpl.send(email)
        verify(exactly = 1) { email.message("<EMAIL>", session) }
        verify(exactly = 1) { Transport.send(message) }
    }

    @Test
    fun givenValidEmailWithDisabledSendingEmailFlag_whenSendEmail_thenReturnNothingAndEmailIsNotSent() {
        every { email.recipients } returns emailRecipients
        every { email.subject } returns "subject"
        every { email.body } returns "body"
        every { email.message("<EMAIL>", session) } returns message
        mockkStatic(Session::class)
        every { Session.getInstance(emailProperties.properties(), emailCredentials.authenticator()) } returns session
        mockkStatic(Transport::class)

        val emailSenderImpl = EmailSenderImpl(false, "<EMAIL>", emailCredentials, emailProperties)
        emailSenderImpl.send(email)
        verify(exactly = 0) { email.message("<EMAIL>", session) }
        verify(exactly = 0) { Transport.send(message) }
    }

    @Test
    fun givenInvalidEmailMessageWithEnabledSendingEmailFlag_whenSendEmail_thenThrowException() {
        every { email.recipients } returns emailRecipients
        every { email.subject } returns "subject"
        every { email.body } returns "body"
        every { email.message("<EMAIL>", session) } returns message
        mockkStatic(Session::class)
        every { Session.getInstance(emailProperties.properties(), emailCredentials.authenticator()) } returns session
        mockkStatic(Transport::class)
        every { Transport.send(message) } throws MessagingException()

        val emailSenderImpl = EmailSenderImpl(true, "<EMAIL>", emailCredentials, emailProperties)
        val exception = assertThrows(FailedToSendEmailException::class.java) {
            emailSenderImpl.send(email)
        }
        assertEquals("Failed To Send Email ...", exception.message)
    }
}