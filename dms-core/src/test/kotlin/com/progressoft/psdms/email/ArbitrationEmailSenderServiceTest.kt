package com.progressoft.psdms.email

import com.progressoft.dms.entities.DMS_ClaimantBankCaseManagement
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.dms.entities.DMS_PaymentSystems
import com.progressoft.psdms.application.utils.Constants.Companion.ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_ADDRESS
import com.progressoft.psdms.util.EmailUtils
import io.mockk.MockKAnnotations.init
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.sql.Timestamp
import java.time.LocalDateTime.of

class ArbitrationEmailSenderServiceTest {

    @MockK
    lateinit var emailUtil: EmailUtils

    @MockK
    lateinit var emailSender: EmailSenderImpl

    private lateinit var emailService: ArbitrationEmailSenderService

    @BeforeEach
    fun setUp() {
        init(this, relaxUnitFun = true)
        emailService = ArbitrationEmailSenderService(emailUtil)
    }

    @Test
    fun givenValidDisputeCase_whenCallSend_thenTheEmailWillBeCreatedAndSentSuccessfully() {
        every { emailUtil.getEmailSender() } returns emailSender
        every { emailSender.send(getEmail()) } returns Unit
        emailService.sendEmail(listOf("<EMAIL>"), getDisputeCase())

        verify(exactly = 1) { emailSender.send(getEmail()) }
    }

    private fun getDisputeCase() = DMS_DisputeCase().apply {
        id = 1
        caseReferenceNumber = "12345"
        creationDateTime = Timestamp.valueOf(of(2024, 5, 28, 5, 1, 2))
        paymentSystem = getPaymentSystem()
        claimantBank = getClaimantBank()
        defendantBank = getDefendantBank()
        transactionReference = "1234567"
        disputedAmount = BigDecimal.valueOf(120)
    }

    private fun getPaymentSystem() = DMS_PaymentSystems().apply {
        code = "payment system code"
        name = "payment system name"
    }

    private fun getClaimantBank() = DMS_Participant().apply {
        code = "claimant bank code"
        name = "claimant bank name"
    }

    private fun getDefendantBank() = DMS_Participant().apply {
        code = "defendant bank code"
        name = "defendant bank name"
    }

    private fun getEmail(): Email {
        return Email(
            EmailRecipients.to("<EMAIL>"),
            getEmailSubject(),
            getEmailBody(),
            null
        )
    }

    private fun getEmailSubject() = "Arbitration of Dispute Case to Operator: 12345"

    private fun getEmailBody() = "Dear Administrator\n" +
            "Kindly note the dispute has been arbitrated to Operator with the following details:\n" +
            "\n" +
            "Dispute ID: 12345\n" +
            "Dispute Creation Date/Time: 2024-05-28 05:01:02\n" +
            "Payment System: payment system code - payment system name\n" +
            "Claimant: claimant bank code - claimant bank name\n" +
            "Defendant: defendant bank code - defendant bank name\n" +
            "Transaction ID: 1234567\n" +
            "Disputed Amount: 120"
}