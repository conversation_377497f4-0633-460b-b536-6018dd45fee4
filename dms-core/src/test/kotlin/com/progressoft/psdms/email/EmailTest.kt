package com.progressoft.psdms.email


import io.mockk.MockKAnnotations.init
import io.mockk.every
import io.mockk.impl.annotations.MockK
import org.junit.jupiter.api.BeforeEach
import java.util.*
import javax.mail.Message
import javax.mail.Message.RecipientType.TO
import javax.mail.Multipart
import javax.mail.Session
import javax.mail.internet.InternetAddress
import kotlin.test.Test
import kotlin.test.assertEquals

internal class EmailTest {

    @MockK
    lateinit var session: Session

    @BeforeEach
    fun setUp() {
        init(this, relaxUnitFun = true)
        every { session.properties } returns Properties()
    }

    @Test
    fun givenEmailContent_whenCallMessage_thenReturnValidEmailMessage() {
        val email = Email(EmailRecipients.to("<EMAIL>"), "subject", "body", null)
        val message = email.message("abdallah", session)
        assertMessage(message)
        val content = message.content as Multipart
        assertEquals(1, content.count)
        assertEquals("body", content.getBodyPart(0).content)
    }

    @Test
    fun givenEmailContentWithAttachment_whenCallMessage_thenReturnValidEmailMessage() {
        val email = Email(
            EmailRecipients.to("<EMAIL>"),
            "subject",
            "body",
            EmailAttachment(
                "TestSheet.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ByteArray(0)
            )
        )
        val message = email.message("abdallah", session)
        assertMessage(message)
        val content = message.content as Multipart
        assertEquals(2, content.count)
        assertEquals("body", content.getBodyPart(0).content)
        assertEquals("TestSheet.xlsx", content.getBodyPart(1).fileName)
    }

    private fun assertMessage(message: Message) {
        assertEquals(1, message.from.size)
        assertEquals(InternetAddress("abdallah"), message.from[0])
        assertEquals(1, message.getRecipients(TO).size)
        assertEquals(InternetAddress("<EMAIL>"), message.getRecipients(TO)[0])
        assertEquals("subject", message.subject)
    }
}
