package com.progressoft.psdms.email

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.dms.entities.DMS_PaymentSystems
import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency
import com.progressoft.jfw.model.bussinessobject.security.BusinessRoleAction_.action
import com.progressoft.psdms.application.dtos.FeeDto
import com.progressoft.psdms.application.utils.Constants
import com.progressoft.psdms.testUtil.FakeItemDao
import com.progressoft.psdms.util.EmailUtils
import io.mockk.MockKAnnotations
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.math.BigDecimal.TEN
import java.sql.Timestamp
import java.time.LocalDateTime

class ActionEmailSenderServiceTest {

    @MockK
    private lateinit var emailUtil: EmailUtils

    private lateinit var itemDao: FakeItemDao
    @MockK
    private lateinit var emailSender: EmailSenderImpl

    private lateinit var emailService: ActionEmailSenderService
    private var actionToSubject :MutableMap<String, String> = HashMap()
    private var actionToBody :MutableMap<String, String> = HashMap()

    init {
        actionToSubject["Created"] = Constants.CREATION_EMAIL_SUBJECT
        actionToSubject["fee"] = Constants.FEE_EMAIL_SUBJECT
        actionToSubject["Accept"] = Constants.AUTO_ACCEPTANCE_EMAIL_SUBJECT

        actionToBody["Created"] = Constants.DISPUTE_CREATION_EMAIL_BODY
        actionToBody["fee"] = Constants.DISPUTE_FEE_EMAIL_BODY
        actionToBody["Accept"] = Constants.DISPUTE_ACTION_EMAIL_BODY
    }

    @BeforeEach
    fun setUp() {
        itemDao = FakeItemDao()
        MockKAnnotations.init(this, relaxUnitFun = true)
        emailService = ActionEmailSenderService(emailUtil, itemDao)
        itemDao.add(DMS_Participant().apply { code = "BHG"
        email = "<EMAIL>"})
    }

    @Test
    fun givenListOfRecipients_whenSendEmail_shouldSendEmailSuccessfully() {
        every { emailUtil.getEmailSender() } returns emailSender
        every { emailSender.send(getEmail("Accept")) } returns Unit
        emailService.sendEmail(listOf("<EMAIL>"), getDisputeCase())

        verify(atMost = 1) { emailUtil.getEmailSender() }
        verify(atMost = 1) { emailSender.send(getEmail("Accept")) }
    }

    @Test
    fun givenListOfRecipientsWithAction_whenSendEmail_shouldSendEmailSuccessfully() {
        every { emailUtil.getEmailSender() } returns emailSender
        every { emailSender.send(getEmail("Created")) } returns Unit
        emailService.sendEmail(listOf("<EMAIL>"), getDisputeCase(), "Created")

        verify(atMost = 1) { emailUtil.getEmailSender() }
        verify(atMost = 1) { emailSender.send(getEmail("Created")) }
    }

    @Test
    fun givenFeeDto_whenSendEmail_shouldSendEmailSuccessfully() {
        every { emailUtil.getEmailSender() } returns emailSender
        every { emailSender.send(getEmail("fee")) } returns Unit
        emailService.sendFeeEmail(getFee())

        verify(atMost = 1) { emailUtil.getEmailSender() }
        verify(atMost = 1) { emailSender.send(getEmail("fee")) }
    }

    private fun getFee() = FeeDto(TEN, "","BHG", getDisputeCase())

    private fun getDisputeCase() = DMS_DisputeCase().apply {
        id = 1
        caseReferenceNumber = "12345"
        creationDateTime = Timestamp.valueOf(LocalDateTime.of(2024, 5, 28, 5, 1, 2))
        paymentSystem = getPaymentSystem()
        claimantBank = getClaimantBank()
        defendantBank = getDefendantBank()
        transactionReference = "123456"
        disputedAmount = BigDecimal.valueOf(120)
        transactionCurrency = getJFWCurrency()
    }
    private fun getEmailBody(action: String) = actionToBody[action]!!
        .replace("{DISPUTE_CASE_ID}", "12345")
        .replace("{CREATION_DATE}", "2024-05-28 05:01:02")
        .replace("{PAYMENT_SYSTEM}", getPaymentSystem().codeNamePair)
        .replace("{CLAIMANT_BANK_NAME}", getClaimantBank().codeNamePair)
        .replace("{DEFENDANT_BANK_NAME}", getDefendantBank().codeNamePair)
        .replace("{TRANSACTION_ID}", "123456")
        .replace("{DISPUTE_AMOUNT}", "120")
        .replace("{FEE_AMOUNT}", "10")
        .replace("{FEE_CURRENCY}", "TEST")

    private fun getPaymentSystem() = DMS_PaymentSystems().apply {
        code = "payment system code"
        name = "payment system name"
    }

    private fun getClaimantBank() = DMS_Participant().apply {
        code = "claimant bank code"
        name = "claimant bank name"
    }

    private fun getDefendantBank() = DMS_Participant().apply {
        code = "defendant bank code"
        name = "defendant bank name"
    }

    private fun getJFWCurrency() = JFWCurrency().apply{
        stringISOCode = "TEST"
    }

    private fun getEmail(action: String): Email {
        return Email(
            EmailRecipients.to("<EMAIL>"),
            getEmailSubject(action),
            getEmailBody(action),
            null
        )
    }
    private fun getEmailSubject(action: String) = actionToSubject[action]!!
        .replace("{DISPUTE_CASE_ID}", "12345")
}