package com.progressoft.psdms.email

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import javax.mail.Message
import javax.mail.Message.RecipientType.CC
import javax.mail.Message.RecipientType.TO
import javax.mail.Session
import javax.mail.internet.InternetAddress
import javax.mail.internet.MimeMessage

internal class EmailRecipientsTest {

    private val message: Message = MimeMessage(null as Session?)

    @Test
    fun givenNullParameters_whenCallingConstructor_thenThrowIllegalArgumentException() {
        val exception = assertThrows(IllegalArgumentException::class.java) {
            EmailRecipients(null, null, null)
        }
        assertEquals("All parameters are empty ...", exception.message)
    }

    @Test
    fun givenNullTo_whenCallingTo_thenThrowIllegalArgumentException() {
        val exception = assertThrows(IllegalArgumentException::class.java) {
            EmailRecipients.to(null)
        }
        assertEquals("All parameters are empty ...", exception.message)
    }

    @Test
    fun givenNullCc_whenCallingCc_thenThrowIllegalArgumentException() {
        val exception = assertThrows(IllegalArgumentException::class.java) {
            EmailRecipients.cc(null)
        }
        assertEquals("All parameters are empty ...", exception.message)
    }

    @Test
    fun givenNullToAndCc_whenCallingToAndCc_thenThrowIllegalArgumentException() {
        val exception = assertThrows(IllegalArgumentException::class.java) {
            EmailRecipients.toAndCc(null, null)
        }
        assertEquals("All parameters are empty ...", exception.message)
    }

    @Test
    fun givenGivenEmailRecipients_whenCallingTo_thenReturnEmailRecipientsWithTo() {
        EmailRecipients.to("<EMAIL>").addTo(message)
        assertEquals(1, message.getRecipients(TO).size)
        assertEquals(InternetAddress("<EMAIL>"), message.getRecipients(TO)[0])
    }

    @Test
    fun givenGivenEmailRecipients_whenCallingCc_thenReturnEmailRecipientsWithCc() {
        EmailRecipients.cc("<EMAIL>").addTo(message)
        assertEquals(1, message.getRecipients(CC).size)
        assertEquals(InternetAddress("<EMAIL>"), message.getRecipients(CC)[0])
    }

    @Test
    fun givenGivenEmailRecipients_whenCallingToAndCc_thenReturnEmailRecipientsWithToAndCc() {
        EmailRecipients.toAndCc("<EMAIL>", "<EMAIL>").addTo(message)
        assertEquals(1, message.getRecipients(TO).size)
        assertEquals(1, message.getRecipients(CC).size)
        assertEquals(InternetAddress("<EMAIL>"), message.getRecipients(TO)[0])
        assertEquals(InternetAddress("<EMAIL>"), message.getRecipients(CC)[0])
    }
}