package com.progressoft.psdms.email

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test


internal class EmailAttachmentTest {

    @Test
    fun givenEmailAttachment_whenCallingAttachment_thenReturnMimeBodyPart() {
        val fileName = "TestSheet.xlsx"
        val contentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        val emailAttachment = EmailAttachment(
            fileName, contentType, ByteArray(0)
        )
        val attachment = emailAttachment.attachment()
        assertEquals(fileName, attachment.fileName)
        assertEquals("$contentType; \r\n\tname=$fileName",attachment.contentType)
    }
}