package com.progressoft.psdms.email

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class EmailPropertiesTest {

    @Test
    fun givenEmailProperties_whenCallingProperties_thenReturnEmailProperties() {
        val emailProperties = EmailProperties(
            isAuthenticated = true,
            isTlsEnabled = false,
            host = "hostname",
            port = 587
        )
        assertEquals("true", emailProperties.properties().getProperty("mail.smtp.auth"))
        assertEquals("false", emailProperties.properties().getProperty("mail.smtp.starttls.enable"))
        assertEquals("hostname", emailProperties.properties().getProperty("mail.smtp.host"))
        assertEquals("587", emailProperties.properties().getProperty("mail.smtp.port"))
    }
}