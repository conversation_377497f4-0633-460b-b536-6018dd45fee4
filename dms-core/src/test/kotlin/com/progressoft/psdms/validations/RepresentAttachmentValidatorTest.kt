package com.progressoft.psdms.validations

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DefendantBankCaseManagement
import com.progressoft.dms.entities.DMS_DisputeAtt
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.jfw.AppValidationException
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.application.validations.RepresentAttachmentValidator
import com.progressoft.psdms.testUtil.FakeItemDao
import io.mockk.every
import io.mockk.mockk
import org.junit.Before
import org.junit.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import kotlin.test.assertEquals

class RepresentAttachmentValidatorTest {

    private val fakeItemDao = FakeItemDao()

    private val repValidator = RepresentAttachmentValidator()

    private val wfContext = mockk<WfContext<DMS_BaseCaseManagement>>()


    @Before
    fun setUp(){
        repValidator.setItemDao(fakeItemDao)
    }


    @Test
    fun givenDisputeCaseWithAttachment_whenCallValidator_shouldNotThrowException(){
        val att = DMS_DisputeAtt().apply{
            entityId = "1"
        }
        fakeItemDao.add(att)
        val disputeCase = DMS_DisputeCase().apply {
            id = 2
            caseReferenceNumber = "1"
        }
        val bankCaseManagement = DMS_DefendantBankCaseManagement().apply {
            this.disputeCase = disputeCase
        }
        every { wfContext.entity } returns bankCaseManagement
        assertDoesNotThrow { repValidator.validate(wfContext) }
    }
    @Test
    fun givenDisputeCaseWithNoAttachment_whenCallValidator_shouldThrowException(){
        val disputeCase = DMS_DisputeCase().apply {
            id = 2
            caseReferenceNumber = "1"
        }
        val bankCaseManagement = DMS_DefendantBankCaseManagement().apply {
            this.disputeCase = disputeCase
        }
        every { wfContext.entity } returns bankCaseManagement
        val exception = assertThrows<AppValidationException> { repValidator.validate(wfContext) }
        assertEquals("Kindly add a new Attachment before you Represent the dispute", exception.message)
    }

}