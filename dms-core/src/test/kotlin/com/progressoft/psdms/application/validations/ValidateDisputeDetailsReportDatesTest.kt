package com.progressoft.psdms.application.validations

import com.progressoft.dms.entities.DMS_DisputeDetailsReport
import com.progressoft.jfw.AppValidationException
import com.progressoft.jfw.Jfw
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.TestHelper
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import testUtil.MockJfw
import java.sql.Timestamp
import java.time.LocalDateTime

class ValidateDisputeDetailsReportDatesTest : TestHelper() {
    private lateinit var validator: ValidateDisputeDetailsReportDates
    private lateinit var jfw: Jfw

    @BeforeEach
    fun setUp() {
        validator = ValidateDisputeDetailsReportDates()
        val messages = HashMap<String, String>()
        messages["date.from.after.date.to.validation"] = "date to should be after date from"
        jfw = MockJfw(messages)
        validator.setJfw(jfw)
    }

    @Test
    fun givenRecordWithValidDates_whenCallingTheHandler_thenShouldPass() {
        val record = DMS_DisputeDetailsReport()
        record.dateFrom = Timestamp.valueOf(LocalDateTime.now())
        record.dateTo = Timestamp.valueOf(LocalDateTime.now().plusDays(3))
        val context: WfContext<DMS_DisputeDetailsReport> = createWfContext(record)
        validator.validate(context)
    }

    @Test
    fun givenRecordWithFromDateGreaterThanToDate_whenCallingTheHandler_thenShouldThrowException() {
        val record = DMS_DisputeDetailsReport()
        record.dateFrom = Timestamp.valueOf(LocalDateTime.now().plusDays(3))
        record.dateTo = Timestamp.valueOf(LocalDateTime.now())
        val context: WfContext<DMS_DisputeDetailsReport> = createWfContext(record)
        val appValidationException = assertThrows(AppValidationException::class.java) { validator.validate(context) }
        assertEquals(jfw.getMessage("date.from.after.date.to.validation"), appValidationException.message)
    }
}