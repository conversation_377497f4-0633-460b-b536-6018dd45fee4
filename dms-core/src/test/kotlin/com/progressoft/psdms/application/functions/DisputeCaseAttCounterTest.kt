package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DefendantBankCaseManagement
import com.progressoft.dms.entities.DMS_DisputeAtt
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.testUtil.FakeItemDao
import io.mockk.every
import io.mockk.mockk
import org.junit.Before
import org.junit.Test
import kotlin.test.assertEquals

class DisputeCaseAttCounterTest{

    private val fakeItemDao = FakeItemDao()

    private val wfContext = mockk<WfContext<DMS_BaseCaseManagement>>()

    private val disputeCaseAttCounter = DisputeCaseAttCounter()

    @Before
    fun setUp(){
        val att = DMS_DisputeAtt().apply{
            entityId = "1"
        }
        fakeItemDao.add(att)
        disputeCaseAttCounter.setItemDao(fakeItemDao)
    }

    @Test
    fun givenDisputeCase_whenCallCounter_shouldCountAndPersist(){
        val disputeCase = DMS_DisputeCase().apply {
            id = 1
            caseReferenceNumber = "1"
        }
        val bankCaseManagement = DMS_DefendantBankCaseManagement().apply {
            this.disputeCase = disputeCase
        }
        every { wfContext.entity } returns bankCaseManagement
        disputeCaseAttCounter.execute(wfContext)
        val mergedItems = fakeItemDao.mergedItems
        assertEquals(1, mergedItems.size)
        assertEquals(1, (mergedItems[0] as DMS_DisputeCase).numberOfAtt)
    }
}