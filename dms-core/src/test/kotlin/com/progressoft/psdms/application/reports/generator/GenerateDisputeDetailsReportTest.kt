package com.progressoft.psdms.application.reports.generator

import com.fasterxml.jackson.databind.ObjectMapper
import com.progressoft.dms.entities.DMS_DisputeDetailsReport
import com.progressoft.jfw.AppValidationException
import com.progressoft.jfw.Jfw
import com.progressoft.jfw.model.bussinessobject.core.JfwCountry
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.web.client.RestTemplate
import testUtil.MockJfw
import java.io.IOException


class GenerateDisputeDetailsReportTest : ReportTestHelper() {
    private lateinit var jfw: Jfw
    private lateinit var restTemplate: RestTemplate
    private lateinit var function: GenerateDisputeDetailsReport
    private lateinit var country: JfwCountry

    @BeforeEach
    fun setUp() {
        val messages = HashMap<String, String>()
        messages["dispute.details.report.failed.generation"] = "Failed To Generate Dispute Details Report"
        jfw = MockJfw(messages)
        country = createCountry(1, "COG")
        createNewNcb(1, "TEST", country)
    }

    @Test
    @Throws(IOException::class)
    fun givenReportEntityWithFilters_whenExecute_thenAttachmentUuidShouldBeUpdatedFromTheResponse() {
        restTemplate = mockRestTemplateAndPullReceivedGenerateReportRequest()
        val report: DMS_DisputeDetailsReport = createDisputeDetailsReportEntity()
        val reportURL = "http://report_url"
        function = GenerateDisputeDetailsReport(reportURL, restTemplate)
        function.setJfw(jfw)
        function.setItemDao(itemDao)
        function.execute(mockWorkFlowContext(report))
        assertEquals(UUID, report.attachmentUuid)
        assertEquals(
            "$reportURL/reports/execution/async?reportName=dispute_details_report_en&format=excel_xlsx",
            generateReportRequest.url
        )
        assertEquals(HttpMethod.POST, generateReportRequest.method)
        assertEquals(1, generateReportRequest.uriVariables.size)
        assertEquals(String::class.java, generateReportRequest.responseType)
        assertEquals(1, generateReportRequest.requestEntity?.headers?.size)
        assertEquals(MediaType.APPLICATION_JSON, generateReportRequest.requestEntity?.headers?.contentType)
        Assertions.assertNotNull(generateReportRequest.requestEntity?.body)
        val body = generateReportRequest.requestEntity?.body
        val reqParams: HashMap<String, LinkedHashMap<String, String>> =
            ObjectMapper().readValue(body.toString(), typeRef())
        assertEquals(1, reqParams.size)
        val parameters = reqParams["parameters"]!!
        assertParameters(parameters, report)
    }

    private fun assertParameters(
        parameters: java.util.LinkedHashMap<String, String>,
        report: DMS_DisputeDetailsReport
    ) {
        assertEquals(12, parameters.size)
        assertEquals(expectedLogo(), parameters["logo"])
        assertEquals("en", parameters["lang"])
        assertEquals(report.id.toString(), parameters["reportID"])
        assertEquals(report.nature, parameters["nature"])
        assertEquals(country.id.toString(), parameters["countryID"])
        assertEquals(country.code, parameters["countryCode"])
        assertEquals(report.reason?.id.toString(), parameters["reasonID"])
        assertEquals(report.reason?.name, parameters["reasonCode"])
        assertEquals(report.paymentSystem?.id.toString(), parameters["paymentSysID"])
        assertEquals(report.paymentSystem?.code, parameters["paymentSysCode"])
        assertEquals(report.amountFrom.toString(), parameters["amountFrom"])
        assertEquals(report.amountTo.toString(), parameters["amountTo"])
    }

    @Test
    fun givenReportEntityWithFiltersAndErrorResponse_whenExecute_thenShouldThrowAppException() {
        val report: DMS_DisputeDetailsReport = createDisputeDetailsReportEntity()
        val restTemplate = mock(RestTemplate::class.java)
        doThrow(RuntimeException("Integration-Error"))
            .`when`(restTemplate)
            .exchange(
                anyString(),
                any(HttpMethod::class.java),
                any(HttpEntity::class.java),
                any<Class<String>>()
            )
        function = GenerateDisputeDetailsReport("api_url", restTemplate)
        function.setJfw(jfw)
        val exception = Assertions.assertThrows(
            AppValidationException::class.java
        ) { function.execute(mockWorkFlowContext(report)) }
        assertEquals("Failed To Generate Dispute Details Report", exception.message)
    }

    @Test
    fun givenFunction_whenRequestReportName_thenShouldReturnCorrectName() {
        restTemplate = mockRestTemplateAndPullReceivedGenerateReportRequest()
        function = GenerateDisputeDetailsReport("api_url", restTemplate)
        assertEquals("dispute_details_report", function.reportName)
    }

    companion object {
        private const val UUID: String = "4e74d8ee-e9c7-4828-9c24-757130ec00a0"
    }
}