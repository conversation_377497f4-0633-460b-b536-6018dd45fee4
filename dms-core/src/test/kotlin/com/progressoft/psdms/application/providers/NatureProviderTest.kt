package com.progressoft.psdms.application.providers

import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.enums.Nature
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class NatureProviderTest {
    private val provider = NatureProvider()

    @Test
    fun whenCallFieldTypeProvider_thenShouldReturnExpectedValues() {
        val allValues: MutableMap<String, String> = provider.allValues(ValueProviderContext())
        assertNotNull(allValues)
        assertEquals(enumValues<Nature>().size, allValues.keys.size)

        enumValues<Nature>().forEach { expectedValue ->
            assertTrue(allValues.values.contains(expectedValue.description))
        }
    }
}
