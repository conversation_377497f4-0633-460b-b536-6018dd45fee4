package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.dms.entities.DMS_PaymentSystems
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import com.progressoft.psdms.TestHelper
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import testUtil.MockJfw
import kotlin.test.assertEquals

class FetchDisputeHandlerTest : TestHelper() {
    private val handler = FetchDisputesHandler()
    private lateinit var arabBank: DMS_Participant
    private lateinit var ahliBank: DMS_Participant
    private lateinit var paymentSystem: DMS_PaymentSystems


    @BeforeEach
    fun setUp() {
        handler.setJfw(MockJfw(hashMapOf()))
        handler.setItemDao(itemDao)
        itemDao.filterItems()
        arabBank = createNewParticipant(1, "ARAB")
        ahliBank = createNewParticipant(2, "AHLI")
        paymentSystem = createPaymentSystem(1, "ACH")
    }

    @Test
    fun givenPersistedDisputeCase_whenCallingTheHandler_thenShouldFillTheEntitiesRefDisputeInformationWithTheExpectedValues() {
        val referenceDispute = createDisputeCase(1, "123", false, arabBank, ahliBank, paymentSystem)
        val disputeOverDispute = createDisputeCase(2, "456", true, arabBank, ahliBank, paymentSystem)
        disputeOverDispute.refDisputeCaseNumber = referenceDispute.caseReferenceNumber
        itemDao.add(disputeOverDispute)
        val context: WfChangeHandlerContext<DMS_DisputeCase> =
            contextAware.createWfChangeHandlerContext(
                disputeOverDispute,
                disputeCaseViewFields,
                disputeCaseViewPortlets,
                arrayOfNulls(0)
            )
        handler.handle(context)
        assertReferenceDisputeInfo(referenceDispute, disputeOverDispute)

    }

    private fun assertReferenceDisputeInfo(referenceDispute: DMS_DisputeCase, disputeOverDispute: DMS_DisputeCase) {
        assertEquals(referenceDispute.caseReferenceNumber, disputeOverDispute.refDisputeCaseNumber)
        assertEquals(referenceDispute.paymentSystem, disputeOverDispute.refDisputePaymentSys)
        assertEquals(referenceDispute.claimantBank, disputeOverDispute.refDisputeDefendantBnk)
        assertEquals(referenceDispute.defendantBank, disputeOverDispute.refDisputeClaimantBnk)
        assertEquals(referenceDispute.transactionAmount, disputeOverDispute.refDisputeTransactionAmount)
    }

}