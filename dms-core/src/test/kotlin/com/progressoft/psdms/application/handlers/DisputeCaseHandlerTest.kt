package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.dms.entities.DMS_PaymentSystems
import com.progressoft.psdms.TestHelper
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_REF_INFO_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.FETCH_DISPUTE_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.FETCH_TRANSACTION_POPUP_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.TRANSACTION_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.TRANSACTION_INFO
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import testUtil.MockJfw
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class DisputeCaseHandlerTest : TestHelper() {
    private val handler = DisputeCaseHandler()
    private lateinit var arabBank: DMS_Participant
    private lateinit var ahliBank: DMS_Participant
    private lateinit var paymentSystem: DMS_PaymentSystems


    @BeforeEach
    fun setUp() {
        handler.setJfw(MockJfw(hashMapOf()))
        arabBank = createNewParticipant(1, "ARAB")
        ahliBank = createNewParticipant(2, "AHLI")
        paymentSystem = createPaymentSystem(1, "ACH")
    }

    @Test
    fun givenDisputeCaseWithNormalType_whenCallingTheHandler_thenTransactionPortletsShouldBeShownAndDisputePortletShouldBeHidden() {
        val disputeCase = createDisputeCase(1, "123", false, arabBank, ahliBank, paymentSystem)
        val context = contextAware.createWfChangeHandlerContext(
            disputeCase,
            disputeCaseViewFields,
            disputeCaseViewPortlets,
            arrayOfNulls(0)
        )
        handler.handle(context)
        assertTrue {
            context.isPortletVisible(FETCH_TRANSACTION_POPUP_PORTLET)
            context.isPortletVisible(TRANSACTION_ADDITIONAL_INFO)
            context.isPortletVisible(TRANSACTION_INFO)
        }
        assertFalse {
            context.isPortletVisible(DISPUTE_REF_INFO_PORTLET)
            context.isPortletVisible(FETCH_DISPUTE_PORTLET)
        }
    }

    @Test
    fun givenDisputeCaseWithDisputeOveDisputeType_whenCallingTheHandler_thenTransactionPortletsShouldBeHiddenAndDisputePortletShouldBeShown() {
        val disputeCase = createDisputeCase(1, "123", true, arabBank, ahliBank, paymentSystem)
        val context = contextAware.createWfChangeHandlerContext(
            disputeCase,
            disputeCaseViewFields,
            disputeCaseViewPortlets,
            arrayOfNulls(0)
        )
        handler.handle(context)
        assertFalse {
            context.isPortletVisible(FETCH_TRANSACTION_POPUP_PORTLET)
            context.isPortletVisible(TRANSACTION_ADDITIONAL_INFO)
            context.isPortletVisible(TRANSACTION_INFO)
        }
        assertTrue {
            context.isPortletVisible(DISPUTE_REF_INFO_PORTLET)
            context.isPortletVisible(FETCH_DISPUTE_PORTLET)
        }
    }

    @Test
    fun givenDisputeOveDisputeCaseWithStatusNotRelateToFetchTransactionStatuses_whenCallingTheHandler_thenFetchTransactionPortletsShouldBeShown() {
        val disputeCase = createDisputeCase(1, "123", false, arabBank, ahliBank, paymentSystem)
        disputeCase.statusId = getWorkflowStatus("1")
        val context = contextAware.createWfChangeHandlerContext(
            disputeCase,
            disputeCaseViewFields,
            disputeCaseViewPortlets,
            arrayOfNulls(0)
        )
        handler.handle(context)
        assertTrue {
            context.isPortletVisible(TRANSACTION_ADDITIONAL_INFO)
            context.isPortletVisible(TRANSACTION_INFO)
        }
        assertFalse {
            context.isPortletVisible(DISPUTE_REF_INFO_PORTLET)
            context.isPortletVisible(FETCH_TRANSACTION_POPUP_PORTLET)
            context.isPortletVisible(FETCH_DISPUTE_PORTLET)
        }
    }


}