package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeDetailsReport
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import com.progressoft.psdms.TestHelper
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import testUtil.MockJfw

class HideDisputeDetailsReportsAttPortletTest: TestHelper(){

    private lateinit var handler: HideDisputeDetailsReportsAttPortlet
    private val reportsAttPortlet = "dispute.details.report.att"
    private val generatedStep = "700002"
    private val readyToGeneratedStep = "700001"

    @BeforeEach
    fun setUp() {
        handler = HideDisputeDetailsReportsAttPortlet()
        handler.setJfw(MockJfw(hashMapOf()))
    }


    @Test
    fun givenRecordWithGeneratedStatus_whenCallingTheHandler_thenThePortletShouldBeVisible() {
        val entity = DMS_DisputeDetailsReport()
        entity.statusId = getWorkflowStatus(generatedStep)
        val context: WfChangeHandlerContext<DMS_DisputeDetailsReport> =
            contextAware.createWfChangeHandlerContext(
                entity,
                arrayOfNulls(0),
                arrayOf(reportsAttPortlet),
                arrayOfNulls(0)
            )
        handler.handle(context)
        Assertions.assertTrue(context.isPortletVisible(reportsAttPortlet))
    }

    @Test
    fun givenRecordWithReadyToGenerateStatus_whenCallingTheHandler_thenThePortletShouldNotBeVisible() {
        val entity = DMS_DisputeDetailsReport()
        entity.statusId = getWorkflowStatus(readyToGeneratedStep)
        val context: WfChangeHandlerContext<DMS_DisputeDetailsReport> =
            contextAware.createWfChangeHandlerContext(
                entity,
                arrayOfNulls(0),
                arrayOf(reportsAttPortlet),
                arrayOfNulls(0)
            )
        handler.handle(context)
        Assertions.assertFalse(context.isPortletVisible(reportsAttPortlet))
    }

}