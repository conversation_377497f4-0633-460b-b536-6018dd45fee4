package com.progressoft.psdms.application.validations

import com.progressoft.dms.entities.DMS_DisputeDetailsReport
import com.progressoft.jfw.AppValidationException
import com.progressoft.jfw.Jfw
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.TestHelper
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import testUtil.MockJfw
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN

class ValidateDisputeDetailsReportAmountsTest : TestHelper() {
    private lateinit var validator: ValidateDisputeDetailsReportAmounts
    private lateinit var jfw: Jfw

    @BeforeEach
    fun setUp() {
        validator = ValidateDisputeDetailsReportAmounts()
        val messages = HashMap<String, String>()
        messages["amount.range.validation.msg"] = "Amount From should be less than Amount To"
        jfw = MockJfw(messages)
        validator.setJfw(jfw)
    }

    @Test
    fun givenRecordWithValidAmountRange_whenCallingTheHandler_thenShouldPass() {
        val record = DMS_DisputeDetailsReport()
        record.amountFrom = ONE
        record.amountTo = TEN
        val context: WfContext<DMS_DisputeDetailsReport> = createWfContext(record)
        validator.validate(context)
    }

    @Test
    fun givenRecordWithInvalidAmountRange_whenCallingTheHandler_thenShouldThrowException() {
        val record = DMS_DisputeDetailsReport()
        record.amountFrom = TEN
        record.amountTo = ONE
        val context: WfContext<DMS_DisputeDetailsReport> = createWfContext(record)
        val appValidationException = assertThrows(AppValidationException::class.java) { validator.validate(context) }
        assertEquals(jfw.getMessage("amount.range.validation.msg"), appValidationException.message)
    }
}