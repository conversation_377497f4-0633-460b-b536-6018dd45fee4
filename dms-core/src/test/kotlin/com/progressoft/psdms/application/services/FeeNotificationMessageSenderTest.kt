package com.progressoft.psdms.application.services

import com.progressoft.communication.Envelope
import com.progressoft.communication.messaging.XmlEnvelopeSerializer
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Endpoint
import com.progressoft.dms.entities.DMS_FeeMessage
import com.progressoft.dms.entities.DMS_PaymentSystems
import com.progressoft.psdms.application.dtos.FeeDto
import com.progressoft.psdms.application.utils.Constants.Companion.ACH
import com.progressoft.psdms.application.utils.Constants.Companion.IIPS
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentSystem
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentTenant
import com.progressoft.psdms.application.utils.Utils
import com.progressoft.psdms.application.utils.Utils.Companion.generateNewId
import com.progressoft.psdms.email.ActionEmailSenderService
import com.progressoft.psdms.testUtil.FakeItemDao
import io.mockk.*
import io.mockk.MockKAnnotations.init
import io.mockk.impl.annotations.MockK
import org.apache.camel.CamelContext
import org.apache.commons.lang.StringUtils.EMPTY
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import testUtil.FakeProducerTemplate
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalDateTime.now
import java.time.LocalDateTime.of
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class FeeNotificationMessageSenderTest {

    @MockK
    private lateinit var camelContext: CamelContext
    @MockK
    private lateinit var notifSender: ActionEmailSenderService

    private val producerTemplate = FakeProducerTemplate()

    private val itemDao = FakeItemDao()

    private lateinit var feeNotificationMessageSender: FeeNotificationMessageSender

    @BeforeEach
    fun setUp() {
        init(this, relaxUnitFun = true)
        itemDao.add(getPaymentSystemEndpoint(123L, ACH, ACH_URL))
        itemDao.add(getPaymentSystemEndpoint(321L, IIPS, IIPS_URL))
        feeNotificationMessageSender = FeeNotificationMessageSender(itemDao, camelContext, notifSender)
    }

    @Test
    fun givenListOfFeeWithSystemCode_whenCallSend_thenShouldSendTheListOfFeeAsCSVToThePaymentSystem() {
        mockkConstructor(Utils.Companion::class)
        mockkStatic(LocalDateTime::class)
        every { generateNewId() } returns "1234abcd"
        every { now() } returns of(2024, 5, 28, 12, 44, 5)
        every { notifSender.sendFeeEmail(getFeeDto(ACH)) } just Runs
        every { camelContext.createProducerTemplate() } returns producerTemplate
        assertEquals(0, producerTemplate.messages.size)
        sendFeeNotificationMessage(ACH)
        sendFeeNotificationMessage(IIPS)
        assertEquals(2, producerTemplate.messages.size)
        assertEquals(2, itemDao.persistedItems.size)
        assertFeeMessageEntity(itemDao.persistedItems[0] as DMS_FeeMessage, ACH)
        assertFeeMessageEntity(itemDao.persistedItems[1] as DMS_FeeMessage, IIPS)
        verify(exactly = 1) { notifSender.sendFeeEmail(getFeeDto(ACH)) }
    }

    private fun sendFeeNotificationMessage(paymentSystemCode: String) {
        feeNotificationMessageSender.send(getFeeDto(paymentSystemCode))
        if (paymentSystemCode == ACH) {
            assertTrue(producerTemplate.messages.containsKey(ACH_URL))
            assertEquals(getEnvelopedMessage(ACH), producerTemplate.messages[ACH_URL])
        } else {
            assertTrue(producerTemplate.messages.containsKey(IIPS_URL))
            assertEquals(getEnvelopedMessage(IIPS), producerTemplate.messages[IIPS_URL])
        }
    }

    private fun assertFeeMessageEntity(feeMessage: DMS_FeeMessage, paymentSystemCode: String) {
        val feeDto = getFeeDto(paymentSystemCode)
        assertEquals(feeDto.amount, feeMessage.amount)
        assertEquals(feeDto.type, feeMessage.type)
        assertEquals(feeDto.participantCode, feeMessage.participantCode)
        assertEquals(feeDto.dispute, feeMessage.disputeCase)
    }

    private fun getFeeDto(paymentSystemCode: String) =
        FeeDto(BigDecimal.valueOf(100L), "Type", "Participant", getDisputeCase(paymentSystemCode))

    private fun getDisputeCase(paymentSystemCode: String) = DMS_DisputeCase().apply {
        id = 123
        caseReferenceNumber = "12345abcd"
        paymentSystem = getPaymentSystem(paymentSystemCode)
    }

    private fun getPaymentSystem(paymentSystemCode: String) = DMS_PaymentSystems().apply {
        id = 123
        code = paymentSystemCode
    }

    private fun getEnvelopedMessage(paymentSystemCode: String) =
        XmlEnvelopeSerializer().serialize(getEnvelope(paymentSystemCode)).value()

    private fun getEnvelope(paymentSystemCode: String) = Envelope.newBuilder()
        .setId("1234abcd")
        .setDate(of(2024, 5, 28, 12, 44, 5))
        .setType(MESSAGE_TYPE)
        .setFormat(MESSAGE_TYPE)
        .setSourceSystem(DMS)
        .setSourceTenant(DMS)
        .setDestinationSystem(resolvePaymentSystem(paymentSystemCode))
        .setDestinationTenant(resolvePaymentTenant(paymentSystemCode))
        .setSignature(EMPTY)
        .setContent(CSV_FEES)
        .build()

    private fun getPaymentSystemEndpoint(endpointId: Long, systemCode: String, systemUrl: String) =
        DMS_Endpoint().apply {
            id = endpointId
            system = systemCode
            targetUri = systemUrl
        }

    companion object {
        private const val ACH_URL = "ACH_URL"
        private const val IIPS_URL = "IIPS_URL"
        private const val CSV_FEES = "Amount,Type,Participant Code\n" +
                "100,Type,Participant\n"
        private const val MESSAGE_TYPE = "DMSFee"
        private const val DMS = "DMS"
    }
}