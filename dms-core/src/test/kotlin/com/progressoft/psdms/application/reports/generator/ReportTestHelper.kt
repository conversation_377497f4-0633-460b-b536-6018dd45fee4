package com.progressoft.psdms.application.reports.generator

import com.fasterxml.jackson.core.type.TypeReference
import com.progressoft.dms.entities.*
import com.progressoft.jfw.model.bussinessobject.core.IJFWEntity
import com.progressoft.jfw.model.bussinessobject.core.JfwCountry
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.TestHelper
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.client.RestClientException
import org.springframework.web.client.RestTemplate
import testUtil.WorkflowContextAware
import java.io.IOException
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.util.*

abstract class ReportTestHelper : TestHelper() {
    protected lateinit var generateReportRequest: GenerateReportRequest

    protected fun mockRestTemplateAndPullReceivedGenerateReportRequest(): RestTemplate {
        return object : RestTemplate() {
            @Throws(RestClientException::class)
            override fun <T> exchange(
                url: String,
                method: HttpMethod,
                requestEntity: HttpEntity<*>?,
                responseType: Class<T>,
                vararg uriVariables: Any
            ): ResponseEntity<T> {
                generateReportRequest = GenerateReportRequest()
                generateReportRequest.requestEntity = requestEntity
                generateReportRequest.uriVariables = arrayOf(uriVariables)
                generateReportRequest.method = method
                generateReportRequest.responseType = responseType
                generateReportRequest.url = url
                return ResponseEntity(("{\"uuid\" : \"" + UUID + "\"}") as T, HttpStatus.OK)
            }
        }
    }

    @Throws(IOException::class)
    protected fun expectedLogo(): String {
        val logoStream = Objects.requireNonNull(javaClass.getResourceAsStream("/reports/kuwait_logo.jpg"))
        return Base64.getEncoder().encodeToString(logoStream.readAllBytes())
    }

    protected fun <T : IJFWEntity?> mockWorkFlowContext(report: T): WfContext<T> {
        val aware = WorkflowContextAware()
        return aware.createWfContext(report)
    }

    protected class GenerateReportRequest {
        var url: String? = null
        var method: HttpMethod? = null
        var requestEntity: HttpEntity<*>? = null
        var responseType: Class<*>? = null
        lateinit var uriVariables: Array<Any>
    }

    protected fun typeRef(): TypeReference<HashMap<String, LinkedHashMap<String, String>>> {
        return object : TypeReference<HashMap<String, LinkedHashMap<String, String>>>() {
        }
    }

    protected fun createReportEntity() = DMS_DisputeSummaryReport().apply {
        id = 1
        nature = "CLAIMANT"
        participants = getParticipant()
        paymentSystem = getPaymentSystem()
        reason = getReason()
        type = "excel_xlsx"
    }

    protected fun createDisputeDetailsReportEntity() = DMS_DisputeDetailsReport().apply {
        id = 1
        nature = "CLAIMANT"
        participants = getParticipant()
        paymentSystem = getPaymentSystem()
        reason = getReason()
        type = "excel_xlsx"
        amountFrom = ONE
        amountTo = TEN
    }

    private fun getReason() = DMS_ReasonManagement().apply {
        id = 1
        code = "1"
        name = "REASON"
    }

    private fun getPaymentSystem() = DMS_PaymentSystems().apply {
        id = 1
        code = "ACH"
        name = "ACH"
    }

    private fun getParticipant() = mutableListOf(DMS_Participant().apply {
        id = 1
        code = "ARAB"
        name = "ARAB"
        fullName = "ARAB BANK"
        nationalCentralBank = getNcb()
        country = getCountry()
    })

    private fun getNcb() = DMS_NationalCentralBank().apply {
        id = 1
        code = "TEST"
        name = "TEST"
        country = getCountry()
    }

    private fun getCountry() = JfwCountry().apply {
        id = 1
        code = "COG"
        name = "COG"
    }

    companion object {
        protected const val UUID: String = "4e74d8ee-e9c7-4828-9c24-757130ec00a0"
    }
}