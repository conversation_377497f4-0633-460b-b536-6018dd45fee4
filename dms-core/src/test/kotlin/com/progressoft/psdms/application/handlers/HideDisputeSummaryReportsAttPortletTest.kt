package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeSummaryReport
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import com.progressoft.psdms.TestHelper
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import testUtil.MockJfw

class HideDisputeSummaryReportsAttPortletTest : TestHelper() {
    private lateinit var handler: HideDisputeSummaryReportsAttPortlet
    private val reportsAttPortlet = "dispute.summary.report.att"
    private val generatedStep = "700002"

    @BeforeEach
    fun setUp() {
        handler = HideDisputeSummaryReportsAttPortlet()
        handler.setJfw(MockJfw(hashMapOf()))
    }

    @Test
    fun givenRecordWithGeneratedStatus_whenCallingTheHandler_thenThePortletShouldBeVisible() {
        val entity = DMS_DisputeSummaryReport()
        entity.statusId = getWorkflowStatus(generatedStep)
        val context: WfChangeHandlerContext<DMS_DisputeSummaryReport> =
            contextAware.createWfChangeHandlerContext(
                entity,
                arrayOfNulls(0),
                arrayOf(reportsAttPortlet),
                arrayOfNulls(0)
            )
        handler.handle(context)
        Assertions.assertTrue(context.isPortletVisible(reportsAttPortlet))
    }

    @Test
    fun givenRecordWithReadyToGenerateStatus_whenCallingTheHandler_thenThePortletShouldNotBeVisible() {
        val entity = DMS_DisputeSummaryReport()
        entity.statusId = getWorkflowStatus("70001")
        val context: WfChangeHandlerContext<DMS_DisputeSummaryReport> =
            contextAware.createWfChangeHandlerContext(
                entity,
                arrayOfNulls(0),
                arrayOf(reportsAttPortlet),
                arrayOfNulls(0)
            )
        handler.handle(context)
        Assertions.assertFalse(context.isPortletVisible(reportsAttPortlet))
    }

}