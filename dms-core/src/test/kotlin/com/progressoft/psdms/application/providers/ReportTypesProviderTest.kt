package com.progressoft.psdms.application.providers

import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.enums.Nature
import com.progressoft.psdms.application.enums.ReportTypes
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test

class ReportTypesProviderTest {
    private val provider = ReportTypesProvider()

    @Test
    fun whenCallFieldTypeProvider_thenShouldReturnExpectedValues() {
        val allValues: MutableMap<String, String> = provider.allValues(ValueProviderContext())
        assertNotNull(allValues)
        assertEquals(enumValues<ReportTypes>().size, allValues.keys.size)

        enumValues<ReportTypes>().forEach { expectedValue ->
            assertTrue(allValues.values.contains(expectedValue.name))
        }
    }
}
