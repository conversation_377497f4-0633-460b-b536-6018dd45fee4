package com.progressoft.psdms.application.functions

import com.google.gson.GsonBuilder
import com.progressoft.communication.Envelope
import com.progressoft.communication.messaging.XmlEnvelopeSerializer
import com.progressoft.dms.entities.*
import com.progressoft.jfw.Jfw
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.bussinessobject.security.Org
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.application.dtos.ApprovedDisputeMessage
import com.progressoft.psdms.application.dtos.FeeDto
import com.progressoft.psdms.application.services.FeeNotificationMessageSender
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_OF_A_DISPUTE_FEE
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import com.progressoft.psdms.application.utils.Constants.Companion.URGENT_DISPUTE_FEE
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentSystem
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentTenant
import com.progressoft.psdms.application.utils.Utils.Companion.generateNewId
import com.progressoft.psdms.testUtil.FakeItemDao
import io.mockk.MockKAnnotations.init
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.verify
import org.apache.camel.CamelContext
import org.apache.commons.lang.StringUtils.EMPTY
import org.junit.Before
import org.junit.Test
import testUtil.FakeProducerTemplate
import java.math.BigDecimal
import java.time.LocalDateTime.now
import kotlin.test.assertEquals
import kotlin.test.assertTrue

class ApprovedDisputeNotificationMessageTest {
    @MockK
    private lateinit var jfw: Jfw

    @MockK
    private lateinit var camelContext: CamelContext

    @MockK
    private lateinit var wfContext: WfContext<JFWEntity>

    private val producerTemplate = FakeProducerTemplate()

    private val itemDao = FakeItemDao()

    @MockK
    private lateinit var feeSender: FeeNotificationMessageSender

    private lateinit var approvedDisputedNotificationMessage: ApprovedDisputeNotificationMessage

    @Before
    fun setUp() {
        init(this, relaxUnitFun = true)
        approvedDisputedNotificationMessage = ApprovedDisputeNotificationMessage(feeSender)
        approvedDisputedNotificationMessage.setJfw(jfw)
        approvedDisputedNotificationMessage.setItemDao(itemDao)
    }

    @Test
    fun givenDisputeIsApproved_shouldSendNotificationMessage() {
        every { jfw.getBean("defaultCamelContext", CamelContext::class.java) } returns camelContext
        every { jfw.currentPrefOrg } returns getOrg()
        every { wfContext.entity } returns dmsDispute()
        every { camelContext.createProducerTemplate() } returns producerTemplate
        itemDao.add(dmsEndpoint())
        approvedDisputedNotificationMessage.execute(wfContext)
        assertProducerTemplateMessage(dmsDispute())
        verify(exactly = 0) { feeSender.send(getFeeDto(URGENT_DISPUTE_FEE, urgentDmsDispute())) }
    }

    @Test
    fun givenDisputeIsApprovedAndUrgent_shouldSendNotificationMessage() {
        every { jfw.getBean("defaultCamelContext", CamelContext::class.java) } returns camelContext
        every { jfw.currentPrefOrg } returns getOrg()
        every { wfContext.entity } returns urgentDmsDispute()
        every { camelContext.createProducerTemplate() } returns producerTemplate
        itemDao.add(dmsEndpoint())
        itemDao.add(getUrgentFeeConfig())
        approvedDisputedNotificationMessage.execute(wfContext)
        assertProducerTemplateMessage(urgentDmsDispute())
        verify(exactly = 1) { feeSender.send(getFeeDto(URGENT_DISPUTE_FEE, urgentDmsDispute())) }
    }

    @Test
    fun givenDisputeOfADispute_whenTheActionExecuted_thenShouldSendNotificationMessageAndFeeMessage() {
        every { jfw.getBean("defaultCamelContext", CamelContext::class.java) } returns camelContext
        every { jfw.currentPrefOrg } returns getOrg()
        every { wfContext.entity } returns getDisputeOfADispute()
        every { camelContext.createProducerTemplate() } returns producerTemplate
        itemDao.add(dmsEndpoint())
        itemDao.add(getDisputeOfADisputeFeeConfig())
        approvedDisputedNotificationMessage.execute(wfContext)
        assertProducerTemplateMessage(getDisputeOfADispute())
        verify(exactly = 1) { feeSender.send(getFeeDto(DISPUTE_OF_A_DISPUTE_FEE, getDisputeOfADispute())) }
    }

    private fun assertProducerTemplateMessage(disputeCase: DMS_DisputeCase) {
        val envelopedMessage =
            getEnvelopedMessage(buildAndSerializeMessageBody(disputeCase), disputeCase.paymentSystem?.code!!).value()
        assertEquals(1, producerTemplate.messages.size)
        assertTrue(producerTemplate.messages.containsKey("targetUri"))
        assertEquals(
            removeDateAndIdFromEnvelop(envelopedMessage),
            removeDateAndIdFromEnvelop(producerTemplate.messages["targetUri"]!!)
        )
    }

    private fun getOrg() = Org().apply {
        orgName = "DMS"
    }

    private fun dmsDispute() = DMS_DisputeCase().apply {
        disputedAmount = BigDecimal(20)
        caseReferenceNumber = "12"
        transactionReference = "2"
        paymentSystem = DMS_PaymentSystems().apply { code = "abc" }
    }

    private fun urgentDmsDispute() = DMS_DisputeCase().apply {
        disputedAmount = BigDecimal(10)
        caseReferenceNumber = "11"
        transactionReference = "1"
        urgency = "Urgent"
        claimantBank = getClaimantBank()
        paymentSystem = DMS_PaymentSystems().apply { code = "abc" }
    }

    private fun getDisputeOfADispute() = DMS_DisputeCase().apply {
        disputedAmount = BigDecimal(10)
        caseReferenceNumber = "11"
        transactionReference = "1"
        disputeOverDispute = true
        claimantBank = getClaimantBank()
        paymentSystem = DMS_PaymentSystems().apply { code = "abc" }
    }

    private fun getClaimantBank() = DMS_Participant().apply {
        id = 123
        code = "TEST"
    }

    private fun getFeeDto(disputeType: String, disputeCase: DMS_DisputeCase) =
        FeeDto(BigDecimal.valueOf(10L), disputeType, "TEST", disputeCase)

    private fun getUrgentFeeConfig() = DMS_SystemConfiguration().apply {
        configKey = URGENT_DISPUTE_FEE
        configValue = "10"
    }

    private fun dmsEndpoint() = DMS_Endpoint().apply {
        system = "abc"
        targetUri = "targetUri"
    }

    private fun getDisputeOfADisputeFeeConfig() = DMS_SystemConfiguration().apply {
        configKey = DISPUTE_OF_A_DISPUTE_FEE
        configValue = "10"
    }

    private fun buildAndSerializeMessageBody(disputeCase: DMS_DisputeCase): String {
        val gson = GsonBuilder().serializeNulls().setPrettyPrinting().create()
        val message = ApprovedDisputeMessage(
            disputeCase.transactionReference,
            disputeCase.disputedAmount,
            disputeCase.caseReferenceNumber
        )
        return gson.toJson(message)
    }

    private fun getEnvelopedMessage(payload: String, paymentSystem: String) =
        XmlEnvelopeSerializer().serialize(getEnvelope(payload, paymentSystem))

    private fun getEnvelope(payload: String, paymentSystem: String) = Envelope.newBuilder()
        .setId(generateNewId())
        .setDate(now())
        .setType("DMSApproved")
        .setFormat("DMSApproved")
        .setSourceSystem(DMS)
        .setSourceTenant(DMS)
        .setDestinationSystem(resolvePaymentSystem(paymentSystem))
        .setDestinationTenant(resolvePaymentTenant(paymentSystem))
        .setSignature("no signature")
        .setContent(payload)
        .build()

    private fun removeDateAndIdFromEnvelop(envelopedMessage: String) =
        envelopedMessage.replace("<date>.*?</date>".toRegex(), EMPTY).replace("<id>.*?</id>".toRegex(), EMPTY)
}