package com.progressoft.psdms.application.functions

import com.progressoft.jfw.AppValidationException
import com.progressoft.psdms.TestHelper
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import testUtil.MockJfw
import testUtil.WorkflowContextAware
import kotlin.test.assertEquals

class ReasonMngmntValidatorTest : TestHelper() {
    private val validator = ReasonMngmntValidator()

    @BeforeEach
    fun setUp() {
        itemDao.affectItems()
        itemDao.filterItems()
        val messages = HashMap<String, String>()
        messages["reason.code.uniqueness.msg"] = REASON_CODE_UNIQUENESS_ERROR_MSG
        messages["reason.type.select.msg"] = REASON_TYPE_ERROR_MSG
        validator.setItemDao(itemDao)
        validator.setJfw(MockJfw(messages))

    }

    @Test
    fun givenCreatedReasonWithUniqueCodeExist_whenCallingTheValidator_thenShouldDoNothing() {
        val reason = createNewReason(1, "RSN1", "REASON_1")
        val reasonWithDuplicateCode = createNewReason(2, "RSN2", "REASON_2").apply { disputeReason=true }
        itemDao.add(reason)
        val workflowContextAware = WorkflowContextAware()
        validator.execute(workflowContextAware.createWfContext(reasonWithDuplicateCode))
    }

    @Test
    fun givenCreatedReasonWithNoReasonPurpose_whenCallingTheValidator_thenShouldThrowException() {
        val reason = createNewReason(1, "RSN1", "REASON_1")
        val reasonWithDuplicateCode = createNewReason(2, "RSN2", "REASON_2")
        itemDao.add(reason)
        val workflowContextAware = WorkflowContextAware()
        val ctx = workflowContextAware.createWfContext(reasonWithDuplicateCode)
        val exception = assertThrows(AppValidationException::class.java) { validator.execute(ctx) }
        assertEquals(REASON_TYPE_ERROR_MSG, exception.message)
    }

    @Test
    fun givenCreatedReasonWithCodeAlreadyExist_whenCallingTheValidator_thenErrorMessageShouldDisplayed() {
        val reason = createNewReason(1, "RSN1", "REASON_1")
        val reasonWithDuplicateCode = createNewReason(2, "RSN1", "REASON_2")
        itemDao.add(reason)
        val workflowContextAware = WorkflowContextAware()
        val context = workflowContextAware.createWfContext(reasonWithDuplicateCode)
        val exception = assertThrows(AppValidationException::class.java) { validator.execute(context) }
        assertEquals(REASON_CODE_UNIQUENESS_ERROR_MSG, exception.message)
    }
}
