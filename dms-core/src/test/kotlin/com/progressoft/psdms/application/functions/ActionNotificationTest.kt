package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.email.ActionEmailSenderService
import io.mockk.every
import io.mockk.justRun
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

import org.junit.jupiter.api.BeforeEach

class ActionNotificationTest {

    val sender = mockk<ActionEmailSenderService>()

    val context = mockk<WfContext<DMS_DisputeCase>>()

    lateinit var actionNotification: ActionNotification

    @BeforeEach
    fun setUp(){
        actionNotification = ActionNotification(sender)
    }

    @Test
    fun givenContext_whenCallExecute_shouldCallEmailSender() {
        val dispute = getDispute()
        every { context.entity } returns dispute
        every { context.getArgument("Action") } returns "Action"
        justRun { sender.sendEmail(mutableListOf("<EMAIL>", "<EMAIL>"), dispute, "Action") }
        actionNotification.execute(context)

        verify(exactly = 1) { sender.sendEmail(mutableListOf("<EMAIL>", "<EMAIL>"), dispute, "Action") }
    }

    private fun getDispute() = DMS_DisputeCase().apply {
        this.defendantBank = bank("<EMAIL>")
        this.claimantBank = bank("<EMAIL>")
    }

    private fun bank(email: String)= DMS_Participant().apply {
        this.email = email
    }
}