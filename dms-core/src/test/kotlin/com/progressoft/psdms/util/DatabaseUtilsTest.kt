package com.progressoft.psdms.util

import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.psdms.email.Email
import com.progressoft.psdms.email.EmailRecipients
import com.progressoft.psdms.exception.FailedToGetEntityException
import com.progressoft.psdms.testUtil.FakeItemDao
import io.mockk.MockKAnnotations.init
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.mockkStatic
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import javax.mail.Message
import javax.mail.Session
import javax.mail.Transport


class DatabaseUtilsTest {


    @MockK
    lateinit var message: Message

    @MockK
    lateinit var session: Session

    @MockK
    lateinit var emailRecipients: EmailRecipients

    @MockK
    lateinit var email: Email

    private lateinit var itemDao: FakeItemDao

    private lateinit var emailUtils :EmailUtils

    @BeforeEach
    fun setUp() {
        init(this, relaxUnitFun = true)
        itemDao = FakeItemDao()
        emailUtils = EmailUtils(itemDao)
    }

    @Test
    fun givenEmptyConfigs_whenCallingGetEmailSender_thenThrowException() {
        val exception = assertThrows(FailedToGetEntityException::class.java) {
            emailUtils.getEmailSender()
        }
        assertEquals("Failed to get email configurations ...", exception.message)
    }

    @Test
    fun givenConfigsNotEqualEight_whenCallingGetEmailSender_thenThrowException() {
        itemDao.add(getSystemConfiguration(1L, "key1", "value1"))
        itemDao.add(getSystemConfiguration(2L, "key2", "value2"))
        itemDao.add(getSystemConfiguration(3L, "key3", "value3"))
        itemDao.add(getSystemConfiguration(4L, "key4", "value4"))
        val exception = assertThrows(FailedToGetEntityException::class.java) {
            emailUtils.getEmailSender()
        }
        assertEquals("Failed to get email configurations ...", exception.message)
    }

    @Test
    fun givenEightCorrectConfigs_whenCallingGetEmailSender_thenReturnEmailSenderImpl() {
        itemDao.add(getSystemConfiguration(1L, "mail.smtp.enabled", "true"))
        itemDao.add(getSystemConfiguration(2L, "mail.smtp.from", "test"))
        itemDao.add(getSystemConfiguration(3L, "mail.smtp.user", "<EMAIL>"))
        itemDao.add(getSystemConfiguration(4L, "mail.smtp.password", "1234"))
        itemDao.add(getSystemConfiguration(5L, "mail.smtp.auth", "false"))
        itemDao.add(getSystemConfiguration(6L, "mail.smtp.starttls.enable", "true"))
        itemDao.add(getSystemConfiguration(7L, "mail.smtp.host", "testHost"))
        itemDao.add(getSystemConfiguration(8L, "mail.smtp.port", "500"))
        val emailSender = emailUtils.getEmailSender()
        every { email.recipients } returns emailRecipients
        every { email.subject } returns "subject"
        every { email.body } returns "body"
        mockkStatic(Session::class)
        every { Session.getInstance(any(), any()) } returns session
        every { email.message("test", session) } returns message
        mockkStatic(Transport::class)
        every { Transport.send(message) } returns Unit
        emailSender.send(email)
        verify(exactly = 1) { email.message("test", session) }
        verify(exactly = 1) { Transport.send(message) }
    }

    private fun getSystemConfiguration(id: Long, key: String, value: String): DMS_SystemConfiguration {
        val wpsGlobalConfig = DMS_SystemConfiguration()
        wpsGlobalConfig.id = id
        wpsGlobalConfig.configKey = key
        wpsGlobalConfig.configValue = value
        return wpsGlobalConfig
    }

}