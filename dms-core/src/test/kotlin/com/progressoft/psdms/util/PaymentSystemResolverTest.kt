package com.progressoft.psdms.util

import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentSystem
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentTenant
import io.mockk.MockKAnnotations.init
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class PaymentSystemResolverTest {

    @BeforeEach
    fun setUp() {
        init(this, relaxUnitFun = true)
    }

    @Test
    fun givenACHPaymentSystem_whenResolvePaymentSystem_thenShouldReturnAch() {
        assertEquals("Ach", resolvePaymentSystem("ACH"))
    }

    @Test
    fun givenIIPSPaymentSystem_whenResolvePaymentSystem_thenShouldReturnIIPS() {
        assertEquals("IIPS", resolvePaymentSystem("IIPS"))
    }

    @Test
    fun givenACHPaymentSystem_whenResolvePaymentTenant_thenShouldReturnATS() {
        assertEquals("ATS", resolvePaymentTenant("ACH"))
    }

    @Test
    fun givenIIPSPaymentSystem_whenResolvePaymentTenant_thenShouldReturnIIPS() {
        assertEquals("IIPS", resolvePaymentTenant("IIPS"))
    }
}