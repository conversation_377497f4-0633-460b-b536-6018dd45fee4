package com.progressoft.psdms.application.utils

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.progressoft.dms.entities.*
import com.progressoft.jfw.model.bussinessobject.core.AbstractJFWEntity
import com.progressoft.jfw.model.bussinessobject.security.Org
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jfw.model.service.utils.AppContext
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.psdms.application.models.OrgModel
import com.progressoft.psdms.application.utils.Constants.Companion.CLAIMANT_BANK
import com.progressoft.psdms.application.utils.Constants.Companion.CLAIMANT_NCB
import com.progressoft.psdms.application.utils.Constants.Companion.DEFENDANT_BANK
import com.progressoft.psdms.application.utils.Constants.Companion.DEFENDANT_NCB
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import org.apache.commons.lang3.RandomStringUtils.random
import java.math.BigDecimal
import java.util.*

class Utils {
    companion object {
        fun getAmountValue(amount: String?): BigDecimal? {
            if (amount.isNullOrBlank() || amount == "null") {
                return null
            }
            return amount.toBigDecimal()
        }

        fun getStringValue(value: String?): String {
            if (value.isNullOrBlank()) {
                return ""
            }
            return value.toString()
        }

        fun <T> toJSON(obj: T): String {
            val gson = gsonBuilderGenerator()
            return gson.toJson(obj)
        }

        fun <T> fromJSON(jsonString: String, type: Class<T>): T {
            val gson = gsonBuilderGenerator()
            return gson.fromJson(jsonString, type)
        }

        fun generateNewId() = random(35, "0123456789abcdefghijklmnopqrstuvwxyz")!!

        fun getCurrentParticipantCode(partyCode: String, disputeCase: DMS_DisputeCase) =
            when (partyCode) {
                CLAIMANT_BANK -> disputeCase.claimantBank!!.code
                DEFENDANT_BANK -> disputeCase.defendantBank!!.code
                CLAIMANT_NCB -> disputeCase.claimantBank!!.nationalCentralBank!!.code
                DEFENDANT_NCB -> disputeCase.defendantBank!!.nationalCentralBank!!.code
                else -> DMS
            }!!

        private fun gsonBuilderGenerator(): Gson {
            val builder = GsonBuilder()
            builder.setPrettyPrinting()
            return builder.create()
        }

        fun updateDisputeCaseAndCreateCorrespondence(
            context: WfContext<DMS_BaseCaseManagement>,
            itemDao: ItemDao
        ): DMS_DisputeCase? {
            val disputeCase = context.entity.disputeCase
            disputeCase?.lastAction = getArgument(context, Constants.LAST_ACTION)
            disputeCase?.lastActionBy = getArgument(context, Constants.LAST_ACTION_BY)
            disputeCase?.lastNote = context.entity.note
            disputeCase?.correspondence?.add(createAndPersistCorrespondence(context, itemDao))
            return disputeCase
        }

        private fun createAndPersistCorrespondence(
            context: WfContext<DMS_BaseCaseManagement>,
            itemDao: ItemDao
        ): DMS_Correspondence {
            val correspondence = DMS_Correspondence()
            correspondence.action = getArgument(context, Constants.LAST_ACTION)
            correspondence.actionBy = getArgument(context, Constants.LAST_ACTION_BY)
            val baseCase = context.entity
            correspondence.dispute = baseCase.disputeCase!!
            correspondence.note = baseCase.note
            correspondence.orgId = 0
            correspondence.tenantId = "SYSTEM"
            correspondence.reason = getActionReason(baseCase!!, itemDao)
            itemDao.persist(correspondence)
            return correspondence
        }

        private fun getActionReason(entity: DMS_BaseCaseManagement, itemDao: ItemDao): DMS_ReasonManagement? {
            val actionReason = when(entity){
                is DMS_DefendantBankCaseManagement -> entity.resolveReason()
                is DMS_ClaimantBankCaseManagement -> entity.resolveReason()
                is DMS_DefendantNCBCaseManagement -> entity.resolveReason()
                is DMS_ClaimantNCBCaseManagement -> entity.resolveReason()
                is DMS_OperatorCaseManagement -> entity.resolveReason()
                else -> null
            }
            itemDao.merge(entity)
            return actionReason
        }

        fun getArgument(context: WfContext<DMS_BaseCaseManagement>, arg: String): String {
            return context.getArgument(arg, String::class.java)
        }

        fun <T : AbstractJFWEntity> promoteToRoot(obj: T?) {
            obj?.orgId = 0
            obj?.tenantId = "SYSTEM"
        }

        fun createNewOrg(entity: OrgModel): Org {
            val org = Org()
            org.orgName = entity.orgName
            org.abbreviation = entity.abbreviation
            org.description = entity.description
            org.shortName = entity.shortName
            org.parent = entity.parent
            org.hasAdmin = true
            org.email = "info@" + entity.shortName + ".com"
            org.makerAdminEmail = "maker@" + entity.shortName + ".com"
            org.checkerAdminEmail = "checker@" + entity.shortName + ".com"
            org.dutiesSeparationType = 0
            org.orgId = AppContext.getCurrentPrefOrg().id
            org.tenantId = AppContext.getCurrentTenant()
            org.jfwViewsScheme = entity.jfwViewsScheme
            return org
        }

        fun getOrgFromName(shortName: String?, itemDao: ItemDao): Long {
            return itemDao.getItem(Org::class.java, "shortName", shortName).id
        }

        fun isNationalOperationMode(itemDao: ItemDao): Boolean {
            val configuration = itemDao.getItem(DMS_SystemConfiguration::class.java, "configKey", "Operation Mode")
            return Objects.requireNonNull(configuration.configValue) == Constants.NATIONAL
        }
    }
}