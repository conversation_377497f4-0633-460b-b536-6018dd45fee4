package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.CASE_REF_NO
import com.progressoft.jfw.Query
import com.progressoft.jfw.workflow.WfChangeHandler
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import org.springframework.stereotype.Component

@Component
class FetchDisputesHandler : <PERSON>f<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><DMS_DisputeCase>() {

    override fun handle(context: WfChangeHandlerContext<DMS_DisputeCase>) {
        val entity: DMS_DisputeCase = context.entity
        val disputeCase = Query(itemDao)
            .equals(CASE_REF_NO, entity.refDisputeCaseNumber)
            .first(DMS_DisputeCase::class.java)
        updateFields(entity, disputeCase)
    }

    private fun updateFields(entity: DMS_DisputeCase, selectedDispute: DMS_DisputeCase) {
        entity.refDisputeCaseNumber = selectedDispute.caseReferenceNumber
        entity.refDisputeClaimantBnk = selectedDispute.defendantBank
        entity.refDisputeDefendantBnk = selectedDispute.claimantBank
        entity.refDisputePaymentSys = selectedDispute.paymentSystem
        entity.refDisputeTransactionAmount = selectedDispute.transactionAmount
        itemDao.merge(entity)
        itemDao.flush()
    }


}