package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.DISPUTE_AMOUNT
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.REF_DISPUTE_REFERENCE_NUM
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.TRANSACTION_REF
import com.progressoft.jfw.workflow.WfChangeHandler
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_REF_INFO_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.FETCH_DISPUTE_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.FETCH_TRANSACTION_POPUP_PORTLET
import com.progressoft.psdms.application.utils.Constants.Companion.MODIFICATION_WF_STATUSES
import com.progressoft.psdms.application.utils.Constants.Companion.TRANSACTION_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.TRANSACTION_INFO
import org.springframework.stereotype.Component

@Component
class DisputeCaseHandler : WfChangeHandler<DMS_DisputeCase>() {

    override fun handle(context: WfChangeHandlerContext<DMS_DisputeCase>) {
        val entity = context.entity
        toggleViewPortlets(context)
        toggleViewFields(context)
        if (entity.paymentSystem == null)
            context.setPortletVisible(false, FETCH_TRANSACTION_POPUP_PORTLET)
    }

    private fun toggleViewFields(context: WfChangeHandlerContext<DMS_DisputeCase>) {
        context.setRequired(
            !context.entity.disputeOverDispute,
            TRANSACTION_REF
        )
        if (context.entity.disputeOverDispute) {
            context.setVisible(true, DISPUTE_AMOUNT)
            context.setRequired(true, DISPUTE_AMOUNT, REF_DISPUTE_REFERENCE_NUM)
        }
    }

    private fun toggleViewPortlets(context: WfChangeHandlerContext<DMS_DisputeCase>) {
        val status = jfw.getStatus(context.entity)
        context.setPortletVisible(
            !context.entity.disputeOverDispute,
            TRANSACTION_ADDITIONAL_INFO,
            TRANSACTION_INFO
        )
        context.setPortletVisible(
            !context.entity.disputeOverDispute && MODIFICATION_WF_STATUSES.contains(status.code),
            FETCH_TRANSACTION_POPUP_PORTLET
        )
        context.setPortletVisible(
            context.entity.disputeOverDispute,
            DISPUTE_REF_INFO_PORTLET
        )
        context.setPortletVisible(
            context.entity.disputeOverDispute && MODIFICATION_WF_STATUSES.contains(status.code),
            FETCH_DISPUTE_PORTLET
        )
    }
}