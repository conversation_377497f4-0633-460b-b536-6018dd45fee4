package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_ClaimantBankCaseManagement
import com.progressoft.dms.entities.DMS_DefendantBankCaseManagement
import com.progressoft.dms.entities.DMS_OperatorCaseManagement
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.functions.BellNotification.Companion.generateJfwMessage
import com.progressoft.psdms.application.functions.BellNotification.Companion.getParameters
import com.progressoft.psdms.application.utils.Constants.Companion.APPROVED
import com.progressoft.psdms.application.utils.Constants.Companion.ARBITRATE_REQUEST
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER_ARBITRATE
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER_REPAIR
import com.progressoft.psdms.application.utils.Constants.Companion.CLOSE_DISPUTE
import com.progressoft.psdms.application.utils.Constants.Companion.DEFENDANT_MAKER_REJECTION
import com.progressoft.psdms.application.utils.Constants.Companion.DISPLAY_NAME
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER_APPROVED_OR_REJECT
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER_APPROVED_OR_REJECT_REPRESENT
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_CHECKER_APPROVED
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_CHECKER_REJECT
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_CHECKER_REPAIR
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_CHECKER_TO_DEFENDANT_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_MAKER_APPROVED_OR_REJECT
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_MAKER_TO_CLAIMANT
import com.progressoft.psdms.bellNotification.NotificationUtil
import org.springframework.stereotype.Component

@Component
class BankCaseBellNotification : WfFunction<JFWEntity>() {

    override fun execute(context: WfContext<JFWEntity>) {
        val entity = when (val obj = context.entity) {
            is DMS_ClaimantBankCaseManagement -> obj
            is DMS_DefendantBankCaseManagement -> obj
            is DMS_OperatorCaseManagement -> obj
            else -> throw IllegalArgumentException("Unsupported entity type: ${obj::class.java.name}")
        }
        val senderName = entity.disputeCase?.claimantBank?.name
        val receiverName = entity.disputeCase?.defendantBank?.name
        val notificationType = context.getArgument("notificationType", String::class.java)
        val parameters = getParameters(entity.disputeCase!!)
        val jfwMessage = generateJfwMessage(parameters, notificationType)
        val displayNames = getDisplayNames(notificationType, senderName, receiverName)
        displayNames.forEach { displayName ->
            val receiver = itemDao.getItem(User::class.java, DISPLAY_NAME, displayName)
            val message = if (notificationType == APPROVED && displayName == "$receiverName$MAKER") {
                generateJfwMessage(parameters, MAKER_APPROVED_OR_REJECT_REPRESENT)
            } else {
                jfwMessage
            }
            receiver?.let { NotificationUtil.sendMessage(it, message) }
        }
    }

    private fun getDisplayNames(
        notificationType: String,
        senderName: String?,
        receiverName: String?
    ): List<String> {
        return when (notificationType) {
            APPROVED -> listOf(
                "$senderName$MAKER",
                "$receiverName$MAKER"
            )
            MAKER_APPROVED_OR_REJECT, MAKER_APPROVED_OR_REJECT_REPRESENT,
            DEFENDANT_MAKER_REJECTION, CLOSE_DISPUTE, ARBITRATE_REQUEST,
            MAKER_ADDITIONAL_INFO -> listOf("$senderName$CHECKER")
            CHECKER_ADDITIONAL_INFO, OPERATOR_CHECKER_TO_DEFENDANT_ADDITIONAL_INFO -> listOf("$receiverName$MAKER")
            CHECKER_REPAIR, OPERATOR_MAKER_TO_CLAIMANT -> listOf("$senderName$MAKER")
            OPERATOR_CHECKER_APPROVED -> listOf(
                "$senderName$MAKER", "$senderName$CHECKER", "$receiverName$CHECKER", "$receiverName$MAKER", "$DMS$MAKER@DMS"
            )
            OPERATOR_MAKER_APPROVED_OR_REJECT -> listOf("$DMS$CHECKER@DMS")
            OPERATOR_CHECKER_REPAIR, OPERATOR_ADDITIONAL_INFO -> listOf("$DMS$MAKER@DMS")
            OPERATOR_CHECKER_REJECT -> listOf(
                "$senderName$MAKER", "$senderName$CHECKER", "$DMS$MAKER@DMS"
            )
            CHECKER_ARBITRATE -> listOf("${DMS}$MAKER@DMS")
            else -> emptyList()
        }
    }
}