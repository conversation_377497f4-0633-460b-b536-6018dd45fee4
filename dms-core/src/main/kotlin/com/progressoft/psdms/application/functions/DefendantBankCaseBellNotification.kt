package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.functions.BellNotification.Companion.generateJfwMessage
import com.progressoft.psdms.application.functions.BellNotification.Companion.getParameters
import com.progressoft.psdms.application.utils.Constants.Companion.APPROVED
import com.progressoft.psdms.application.utils.Constants.Companion.ARBITRATE_REQUEST
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER_ARBITRATE
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER_REPAIR
import com.progressoft.psdms.application.utils.Constants.Companion.DEFENDANT_CHECKER_REJECTION
import com.progressoft.psdms.application.utils.Constants.Companion.DEFENDANT_MAKER_REJECTION
import com.progressoft.psdms.application.utils.Constants.Companion.DISPLAY_NAME
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER_APPROVED_OR_REJECT
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER_APPROVED_OR_REJECT_REPRESENT
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_ADDITIONAL_INFO
import com.progressoft.psdms.bellNotification.NotificationUtil
import org.springframework.stereotype.Component

@Component
class DefendantBankCaseBellNotification : WfFunction<DMS_BaseCaseManagement>() {

    override fun execute(context: WfContext<DMS_BaseCaseManagement>) {
        val entity = context.entity
        val senderName = entity.disputeCase?.claimantBank?.name
        val receiverName = entity.disputeCase?.defendantBank?.name
        val notificationType = context.getArgument("notificationType", String::class.java)
        val parameters = getParameters(entity.disputeCase!!)
        val jfwMessage = generateJfwMessage(parameters, notificationType)

        val displayNames = when (notificationType) {
            APPROVED, DEFENDANT_CHECKER_REJECTION -> listOf(
                "$senderName$MAKER",
                "$senderName$CHECKER",
                "$receiverName$MAKER"
            )
            MAKER_APPROVED_OR_REJECT, MAKER_APPROVED_OR_REJECT_REPRESENT,
            DEFENDANT_MAKER_REJECTION, MAKER_ADDITIONAL_INFO, ARBITRATE_REQUEST -> listOf(
                "$receiverName$CHECKER"
            )
            CHECKER_ADDITIONAL_INFO -> listOf(
                "$senderName$MAKER", "$DMS$MAKER@DMS"
            )
            CHECKER_REPAIR -> listOf("$senderName$MAKER")
            OPERATOR_ADDITIONAL_INFO -> listOf("${DMS}$MAKER@DMS")
            CHECKER_ARBITRATE -> listOf("${DMS}$MAKER@DMS")
            else -> emptyList()
        }

        displayNames.forEach { displayName ->
            val receiver = itemDao.getItem(User::class.java, DISPLAY_NAME, displayName)
            receiver?.let { NotificationUtil.sendMessage(it, jfwMessage) }
        }
    }
}

