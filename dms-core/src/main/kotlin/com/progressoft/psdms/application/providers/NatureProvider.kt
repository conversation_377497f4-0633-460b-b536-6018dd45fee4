package com.progressoft.psdms.application.providers

import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.enums.Nature
import org.springframework.stereotype.Component

@Component
class NatureProvider : JupiterValueProvider<String, String> {

    override fun allValues(ctx: ValueProviderContext<String>?): MutableMap<String, String> {
        return enumValues<Nature>().associateBy({ it.name }, { it.description }).toMutableMap()
    }
}

