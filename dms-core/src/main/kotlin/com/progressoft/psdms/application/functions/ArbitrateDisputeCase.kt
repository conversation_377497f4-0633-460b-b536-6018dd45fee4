package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_ClaimantNCBCaseManagement
import com.progressoft.dms.entities.DMS_DefendantNCBCaseManagement
import com.progressoft.dms.entities.DMS_OperatorCaseManagement
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.utils.Constants.Companion.NATIONAL_TARGET_ENTITY
import com.progressoft.psdms.application.utils.Constants.Companion.REGIONAL_TARGET_ENTITY
import com.progressoft.psdms.application.utils.Utils.Companion.getOrgFromName
import com.progressoft.psdms.application.utils.Utils.Companion.isNationalOperationMode
import com.progressoft.psdms.application.utils.Utils.Companion.updateDisputeCaseAndCreateCorrespondence
import org.springframework.stereotype.Component
import kotlin.reflect.full.createInstance


@Component
class ArbitrateDisputeCase : WfFunction<DMS_BaseCaseManagement>() {

    override fun execute(context: WfContext<DMS_BaseCaseManagement>) {

        val entityType = if (isNationalOperationMode(itemDao))
            context.getArgument(NATIONAL_TARGET_ENTITY, String::class.java)
        else
            context.getArgument(REGIONAL_TARGET_ENTITY, String::class.java)


        val targetEntity =
            Class.forName(entityType)
                .kotlin.createInstance() as DMS_BaseCaseManagement

        createNewCaseManagement(context, targetEntity)
    }

    private fun createNewCaseManagement(
        context: WfContext<DMS_BaseCaseManagement>,
        targetEntity: DMS_BaseCaseManagement
    ) {
        itemDao.merge(updateDisputeCaseAndCreateCorrespondence(context, itemDao))
        itemDao.flush()

        val disputeCase = context.entity.disputeCase
        targetEntity.disputeCase = disputeCase
        jfw.createEntity(targetEntity)

        when (targetEntity) {
            is DMS_ClaimantNCBCaseManagement -> {
                disputeCase?.claimantNCBCaseManagement = targetEntity
                targetEntity.orgId = getOrgFromName(disputeCase?.claimantBank?.nationalCentralBank?.name, itemDao)
            }

            is DMS_DefendantNCBCaseManagement -> {
                disputeCase?.defendantNCBCaseManagement = targetEntity
                targetEntity.orgId = getOrgFromName(disputeCase?.defendantBank?.nationalCentralBank?.name, itemDao)
            }

            is DMS_OperatorCaseManagement -> {
                disputeCase?.operatorCaseManagement = targetEntity
                targetEntity.orgId = getOrgFromName("DMS", itemDao)
            }
        }
        context.entity.note = ""
        itemDao.merge(targetEntity)

        itemDao.merge(disputeCase)
        itemDao.flush()
    }

}