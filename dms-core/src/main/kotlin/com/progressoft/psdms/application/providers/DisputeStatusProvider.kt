package com.progressoft.psdms.application.providers

import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.enums.DisputeStatus
import org.springframework.stereotype.Component

@Component
class DisputeStatusProvider : JupiterValueProvider<String, String> {
    override fun allValues(ctx: ValueProviderContext<String>?): MutableMap<String, String> {
        return enumValues<DisputeStatus>().associateBy({ it.description }, { it.name }).toMutableMap()
    }

}