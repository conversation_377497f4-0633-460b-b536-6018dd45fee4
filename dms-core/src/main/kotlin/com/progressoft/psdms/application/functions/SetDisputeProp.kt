package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.IdGenerator
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.sql.Timestamp
import java.util.*

@Component
class SetDisputeProp @Autowired constructor(private val idGenerator: IdGenerator) : WfFunction<DMS_DisputeCase>() {
    override fun execute(context : WfContext<DMS_DisputeCase>) {
        val disputeCase = context.entity
        if (disputeCase.creationDateTime ==null && disputeCase.caseReferenceNumber == null){
            disputeCase.creationDateTime = Timestamp(Date().time)
            disputeCase.caseReferenceNumber = idGenerator.newDbId()
            itemDao.persist(disputeCase)
        }
    }
}