package com.progressoft.psdms.application.validations

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DisputeAtt
import com.progressoft.jfw.AppValidationException.raiseIf
import com.progressoft.jfw.Query
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfValidator
import org.springframework.stereotype.Component


@Component
class RepresentAttachmentValidator: WfValidator<DMS_BaseCaseManagement>() {
    override fun validate(context: WfContext<DMS_BaseCaseManagement>) {
        val entity = context.entity.disputeCase
        val attachments = Query(itemDao)
            .equals("recordId", entity?.id.toString())
            .count(DMS_DisputeAtt::class.java)
        raiseIf(attachments <= entity?.numberOfAtt!!, REPRESENT_ATT_ERROR_MSG)
    }

    companion object {
        private const val REPRESENT_ATT_ERROR_MSG = "Kindly add a new Attachment before you Represent the dispute"
    }
}