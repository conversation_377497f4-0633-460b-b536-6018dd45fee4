package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DefendantBankCaseManagement
import com.progressoft.dms.entities.DMS_DefendantNCBCaseManagement
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import org.springframework.stereotype.Component

@Component
class IncrementRejectionCount : WfFunction<DMS_BaseCaseManagement>() {

    override fun execute(context: WfContext<DMS_BaseCaseManagement>) {
        val actionSource = context.getArgument("type", String::class.java)
        if (actionSource.equals("DEFENDANTBANK")) {
            val rejectedDispute = context.entity as DMS_DefendantBankCaseManagement
            val disputeCaseEntity = rejectedDispute.disputeCase
            disputeCaseEntity?.defendantBankRejectionCount = disputeCaseEntity?.defendantBankRejectionCount?.plus(1)!!
            itemDao.merge(disputeCaseEntity)
        } else {
            val rejectedDispute = context.entity as DMS_DefendantNCBCaseManagement
            val disputeCaseEntity = rejectedDispute.disputeCase
            disputeCaseEntity?.defendantNCBRejectionCount = disputeCaseEntity?.defendantNCBRejectionCount?.plus(1)!!
            itemDao.merge(disputeCaseEntity)
        }
    }
}