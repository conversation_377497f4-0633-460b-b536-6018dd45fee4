package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jfw.model.messaging.content.VelocityUtils
import com.progressoft.jfw.shared.push.message.JFWInfoMessage
import com.progressoft.jfw.shared.push.message.JFWMessage
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.utils.Constants.Companion.APPROVED
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER_CLOSE_DISPUTE
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER_REPAIR
import com.progressoft.psdms.application.utils.Constants.Companion.CLOSE_DISPUTE
import com.progressoft.psdms.application.utils.Constants.Companion.DEFAULT_MESSAGE
import com.progressoft.psdms.application.utils.Constants.Companion.DEFENDANT_APPROVAL
import com.progressoft.psdms.application.utils.Constants.Companion.DELETED
import com.progressoft.psdms.application.utils.Constants.Companion.DISPLAY_NAME
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_SENT
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER
import com.progressoft.psdms.application.utils.Constants.Companion.MESSAGES
import com.progressoft.psdms.application.utils.Constants.Companion.MODIFIED
import com.progressoft.psdms.application.utils.Constants.Companion.REJECTED
import com.progressoft.psdms.application.utils.Constants.Companion.REQUEST_APPROVAL
import com.progressoft.psdms.bellNotification.NotificationUtil
import com.progressoft.psdms.bellNotification.NotificationUtil.prepareNotificationPayload
import org.springframework.stereotype.Component

@Component
class BellNotification : WfFunction<DMS_DisputeCase>() {

    override fun execute(context: WfContext<DMS_DisputeCase>) {
        val entity = context.entity
        val senderName = entity.claimantBank?.name
        val receiverName = entity.defendantBank?.name
        val notificationType = context.getArgument("notificationType", String::class.java)
        val parameters = getParameters(entity)
        val jfwMessage = generateJfwMessage(parameters, notificationType)

        when (notificationType) {
            REQUEST_APPROVAL, MODIFIED, DELETED, CLOSE_DISPUTE, REJECTED, CHECKER_REPAIR -> {
                val suffix = if (notificationType == REJECTED || notificationType == CHECKER_REPAIR) MAKER else CHECKER
                if(jfw.currentPrefOrg.shortName.equals(entity.claimantBank?.name)) {
                    prepareNotificationPayload(jfwMessage, "$senderName$suffix", itemDao)
                } else {
                    prepareNotificationPayload(jfwMessage, "$DMS$suffix@$DMS", itemDao)
                }
            }

            APPROVED -> {
                val displayNames = if (jfw.currentPrefOrg.shortName.equals(entity.claimantBank?.name)) {
                    listOf("$receiverName$MAKER", "$senderName$MAKER")
                } else {
                    listOf("$DMS$MAKER@$DMS", "$senderName$MAKER", "$senderName$CHECKER")
                }
                sendMessagesToReceivers(displayNames, itemDao) { displayName ->
                    if (displayName == "$receiverName$MAKER") generateJfwMessage(parameters, DEFENDANT_APPROVAL) else jfwMessage
                }
            }
            CHECKER_CLOSE_DISPUTE -> {
                val displayNames = listOf("$senderName$MAKER", "$receiverName$MAKER", "$receiverName$CHECKER")
                sendMessagesToReceivers(displayNames, itemDao) { jfwMessage }
            }
        }
    }

    companion object {
        fun getParameters(entity: DMS_DisputeCase): Map<String, Any> {
            val parameters = mutableMapOf<String, Any>()
            parameters["sender"] = entity.claimantBank?.fullName!!
            parameters["caseRefNumber"] = entity.caseReferenceNumber!!
            parameters["receiver"] = entity.defendantBank?.fullName!!
            parameters["claimantArbitrationBankName"] = entity.claimantBank?.nationalCentralBank?.name!!
            return parameters
        }

        fun sendMessagesToReceivers(
            displayNames: List<String>,
            itemDao: ItemDao,
            messageProvider: (String) -> JFWMessage
        ) {
            displayNames.forEach { displayName ->
                val receiver = itemDao.getItem(User::class.java, DISPLAY_NAME, displayName)
                receiver?.let { NotificationUtil.sendMessage(it, messageProvider(displayName)) }
            }
        }

        fun generateJfwMessage(parameters: Map<String, Any>, notificationType: String): JFWMessage {
            val messageContent = MESSAGES[notificationType]
                ?: DEFAULT_MESSAGE
            return JFWInfoMessage
                .createSystemMessage(
                    VelocityUtils.evaluateTemplate("Dispute Notification", parameters),
                    VelocityUtils.evaluateTemplate(
                        messageContent,
                        parameters
                    )
                )
                .withCategory(DISPUTE_SENT)
        }
    }

}