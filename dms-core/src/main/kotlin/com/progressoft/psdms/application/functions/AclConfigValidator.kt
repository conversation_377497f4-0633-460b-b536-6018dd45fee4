package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_AclConfig
import com.progressoft.jfw.AppValidationException.raiseIf
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.core.IJFWEntity
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfValidator
import org.apache.commons.collections.CollectionUtils.isEmpty
import org.springframework.stereotype.Component

@Component
class AclConfigValidator : WfValidator<IJFWEntity?>() {

    override fun validate(context: WfContext<IJFWEntity?>?) {
        val entity = context?.entity
        if (entity is DMS_AclConfig) {
            raiseIf(isAmountAndParticipantNotProvided(entity), UNPROVIDED_AMOUNT_AND_PARTICIPANT_ERROR)
            val configs = Query(itemDao)
                .idNotEquals(entity.id)
                .items(DMS_AclConfig::class.java)
            configs.forEach {
                raiseIf(doesUserConfigExist(entity, it), EXISTING_USER_CONFIG_ERROR)
            }
        }
    }

    private fun doesUserConfigExist(newConfig: DMS_AclConfig, existingConfig: DMS_AclConfig): Boolean {
        val configUserIds = existingConfig.users.map(User::getId)
        return newConfig.users.stream()
            .map(User::getId)
            .anyMatch(configUserIds::contains)
    }

    private fun isAmountAndParticipantNotProvided(config: DMS_AclConfig) =
        config.maxAmount == null && isEmpty(config.participants)

    companion object {
        private const val EXISTING_USER_CONFIG_ERROR = "acl-config.existing.user.error"
        private const val UNPROVIDED_AMOUNT_AND_PARTICIPANT_ERROR = "acl-config.unprovided.amount.and.participant.error"
    }
}
