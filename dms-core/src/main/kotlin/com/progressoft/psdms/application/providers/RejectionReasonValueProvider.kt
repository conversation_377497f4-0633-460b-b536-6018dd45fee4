package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_ReasonManagement
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.utils.Constants.Companion.APPROVED_REASON_STATUS_CODE
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class RejectionReasonValueProvider(private val itemDao: ItemDao) : JupiterValueProvider<JFWEntity, Long> {

    override fun allValues(context: ValueProviderContext<JFWEntity>?): MutableMap<Long, String> {
        return Query(itemDao)
            .equals("rejectionReason", true)
            .statusCodeEquals(APPROVED_REASON_STATUS_CODE)
            .items(DMS_ReasonManagement::class.java).stream()
            .collect(Collectors.toMap(DMS_ReasonManagement::id, DMS_ReasonManagement::getCodeNamePair))
    }

}