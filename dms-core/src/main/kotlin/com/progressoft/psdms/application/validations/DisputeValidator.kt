package com.progressoft.psdms.application.validations

import com.progressoft.dms.entities.DMS_DisputeAtt
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.CASE_REF_NO
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.REF_DISPUTE_REFERENCE_NUM
import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.jfw.AppValidationException.raiseIf
import com.progressoft.jfw.Query
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfValidator
import org.springframework.stereotype.Component
import java.lang.String.format
import java.math.BigDecimal

@Component
class DisputeValidator : WfValidator<DMS_DisputeCase>() {

    companion object {
        private const val AMOUNT_ERROR_MESSAGE = "dms_disputeCase.amount.validation.message"
        private const val ZERO_AMOUNT_ERROR_MESSAGE =
            "dms_disputeCase.null.amount.validation.message"
        private const val ATTACHMENT_ERROR_MESSAGE = "dms_disputeCase.attachment.validation.message"
        private const val EXISTING_DISPUTE_TXN_REF_ERROR_MESSAGE = "dms_disputeCase.existing.dispute.validation.message"
        private const val DISPUTE_OVER_DISPUTE_ERROR_MESSAGE = "dms_disputeCase.dispute.over.dispute.validation.message"
    }

    override fun validate(context: WfContext<DMS_DisputeCase>) {
        val entity = context.originalEntity

        validateRef(entity)
        raiseIf(entity.disputedAmount == null, ZERO_AMOUNT_ERROR_MESSAGE)
        raiseIf(entity.disputedAmount?.compareTo(BigDecimal.ZERO)!! <= 0, ZERO_AMOUNT_ERROR_MESSAGE)

        if (!entity.disputeOverDispute)
            raiseIf((entity.disputedAmount?.compareTo(entity.transactionAmount)!! > 0), AMOUNT_ERROR_MESSAGE)
        else
            raiseIf((entity.disputedAmount?.compareTo(entity.refDisputeTransactionAmount)!! > 0), AMOUNT_ERROR_MESSAGE)

        val isMandatory = itemDao.getItem(DMS_SystemConfiguration::class.java, "configKey", "ATTACHMENT_MANDATE_FLAG")
        if (isMandatory.configValue.equals("true")) {
            val disputeAtt = Query(itemDao)
                .equals("recordId",entity.id.toString())
                .items(DMS_DisputeAtt::class.java)
            raiseIf(disputeAtt.isEmpty(), ATTACHMENT_ERROR_MESSAGE)
        }

    }

    private fun validateRef(entity: DMS_DisputeCase) {
        if (!entity.disputeOverDispute){
            val txnAlreadyDisputed = Query(itemDao)
                    .equals("transactionReference", entity.transactionReference)
                    .notEquals(CASE_REF_NO, entity.caseReferenceNumber)
                    .notEquals("statusId.description", "Deleted")
                    .doesExist(DMS_DisputeCase::class.java)
            raiseIf(txnAlreadyDisputed, format(EXISTING_DISPUTE_TXN_REF_ERROR_MESSAGE, entity.transactionReference))
        } else {
            val disputeAlreadyDisputed = Query(itemDao)
                    .isTrue(DMS_DisputeCase.DISPUTE_OVER_DISPUTE)
                    .idNotEquals(entity.id)
                    .equals(REF_DISPUTE_REFERENCE_NUM, entity.refDisputeCaseNumber)
                    .notEquals("statusId.description", "Deleted")
                    .doesExist(DMS_DisputeCase::class.java)
            raiseIf(disputeAlreadyDisputed, DISPUTE_OVER_DISPUTE_ERROR_MESSAGE)

        }

    }
}