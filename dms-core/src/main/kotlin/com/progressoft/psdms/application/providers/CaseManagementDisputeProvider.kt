package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class CaseManagementDisputeProvider(@Autowired private val itemDao: ItemDao) :
    JupiterValueProvider<DMS_BaseCaseManagement, Long> {

    override fun allValues(context: ValueProviderContext<DMS_BaseCaseManagement>): MutableMap<Long, String> {
        val entity = context.entity
        return itemDao.getItems(
            DMS_DisputeCase::class.java,
            null,
            null,
            "id = " + entity.disputeCase?.id,
            null
        ).stream()
            .collect(Collectors.toMap(DMS_DisputeCase::id, DMS_DisputeCase::caseReferenceNumber))
    }

}