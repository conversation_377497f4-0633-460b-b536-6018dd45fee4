package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.*
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.utils.Utils.Companion.getOrgFromName
import com.progressoft.psdms.application.utils.Utils.Companion.promoteToRoot
import org.springframework.stereotype.Component
import java.util.Objects.isNull

@Component
class SubmitDisputeCase : WfFunction<DMS_DisputeCase>() {

    override fun execute(context: WfContext<DMS_DisputeCase>) {
        val entity = context.entity
        if (jfw.currentPrefOrg.orgName == "DMS") {
            createCorrespondenceAndCloseDispute(entity)
            promoteEntityToRoot(entity)
            return
        }
        val claimantBankCaseManagement = DMS_ClaimantBankCaseManagement()
        claimantBankCaseManagement.disputeCase = entity
        val defendantBankCaseManagement = DMS_DefendantBankCaseManagement()
        defendantBankCaseManagement.disputeCase = entity
        promoteEntityToRoot(entity)
        jfw.createEntity(claimantBankCaseManagement)
        jfw.createEntity(defendantBankCaseManagement)
        claimantBankCaseManagement.orgId = getOrgFromName(entity?.claimantBank?.name, itemDao)
        defendantBankCaseManagement.orgId = getOrgFromName(entity?.defendantBank?.name, itemDao)
        entity.claimantBankCaseManagement = claimantBankCaseManagement
        entity.defendantBankCaseManagement = defendantBankCaseManagement


    }

    private fun createCorrespondenceAndCloseDispute(
        entity: DMS_DisputeCase
    ) {
        addCorrespondence(entity)
        jfw.enableServiceUser("DMS").use {
            jfw.executeAction(entity, "SVC_OperatorCloseDispute")
        }
    }

    private fun addCorrespondence(entity: DMS_DisputeCase) {
        jfw.enableServiceUser("SYSTEM").use {
            val correspondence = DMS_Correspondence()
            correspondence.dispute = entity
            correspondence.action = "Dispute closed"
            correspondence.note = "Adjusted entry"
            correspondence.actionBy = "Operator"
            itemDao.persist(correspondence)
            entity.correspondence.add(correspondence)
            itemDao.merge(entity)
            itemDao.flush()
        }
    }

    private fun promoteEntityToRoot(entity: DMS_DisputeCase) {
        promoteToRoot(entity)
        entity.transactionAdditionalInfos.forEach {
            promoteToRoot(it)
        }
        promoteAttachmentToRoot(entity)
    }

    private fun promoteAttachmentToRoot(entity: DMS_DisputeCase) {
        val disputeAtt = itemDao.getItem(DMS_DisputeAtt::class.java, "recordId", entity.id.toString())
        if (!isNull(disputeAtt)) {
            promoteToRoot(disputeAtt)
            itemDao.merge(disputeAtt)
        }
    }
}