package com.progressoft.psdms.application.enums

enum class ReportTypes(val description: String, val mimeType: String) {
    PDF("pdf", "application/pdf"),
    XLSX("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
    CSV("csv", "text/csv");

    companion object {
        fun getMimeTypeByDescription(description: String?): String? {
            return entries.find { it.description.equals(description, ignoreCase = true) }?.mimeType
        }
    }
}