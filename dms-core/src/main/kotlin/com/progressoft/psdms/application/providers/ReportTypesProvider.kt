package com.progressoft.psdms.application.providers

import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.enums.Nature
import com.progressoft.psdms.application.enums.ReportTypes
import org.springframework.stereotype.Component

@Component
class ReportTypesProvider : JupiterValueProvider<String, String> {

    override fun allValues(ctx: ValueProviderContext<String>?): MutableMap<String, String> {
        return enumValues<ReportTypes>().associateBy({ it.description }, { it.name }).toMutableMap()
    }
}

