package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_SLAConfigAutomaticAction
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class SlaConfigAutomaticActionProvider(@Autowired private val itemDao: ItemDao) : JupiterValueProvider<JFWEntity, Long> {

    override fun allValues(context: ValueProviderContext<JFWEntity>?): MutableMap<Long, String> {
        return itemDao.getItems(DMS_SLAConfigAutomaticAction::class.java).stream()
            .collect(Collectors.toMap(DMS_SLAConfigAutomaticAction::id, DMS_SLAConfigAutomaticAction::getCode))
    }

}