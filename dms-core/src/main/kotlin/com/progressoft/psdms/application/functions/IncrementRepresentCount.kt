package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_ClaimantBankCaseManagement
import com.progressoft.dms.entities.DMS_ClaimantNCBCaseManagement
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import org.springframework.stereotype.Component

@Component
class IncrementRepresentCount : WfFunction<DMS_BaseCaseManagement>() {

    override fun execute(context: WfContext<DMS_BaseCaseManagement>) {
        val actionSource = context.getArgument("type", String::class.java)
        if (actionSource.equals("CLAIMANTBANK")) {
            val rejectedDispute = context.entity as DMS_ClaimantBankCaseManagement
            val disputeCaseEntity = rejectedDispute.disputeCase
            disputeCaseEntity?.claimantBankRepresentCount = disputeCaseEntity?.claimantBankRepresentCount?.plus(1)!!
            itemDao.merge(disputeCaseEntity)
        } else {
            val rejectedDispute = context.entity as DMS_ClaimantNCBCaseManagement
            val disputeCaseEntity = rejectedDispute.disputeCase
            disputeCaseEntity?.claimantNCBRepresentCount = disputeCaseEntity?.claimantNCBRepresentCount?.plus(1)!!
            itemDao.merge(disputeCaseEntity)
        }
    }
}