package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_ReasonManagement
import com.progressoft.jfw.AppValidationException
import com.progressoft.jfw.Query
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import org.springframework.stereotype.Component
import java.util.Objects.isNull

@Component
class ReasonMngmntValidator : WfFunction<DMS_ReasonManagement>() {

    override fun execute(ctx: WfContext<DMS_ReasonManagement>) {
        if (isDuplicateCode(ctx.entity))
            throw AppValidationException(jfw.getMessage("reason.code.uniqueness.msg"))
        if (isReasonTypeNull(ctx.entity))
            throw AppValidationException(jfw.getMessage("reason.type.select.msg"))
    }

    private fun isDuplicateCode(entity: DMS_ReasonManagement): Boolean {
        return Query(itemDao)
            .idNotEquals(entity.id)
            .codeEquals(entity.code)
            .doesExist(DMS_ReasonManagement::class.java)
    }

    private fun isReasonTypeNull(entity: DMS_ReasonManagement?): Boolean {
        return isNullOrFalse(entity?.disputeReason!!) && isNullOrFalse(entity.rejectionReason)
                && isNullOrFalse(entity.representedReason) && isNullOrFalse(entity.requestInfoReason)
    }
    
    private fun isNullOrFalse(field: Boolean?): Boolean {
        return isNull(field) || field == false
    }
}