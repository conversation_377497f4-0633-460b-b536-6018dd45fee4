package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.jfw.workflow.WfChangeHandler
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import org.springframework.stereotype.Component

@Component
class ClaimantBankChangeHandler : WfChangeHandler<DMS_DisputeCase>() {
    override fun handle(context: WfChangeHandlerContext<DMS_DisputeCase>) {
        val entity = context.entity
        val currentUser = jfw.currentPrefOrg.shortName
        if (currentUser != "DMS") {
            entity.claimantBank = itemDao.getItem(DMS_Participant::class.java, "name", currentUser)
            context.setEnabled(false, "claimantBank")
        }
    }

}