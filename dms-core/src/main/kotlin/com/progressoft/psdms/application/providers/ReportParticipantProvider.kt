package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_NationalCentralBank
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.jfw.Jfw
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class ReportParticipantProvider(@Autowired private val itemDao: ItemDao,@Autowired private val jfw: Jfw) : JupiterValueProvider<JFWEntity, Long> {

    override fun allValues(context: ValueProviderContext<JFWEntity>?): MutableMap<Long, String> {
        val nationalCentralBank = Query(itemDao)
            .nameEquals(jfw.currentPrefOrg.shortName)
            .first(DMS_NationalCentralBank::class.java)

        return itemDao.getItems(DMS_Participant::class.java)
            .stream()
            .filter{ nationalCentralBank == null || it.nationalCentralBank?.id == nationalCentralBank.id }
            .collect(Collectors.toMap(DMS_Participant::id, DMS_Participant::getCodeNamePair))
    }

}