package com.progressoft.psdms.application.services

import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.psdms.application.exceptions.FetchTransactionException
import com.progressoft.psdms.application.models.Payment
import com.progressoft.psdms.application.models.TransactionFilter
import com.progressoft.psdms.application.utils.Constants.Companion.ACH
import com.progressoft.psdms.application.utils.Constants.Companion.IIPS
import com.progressoft.psdms.application.utils.Constants.Companion.paymentSystemEndPoints
import com.progressoft.psdms.application.utils.Utils.Companion.fromJSON
import com.progressoft.psdms.application.utils.Utils.Companion.getAmountValue
import com.progressoft.psdms.application.utils.Utils.Companion.getStringValue
import com.progressoft.psdms.application.utils.Utils.Companion.toJSON
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.OkHttpClient
import okhttp3.Request.Builder
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import jakarta.ws.rs.HttpMethod.POST

@Service
class ApiService {

    @Value("\${IIPSTransactionApiKey}")
    val apiKey: String = ""

    @Autowired
    private val itemDao: ItemDao? = null

    private val client = OkHttpClient()

    fun fetchTransactionApi(filters: Map<String?, String?>, paymentSystem: String?, size: Int, page: Int): Payment {
        val request = buildRequest(paymentSystem, size, page, filters)
        client.newCall(request).execute().use { response ->
            if (!response.isSuccessful)
                throw FetchTransactionException(response.body.string())
            return fromJSON(response.body.string(), Payment::class.java)
        }
    }

    private fun buildRequest(paymentSystem: String?, size: Int, page: Int, filters: Map<String?, String?>) =
        if (paymentSystem.equals(ACH)) {
            val build = getPaymentSystemUrl(paymentSystem)!!.toHttpUrlOrNull()?.newBuilder()!!
            filters.filterValues { !it.isNullOrEmpty() }.forEach { (k, v) ->
                build.addQueryParameter(k!!, v)
            }
            Builder().url(build.build()).get().build()
        } else {
            val builder = Builder().url(getUrlWithParam(paymentSystem, size, page))
                .get().method(
                    POST,
                    toJSON(getTransactionFilters(filters)).toRequestBody("application/json; charset=utf-8".toMediaType())
                )
                .header("Content-Type", "application/json")
            if (paymentSystem.equals(IIPS)) {
                builder.addHeader("client", "DMS").addHeader("api-key", apiKey)
            }
            builder.build()
        }

    private fun getUrlWithParam(paymentSystem: String?, size: Int, page: Int) =
        getPaymentSystemUrl(paymentSystem)!! + getParams(size, page)

    private fun getParams(size: Int, page: Int) = "?size=$size&page=$page"

    private fun getTransactionFilters(filters: Map<String?, String?>): TransactionFilter {
        val transactionFilter = TransactionFilter()
        transactionFilter.transactionReference = getStringValue(filters["transactionReference"])
        transactionFilter.transactionCurrency = getStringValue(filters["currency"])
        transactionFilter.senderParticipant = getStringValue(filters["senderParticipant"])
        transactionFilter.receiverParticipant = getStringValue(filters["receiverParticipant"])
        transactionFilter.fromDate = getStringValue(filters["fromDate"])
        transactionFilter.toDate = getStringValue(filters["toDate"])
        transactionFilter.fromAmount = getAmountValue(filters["fromAmount"])
        transactionFilter.toAmount = getAmountValue(filters["toAmount"])
        transactionFilter.claimantBank = getStringValue(filters["claimantBank"])
        transactionFilter.defendantBank = getStringValue(filters["defendantBank"])
        transactionFilter.endToEndId = getStringValue(filters["endToEndId"])
        return transactionFilter
    }

    private fun getPaymentSystemUrl(paymentSystem: String?) = itemDao?.getItem(
        DMS_SystemConfiguration::class.java,
        "configKey",
        paymentSystemEndPoints[paymentSystem]
    )?.configValue

}