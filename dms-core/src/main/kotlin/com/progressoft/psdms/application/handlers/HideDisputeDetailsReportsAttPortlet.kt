package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeDetailsReport
import com.progressoft.jfw.workflow.WfChangeHandler
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import org.springframework.stereotype.Component

@Component
class HideDisputeDetailsReportsAttPortlet : WfChangeHandler<DMS_DisputeDetailsReport>() {
    override fun handle(ctx: WfChangeHandlerContext<DMS_DisputeDetailsReport>) {
        val entity = ctx.entity
        ctx.setPortletVisible(entity?.statusId?.code == "700002", "dispute.details.report.att")
    }
}
