package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.dms.entities.DMS_TransactionAdditionalInfo
import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency
import com.progressoft.jfw.workflow.WfChangeHandler
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import com.progressoft.psdms.application.models.AdditionalInfo
import com.progressoft.psdms.application.models.Transaction
import com.progressoft.psdms.application.services.ApiService
import com.progressoft.psdms.application.utils.Constants.Companion.TRANSACTION_ADDITIONAL_INFO
import com.progressoft.psdms.application.utils.Constants.Companion.TRANSACTION_DATE_FORMATTER
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.Optional.of

@Component
class TransactionPaymentHandler : WfChangeHandler<DMS_DisputeCase>() {

    @Autowired
    val apiService: ApiService = ApiService()

    override fun handle(context: WfChangeHandlerContext<DMS_DisputeCase>) {
        val entity: DMS_DisputeCase? = context.entity
        if (entity?.transactionReference != null) {
            val transactions = apiService.fetchTransactionApi(
                mapOf("transactionReference" to entity.transactionReference),
                entity.paymentSystem?.code!!,
                1, 0
            ).transactions
            if (transactions.isNotEmpty()) {
                updateFields(entity, transactions.first())
                context.setPortletVisible(true, TRANSACTION_ADDITIONAL_INFO)
            }
        }
    }

    private fun updateFields(entity: DMS_DisputeCase, transaction: Transaction) {
        entity.transactionAdditionalInfos.forEach {
            it.deletedFlag = true
            itemDao.merge(it)
        }
        entity.transactionReference = transaction.transactionReference
        entity.transactionCurrency = getEntity(JFWCurrency::class.java, transaction.transactionCurrency)
        entity.senderParticipant = getEntity(DMS_Participant::class.java, transaction.senderParticipant)
        entity.receiverParticipant = getEntity(DMS_Participant::class.java, transaction.receiverParticipant)
        entity.transactionDate = TRANSACTION_DATE_FORMATTER.parse(transaction.transactionDate)
        entity.transactionAmount = transaction.transactionAmount
        of(transaction).map(Transaction::additionalInfo).ifPresent { additionalInfos ->
            additionalInfos.forEach { createTransactionAdditionalInfo(it, entity) }
        }
        itemDao.merge(entity)
        itemDao.flush()
    }

    private fun createTransactionAdditionalInfo(additionalInfo: AdditionalInfo, entity: DMS_DisputeCase) {
        val transactionAdditionalInfo = DMS_TransactionAdditionalInfo()
        transactionAdditionalInfo.additionalInfoKey = additionalInfo.key
        transactionAdditionalInfo.additionalInfoValue = additionalInfo.value
        transactionAdditionalInfo.disputeCase = entity
        itemDao.persist(transactionAdditionalInfo)
        entity.transactionAdditionalInfos.add(transactionAdditionalInfo)
    }

    private fun <T> getEntity(entityType: Class<T>, value: String): T {
        return itemDao.getItem(entityType, "code", value)
    }

}