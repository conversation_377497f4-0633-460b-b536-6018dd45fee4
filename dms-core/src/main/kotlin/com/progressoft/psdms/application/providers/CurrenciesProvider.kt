package com.progressoft.psdms.application.providers

import com.progressoft.jfw.Currencies
import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jupiter.search.SearchProvider
import com.progressoft.jupiter.search.SearchProvider.Pair
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class CurrenciesProvider(@Autowired private val currencies: Currencies) : SearchProvider, JupiterValueProvider<JFWEntity, Long> {

    override fun allValues(context: ValueProviderContext<JFWEntity>?): MutableMap<Long, String> {
        return currencies.get().stream().collect(Collectors.toMap(JFWCurrency::getId, JFWCurrency::getCodeNamePair))
    }

    override fun allValues(): List<Pair> = currencies.get().map { Pair(it.id.toString(), it.codeNamePair) }

}