package com.progressoft.psdms.application.handlers

import com.progressoft.dms.entities.DMS_DisputeSummaryReport
import com.progressoft.jfw.workflow.WfChangeHandler
import com.progressoft.jfw.workflow.WfChangeHandlerContext
import org.springframework.stereotype.Component

@Component
class HideDisputeSummaryReportsAttPortlet : WfChangeHandler<DMS_DisputeSummaryReport>() {
    override fun handle(ctx: WfChangeHandlerContext<DMS_DisputeSummaryReport>) {
        val entity = ctx.entity
        ctx.setPortletVisible(entity?.statusId?.code == "700002", "dispute.summary.report.att")
    }
}
