package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.search.SearchProvider
import com.progressoft.jupiter.search.SearchProvider.Pair
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class ParticipantProvider(@Autowired private val itemDao: ItemDao) : SearchProvider, JupiterValueProvider<JFWEntity, Long> {

    override fun allValues(context: ValueProviderContext<JFWEntity>?): MutableMap<Long, String> {
        return itemDao.getItems(DMS_Participant::class.java).stream().collect(Collectors.toMap(DMS_Participant::id, DMS_Participant::getCodeNamePair))
    }

    override fun allValues(): List<Pair> = itemDao.getItems(DMS_Participant::class.java).map {
            Pair(it.id.toString(), it.codeNamePair)
    }
}