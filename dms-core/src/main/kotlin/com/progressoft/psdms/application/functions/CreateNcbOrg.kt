package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_NationalCentralBank
import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.jfw.AppValidationException
import com.progressoft.jfw.model.bussinessobject.core.JfwViewsScheme
import com.progressoft.jfw.model.bussinessobject.security.Org
import com.progressoft.jfw.model.exception.BusinessException
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.models.OrgModel
import com.progressoft.psdms.application.startuptasks.JfwUsersUtil
import com.progressoft.psdms.application.utils.Constants
import com.progressoft.psdms.application.utils.Constants.Companion.CHECKER
import com.progressoft.psdms.application.utils.Constants.Companion.MAKER
import com.progressoft.psdms.application.utils.Constants.Companion.NCB
import com.progressoft.psdms.application.utils.Utils.Companion.createNewOrg
import com.progressoft.psdms.application.utils.Utils.Companion.isNationalOperationMode
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.util.Objects.requireNonNull

@Component
class CreateNcbOrg : WfFunction<DMS_NationalCentralBank>() {

    @Autowired
    private lateinit var jfwUsersUtil: JfwUsersUtil

    @Value("\${new.participant.default.password}")
    private lateinit var defaultPassword: String

    override fun execute(context: WfContext<DMS_NationalCentralBank>) {
        try {
            if (!isNationalOperationMode(itemDao)) {
                jfw.enableServiceUser("DMS").use {
                    val entity = context.entity
                    val org = createNewOrg(entity)
                    jfw.createEntity("Orgs", org)
                    jfw.executeAction("Orgs", org, "Request Activation")
                    jfw.executeAction("Orgs", org, "Approve Activation")
                    itemDao.merge(entity)
                    val config = itemDao.getItem(DMS_SystemConfiguration::class.java, "configKey", "Operation Mode")
                    if (requireNonNull(config.configValue) != Constants.NATIONAL) {
                        jfwUsersUtil.createNewUserAccount(defaultPassword, entity.name, org, MAKER, NCB)
                        jfwUsersUtil.createNewUserAccount(defaultPassword, entity.name, org, CHECKER, NCB)
                    }
                }
            }
        } catch (ex: BusinessException) {
            throw AppValidationException(ex)
        }
    }

    private fun createNewOrg(entity: DMS_NationalCentralBank): Org {
        val parentOrg = itemDao.getItem(Org::class.java, "shortName", "DMS")
        val jfwViewsScheme = itemDao.getItem(JfwViewsScheme::class.java, "description", "Ncbs")
        return createNewOrg(
            OrgModel(
                entity.description,
                entity.name,
                entity.description,
                entity.name,
                parentOrg,
                jfwViewsScheme
            )
        )
    }

}