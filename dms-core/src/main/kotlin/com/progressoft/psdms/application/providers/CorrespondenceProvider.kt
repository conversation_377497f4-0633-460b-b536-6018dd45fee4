package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_Correspondence
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class CorrespondenceProvider(@Autowired private val itemDao: ItemDao) :
    JupiterValueProvider<DMS_DisputeCase, Long> {

    override fun allValues(context: ValueProviderContext<DMS_DisputeCase>): MutableMap<Long, String> {
        val disputeCase = context.entity
        return itemDao.getItems(
            DMS_Correspondence::class.java,
            null,
            null,
            "dispute.id = " + disputeCase.id,
            null
        ).stream()
            .collect(
                Collectors.toMap(
                    DMS_Correspondence::id
                ) { correspondence: DMS_Correspondence ->
                    correspondence.action.plus("-").plus(correspondence.actionBy)
                }
            )
    }

}