package com.progressoft.psdms.application.processor

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.jfw.AppProcessor
import com.progressoft.jfw.Query
import com.progressoft.psdms.application.utils.Constants.Companion.ARBITRATION_NOTIFICATION_THRESHOLD_IN_DAYS
import com.progressoft.psdms.application.utils.Constants.Companion.BANK
import com.progressoft.psdms.application.utils.Constants.Companion.BANK_EXCEED_ARBITRATION_NOTIFIED
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_ARBITRATED
import com.progressoft.psdms.application.utils.Constants.Companion.NCB_EXCEED_ARBITRATION_NOTIFIED
import com.progressoft.psdms.application.utils.Constants.Companion.notifyArbitrationExceedingMap
import com.progressoft.psdms.email.EmailSenderService
import com.progressoft.psdms.util.EmailUtils
import org.apache.camel.Exchange
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component


@Component
open class NotifyArbitrationExceedingProcessor @Autowired constructor(
    private val emailSenderService: EmailSenderService,
    private val emailUtil: EmailUtils
) : AppProcessor() {

    @Value("\${calculate.the.number.of.days.script}")
    val script:String = ""

    override fun doProcess(exchange: Exchange) {
        jfw.enableServiceUser("DMS").use {
            LOG.info("Start NotifyArbitrationExceedingProcessor ...")
            val maxDays = emailUtil.getConfigValue(ARBITRATION_NOTIFICATION_THRESHOLD_IN_DAYS)
            notifyArbitrationExceedingMap.forEach { executeNotifyAction(maxDays.toLong(), it.key, it.value) }
        }
    }

    private fun executeNotifyAction(maxDays: Long, type: String, clazz: Class<out DMS_BaseCaseManagement>) {
        val caseManagements = Query(itemDao)
            .statusNameEquals(DISPUTE_ARBITRATED)
            .isFalse(if (type == BANK) BANK_EXCEED_ARBITRATION_NOTIFIED else NCB_EXCEED_ARBITRATION_NOTIFIED)
            .where("$script >= $maxDays")
            .items(clazz)
        caseManagements.forEach {
            emailSenderService.sendEmail(it)
            if (type == BANK)
                it.disputeCase!!.bankExceedArbitrationNotified = true
            else
                it.disputeCase!!.ncbExceedArbitrationNotified = true
            itemDao.merge(it.disputeCase)
        }
    }
}