package com.progressoft.psdms.application.utils

import com.progressoft.dms.entities.*
import java.text.SimpleDateFormat

class Constants {
    companion object {
        const val SLA_EXCEEDED_PENALTY = "SLA Exceeded Penalty"
        const val URGENT_DISPUTE_FEE = "Urgent Dispute Fee"
        const val DISPUTE_OF_A_DISPUTE_FEE = "Dispute of a Dispute Fee"
        const val SLA_EXCEEDED = "SLA Exceeding"
        const val SLA_Arbitrate = "SVC_SLA_Arbitrate"
        const val CLAIMANT_BANK = "ClaimantBank"
        const val DEFENDANT_BANK = "DefendantBank"
        const val CLAIMANT_NCB = "ClaimantNCB"
        const val DEFENDANT_NCB = "DefendantNCB"
        const val OPERATOR = "Operator"
        const val OPERATOR_EMAIL = "operatorEmail"
        const val BANK = "bank"
        const val NCB = "ncb"
        const val ACH = "ACH"
        const val IIPS = "IIPS"
        const val DMS = "DMS"
        const val USERS = "Users"
        const val BANKS_CHECKER = "BANKS CHECKER"
        const val MAKER = "MAKER"
        const val CHECKER = "CHECKER"
        const val BANKS_MAKER = "BANKS MAKER"
        const val NCBS_MAKER = "NCBS MAKER"
        const val NCBS_CHECKER = "NCBS CHECKER"
        const val GROUPS = "Groups"
        const val WF_GROUPS = "SecurityGroupWF"
        const val STEP_ACTIVE = "20"
        val paymentSystemEndPoints =
            mapOf(
                ACH to "ACH_TRANSACTION_ENDPOINT",
                IIPS to "IIPS_TRANSACTION_ENDPOINT"
            )
        val slaConfigurationRegionalPartyMap =
            mapOf(
                CLAIMANT_BANK to DMS_ClaimantBankCaseManagement::class.java,
                DEFENDANT_BANK to DMS_DefendantBankCaseManagement::class.java,
                CLAIMANT_NCB to DMS_ClaimantNCBCaseManagement::class.java,
                DEFENDANT_NCB to DMS_DefendantNCBCaseManagement::class.java,
                OPERATOR to DMS_OperatorCaseManagement::class.java
            )
        val slaConfigurationNationalPartyMap =
            mapOf(
                CLAIMANT_BANK to DMS_ClaimantBankCaseManagement::class.java,
                DEFENDANT_BANK to DMS_DefendantBankCaseManagement::class.java,
                OPERATOR to DMS_OperatorCaseManagement::class.java
            )
        val notifyArbitrationExceedingMap = mapOf(
            BANK to DMS_ClaimantBankCaseManagement::class.java,
            NCB to DMS_ClaimantNCBCaseManagement::class.java
        )
        const val DEFAULT_MESSAGE = "New Dispute notification from \${sender}"

        const val APPROVED = "Approved"
        const val OPERATOR_CHECKER_APPROVED = "OperatorCheckerApprove"
        const val OPERATOR_CHECKER_REJECT = "OperatorCheckerReject"
        const val OPERATOR_CHECKER_TO_DEFENDANT_ADDITIONAL_INFO = "OperatorToDefendantAdditionalInfo"
        const val OPERATOR_MAKER_TO_CLAIMANT = "OperatorMakerToClaimant"
        const val MAKER_APPROVED_OR_REJECT = "MakerAction"
        const val OPERATOR_MAKER_APPROVED_OR_REJECT = "OperatorMakerAction"
        const val OPERATOR_CHECKER_REPAIR = "OperatorCheckerRepair"
        const val NCB_MAKER_ACTION = "NcbMakerAction"
        const val NCB_CHECKER_ACTION = "NcbCheckerAction"
        const val NCB_CHECKER_TO_OPERATOR = "NcbCheckerToOperator"
        const val MAKER_APPROVED_OR_REJECT_REPRESENT = "MakerActionRepresent"
        const val DEFENDANT_APPROVAL = "DefendantApproved"
        const val REJECTED = "Rejected"
        const val MODIFIED = "Modified"
        const val DELETED = "Deleted"
        const val REQUEST_APPROVAL = "RequestApprove"
        const val CHECKER_REPAIR = "CheckerRepair"
        const val ARBITRATE_REQUEST = "ArbitrateRequest"
        const val CHECKER_ARBITRATE = "CheckerArbitrate"
        const val DEFENDANT_MAKER_REJECTION = "DefendantMakerReject"
        const val DEFENDANT_CHECKER_REJECTION = "DefendantCheckerReject"
        const val DISPLAY_NAME = "displayName"
        const val MAKER_ADDITIONAL_INFO = "MakerDefendantAdditionalInfo"
        const val CHECKER_ADDITIONAL_INFO = "CheckerDefendantAdditionalInfo"
        const val OPERATOR_ADDITIONAL_INFO = "OperatorAdditionalInfoProvided"
        const val CLOSE_DISPUTE = "CloseDispute"
        const val CHECKER_CLOSE_DISPUTE = "ApproveCloseDispute"
        const val DISPUTE_SENT = "Dispute Sent"
        val MESSAGES = mapOf(
            APPROVED to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has been approved.",
            OPERATOR_CHECKER_APPROVED to "New Dispute notification: Represented Dispute from Sender: \${sender} with Case Reference Number \${caseRefNumber} has been approved by Operator.",
            OPERATOR_CHECKER_REJECT to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has been rejected by Operator",
            OPERATOR_MAKER_APPROVED_OR_REJECT to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} for your review from Operator.",
            OPERATOR_CHECKER_TO_DEFENDANT_ADDITIONAL_INFO to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} from Operator needs additional information.",
            OPERATOR_CHECKER_REPAIR to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} from operator needs repair.",
            OPERATOR_MAKER_TO_CLAIMANT to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} from operator needs more information.",
            OPERATOR_ADDITIONAL_INFO to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has provided additional info.",
            DEFENDANT_MAKER_REJECTION to "New Dispute notification: Dispute from Sender: \${sender} with Case Reference Number \${caseRefNumber} has been rejected.",
            DEFENDANT_CHECKER_REJECTION to "New Dispute notification: New Dispute from Sender: \${sender} has been rejected by \${receiver}.",
            MAKER_APPROVED_OR_REJECT to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} for your review.",
            MAKER_APPROVED_OR_REJECT_REPRESENT to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} represented for your review.",
            MAKER_ADDITIONAL_INFO to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} needs additional information.",
            CHECKER_ADDITIONAL_INFO to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} provided additional information.",
            CHECKER_REPAIR to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} needs repair.",
            ARBITRATE_REQUEST to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} arbitrated for your review.",
            CHECKER_ARBITRATE to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} for your review.",
            NCB_MAKER_ACTION to "New Dispute notification: Arbitrated Dispute with Case Reference Number \${caseRefNumber} from \${sender} for your review.",
            NCB_CHECKER_ACTION to "New Dispute notification: Arbitrated Dispute with Case Reference Number \${caseRefNumber} from \${sender} for your review.",
            NCB_CHECKER_TO_OPERATOR to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has been arbitrated from \${claimantArbitrationBankName} for your review.",
            CLOSE_DISPUTE to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has been closed and waiting your review.",
            DEFENDANT_APPROVAL to "New Dispute notification: New Dispute from Sender: \${sender} waiting your review",
            REJECTED to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has been rejected",
            MODIFIED to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has been modified",
            DELETED to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has been deleted",
            REQUEST_APPROVAL to "New Dispute notification: Dispute awaiting approval",
            CHECKER_CLOSE_DISPUTE to "New Dispute notification: Dispute with Case Reference Number \${caseRefNumber} has been closed."
        )
        const val FETCH_TRANSACTION_POPUP_PORTLET = "claimant.fetch.transactions"
        const val TRANSACTION_ADDITIONAL_INFO = "dms_disputeCase.TransactionAdditionalInfo"
        const val TRANSACTION_INFO = "dms_disputeCase.transactionInformation"
        const val DISPUTE_REF_INFO_PORTLET = "dms_disputeCase.dispute.info.portlet"
        val MODIFICATION_WF_STATUSES = listOf("1110", "304", "311", "315", "811", "910", "8110")
        val TRANSACTION_DATE_FORMATTER = SimpleDateFormat("yyyy-MM-dd")
        const val FETCH_DISPUTE_PORTLET = "dms_disputeCase.fetch.disputes.portlet"
        const val ACTIVE_DISPUTE_STATUS_CODE = "406"
        const val APPROVED_CLOSED_DISPUTE_STATUS_CODE = "721"
        const val CLOSED_DISPUTE_STATUS_CODE = "714"
        const val APPROVED_REASON_STATUS_CODE = "3003"
        const val LAST_ACTION = "lastAction"
        const val LAST_ACTION_BY = "lastActionBy"
        const val REGIONAL_TARGET_ENTITY = "regionalTargetEntity"
        const val STEP_NAME = "stepName"
        const val NATIONAL = "National"
        const val NATIONAL_TARGET_ENTITY = "nationalTargetEntity"
        const val DISPUTE_ARBITRATED = "Dispute Arbitrated"
        const val BANK_EXCEED_ARBITRATION_NOTIFIED = "disputeCase.bankExceedArbitrationNotified"
        const val NCB_EXCEED_ARBITRATION_NOTIFIED = "disputeCase.ncbExceedArbitrationNotified"
        const val ARBITRATION_NOTIFICATION_THRESHOLD_IN_DAYS = "Arbitration Notification Threshold in Days"
        const val ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_ADDRESS = "Arbitration Threshold Exceeding E-Mail Address"
        const val ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_SUBJECT = "Arbitration of Dispute Case: {DISPUTE_CASE_ID}"
        const val NCB_ARBITRATION_TO_OPERATOR_EMAIL_SUBJECT =
            "Arbitration of Dispute Case to Operator: {DISPUTE_CASE_ID}"
        const val AUTO_ACCEPTANCE_EMAIL_SUBJECT = "Auto acceptance of Dispute Case: {DISPUTE_CASE_ID}"
        const val CREATION_EMAIL_SUBJECT = "Dispute Case: {DISPUTE_CASE_ID} has been created"
        const val FEE_EMAIL_SUBJECT = "Dispute Case: {DISPUTE_CASE_ID} fee"
        const val ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_BODY = "Dear Administrator\n" +
                "Kindly note the dispute has been arbitrated with the following details:\n" +
                "\n" +
                "Dispute ID: {DISPUTE_CASE_ID}\n" +
                "Dispute Creation Date/Time: {CREATION_DATE}\n" +
                "Payment System: {PAYMENT_SYSTEM}\n" +
                "Assigner: {CLAIMANT_BANK_NAME}\n" +
                "Assignee: {DEFENDANT_BANK_NAME}\n" +
                "Transaction ID: {TRANSACTION_ID}\n" +
                "Disputed Amount: {DISPUTE_AMOUNT}"
        const val DISPUTE_ACTION_EMAIL_BODY = "Dear,\n" +
                "Kindly note the dispute with the following details:\n" +
                "\n" +
                "Dispute ID: {DISPUTE_CASE_ID}\n" +
                "Dispute Creation Date/Time: {CREATION_DATE}\n" +
                "Payment System: {PAYMENT_SYSTEM}\n" +
                "Assigner: {CLAIMANT_BANK_NAME}\n" +
                "Assignee: {DEFENDANT_BANK_NAME}\n" +
                "Transaction ID: {TRANSACTION_ID}\n" +
                "Disputed Amount: {DISPUTE_AMOUNT}\n" +
                "has been accepted"
        const val DISPUTE_CREATION_EMAIL_BODY = "Dear,\n" +
                "Kindly note the dispute with the following details:\n" +
                "\n" +
                "Dispute ID: {DISPUTE_CASE_ID}\n" +
                "Dispute Creation Date/Time: {CREATION_DATE}\n" +
                "Payment System: {PAYMENT_SYSTEM}\n" +
                "Assigner: {CLAIMANT_BANK_NAME}\n" +
                "Assignee: {DEFENDANT_BANK_NAME}\n" +
                "Transaction ID: {TRANSACTION_ID}\n" +
                "Disputed Amount: {DISPUTE_AMOUNT}\n" +
                "has been created."
        const val DISPUTE_FEE_EMAIL_BODY = "Dear,\n" +
                "Kindly note that you have incurred a fee of {FEE_AMOUNT} {FEE_CURRENCY} on the dispute with the following details:\n" +
                "\n" +
                "Dispute ID: {DISPUTE_CASE_ID}\n" +
                "Dispute Creation Date/Time: {CREATION_DATE}\n" +
                "Payment System: {PAYMENT_SYSTEM}\n" +
                "Assigner: {CLAIMANT_BANK_NAME}\n" +
                "Assignee: {DEFENDANT_BANK_NAME}\n" +
                "Transaction ID: {TRANSACTION_ID}\n" +
                "Disputed Amount: {DISPUTE_AMOUNT}\n" +
                "has been created."
        const val NCB_ARBITRATION_TO_OPERATOR_EMAIL_BODY = "Dear Administrator\n" +
                "Kindly note the dispute has been arbitrated to Operator with the following details:\n" +
                "\n" +
                "Dispute ID: {DISPUTE_CASE_ID}\n" +
                "Dispute Creation Date/Time: {CREATION_DATE}\n" +
                "Payment System: {PAYMENT_SYSTEM}\n" +
                "Assigner: {CLAIMANT_BANK_NAME}\n" +
                "Assignee: {DEFENDANT_BANK_NAME}\n" +
                "Transaction ID: {TRANSACTION_ID}\n" +
                "Disputed Amount: {DISPUTE_AMOUNT}"
    }
}