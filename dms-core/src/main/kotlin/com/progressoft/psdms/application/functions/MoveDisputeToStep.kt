package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.utils.Constants
import com.progressoft.psdms.application.utils.Constants.Companion.REGIONAL_TARGET_ENTITY
import com.progressoft.psdms.application.utils.Constants.Companion.STEP_NAME
import com.progressoft.psdms.application.utils.Utils.Companion.getArgument
import com.progressoft.psdms.application.utils.Utils.Companion.isNationalOperationMode
import com.progressoft.psdms.application.utils.Utils.Companion.updateDisputeCaseAndCreateCorrespondence
import org.springframework.stereotype.Component

@Component
open class MoveDisputeToStep : WfFunction<DMS_BaseCaseManagement>() {

    override fun execute(context: WfContext<DMS_BaseCaseManagement>) {

        val entityType = if (isNationalOperationMode(itemDao))
            context.getArgument(Constants.NATIONAL_TARGET_ENTITY, String::class.java)
        else
            context.getArgument(REGIONAL_TARGET_ENTITY, String::class.java)

        val entity = context.entity
        jfw.enableServiceUser("DMS").use {
            val target = itemDao.getItem(
            Class.forName(entityType),
                "disputeCase.id",
                entity.disputeCase?.id
            ) as DMS_BaseCaseManagement
            updateBaseDispute(context)
            entity.note = ""
            itemDao.merge(entity)
            jfw.executeAction(target, getArgument(context, STEP_NAME))
        }
    }

    private fun updateBaseDispute(
        context: WfContext<DMS_BaseCaseManagement>
    ) {
        val updatedDisputeCase = updateDisputeCaseAndCreateCorrespondence(context = context, itemDao)
        itemDao.merge(updatedDisputeCase)
        itemDao.flush()
    }


}
