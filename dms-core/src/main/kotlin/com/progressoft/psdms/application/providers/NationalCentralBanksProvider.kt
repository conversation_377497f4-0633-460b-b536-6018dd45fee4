package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_NationalCentralBank
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.constants.WF_Bank
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class NationalCentralBanksProvider(@Autowired private val itemDao: ItemDao) : JupiterValueProvider<JFWEntity, Long> {

    override fun allValues(valueProviderContext: ValueProviderContext<JFWEntity>): MutableMap<Long, String> {
        if (valueProviderContext.entity is DMS_Participant) {
            return Query(itemDao).where("statusId.name = '${WF_Bank.STEP_STATUS_ToApproved}'").items(DMS_NationalCentralBank::class.java).stream()
                .collect(Collectors.toMap(DMS_NationalCentralBank::id, DMS_NationalCentralBank::getCodeNamePair))
        }
        return mutableMapOf()
    }
}