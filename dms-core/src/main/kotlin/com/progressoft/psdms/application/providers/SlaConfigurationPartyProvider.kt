package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_SLAConfigurationParty
import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.utils.Constants.Companion.slaConfigurationNationalPartyMap
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class SlaConfigurationPartyProvider(@Autowired private val itemDao: ItemDao) : JupiterValueProvider<JFWEntity, Long> {

    companion object {
        private const val OPERATION_MODE = "National"
    }

    override fun allValues(context: ValueProviderContext<JFWEntity>?): MutableMap<Long, String> {
        val item = itemDao.getItem(DMS_SystemConfiguration::class.java, "configKey", "Operation Mode")

        if (item.configValue == OPERATION_MODE)
            return itemDao.getItems(DMS_SLAConfigurationParty::class.java).stream()
                .filter { slaConfigurationNationalPartyMap.containsKey(it.code) }
                .collect(Collectors.toMap(DMS_SLAConfigurationParty::id, DMS_SLAConfigurationParty::getName))

        return itemDao.getItems(DMS_SLAConfigurationParty::class.java).stream()
            .collect(Collectors.toMap(DMS_SLAConfigurationParty::id, DMS_SLAConfigurationParty::getName))
    }

}