package com.progressoft.psdms.application.reports.generator

import com.progressoft.dms.entities.DMS_DisputeDetailsReport
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate

@Component
class GenerateDisputeDetailsReport(
    @Value("\${reporting.base.url}") reportingBaseUrl: String?, restTemplate: RestTemplate
) :
    AbstractReportGenerator<DMS_DisputeDetailsReport?>(restTemplate, reportingBaseUrl!!) {
    override fun getReportType(entity: DMS_DisputeDetailsReport?): String? {
       return entity?.type
    }

    override val reportName: String
        get() = "dispute_details_report"

    override val errorMsgLabel: String
        get() = "dispute.details.report.failed.generation"

    override fun getRequestContent(entity: DMS_DisputeDetailsReport?): MutableMap<String, String?> {
        val userCountry = getUserCountry()
        val parameters: MutableMap<String, String?> = HashMap()
        parameters["reportID"] = entity!!.id.toString()
        parameters["nature"] = entity.nature
        parameters["countryID"] = userCountry?.id?.toString()
        parameters["countryCode"] = userCountry?.code
        parameters["reasonID"] = if (entity.reason!= null) entity.reason!!.id.toString() else null
        parameters["reasonCode"] = entity.reason?.name
        parameters["paymentSysID"] = if (entity.paymentSystem!= null) entity.paymentSystem!!.id.toString() else null
        parameters["paymentSysCode"] = entity.paymentSystem?.code
        parameters["dateFrom"] = if (entity.dateFrom != null) entity.dateFrom.toString() else null
        parameters["dateTo"] = if (entity.dateTo != null) entity.dateTo.toString() else null
        parameters["amountFrom"] = if (entity.amountFrom != null) entity.amountFrom.toString() else null
        parameters["amountTo"] = if (entity.amountTo != null) entity.amountTo.toString() else null
        parameters["disputeStatus"] = if (entity.disputeStatus != null) entity.disputeStatus else null
        return parameters
    }
}