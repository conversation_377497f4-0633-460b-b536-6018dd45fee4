package com.progressoft.psdms.application.conditions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_ClaimantBankCaseManagement
import com.progressoft.dms.entities.DMS_ClaimantNCBCaseManagement
import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.jfw.workflow.WfCondition
import com.progressoft.jfw.workflow.WfContext
import org.springframework.stereotype.Component

@Component
class RepresentDisputeCondition : WfCondition<DMS_BaseCaseManagement>() {

    companion object {
        private const val MAX_REPRESENT_COUNT = "MAX_REPRESENT_COUNT"
        private const val CLAIMANT_BANK = "CLAIMANTBANK"
    }
    override fun passesCondition(context: WfContext<DMS_BaseCaseManagement>): Boolean {
        val disputeRepresentCount = getDisputeRepresentCount(context)
        val maxRepresentCount = getMaxRepresentCount()
        if (disputeRepresentCount!! >= maxRepresentCount)
            return false
        return true
    }

    private fun getDisputeRepresentCount(context: WfContext<DMS_BaseCaseManagement>): Long? {
        val source = context.getArgument("type", String::class.java)

        if (source.equals(CLAIMANT_BANK)) {
            val rejectedDispute = context.entity as DMS_ClaimantBankCaseManagement
            return rejectedDispute.disputeCase?.claimantBankRepresentCount
        }
        val rejectedDispute = context.entity as DMS_ClaimantNCBCaseManagement
        return rejectedDispute.disputeCase?.claimantNCBRepresentCount
    }

    private fun getMaxRepresentCount(): Long = itemDao.getItem(
        DMS_SystemConfiguration::class.java,
        "configKey",
        MAX_REPRESENT_COUNT
    )?.configValue.toString().toLong()
}