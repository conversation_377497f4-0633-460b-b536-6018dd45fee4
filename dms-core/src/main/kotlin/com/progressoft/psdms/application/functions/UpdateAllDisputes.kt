package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.utils.Utils.Companion.getArgument
import com.progressoft.psdms.application.utils.Utils.Companion.updateDisputeCaseAndCreateCorrespondence
import com.progressoft.psdms.application.utils.Constants.Companion.STEP_NAME
import org.springframework.stereotype.Component
import java.util.*

@Component
class UpdateAllDisputes : WfFunction<DMS_BaseCaseManagement>() {

    companion object {
        private const val DISPUTE_NAMES = "disputeNames"
    }

    override fun execute(context: WfContext<DMS_BaseCaseManagement>) {
        val entity = context.entity
        val actionName = getArgument(context, STEP_NAME)
        val updatedDisputeCase = updateDisputeCaseAndCreateCorrespondence(context = context, itemDao = itemDao)
        itemDao.merge(updatedDisputeCase)
        itemDao.flush()
        entity.note = ""
        itemDao.merge(entity)
        val disputes = getArgument(context, DISPUTE_NAMES).split(",")
        disputes.forEach {
            val disputeCase = entity.disputeCase
            updateDisputes(disputeCase, it, actionName)
        }
    }

    private fun updateDisputes(
        disputeCase: DMS_DisputeCase?,
        disputeName: String,
        actionName: String
    ) {
        if (disputeCase?.javaClass?.simpleName == disputeName)
            jfw.executeAction(disputeCase, actionName)
        else if (disputeCase?.claimantBankCaseManagement?.javaClass?.simpleName == disputeName)
            updateDispute(disputeCase.claimantBankCaseManagement, actionName)
        else if (disputeCase?.defendantBankCaseManagement?.javaClass?.simpleName == disputeName)
            updateDispute(disputeCase.defendantBankCaseManagement, actionName)
        else if (disputeCase?.claimantNCBCaseManagement?.javaClass?.simpleName == disputeName)
            updateDispute(disputeCase.claimantNCBCaseManagement, actionName)
        else if (disputeCase?.defendantNCBCaseManagement?.javaClass?.simpleName == disputeName)
            updateDispute(disputeCase.defendantNCBCaseManagement, actionName)
        else if (disputeCase?.operatorCaseManagement?.javaClass?.simpleName == disputeName)
            updateDispute(disputeCase.operatorCaseManagement, actionName)
    }

    private fun <T : DMS_BaseCaseManagement> updateDispute(
        dispute: T?,
        actionName: String
    ) {
        if (Objects.nonNull(dispute))
            jfw.executeAction(dispute, actionName)
    }

}
