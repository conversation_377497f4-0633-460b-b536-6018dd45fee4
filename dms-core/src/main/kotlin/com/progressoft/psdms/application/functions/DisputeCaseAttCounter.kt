package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DisputeAtt
import com.progressoft.jfw.Query
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import org.springframework.stereotype.Component


@Component
class DisputeCaseAttCounter : WfFunction<DMS_BaseCaseManagement>() {

    override fun execute(context: WfContext<DMS_BaseCaseManagement>) {
        val entity = context.entity.disputeCase
        entity?.numberOfAtt = Query(itemDao)
            .equals("recordId", entity?.id.toString())
            .count(DMS_DisputeAtt::class.java)
        itemDao.merge(entity)
    }
}