package com.progressoft.psdms.application.validations

import com.progressoft.dms.entities.DMS_DisputeDetailsReport
import com.progressoft.jfw.AppValidationException.raiseIf
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfValidator
import org.springframework.stereotype.Component

@Component
class ValidateDisputeDetailsReportDates : WfValidator<DMS_DisputeDetailsReport>() {
    override fun validate(ctx: WfContext<DMS_DisputeDetailsReport>) {
        val entity = ctx.entity
        if (entity.dateTo != null && entity.dateFrom != null) {
            raiseIf(entity.dateTo!!.before(entity.dateFrom),
                jfw.getMessage("date.from.after.date.to.validation")
            )
        }
    }
}