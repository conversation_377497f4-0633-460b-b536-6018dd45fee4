package com.progressoft.psdms.application.validations

import com.progressoft.dms.entities.DMS_DisputeDetailsReport
import com.progressoft.jfw.AppValidationException.raiseIf
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfValidator
import org.springframework.stereotype.Component

@Component
class ValidateDisputeDetailsReportAmounts : WfValidator<DMS_DisputeDetailsReport>() {
    override fun validate(ctx: WfContext<DMS_DisputeDetailsReport>) {
        val entity = ctx.entity
        if (entity.amountTo != null && entity.amountFrom != null) {
            raiseIf(entity.amountFrom!! > entity.amountTo,
                jfw.getMessage("amount.range.validation.msg")
            )
        }
    }
}