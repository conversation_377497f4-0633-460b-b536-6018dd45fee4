package com.progressoft.psdms.application.utils

import com.progressoft.psdms.application.utils.Constants.Companion.ACH

class PaymentSystemResolver {
    companion object {
        fun resolvePaymentSystem(paymentSystemCode: String) =
            when (paymentSystemCode) {
                ACH -> "Ach"
                else -> paymentSystemCode
            }

        fun resolvePaymentTenant(paymentSystemCode: String) =
            when (paymentSystemCode) {
                ACH -> "ATS"
                else -> paymentSystemCode
            }
    }
}