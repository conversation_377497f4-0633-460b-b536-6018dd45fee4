package com.progressoft.psdms.application.conditions

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_ClaimantBankCaseManagement
import com.progressoft.dms.entities.DMS_ClaimantNCBCaseManagement
import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.jfw.workflow.WfCondition
import com.progressoft.jfw.workflow.WfContext
import org.springframework.stereotype.Component


@Component
class ArbitrateDisputeCondition : WfCondition<DMS_BaseCaseManagement>()  {

    companion object {
        private const val MIN_COUNT = "MIN_REJECTION_COUNT_FOR_ARBITRATION"
        private const val CLAIMANT_BANK = "CLAIMANTBANK"
    }
    override fun passesCondition(context: WfContext<DMS_BaseCaseManagement>): Boolean {
        val disputeRejectionCount = getDisputeRejectionCount(context)
        val minRejectionCount = getMinRejectionCount()
        if (disputeRejectionCount!! < minRejectionCount)
            return false
        return true
    }


    private fun getDisputeRejectionCount(context: WfContext<DMS_BaseCaseManagement>): Long? {
        val actionSource = context.getArgument("type", String::class.java)

        if (actionSource.equals(CLAIMANT_BANK)) {
            val rejectedDispute = context.entity as DMS_ClaimantBankCaseManagement
            return rejectedDispute.disputeCase?.defendantBankRejectionCount
        }
        val rejectedDispute = context.entity as DMS_ClaimantNCBCaseManagement
        return rejectedDispute.disputeCase?.defendantNCBRejectionCount
    }

    private fun getMinRejectionCount(): Long = itemDao.getItem(
        DMS_SystemConfiguration::class.java,
        "configKey",
        MIN_COUNT
    )?.configValue.toString().toLong()
}