package com.progressoft.psdms.application.services

import com.progressoft.communication.Envelope
import com.progressoft.communication.messaging.XmlEnvelopeSerializer
import com.progressoft.dms.entities.DMS_Endpoint
import com.progressoft.dms.entities.DMS_FeeMessage
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.psdms.application.dtos.FeeDto
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentSystem
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentTenant
import com.progressoft.psdms.application.utils.Utils.Companion.generateNewId
import com.progressoft.psdms.email.ActionEmailSenderService
import org.apache.camel.CamelContext
import org.apache.commons.lang.StringUtils.EMPTY
import org.slf4j.LoggerFactory.getLogger
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.time.LocalDateTime.now

@Component
class FeeNotificationMessageSender @Autowired constructor(
    private val itemDao: ItemDao,
    private val camelContext: CamelContext,
    private val notifSender: ActionEmailSenderService
) {

    fun send(fee: FeeDto) {
        camelContext.createProducerTemplate()
            .sendBody(
                getPaymentSystemUri(fee.dispute.paymentSystem!!.code),
                getEnvelopedMessage(buildAndSerializeMessageBody(fee), fee.dispute.paymentSystem!!.code)?.value()
            )
        itemDao.persist(getFeeMessageEntity(fee))
        notifSender.sendFeeEmail(fee)
    }

    private fun getPaymentSystemUri(paymentSystemCode: String) =
        itemDao.getItem(DMS_Endpoint::class.java, SYSTEM, paymentSystemCode).targetUri

    private fun buildAndSerializeMessageBody(fee: FeeDto) = StringBuilder().apply {
        appendLine(CSV_HEADER)
        appendLine("${fee.amount},${fee.type},${fee.participantCode}")
        LOG.info("Send Fee Message : \n${toString()}")
    }.toString()

    private fun getEnvelopedMessage(payload: String, paymentSystemCode: String) =
        XmlEnvelopeSerializer().serialize(getEnvelope(payload, paymentSystemCode))

    private fun getEnvelope(payload: String, paymentSystemCode: String) = Envelope.newBuilder()
        .setId(generateNewId())
        .setDate(now())
        .setType(MESSAGE_TYPE)
        .setFormat(MESSAGE_TYPE)
        .setSourceSystem(DMS)
        .setSourceTenant(DMS)
        .setDestinationSystem(resolvePaymentSystem(paymentSystemCode))
        .setDestinationTenant(resolvePaymentTenant(paymentSystemCode))
        .setSignature(EMPTY)
        .setContent(payload)
        .build()

    private fun getFeeMessageEntity(fee: FeeDto) = DMS_FeeMessage().apply {
        amount = fee.amount
        type = fee.type
        participantCode = fee.participantCode
        disputeCase = fee.dispute
    }

    companion object {
        private val LOG = getLogger(FeeNotificationMessageSender::class.java)
        private const val SYSTEM = "system"
        private const val CSV_HEADER = "Amount,Type,Participant Code"
        private const val MESSAGE_TYPE = "DMSFee"
    }
}