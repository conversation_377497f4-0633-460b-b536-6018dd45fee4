package com.progressoft.psdms.application

import com.progressoft.jfw.AppTransaction
import com.progressoft.jfw.AppTransaction.COMMIT_ACTION
import com.progressoft.jfw.AppUnhandledException
import com.progressoft.jfw.model.dao.item.ItemDao
import org.hibernate.Session
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.PlatformTransactionManager
import java.sql.CallableStatement
import java.sql.Connection
import java.sql.SQLException
import java.sql.Types

@Component
class IdGenerator @Autowired constructor(
    private val transactionManager: PlatformTransactionManager,
    private val itemDao: ItemDao
) {

    fun newDbId(): String {
        try {
            AppTransaction(transactionManager, COMMIT_ACTION).use { tx ->
                prepareCall("{call gen_dispute_case_ref(?)}").use { statement ->
                    statement.registerOutParameter(1, Types.BIGINT)
                    statement.executeUpdate()
                    return statement.getLong(1).toString()
                }
            }
        } catch (e: Exception) {
            throw AppUnhandledException(e)
        }
    }

    @Throws(SQLException::class)
    protected fun prepareCall(sql: String?): CallableStatement {
        val session = itemDao.entityManager.unwrap(Session::class.java)
        var callableStatement: CallableStatement? = null

        session.doWork { connection: Connection ->
            callableStatement = connection.prepareCall(sql)
        }

        return callableStatement ?: throw SQLException("Failed to create CallableStatement")
    }
}