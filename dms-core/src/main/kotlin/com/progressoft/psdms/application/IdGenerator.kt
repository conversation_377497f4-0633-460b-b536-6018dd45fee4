package com.progressoft.psdms.application

import com.progressoft.jfw.AppTransaction
import com.progressoft.jfw.AppTransaction.COMMIT_ACTION
import com.progressoft.jfw.AppUnhandledException
import com.progressoft.jfw.model.dao.item.ItemDao
import jakarta.persistence.ParameterMode
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.PlatformTransactionManager

@Component
class IdGenerator @Autowired constructor(
    private val transactionManager: PlatformTransactionManager,
    private val itemDao: ItemDao
) {

    fun newDbId(): String {
        try {
            AppTransaction(transactionManager, COMMIT_ACTION).use {
                val em = itemDao.entityManager
                val sp = em.createStoredProcedureQuery("gen_dispute_case_ref")
                
                // Register the OUT parameter
                sp.registerStoredProcedureParameter(1, java.lang.Long::class.java, ParameterMode.OUT)
                
                // Execute the stored procedure
                sp.execute()
                
                // Get the result
                val result = sp.getOutputParameterValue(1) as Long
                return result.toString()
            }
        } catch (e: Exception) {
            throw AppUnhandledException(e)
        }
    }
}