package com.progressoft.psdms.application.functions

import com.google.gson.GsonBuilder
import com.progressoft.communication.Envelope
import com.progressoft.communication.messaging.XmlEnvelopeSerializer
import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Endpoint
import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.dtos.ApprovedDisputeMessage
import com.progressoft.psdms.application.dtos.FeeDto
import com.progressoft.psdms.application.services.FeeNotificationMessageSender
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_OF_A_DISPUTE_FEE
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import com.progressoft.psdms.application.utils.Constants.Companion.NATIONAL
import com.progressoft.psdms.application.utils.Constants.Companion.URGENT_DISPUTE_FEE
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentSystem
import com.progressoft.psdms.application.utils.PaymentSystemResolver.Companion.resolvePaymentTenant
import com.progressoft.psdms.application.utils.Utils.Companion.generateNewId
import org.apache.camel.CamelContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.LocalDateTime.now
import java.util.Objects.requireNonNull

@Component
class ApprovedDisputeNotificationMessage @Autowired constructor(
    private val feeNotifySender: FeeNotificationMessageSender
) : WfFunction<JFWEntity>() {

    override fun execute(context: WfContext<JFWEntity>) {
        val camelContext = jfw.getBean("defaultCamelContext", CamelContext::class.java)

        val disputeCase = getDisputeCase(context)
        camelContext.createProducerTemplate()
            .sendBody(
                getPaymentSystemUri(disputeCase),
                getEnvelopedMessage(
                    buildAndSerializeMessageBody(
                        disputeCase?.transactionReference,
                        disputeCase?.disputedAmount,
                        disputeCase?.caseReferenceNumber
                    ), disputeCase?.paymentSystem?.code!!
                )?.value()
            )
        if (!isNationalOperationMode()) {
            if (isDisputeUrgent(disputeCase))
                sendFee(disputeCase, URGENT_DISPUTE_FEE)
            if (disputeCase.disputeOverDispute)
                sendFee(disputeCase, DISPUTE_OF_A_DISPUTE_FEE)
        }
    }

    private fun getDisputeCase(context: WfContext<JFWEntity>) =
        if (context.entity is DMS_DisputeCase) context.entity as DMS_DisputeCase else (context.entity as DMS_BaseCaseManagement).disputeCase


    private fun getPaymentSystemUri(entity: DMS_DisputeCase?) = itemDao
        .getItem(DMS_Endpoint::class.java, "system", entity?.paymentSystem?.code)?.targetUri

    private fun buildAndSerializeMessageBody(transactionReference: String?, disputedAmount: BigDecimal?,disputeCaseReference: String?): String {
        val gson = GsonBuilder().serializeNulls().setPrettyPrinting().create()
        val message = ApprovedDisputeMessage(
            transactionReference,
            disputedAmount,
            disputeCaseReference
        )
        return gson.toJson(message)
    }

    private fun getEnvelopedMessage(payload: String, paymentSystem: String) =
        XmlEnvelopeSerializer().serialize(getEnvelope(payload, paymentSystem))

    private fun getEnvelope(payload: String, paymentSystem: String) = Envelope.newBuilder()
        .setId(generateNewId())
        .setDate(now())
        .setType("DMSApproved")
        .setFormat("DMSApproved")
        .setSourceSystem(DMS)
        .setSourceTenant(DMS)
        .setDestinationSystem(resolvePaymentSystem(paymentSystem))
        .setDestinationTenant(resolvePaymentTenant(paymentSystem))
        .setSignature("no signature")
        .setContent(payload)
        .build()

    private fun isDisputeUrgent(disputeCase: DMS_DisputeCase) = disputeCase.urgency == "Urgent"

    private fun sendFee(disputeCase: DMS_DisputeCase, feeType: String) {
        val amount = Query(itemDao)
            .equals("configKey", feeType)
            .first(DMS_SystemConfiguration::class.java)
            .configValue
        feeNotifySender.send(FeeDto(BigDecimal(amount), feeType, disputeCase.claimantBank!!.code, disputeCase))
    }

    private fun isNationalOperationMode() =
        requireNonNull(
            itemDao.getItem(DMS_SystemConfiguration::class.java, "configKey", "Operation Mode").configValue
        ) == NATIONAL

}
