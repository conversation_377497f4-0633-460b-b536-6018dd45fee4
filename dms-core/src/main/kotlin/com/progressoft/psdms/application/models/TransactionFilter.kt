package com.progressoft.psdms.application.models

import java.math.BigDecimal

data class TransactionFilter (
    var fromDate: String = "",
    var toDate: String = "",
    var transactionReference: String = "",
    var transactionCurrency: String = "",
    var fromAmount: BigDecimal? = null,
    var toAmount: BigDecimal? = null,
    var senderParticipant: String = "",
    var receiverParticipant: String = "",
    var claimantBank: String = "",
    var defendantBank: String = "",
    var endToEndId: String = ""
)