package com.progressoft.psdms.application.reports.generator

import com.progressoft.dms.entities.DMS_DisputeSummaryReport
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate

@Component
class GenerateDisputesSummaryReport(
    @Value("\${reporting.base.url}") reportingBaseUrl: String?, restTemplate: RestTemplate
) :
    AbstractReportGenerator<DMS_DisputeSummaryReport?>(restTemplate, reportingBaseUrl!!) {
    override fun getReportType(entity: DMS_DisputeSummaryReport?): String? {
       return entity?.type
    }

    override val reportName: String
        get() = "disputes_summary_report"

    override val errorMsgLabel: String
        get() = "dispute.summary.report.failed.generation"

    override fun getRequestContent(entity: DMS_DisputeSummaryReport?): MutableMap<String, String?> {
        val userCountry = getUserCountry()
        val parameters: MutableMap<String, String?> = HashMap()
        parameters["reportID"] = entity!!.id.toString()
        parameters["nature"] = entity.nature
        parameters["countryID"] = userCountry?.id?.toString()
        parameters["countryCode"] = userCountry?.code
        parameters["reasonID"] = if (entity.reason!= null) entity.reason!!.id.toString() else null
        parameters["reasonCode"] = entity.reason?.name
        parameters["paymentSysID"] = if (entity.paymentSystem!= null) entity.paymentSystem!!.id.toString() else null
        parameters["paymentSysCode"] = entity.paymentSystem?.code
        parameters["dateFrom"] = entity.dateFrom?.toString()
        parameters["dateTo"] = entity.dateTo?.toString()
        return parameters
    }
}