package com.progressoft.psdms.application.processor

import com.progressoft.dms.entities.*
import com.progressoft.jfw.AppProcessor
import com.progressoft.jfw.Query
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import com.progressoft.psdms.application.utils.Constants.Companion.SLA_Arbitrate
import com.progressoft.psdms.email.ArbitrationEmailSenderService
import org.apache.camel.Exchange
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component


@Component
open class DisputeArbitrationProcessor @Autowired constructor(
    private val arbitrationNotifierSender: ArbitrationEmailSenderService
) : AppProcessor() {

    @Value("\${calculate.the.number.of.days.script}")
    val script:String = ""

    override fun doProcess(exchange: Exchange?) {
        jfw.enableServiceUser(DMS).use {
            LOG.info("Start DisputeArbitrationProcessor ...")
            val slaConfigurationsForDefendantNCBParty = Query(itemDao).equals("slaConfigurationParty.code", "DefendantNCB").items(DMS_SLAConfiguration::class.java)
            slaConfigurationsForDefendantNCBParty.forEach {
                disputeArbitrationProcessor(it, DMS_DefendantNCBCaseManagement::class.java, "New Dispute" )

            }
            val slaConfigurationsForClaimantNCBParty = Query(itemDao).equals("slaConfigurationParty.code", "ClaimantNCB").items(DMS_SLAConfiguration::class.java)
            slaConfigurationsForClaimantNCBParty.forEach {
                disputeArbitrationProcessor(it, DMS_ClaimantNCBCaseManagement::class.java, "Arbitrated Dispute")
            }

        }
    }
    private fun disputeArbitrationProcessor(
        slaConfiguration: DMS_SLAConfiguration,
        clazz: Class<out DMS_BaseCaseManagement>?,
        description: String,

    ) {
        val caseManagements = Query(itemDao)
            .equals("statusId.description", description)
            .where("$script >= " + slaConfiguration.maxDays)
            .equals("disputeCase.urgency", slaConfiguration.urgency)
            .items(clazz)
        caseManagements.forEach {
            jfw.executeAction(it,SLA_Arbitrate )
            arbitrationNotifierSender.sendEmail(getRecipients(it.disputeCase),it.disputeCase!!)
        }
    }

    private fun getRecipients(
        disputeCase: DMS_DisputeCase?
    ): List<String?> {
        return listOf(
            disputeCase?.claimantBank?.email,
            disputeCase?.defendantBank?.email,
            disputeCase?.claimantBank?.nationalCentralBank?.email,
            disputeCase?.defendantBank?.nationalCentralBank?.email
        )
    }
}