package com.progressoft.psdms.application.providers

import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class JfwUsersProvider(@Autowired private val itemDao: ItemDao) : JupiterValueProvider<User, Long> {

    override fun allValues(context: ValueProviderContext<User>?): MutableMap<Long, String> {
        return itemDao.getItems(User::class.java).stream().collect(Collectors.toMap(User::getId, User::getDisplayName))
    }
}