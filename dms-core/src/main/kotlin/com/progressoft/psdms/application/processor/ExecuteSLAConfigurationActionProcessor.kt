package com.progressoft.psdms.application.processor

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_SLAConfiguration
import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.dms.entities.DMS_SystemConfiguration.Companion.CONFIG_KEY
import com.progressoft.jfw.AppProcessor
import com.progressoft.jfw.Query
import com.progressoft.psdms.application.dtos.FeeDto
import com.progressoft.psdms.application.services.FeeNotificationMessageSender
import com.progressoft.psdms.application.utils.Constants.Companion.DMS
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR
import com.progressoft.psdms.application.utils.Constants.Companion.OPERATOR_EMAIL
import com.progressoft.psdms.application.utils.Constants.Companion.SLA_EXCEEDED
import com.progressoft.psdms.application.utils.Constants.Companion.SLA_EXCEEDED_PENALTY
import com.progressoft.psdms.application.utils.Constants.Companion.slaConfigurationRegionalPartyMap
import com.progressoft.psdms.application.utils.Utils.Companion.getCurrentParticipantCode
import com.progressoft.psdms.email.ActionEmailSenderService
import org.apache.camel.Exchange
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.math.BigDecimal


@Component
open class ExecuteSLAConfigurationActionProcessor @Autowired constructor(
    private val feeMessageSender: FeeNotificationMessageSender,
    private val actionNotifSender: ActionEmailSenderService
) : AppProcessor() {

    @Value("\${calculate.the.number.of.days.script}")
    val script:String = ""

    override fun doProcess(exchange: Exchange?) {
        jfw.enableServiceUser(DMS).use {
            LOG.info("Start ExecuteSLAConfigurationActionProcessor ...")
            val slaConfigurations = itemDao.getItems(DMS_SLAConfiguration::class.java)
            slaConfigurations.forEach {
                executeSLAConfigurationAction(it, slaConfigurationRegionalPartyMap[it.slaConfigurationParty?.code])
            }
        }
    }

    private fun executeSLAConfigurationAction(
        slaConfiguration: DMS_SLAConfiguration,
        clazz: Class<out DMS_BaseCaseManagement>?
    ) {
        val stageList = slaConfiguration.stage?.split(",")
        val caseManagements = Query(itemDao)
            .equals("disputeCase.paymentSystem", slaConfiguration.paymentSystem)
            .where("$script >= " + slaConfiguration.maxDays)
            .equals("disputeCase.urgency", slaConfiguration.urgency)
            .items(clazz)
            .filter { stageList?.contains(it.statusId.description) == true }
        caseManagements.forEach {
            val name = slaConfiguration.slaConfigAutomaticAction?.name
            jfw.executeAction(it, name)
            val partyCode = slaConfiguration.slaConfigurationParty!!.code!!
            if (partyCode != OPERATOR)
                feeMessageSender.send(
                    getFeeDto(
                        getCurrentParticipantCode(partyCode, it.disputeCase!!),
                        it.disputeCase!!
                    )
                )
            if (name.equals("Accept", true))
                notifyParties(it.disputeCase, partyCode)
        }
    }

    private fun notifyParties(disputeCase: DMS_DisputeCase?, partyCode: String) {
        actionNotifSender.sendEmail(getRecipients(partyCode, disputeCase), disputeCase!!)
    }

    private fun getRecipients(
        partyCode: String,
        disputeCase: DMS_DisputeCase?
    ): List<String?> {
        if (listOf("Assigner Bank", "Assignee Bank").contains(partyCode)) {
            return listOf(disputeCase?.claimantBank?.email, disputeCase?.defendantBank?.email)
        }
        if (listOf("Claimant NCB", "Defendant NCB").contains(partyCode)) {
            return listOf(
                disputeCase?.claimantBank?.email,
                disputeCase?.defendantBank?.email,
                disputeCase?.claimantBank?.nationalCentralBank?.email,
                disputeCase?.defendantBank?.nationalCentralBank?.email,
            )
        }
        return listOf(
            disputeCase?.claimantBank?.email,
            disputeCase?.defendantBank?.email,
            disputeCase?.claimantBank?.nationalCentralBank?.email,
            disputeCase?.defendantBank?.nationalCentralBank?.email,
            getConfigProperty(OPERATOR_EMAIL),
        )
    }

    private fun getFeeDto(participantCode: String, dispute: DMS_DisputeCase) =
        FeeDto(BigDecimal.valueOf(getConfigProperty(SLA_EXCEEDED_PENALTY).toLong()), SLA_EXCEEDED, participantCode, dispute)

    private fun getConfigProperty(configKey: String) = Query(itemDao)
        .equals(CONFIG_KEY, configKey)
        .first(DMS_SystemConfiguration::class.java)
        .configValue!!

}