package com.progressoft.psdms.application.validations

import com.progressoft.dms.entities.DMS_DisputeSummaryReport
import com.progressoft.jfw.AppValidationException.raiseIf
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfValidator
import org.springframework.stereotype.Component

@Component
class ValidateDisputeSummaryReportDates : WfValidator<DMS_DisputeSummaryReport>() {
    override fun validate(ctx: WfContext<DMS_DisputeSummaryReport>) {
        val entity = ctx.entity
        if (entity.dateTo != null && entity.dateFrom != null) {
            raiseIf(entity.dateTo!!.before(entity.dateFrom),
                jfw.getMessage("date.from.after.date.to.validation")
            )
        }
    }
}