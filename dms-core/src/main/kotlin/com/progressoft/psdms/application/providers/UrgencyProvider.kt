package com.progressoft.psdms.application.providers

import com.progressoft.jupiter.search.SearchProvider
import com.progressoft.jupiter.search.SearchProvider.Pair
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import com.progressoft.psdms.application.enums.Urgency
import org.springframework.stereotype.Component

@Component
class UrgencyProvider : SearchProvider, JupiterValueProvider<String, String> {

    override fun allValues(ctx: ValueProviderContext<String>?): MutableMap<String, String> = enumValues<Urgency>()
        .associateBy({ it.description }, { it.description }).toMutableMap()

    override fun allValues(): List<Pair> = enumValues<Urgency>().map { Pair(it.description, it.description) }

}

