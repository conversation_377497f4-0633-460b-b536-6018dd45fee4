package com.progressoft.psdms.application.functions

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.email.ActionEmailSenderService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component


@Component
class ActionNotification @Autowired constructor(private val actionNotifSender: ActionEmailSenderService): WfFunction<DMS_DisputeCase>()  {

    private val ACTION="Action"
    override fun execute(context: WfContext<DMS_DisputeCase>) {
        val entity = context.entity
        val action = context.getArgument(ACTION) as String
        actionNotifSender.sendEmail(listOf(entity.defendantBank?.email, entity.claimantBank?.email),entity, action)
    }
}