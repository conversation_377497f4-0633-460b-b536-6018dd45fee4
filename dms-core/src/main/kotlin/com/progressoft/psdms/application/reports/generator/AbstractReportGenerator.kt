package com.progressoft.psdms.application.reports.generator

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.progressoft.dms.entities.DMS_NationalCentralBank
import com.progressoft.dms.entities.DMS_UserReport
import com.progressoft.jfw.AppValidationException
import com.progressoft.jfw.Query
import com.progressoft.jfw.labels.LocaleHolder
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import com.progressoft.psdms.application.enums.ReportTypes.XLSX
import com.progressoft.psdms.application.reports.ReportingRequest
import com.progressoft.psdms.application.reports.ReportingResponse
import org.springframework.http.*
import org.springframework.web.client.RestTemplate
import java.io.IOException
import java.util.*

abstract class AbstractReportGenerator<T : DMS_UserReport?>(
    private val restTemplate: RestTemplate,
    private val reportingBaseUrl: String
) :
    WfFunction<T>() {
    private val objectMapper: ObjectMapper

    init {
        val mapper = ObjectMapper()
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL)
        this.objectMapper = mapper
    }

    override fun execute(wfContext: WfContext<T>) {
        val entity = wfContext.entity
        try {
            val httpEntity = HttpEntity(
                objectMapper.writeValueAsString(getContent(entity)),
                httpHeaders
            )
            val exchange = callReportingService(httpEntity, entity)
            if (exchange.statusCode.is2xxSuccessful) {
                val reportingResponse: ReportingResponse =
                    objectMapper.readValue(exchange.body, ReportingResponse::class.java)
                entity?.attachmentUuid = reportingResponse.uuid.toString()
                entity?.reportType = getReportType(entity)
            }
        } catch (ex: Exception) {
            throw AppValidationException(ex, jfw.getMessage(errorMsgLabel))
        }
    }

    private fun callReportingService(httpEntity: HttpEntity<String>, entity: T): ResponseEntity<String> {
        val reportType = getReportType(entity)
        val format = if (reportType == XLSX.description) "excel_xlsx" else reportType
        val selectedLang = LocaleHolder.currentLocale().language
        return restTemplate.exchange(
            "$reportingBaseUrl/reports/execution/async?reportName=$reportName"+"_"+"$selectedLang&format=$format",
            HttpMethod.POST,
            httpEntity,
            String::class.java
        )
    }

    @Throws(IOException::class)
    private fun getContent(entity: T): ReportingRequest {
        val request = ReportingRequest()
        val parameters = getRequestContent(entity)
        parameters["logo"] = logoBase64
        parameters["lang"] = LocaleHolder.currentLocale().language
        request.parameters = parameters
        return request
    }

    private val httpHeaders: HttpHeaders
        get() {
            val httpHeaders = HttpHeaders()
            httpHeaders.contentType = MediaType.APPLICATION_JSON
            return httpHeaders
        }

    @get:Throws(IOException::class)
    private val logoBase64: String
        get() {
            val logoStream = Objects.requireNonNull(javaClass.getResourceAsStream("/reports/kuwait_logo.jpg"))
            return Base64.getEncoder().encodeToString(logoStream.readAllBytes())
        }

    @Throws(IOException::class)
    abstract fun getRequestContent(entity: T): MutableMap<String, String?>
    abstract fun getReportType(entity: T): String?

    fun getUserCountry()  = Query(itemDao)
            .nameEquals(jfw.currentPrefOrg.shortName)
            .first(DMS_NationalCentralBank::class.java)?.country

    abstract val reportName: String
    abstract val errorMsgLabel: String
}
