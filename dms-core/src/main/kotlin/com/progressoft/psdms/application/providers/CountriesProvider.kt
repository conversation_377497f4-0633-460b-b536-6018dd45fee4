package com.progressoft.psdms.application.providers

import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.core.IJFWEntity
import com.progressoft.jfw.model.bussinessobject.core.JfwCountry
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors.toMap

@Component
class CountriesProvider(@Autowired private val itemDao: ItemDao) : JupiterValueProvider<IJFWEntity, Long> {

    override fun allValues(valueProviderContext: ValueProviderContext<IJFWEntity>): MutableMap<Long, String> {
        return Query(itemDao)
            .isTrue("activeFlag")
            .items(JfwCountry::class.java)
            .stream()
            .collect(toMap(JfwCountry::getId, JfwCountry::getCountryName))
    }
}