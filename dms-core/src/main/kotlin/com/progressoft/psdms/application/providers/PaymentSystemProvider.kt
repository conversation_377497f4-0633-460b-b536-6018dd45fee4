package com.progressoft.psdms.application.providers

import com.progressoft.dms.entities.DMS_PaymentSystems
import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jupiter.search.SearchProvider
import com.progressoft.jupiter.search.SearchProvider.Pair
import com.progressoft.jupiter.valueprovider.JupiterValueProvider
import com.progressoft.jupiter.valueprovider.ValueProviderContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.stream.Collectors

@Component
class PaymentSystemProvider (@Autowired private val itemDao: ItemDao) : SearchProvider, JupiterValueProvider<JFWEntity, Long> {

    override fun allValues(context: ValueProviderContext<JFWEntity>?): MutableMap<Long, String> =
        itemDao.getItems(DMS_PaymentSystems::class.java).stream().collect(Collectors.toMap(DMS_PaymentSystems::id, DMS_PaymentSystems::getCodeNamePair))

    override fun allValues(): List<Pair> = itemDao.getItems(DMS_PaymentSystems::class.java).map {
            Pair(it.id.toString(), it.codeNamePair)
        }
}