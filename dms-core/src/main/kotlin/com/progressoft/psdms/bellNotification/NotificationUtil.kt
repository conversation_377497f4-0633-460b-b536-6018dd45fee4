package com.progressoft.psdms.bellNotification

import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jfw.model.messaging.channels.JFWMessageChannel
import com.progressoft.jfw.shared.push.message.JFWMessage
import com.progressoft.psdms.application.utils.Constants

object NotificationUtil {

    fun prepareNotificationPayload(jfwMessage: JFWMessage, userType: String, itemDao: ItemDao){
        val receiver = itemDao.getItem(User::class.java, Constants.DISPLAY_NAME, userType)
        sendMessage(receiver, jfwMessage)
    }

    fun sendMessage(user: User, jfwMessage: JFWMessage) {
        JFWMessageChannel.createInstance().appendTarget(user).send(jfwMessage)
    }

}