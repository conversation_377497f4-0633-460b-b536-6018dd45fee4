package com.progressoft.psdms.util

import com.progressoft.dms.entities.DMS_SystemConfiguration
import com.progressoft.dms.entities.DMS_SystemConfiguration.Companion.CONFIG_KEY
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.psdms.email.EmailCredentials
import com.progressoft.psdms.email.EmailProperties
import com.progressoft.psdms.email.EmailSenderImpl
import com.progressoft.psdms.exception.FailedToGetEntityException
import org.apache.commons.lang.BooleanUtils.toBooleanObject
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
open class EmailUtils @Autowired constructor(private var itemDao: ItemDao) {

    open fun getEmailSender(): EmailSenderImpl {
        val configs = getConfigs(
            "mail.smtp.enabled", "mail.smtp.from", "mail.smtp.user", "mail.smtp.password",
            "mail.smtp.auth", "mail.smtp.starttls.enable", "mail.smtp.host", "mail.smtp.port"
        )
        if (configs.isEmpty() || configs.size != 8)
            throw FailedToGetEntityException("Failed to get email configurations ...")
        return emailSenderImpl(configs)
    }

    fun getConfigValue(configKey:String) = getConfigs(configKey).first().configValue!!

    private fun getConfigs(vararg key: String) = Query(itemDao)
        .`in`(CONFIG_KEY, *key)
        .items(DMS_SystemConfiguration::class.java)

    private fun emailSenderImpl(configs: List<DMS_SystemConfiguration>) = EmailSenderImpl(
        toBooleanObject(findFromList(configs, "mail.smtp.enabled")),
        findFromList(configs, "mail.smtp.from"),
        emailCredentials(configs),
        emailProperties(configs)
    )

    private fun emailProperties(configs: List<DMS_SystemConfiguration>) = EmailProperties(
        toBooleanObject(findFromList(configs, "mail.smtp.auth")),
        toBooleanObject(findFromList(configs, "mail.smtp.starttls.enable")),
        findFromList(configs, "mail.smtp.host"),
        Integer.parseInt(findFromList(configs, "mail.smtp.port"))
    )

    private fun emailCredentials(configs: List<DMS_SystemConfiguration>) = EmailCredentials(
        findFromList(configs, "mail.smtp.user"),
        findFromList(configs, "mail.smtp.password")
    )

    private fun findFromList(configs: List<DMS_SystemConfiguration>, configKey: String) =
        configs.stream().filter { t -> t.configKey.equals(configKey) }.findFirst().get().configValue!!
}