package com.progressoft.psdms.email

import java.lang.String.valueOf
import java.util.*

class EmailProperties(
    private val isAuthenticated: Boolean,
    private val isTlsEnabled: Boolean,
    private val host: String,
    private val port: Int
) {
    fun properties() = Properties().apply {
        setProperty("mail.smtp.auth", valueOf(isAuthenticated))
        setProperty("mail.smtp.starttls.enable", valueOf(isTlsEnabled))
        setProperty("mail.smtp.host", host)
        setProperty("mail.smtp.port", valueOf(port))
    }
}