package com.progressoft.psdms.email

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.psdms.application.dtos.FeeDto
import com.progressoft.psdms.application.utils.Constants.Companion.AUTO_ACCEPTANCE_EMAIL_SUBJECT
import com.progressoft.psdms.application.utils.Constants.Companion.CREATION_EMAIL_SUBJECT
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_ACTION_EMAIL_BODY
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_CREATION_EMAIL_BODY
import com.progressoft.psdms.application.utils.Constants.Companion.DISPUTE_FEE_EMAIL_BODY
import com.progressoft.psdms.application.utils.Constants.Companion.FEE_EMAIL_SUBJECT
import com.progressoft.psdms.util.EmailUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.sql.Timestamp
import java.text.SimpleDateFormat
import java.util.Objects.isNull

@Component
open class ActionEmailSenderService @Autowired constructor(
    private var emailUtil: EmailUtils,
    private var itemDao: ItemDao
) {

    private var actionToSubject :MutableMap<String, String> = HashMap()
    private var actionToBody :MutableMap<String, String> = HashMap()

    init {
        actionToSubject["Created"] = CREATION_EMAIL_SUBJECT
        actionToSubject["fee"] = FEE_EMAIL_SUBJECT
        actionToSubject["Accept"] = AUTO_ACCEPTANCE_EMAIL_SUBJECT

        actionToBody["Created"] = DISPUTE_CREATION_EMAIL_BODY
        actionToBody["fee"] = DISPUTE_FEE_EMAIL_BODY
        actionToBody["Accept"] = DISPUTE_ACTION_EMAIL_BODY
    }

    @Async
    open fun sendEmail(recipients: List<String?>, entity: DMS_DisputeCase, action: String = "Accept") {
        if (actionToBody.containsKey(action) && actionToSubject.containsKey(action)){
            recipients.forEach {
                emailUtil.getEmailSender().send(getEmail(it, action, entity, null))
            }
        }
    }


    @Async
    open fun sendFeeEmail(fee: FeeDto) {
        val participant = Query(itemDao)
            .codeEquals(fee.participantCode)
            .first(DMS_Participant::class.java)
        if (!isNull(participant) && !isNull(participant.email)){
            emailUtil.getEmailSender().send(getEmail(participant.email, "fee", fee.dispute, fee.amount))
        }
    }

    private fun getEmail(recipient: String?, action: String, entity: DMS_DisputeCase, amount: BigDecimal?) = Email(
        EmailRecipients.to(recipient),
        getEmailSubject(entity.caseReferenceNumber!!, action),
        getEmailBody(entity, action, amount),
        null
    )

    private fun getEmailSubject(caseRef: String, action: String) = actionToSubject[action]!!
        .replace("{DISPUTE_CASE_ID}", caseRef)

    private fun getEmailBody(
        disputeCase: DMS_DisputeCase,
        action: String,
        amount: BigDecimal?
    ) = actionToBody[action]!!
        .replace("{DISPUTE_CASE_ID}", disputeCase.caseReferenceNumber!!)
        .replace("{CREATION_DATE}", getCreationDate(disputeCase.creationDateTime!!))
        .replace("{PAYMENT_SYSTEM}", disputeCase.paymentSystem?.codeNamePair!!)
        .replace("{CLAIMANT_BANK_NAME}", disputeCase.claimantBank?.codeNamePair!!)
        .replace("{DEFENDANT_BANK_NAME}", disputeCase.defendantBank?.codeNamePair!!)
        .replace(
            "{TRANSACTION_ID}",
            if (disputeCase.disputeOverDispute)
                itemDao.getItem(DMS_DisputeCase::class.java, "caseReferenceNumber",disputeCase.refDisputeCaseNumber).transactionReference!!
            else disputeCase.transactionReference!!
        )
        .replace("{DISPUTE_AMOUNT}", disputeCase.disputedAmount.toString())
        .replace("{FEE_AMOUNT}", amount.toString())
        .replace("{FEE_CURRENCY}", disputeCase.transactionCurrency?.stringISOCode!!)

    private fun getCreationDate(creationDateTime: Timestamp) =
        SimpleDateFormat(CREATION_DATE_FORMAT).format(creationDateTime)


    companion object {
        private const val CREATION_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"
    }
}