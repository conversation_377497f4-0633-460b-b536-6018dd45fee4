package com.progressoft.psdms.email

import org.apache.commons.lang.StringUtils
import org.apache.commons.mail.Email
import java.util.Objects.nonNull
import java.util.stream.Stream
import javax.mail.Message
import javax.mail.MessagingException
import javax.mail.internet.InternetAddress

data class EmailRecipients(private var to: String?, private var cc: String?, private var bcc: String?) {

    init {
        require(!Stream.of(to, cc, bcc).allMatch(StringUtils::isBlank)) { "All parameters are empty ..." }
    }

    @Throws(MessagingException::class)
    fun addTo(email: Email) {
        to?.split(",")?.forEach { address -> email.addTo(address.trim()) }
        cc?.split(",")?.forEach { address -> email.addCc(address.trim()) }
        bcc?.split(",")?.forEach { address -> email.addBcc(address.trim()) }
    }

    @Throws(MessagingException::class)
    fun addTo(message: Message) {
        addTo(message, Message.RecipientType.TO, this.to)
        addTo(message, Message.RecipientType.CC, this.cc)
        addTo(message, Message.RecipientType.BCC, this.bcc)
    }

    @Throws(MessagingException::class)
    private fun addTo(message: Message, type: Message.RecipientType, addresses: String?) {
        if (nonNull(addresses))
            message.setRecipients(type, InternetAddress.parse(addresses))
    }

    companion object {
        fun to(to: String?): EmailRecipients {
            return EmailRecipients(to, null, null)
        }

        fun cc(cc: String?): EmailRecipients {
            return EmailRecipients(null, cc, null)
        }

        fun toAndCc(to: String?, cc: String?): EmailRecipients {
            return EmailRecipients(to, cc, null)
        }
    }
}