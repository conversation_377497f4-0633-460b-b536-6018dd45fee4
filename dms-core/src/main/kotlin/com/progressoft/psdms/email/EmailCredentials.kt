package com.progressoft.psdms.email

import javax.mail.Authenticator
import javax.mail.PasswordAuthentication


class EmailCredentials(private val userName: String, private val password: String) : Authenticator() {

    fun authenticator(): Authenticator {
        return object : Authenticator() {
            override fun getPasswordAuthentication(): PasswordAuthentication {
                return PasswordAuthentication(userName, password)
            }
        }
    }
}