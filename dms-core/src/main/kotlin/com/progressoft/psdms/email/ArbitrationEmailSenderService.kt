package com.progressoft.psdms.email

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.psdms.application.utils.Constants.Companion.NCB_ARBITRATION_TO_OPERATOR_EMAIL_BODY
import com.progressoft.psdms.application.utils.Constants.Companion.NCB_ARBITRATION_TO_OPERATOR_EMAIL_SUBJECT
import com.progressoft.psdms.util.EmailUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.sql.Timestamp
import java.text.SimpleDateFormat


@Component
open class ArbitrationEmailSenderService @Autowired constructor(private var emailUtil: EmailUtils) {
    fun sendEmail(recipients: List<String?>,disputeCase: DMS_DisputeCase) {
        recipients.forEach {
            emailUtil.getEmailSender().send(getEmail(it, disputeCase))
        }
    }

    private fun getEmail(recipient: String?, disputeCase: DMS_DisputeCase) = Email(
            EmailRecipients.to(recipient),getEmailSubject(disputeCase), getEmail<PERSON><PERSON>(disputeCase), null)


    private fun getEmailSubject(disputeCase: DMS_DisputeCase) = NCB_ARBITRATION_TO_OPERATOR_EMAIL_SUBJECT
        .replace("{DISPUTE_CASE_ID}", disputeCase.caseReferenceNumber!!)

    private fun getEmailBody(disputeCase: DMS_DisputeCase) = NCB_ARBITRATION_TO_OPERATOR_EMAIL_BODY
        .replace("{DISPUTE_CASE_ID}", disputeCase.caseReferenceNumber!!)
        .replace("{CREATION_DATE}", getCreationDate(disputeCase.creationDateTime!!))
        .replace("{PAYMENT_SYSTEM}", disputeCase.paymentSystem!!.codeNamePair)
        .replace("{CLAIMANT_BANK_NAME}", disputeCase.claimantBank!!.codeNamePair)
        .replace("{DEFENDANT_BANK_NAME}", disputeCase.defendantBank!!.codeNamePair)
        .replace("{TRANSACTION_ID}", disputeCase.transactionReference!!)
        .replace("{DISPUTE_AMOUNT}", disputeCase.disputedAmount.toString())

    private fun getCreationDate(creationDateTime: Timestamp) =
        SimpleDateFormat(CREATION_DATE_FORMAT).format(creationDateTime)

    companion object {
        private const val CREATION_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"
    }
}