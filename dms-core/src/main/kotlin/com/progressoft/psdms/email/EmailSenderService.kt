package com.progressoft.psdms.email

import com.progressoft.dms.entities.DMS_BaseCaseManagement
import com.progressoft.psdms.application.utils.Constants.Companion.ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_ADDRESS
import com.progressoft.psdms.application.utils.Constants.Companion.ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_BODY
import com.progressoft.psdms.application.utils.Constants.Companion.ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_SUBJECT
import com.progressoft.psdms.util.EmailUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.sql.Timestamp
import java.text.SimpleDateFormat

@Component
class EmailSenderService @Autowired constructor(private var emailUtil: EmailUtils) {
    fun sendEmail(caseManagement: DMS_BaseCaseManagement) {
        emailUtil.getEmailSender().send(getEmail(caseManagement))
    }

    private fun getEmail(caseManagement: DMS_BaseCaseManagement) = Email(
        EmailRecipients.to(emailUtil.getConfigValue(ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_ADDRESS)),
        getEmailSubject(caseManagement),
        getEmailBody(caseManagement),
        null
    )

    private fun getEmailSubject(caseManagement: DMS_BaseCaseManagement) = ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_SUBJECT
        .replace("{DISPUTE_CASE_ID}", caseManagement.disputeCase!!.caseReferenceNumber!!)

    private fun getEmailBody(caseManagement: DMS_BaseCaseManagement) = ARBITRATION_THRESHOLD_EXCEEDING_EMAIL_BODY
        .replace("{DISPUTE_CASE_ID}", caseManagement.disputeCase!!.caseReferenceNumber!!)
        .replace("{CREATION_DATE}", getCreationDate(caseManagement.disputeCase!!.creationDateTime!!))
        .replace("{PAYMENT_SYSTEM}", caseManagement.disputeCase!!.paymentSystem!!.codeNamePair)
        .replace("{CLAIMANT_BANK_NAME}", caseManagement.disputeCase!!.claimantBank!!.codeNamePair)
        .replace("{DEFENDANT_BANK_NAME}", caseManagement.disputeCase!!.defendantBank!!.codeNamePair)
        .replace("{TRANSACTION_ID}", caseManagement.disputeCase!!.transactionReference!!)
        .replace("{DISPUTE_AMOUNT}", caseManagement.disputeCase!!.disputedAmount.toString())

    private fun getCreationDate(creationDateTime: Timestamp) =
        SimpleDateFormat(CREATION_DATE_FORMAT).format(creationDateTime)

    companion object {
        private const val CREATION_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss"
    }
}