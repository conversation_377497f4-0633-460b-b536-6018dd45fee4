package com.progressoft.psdms.email

import javax.mail.*
import javax.mail.internet.InternetAddress
import javax.mail.internet.MimeBodyPart
import javax.mail.internet.MimeMessage
import javax.mail.internet.MimeMultipart

data class Email(val recipients: EmailRecipients, val subject: String, val body: String, val attachment: EmailAttachment?) {

    fun message(from: String, session: Session): Message {
        val message: Message = MimeMessage(session)
        message.setFrom(InternetAddress(from))
        recipients.addTo(message)
        message.subject = subject
        message.setContent(content())
        return message
    }

    @Throws(MessagingException::class)
    private fun content() = MimeMultipart().apply {
        addBodyPart(MimeBodyPart().apply { setText(body) })
        if (attachment != null)
            addBodyPart(attachment.attachment())
    }
}