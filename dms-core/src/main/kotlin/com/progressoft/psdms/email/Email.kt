package com.progressoft.psdms.email

import org.apache.commons.mail.HtmlEmail
import javax.mail.*
import javax.mail.internet.MimeBodyPart
import javax.mail.internet.MimeMultipart

data class Email(val recipients: EmailRecipients, val subject: String, val body: String, val attachment: EmailAttachment?) {

    fun message(from: String, session: Session): Message {
        val email = HtmlEmail().apply {
            setMailSession(session)
            setFrom(from)
            subject = <EMAIL>()
            
            recipients.addTo(this)
            
            if (attachment != null) {
                val multipart = MimeMultipart("mixed").apply {
                    addBodyPart(MimeBodyPart().apply { 
                        setContent(body, "text/html")
                    })
                    addBodyPart(attachment.attachment())
                }
                setContent(multipart, "multipart/mixed")
            } else {
                setHtmlMsg(body)
            }
        }
        
        email.buildMimeMessage()
        return email.getMimeMessage()
    }
}