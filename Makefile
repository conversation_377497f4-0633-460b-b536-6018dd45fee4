#......... ENV variables for POSTGRES DB ...........
#export db_type:=POSTGRESSQL
#export db_dialect:=com.progressoft.jfw.JfwPostgreSQLDialect
#export quartz_dialect:=org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
#export db_driver:=org.postgresql.Driver
#export db_user:=postgres
#export db_password:=postgres
#export db_url:=*****************************************
#export db_check_query:=select count(*) from user_tables
#export db_schema:=public
#export db_validation_query:=SELECT 1
#.........End ENV variables for POSTGRES DB ...........
#......... ENV variables for ORACLE DB ...........
export db_type:=ORACLE10
export db_dialect:=org.hibernate.dialect.OracleDialect
export quartz_dialect:=org.quartz.impl.jdbcjobstore.oracle.OracleDelegate
export db_driver:=oracle.jdbc.OracleDriver
export db_user:=dms
export db_password:=12345
export db_url:=***********************************
export db_check_query:=select count(*) from user_tables
export db_schema:=dms
export db_validation_query:=SELECT 1 FROM DUAL
#.........End ENV variables for ORACLE DB ...........
buildRun:
	clear && mvn clean install -DskipTests && java -Dspring.profiles.active=normal --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar artifacts/dms-app-1.0-SNAPSHOT.jar
run:
	clear && java -Dspring.profiles.active=normal --add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.io=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 -jar artifacts/dms-app-1.0-SNAPSHOT.jar
jar:
	mvn clean install -DskipTests
jarTests:
	mvn clean install
buildRunSim:
	clear && cd transaction-simulator && mvn clean install && java -jar artifacts/transaction-simulator.jar
runSim:
	clear && cd transaction-simulator && java -jar artifacts/transaction-simulator.jar