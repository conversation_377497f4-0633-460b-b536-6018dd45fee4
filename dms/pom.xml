<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>dms-kuwait</artifactId>
        <groupId>com.progressoft.acacia.kuwait.dms</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <version>1.0-SNAPSHOT</version>
    <modules>
        <module>dms-app</module>
        <module>dms-dsl</module>
        <module>dms-controllers</module>
    </modules>

    <artifactId>dms</artifactId>
    <packaging>pom</packaging>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.progressoft.jupiter</groupId>
            <artifactId>spring-boot</artifactId>
            <version>${jfw.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.progressoft.jfw.web.filter</groupId>
                    <artifactId>webfilter-csp</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>lombok</artifactId>
                    <groupId>org.projectlombok</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-core</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>relational-database-change-history</artifactId>
                    <groupId>com.progressoft.jupiter</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.progressoft.jupiter</groupId>
            <artifactId>jfw-utils</artifactId>
            <version>${jfw.version}</version>
        </dependency>
        <dependency>
            <groupId>com.progressoft.jupiter</groupId>
            <artifactId>relational-database-change-history</artifactId>
            <version>${jfw.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-core</artifactId>
            <version>${camel.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.activemq</groupId>
                    <artifactId>activemq-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-spring</artifactId>
            <version>${camel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-activemq</artifactId>
            <version>${camel.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>activemq-spring</artifactId>
                    <groupId>org.apache.activemq</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-core-reifier</artifactId>
            <version>${camel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.camel</groupId>
            <artifactId>camel-core-model</artifactId>
            <version>${camel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>activemq-spring</artifactId>
            <version>6.1.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>activemq-pool</artifactId>
            <version>6.1.6</version>
        </dependency>
        <dependency>
            <artifactId>camel-jms</artifactId>
            <groupId>org.apache.camel</groupId>
            <version>${camel.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-activemq</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
    </dependencies>

</project>