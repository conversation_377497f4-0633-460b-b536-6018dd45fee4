<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <variable name="logs.location" value="${LOGS_LOCATION:-./logs}"/>
    <property name="LOGS" value="${logs.location}"/>
    <appender name="Console"
              class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <Pattern>
                %d{ISO8601} %-5level [%20.20thread] [%30.30logger]: %msg%n%throwable
            </Pattern>
        </layout>
    </appender>
    <appender name="RollingFile" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>threadName</key>
            <defaultValue>general</defaultValue>
        </discriminator>
        <sift>
            <appender name="File-${threadName}"
                      class="ch.qos.logback.core.rolling.RollingFileAppender">
                <file>${LOGS}/portal-${threadName}.log</file>
                <immediateFlush>true</immediateFlush>
                <encoder
                        class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                    <Pattern>%d{ISO8601} %-5level [%20.20thread] [%30.30logger]: %msg%n%throwable</Pattern>
                </encoder>

                <rollingPolicy
                        class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <!-- rollover daily and when the file reaches 10 MegaBytes -->
                    <fileNamePattern>${LOGS}/archived/%d{yyyy-MM-dd}/portal-${threadName}.%i.log
                    </fileNamePattern>
                    <timeBasedFileNamingAndTriggeringPolicy
                            class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                        <maxFileSize>10MB</maxFileSize>
                    </timeBasedFileNamingAndTriggeringPolicy>
                </rollingPolicy>
            </appender>
        </sift>
    </appender>

    <!-- LOG everything at INFO level -->
    <root level="info">
        <appender-ref ref="RollingFile"/>
        <appender-ref ref="Console"/>
    </root>

    <logger name="org.hibernate" level="WARN"/>
    <logger name="com.progressoft.jfw.integration.IntegrationUtils" level="error"/>
    <logger name="com.progressoft.jupiter.audit.log.SecurityAuditChangeLogger" level="TRACE"/>
    <logger name="com.progressoft.workflow.osworkflow.factory.JFWWorkflowFactory" level="error"/>
    <logger name="com.progressoft.jfw.model.service.userdetails.jpa.InternalUserDetailsServiceImpl" level="error"/>
    <logger name="com.progressoft.jfw.util.JfwBusinessRoleCache" level="error"/>
    <logger name="com.progressoft.jupiter.repository.DefaultPropertyExposer" level="error"/>
    <logger name="DummyRepositories" level="error"/>


</configuration>
