server.port=7070
spring.main.allow-bean-definition-overriding=true
spring.h2.console.enabled=true
management.health.jms.enabled=false
management.health.ldap.enabled=false

executor.core.pool.size=2
executor.max.pool.size=2
executor.queue.capacity=500


base.uri=localhost:7070
base.protocol=http
csp.connect.src=${base.protocol}://${base.uri} http://localhost:8080 http://localhost:7070 ws://${base.uri} http://localhost:9092

content.security.policy=default-src 'self';font-src 'self' data: ; img-src 'self' data: ;style-src 'self' 'unsafe-inline' ;\
  object-src 'none'; frame-ancestors 'self'; base-uri 'self';\
  connect-src https://*.progressoft.cloud https://*.kuwait-test.pslabs.co ${csp.connect.src} ws://*.progressoft.cloud ; upgrade-insecure-requests;


allowed_headers=Oidc-Authorization,Jfwhash,JWTAuthorization,Language,Origin,Accept,X-Requested-With,Content-Type,Access-Control-Request-Method,Access-Control-Request-Headers,Latest-Notification-Id
cors_origin=http://localhost:9092,http://localhost:4200,http://localhost:8080,${base.protocol}://${base.uri},http://signoz-otel-collector.monitoring:4317

spring.liquibase.enabled=true

spring.jackson.serialization.FAIL_ON_EMPTY_BEANS=false

# LDAP
ldap.enabled=false
# values: JDBC,LDAP
authentication.strategy=JDBC


spring.main.allow-circular-references=true

spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=500MB
spring.servlet.multipart.max-file-size=500MB
spring.servlet.multipart.max-request-size=500MB
spring.servlet.multipart.resolve-lazily=true

dms.upload.fileSize=********
############ Reporting Service ############
reporting.base.url=${reporting_base_url:http://localhost:8080/api/v1}

new.participant.default.password=${new_participant_default_password:a}

############ Bank Names and Scripts for National Mode ############
bank.names=${bank_names:Al-Ahli Bank S.A.O.G,ARAB BANK PLC,DOHA BANK}
bank.shortnames=${bank_shortnames:AHLI,ARAB,DOHB}
bank.bic=${bank_bic:AUBOOMRU,ARABQAQA,DOHBQAQA}
bank.ncb =${bank_ncb:QMAGQAQA,QMAGQAQA,QMAGQAQA}
bank.email=${bank_email:<EMAIL>,<EMAIL>,<EMAIL>}
bank.countries=${bank_countries:QAT,QAT,QAT}

ncb.names=${ncb_names:Qatar Central Bank}
ncb.shortnames=${ncb_shortnames:QMAG}
ncb.bic=${ncb_bic:QMAGQAQA}
ncb.country=${ncb_country:QAT}
ncb.email=${ncb_email:<EMAIL>}

scripts=Scripts/updateOrgAndTenant.sql,Scripts/National/updateOperationModeAndFees.sql,Scripts/National/updateCurrenciesAndCountries.sql
updateUsersPasswordScript=/Scripts/updateUsersPassword.sql
demo.data.script=Scripts/National/insertDemoData.sql
############ END Bank Names and Scripts for National Mode ############

############ Bank Names and Scripts for Regional Mode ############
#bank.names=${bank_names:Bank Muscat,Bank Muscat Meethaq Islamic Bank,Al-Ahli Bank S.A.O.G,Alizz Islamic Bank,National Bank of Oman,HSBC,National Bank of Abu Dhabi,EMIRATES ISLAMIC BANK P.J.S.C.,AHLI UNITED BANK K.S.C.P.,AL RAJHI BANK,ARAB BANK PLC,DOHA BANK,ABC ISLAMIC BANK (E.C),BAHRAIN ISLAMIC BANK B.S.C.,AL INMA BANK,ARAB NATIONAL BANK,Banque de l'Habitat du Gabon,Banque Commerciale Internationale}
#bank.shortnames=${bank_shortnames:BMCT,MTHQ,AHLI,AIB,NBO,BBME,NBAD,MEBL,BKME,RJHI,ARAB,DOHB,ABCI,BIBB,INMA,ARNB,BHG,BCI}
#bank.bic=${bank_bic:BMUSOMRX123,BMUSOMRXISL,AUBOOMRU,IZZBOMRU,NBOMOMRX,BBMEOMRX,NBADOMRX,MEBLAEADADC,BKMEKWKW,RJHIKWKW,ARABQAQA,DOHBQAQA,ABCIBHBM,BIBBBHBM,INMASARI,ARNBSARI,LHBALBBX,BCBPCGCG}
#bank.ncb =${bank_ncb:CBOMOMRU,CBOMOMRU,CBOMOMRU,CBOMOMRU,CBOMOMRU,CBOMOMRU,CBAUAEAA,CBAUAEAA,CBKUKWKW,CBKUKWKW,QMAGQAQA,QMAGQAQA,BMAGBHBM,BMAGBHBM,SAMASARI,SAMASARI,SAMASARI,BMAGBHBM}
#bank.email=${bank_email:<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>}
#bank.countries=${bank_countries:OMN,OMN,OMN,OMN,OMN,OMN,ARE,ARE,KWT,KWT,QAT,QAT,BHR,BHR,SAU,SAU,SAU,BHR}
#
#ncb.names=${ncb_names:Saudi Central Bank,Central Bank of the UAE,Central Bank Of Kuwait,Central Bank Of Bahrain,Qatar Central Bank,Central Bank Of Oman}
#ncb.shortnames=${ncb_shortnames:SAMA,CBAU,CBKU,BMAG,QMAG,CBOM}
#ncb.bic=${ncb_bic:SAMASARI,CBAUAEAA,CBKUKWKW,BMAGBHBM,QMAGQAQA,CBOMOMRU}
#ncb.country=${ncb_country:SAU,ARE,KWT,BHR,QAT,OMN}
#ncb.email=${ncb_email:<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>}
#
#scripts=Scripts/updateOrgAndTenant.sql,Scripts/Regional/updateCurrenciesAndCountries.sql
############ END Bank Names and Scripts for Regional Mode ############
############ OTP Config ############
dms.otp.max.resend.attempts=5
dms.otp.max.validate.attempts=5
dms.otp.expiary.in.minutes=2
dms.otp.email.subject=One Time Password
dms.otp.email.body=Your OTP Is : %s