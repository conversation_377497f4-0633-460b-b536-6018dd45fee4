DO $$
DECLARE
vTenant VARCHAR(100);
vOrgId bigint;
BEGIN

SELECT NAME INTO vTenant FROM JFW_TENANT WHERE ID = 1;
SELECT ID INTO vOrgId FROM JFW_ORGS WHERE Z_TENANT_ID = vTenant AND PARENT_ID IS NULL limit 1 ;

update DMS_SYSTEMCONFIGURATION set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_PAYMENTSYSTEMS set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update JFW_CALENDAR set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_ENDPOINTS set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_SLACONFIGURATIONPARTY set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_SLACONFIGAUTOMATICACTION set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_SLACONFIGURATION set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;

END; $$;
