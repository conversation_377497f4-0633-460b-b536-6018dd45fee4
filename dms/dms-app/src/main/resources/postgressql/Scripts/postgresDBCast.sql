CREATE OR <PERSON>EPLACE FUNCTION equal_int_bool(x int, y bool)
RETURNS BOOLEAN AS $$
begin
return x = y::int;
end;
$$ LANGUAGE PLPGSQL IMMUTABLE STRICT;

CREATE OPERATOR = (
  leftarg = INTEGER,
  rightarg = BOOLEAN,
  procedure = equal_int_bool);

CREATE OR REPLACE FUNCTION equal_bool_int(x bool, y int)
RETURNS BOOLEAN AS $$
begin
return x::int = y;
end;
$$ LANGUAGE PLPGSQL IMMUTABLE STRICT;

CREATE OPERATOR = (
  leftarg = BOOLEAN,
  rightarg = INTEGER,
  procedure = equal_bool_int);