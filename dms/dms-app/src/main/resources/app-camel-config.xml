<!-- this is a spring XML file where we have Camel embedded -->
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:camel="http://camel.apache.org/schema/spring"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
		http://camel.apache.org/schema/spring http://camel.apache.org/schema/spring/camel-spring.xsd">

	<camel:camelContext id="defaultCamelContext"
		autoStartup="false" shutdownRunningTask="CompleteAllTasks"
		useMDCLogging="true" useBreadcrumb="false"
		xmlns="http://camel.apache.org/schema/spring">
		<template id="camelProducerTemplate" />

		<route id="ExecuteSLAConfigurationActionJob">
			<from uri="quartz://executeSLAConfigurationAction?cron=0+0/1+*+*+*+?&amp;stateful=true"/>
			<log message="Start Execution of SLA Configuration Action ........"/>
			<process ref="executeSLAConfigurationActionProcessor"/>
			<log message="Finish Execution of SLA Configuration Action ........"/>
		</route>

		<route id="NotifyDisputeCaseArbitrationExceedingJob">
			<from uri="quartz://executeNotifyArbitrationExceedingJob?cron=0+0/1+*+*+*+?&amp;stateful=true"/>
			<log message="Start Execution Notify Arbitration Exceeding Job ........"/>
			<process ref="notifyArbitrationExceedingProcessor"/>
			<log message="Finish Execution Notify Arbitration Exceeding Job ........"/>
		</route>

		<route id="DisputeArbitrationProcessorJob">
			<from uri="quartz://executeDisputeArbitrationProcessorJob?cron=0+0/1+*+*+*+?&amp;stateful=true"/>
			<log message="Start Execution Dispute Arbitration Processor Job ........"/>
			<process ref="disputeArbitrationProcessor"/>
			<log message="Finish Execution Dispute Arbitration Processor Job ........"/>
		</route>
	</camel:camelContext>

	<bean id="properties" class="org.apache.camel.component.properties.PropertiesComponent">
		<property name="location" value="classpath:app-camel-settings.properties"/>
	</bean>

	<bean id="jmsConnectionFactoryDMS_APP" class="org.apache.activemq.ActiveMQConnectionFactory">
		<property name="brokerURL" value="${DMS.ACTIVEMQ.URL}" />
		<property name="redeliveryPolicy">
			<bean class="org.apache.activemq.RedeliveryPolicy">
				<property name="maximumRedeliveries" value="3" />
			</bean>
		</property>
		<property name="alwaysSyncSend" value="true" />
	</bean>

	<bean id="pooledJmsConnectionFactoryDMS_APP" class="org.apache.activemq.pool.PooledConnectionFactory" init-method="start" destroy-method="stop">
		<property name="maxConnections" value="100" />
		<property name="connectionFactory" ref="jmsConnectionFactoryDMS_APP" />
		<property name="idleTimeout" value="0" />
	</bean>

	<bean id="jmsTransactionManagerDMS_APP" class="org.springframework.jms.connection.JmsTransactionManager">
		<property name="connectionFactory" ref="pooledJmsConnectionFactoryDMS_APP" />
	</bean>

	<bean id="DMSAppAmq" class="org.apache.camel.component.activemq.ActiveMQComponent">
		<property name="connectionFactory" ref="pooledJmsConnectionFactoryDMS_APP" />
		<property name="transacted" value="true" />
		<property name="transactionManager" ref="jmsTransactionManagerDMS_APP" />
		<property name="receiveTimeout" value="250" />
		<property name="cacheLevelName" value="CACHE_CONSUMER" />
		<property name="maxConcurrentConsumers" value="1" />
	</bean>



	<bean id="quartz" class="org.apache.camel.component.quartz.QuartzComponent" primary="true">
		<property name="properties" value="#{quartzPropertiesProvider.getProperties()}" />
	</bean>

	<bean name="jms" class="org.apache.camel.component.jms.JmsComponent">
		<property name="connectionFactory" ref="jmsConnectionFactoryDMS_APP" />
	</bean>

	<bean id="dmsRouteBuilder" class="com.progressoft.psdms.camel.DMSRouteBuilder" primary="true">
	</bean>

	<bean id="PROPAGATION_REQUIRED_JMS_DMS_APP" class="org.apache.camel.spring.spi.SpringTransactionPolicy">
		<property name="transactionManager" ref="jmsTransactionManagerDMS_APP" />
		<property name="propagationBehaviorName" value="PROPAGATION_REQUIRED" />
	</bean>

	<bean id="PROPAGATION_REQUIRES_NEW_JMS_DMS_APP" class="org.apache.camel.spring.spi.SpringTransactionPolicy">
		<property name="transactionManager" ref="jmsTransactionManagerDMS_APP" />
		<property name="propagationBehaviorName" value="PROPAGATION_REQUIRES_NEW" />
	</bean>

	<!-- JFW database transaction propagation -->
	<bean id="PROPAGATION_REQUIRED_JFW" class="org.apache.camel.spring.spi.SpringTransactionPolicy">
		<property name="transactionManager" ref="transactionManager" />
		<property name="propagationBehaviorName" value="PROPAGATION_REQUIRED" />
	</bean>
	<bean id="PROPAGATION_REQUIRES_NEW_JFW" class="org.apache.camel.spring.spi.SpringTransactionPolicy">
		<property name="transactionManager" ref="transactionManager" />
		<property name="propagationBehaviorName" value="PROPAGATION_REQUIRES_NEW" />
	</bean>

	<!-- IIPS Amq Configuration -->

	<bean id="iipsJmsConnectionFactory" class="org.apache.activemq.ActiveMQConnectionFactory">
		<property name="brokerURL"
				  value="${IIPS.ACTIVEMQ.URL}"/>
		<property name="redeliveryPolicy">
			<bean class="org.apache.activemq.RedeliveryPolicy">
				<property name="maximumRedeliveries" value="10"/>
			</bean>
		</property>
		<property name="alwaysSyncSend" value="false"/>
	</bean>

	<bean id="iipsPooledJmsConnectionFactory" class="org.apache.activemq.pool.PooledConnectionFactory"
		  init-method="start" destroy-method="stop">
		<property name="maxConnections" value="30"/>
		<property name="maximumActiveSessionPerConnection" value="10"/>
		<property name="connectionFactory" ref="iipsJmsConnectionFactory"/>
		<property name="idleTimeout" value="600000"/>
		<property name="blockIfSessionPoolIsFull" value="true"/>
		<property name="blockIfSessionPoolIsFullTimeout" value="30000"/>
	</bean>

	<bean id="iipsJmsTransactionManager"
		  class="org.springframework.jms.connection.JmsTransactionManager">
		<property name="connectionFactory" ref="iipsPooledJmsConnectionFactory"/>
	</bean>

	<bean id="IIPSAmq" class="org.apache.camel.component.jms.JmsComponent">
		<property name="connectionFactory" ref="iipsPooledJmsConnectionFactory"/>
		<property name="transacted" value="false"/>
		<property name="transactionManager" ref="iipsJmsTransactionManager"/>
		<property name="receiveTimeout" value="250"/>
		<property name="cacheLevelName" value="CACHE_CONSUMER"/>
		<property name="maxConcurrentConsumers" value="30"/>
	</bean>

	<bean id="IIPS_JMS_TRANSACTION_PROPAGATION" class="org.apache.camel.spring.spi.SpringTransactionPolicy">
		<property name="transactionManager" ref="iipsJmsTransactionManager"/>
		<property name="propagationBehaviorName" value="PROPAGATION_REQUIRED"/>
	</bean>

	<bean name="iipsJms" class="org.apache.camel.component.jms.JmsComponent">
		<property name="connectionFactory" ref="iipsJmsConnectionFactory" />
	</bean>
</beans>

