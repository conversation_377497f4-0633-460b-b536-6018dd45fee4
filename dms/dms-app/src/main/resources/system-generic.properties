# Add all generic properties
#Root path for the scan images and the upload files
scanImagesPath=/tmp
#Primary , Secondary
nodeMode=Primary
development.mode=false
skip.license.check=true
#include inactive groups and business roles to admin authorities
include.inactive.groups.and.business.roles.to.admin.authorities=false
#id to indicate that the user is admin
admin.user.type=1
jfw.camel.consumer.recieve.timeout=1000
############################
#### Login Dialog Setup ####
############################
#hide locale combo box on Login dialog
hide.locale.on.login=false
#load captcha Image on Login dialog extended
loadCaptchaImage=false
#how many times i can reload the captcha image  before clear username, password and tenant text fields
allowCaptchaConsecutiveNumbers=3
failed.logins.count.to.force.captcha=3
failed.captcha.count.to.reset.authentication=3
#hide more information panel in the login dialog
hide.more.info.panel=true
#hide copy right panel in the login dialog
hide.copy.right.panel=false
#possible values: JD<PERSON>, JDBC_CAPTCHA, CERTIFICATE, LDAP, LDAP_CAPTCHA ,TOKEN
authentication.root.chain=JDBC
certification.username.tenant.provider.bean.name=defaultCertificationUsernameTenantProvider
prior.to.expiration.date.in.days=30
top.message.length=400
enable.server.push=true
generate.error.im.notifications=true
# place the image at path ==> resources/images/
about.dialog.logo.name=About-Logo.png
#set enable.forget.password true to enable forget password in your login page
enable.forget.password=true
######### JFW security provided interfaces #########
chain.failed.authentication.interface.bean=defaultChainFailedAuthenticationProvidedInterface
chain.successful.authentication.interface.bean=defaultChainSuccessfulAuthenticationProvidedInterface
password.reset.interface.bean=defaultResetPasswordProvidedInterface
successful.login.interface.bean=defaultSuccessfulLoginProvidedInterface
####################################################
######################################
#### Security configuration setup ####
######################################
super.org.security.configurator.bean=orgSecurityConfigurator
group.security.configurator.bean=groupSecurityConfigurator
user.security.configurator.bean=userSecurityConfigurator
role.view.security.configurator.bean=roleViewSecurityConfigurator
role.action.security.configurator.bean=roleActionSecurityConfigurator
app.security.configurator.bean=appSecurityConfigurator
app.role.security.configurator.bean=roleSecurityConfigurator
app.group.security.configurator.bean=groupSecurityConfigurator
app.user.security.configurator.bean=userSecurityConfigurator
application.startup.entry.bean.name=workflowDecorator
change.password.permission.bean=defaultChangePasswordPermissionBean
#direct_change, reset_passowrd_change
forget.password.mechanism=direct_change
######################################
######################################
enter.executes.create.action=false
######################################
########## Restfull service ##########
######################################
restfull.service.version=1
######################################
########## External App link #########
######################################
sso.enable=false
app.link=
app.link.label.key=
# icon must be placed in the resources/images folder
app.link.icon.name=
app.sessions.notifier=defaultExternalApplicationNotifier
######################################
##########  custom reports ###########
######################################
custom.reports.hook.bean=defaultCustomReportsHook
custom.reports.parent.menu.id=220
custom.reports.rebuild.labels=true
#########################################
##########  Password encoding ###########
#########################################
# username, id
user.property.to.use=username
######################################
############ Scan request ############
######################################
#comma separated views names
enhacement.views=Upload Icons View
#enhacer spring bean name, must implement ScanRequestEnhacer interface
enhacer.bean=defaultScanRequestEnhancer
##########################################
############ External Service ############
##########################################
modify.external.service.response.bean.name=
resource.server.filter.exception.renderer.name=
############################################
############ URL authentication ############
############################################
#this is the validity time for URL authentication (i.e. if client request the JFW with URL authentication,
#this open authentication will be valid for a some period of time (in milliseconds), after that the authentication will fail.
#the validity period in milliseconds
url.authentication.grace.period=300000
########################################
############ Change history ############
########################################
grouping.change.history=true
########################################
############ ACL Inheritance config ####
########################################
inherit.condition.scheme.to.combox=1
inherit.condition.scheme.to.overide.entity=1
inherit.condition.scheme.to.direct.item=1
#####################################################
#### Using Default System Tenant in login Dialog ####
#####################################################
using.default.tenant=true
default.tenant=ATS
########################################
############ LogOut URL ################
########################################
#the logout URL will redirect on this home page.
logout.url.homepage.name=jfw.html
########################################
############# CRL ######################
########################################
jfw.certificate.revocation.list.file.path=
jfw.certificate.revocation.list.bean.name=defaultCertificateRevocationList
########################################
########## Session Management ##########
########################################
#application instance name to be used to distinguish the source of the session
#used to clear sessions at each startup
app.instance.name=instance-1
#clear current instance sessions at startup
#this flag must be false in case of load balancer
clear.session.atStartup=false
#sets the pattern for the default currency formatter
defualt.currency.format.pattren=
########################################
##### login information dialog #########
########################################
login.info.show.last.success.location=true
login.info.show.last.failed.location=true
########################################
##### Dashboard default height #########
########################################
dashboard.widget.default.height=300
#set true if the view names are unique among all app tenants otherwise false
is.views.name.unique=true
########################################
##### Logout Button Location   #########
########################################
show.as.topbar.individual=false
########################################
##### Hide org name in top bar #########
########################################
hide.topbar.org.name=false
########################################
##### Security Options #########
########################################
xframe.options=SAMEORIGIN
strict.transport.security=86400
########################################
##### change handler configurations ####
########################################
call.change.handler.on.keyup=false
# set true in case you want to send email/IM to admin user only upon create/reset user password.
# set false in case you want to send an email to the user's only upon create/reset user password.
send.reset.pass.to.admin.only=false
########################################
##### Login Page Tenant Combo Box   ####
########################################
login.tenant.combobox=false
########################################
#####    System Manual Settings     ####
########################################
# system manual sub path should be `system-manual`
# can use {help_path} as replace parameter
show.system.manual.icon=false
manual.base.url=
########################################
#####   Auto Fit Height TextArea    ####
########################################
auto.fit.height.textarea=false
########################################
#####   Encrypt user information    ####
########################################
encrypt.user.data=false
########################################
###  Show titles over DualList field ###
########################################
dualList.show.titles=false
########################################
### Max SMS allowed body message size ##
########################################
# max sms message size allowed by SMS provider
#Note: the max value is 254 ,otherwise the message will be splitted into chunks
max.sms.msg.body.size=160
#############################################################
###  Create Default Account Policy when creating a Tenant ###
#############################################################
create.default.account.policy=false
#############################################################
#######  Select first item in combo box when typing #########
#############################################################
combo.box.select.first=true
#############################################################
############ Encryption Algorithms 3DES or AES ##############
#############################################################
encryption.algorithm=3DES
#############################################################
################### PCI - Enable Logging ####################
#############################################################
enable.logging=false
#############################################################
########## Hashing Algorithm: 1) MD5   2)SHA-256 ############
#############################################################
hashing.algorithm=MD5
#############################################################
#################### Show Filter Panel ######################
#############################################################
show.filter.panel=false
#############################################################
######### PCI - Check service user privilege ################
#############################################################
check.service.user.privilege=false
######### Default Total Cheques Count ################
#############################################################
default.total.cheques.count=1
######### Parameter Tampering Exceptions URIs ###############
#############################################################
parameter.tampering.exception.regex=^.*(\
  /jfw/unSecuredAppServiceRPC.rpc|\
  /jfw/gwteventservice|\
  /reportServlet|\
  /scanUploader|\
  /attachmentServlet/ATTACH_FILES|\
  /downloadUploadServlet/UPLOAD|\
  /viewServlet/IMPORT|\
  /attachmentServlet/DOWNLOAD_ALL_ATTACHMENT|\
  /viewServlet/DOWNLOAD_IMPORT_LOGS).*
#############################################################
#########       Add Gadget display parameter  ###############
#############################################################
# When set to true, the Add Gadget option in the Setting menu
# will be hidden
hide.add.gadget=false
#############################################################
###############     Auto Expand Menu Items   ################
#############################################################
# When set to true, all menu items will be expanded
auto.expand.menu=false
#############################################################
#############################################################
################### JFW New UI Options ######################
#############################################################
#############################################################
###################   Main Menu Style  ######################
############# for old style menu: vertical ##################
############# for new style menu: horizontal ################
#####  For SYS user, the menu will always be vertical  ######
#############################################################
main.menu.style=vertical
#############################################################
######### Choose to show header banner or not ###############
############ header banner is used to show images ###########
### you can modify banner.html file to customize images  ####
############## to use this option make sure #################
#######   you set enable.thin.header option to false  #######
#############################################################
show.header.banner=false
#############################################################
######### Choose to show footer menu or not #################
##### you can modify footer-menu-en.html for english ########
############ or footer-menu-ar.html for arabic ##############
############ files to customize links for footer menu #######
#############################################################
show.footer.menu=false
#############################################################
######### This Feature to reduce the header height ##########
## If you enable this, you will not be able to use banner ###
#############################################################
enable.thin.header=true
#############################################################
#############################################################
##  Show OrgName On TopHeader ( Value is false By Default) ##
#############################################################
show.orgname.in.topheader=false
#############################################################
## Specify view names that will have its own Cheques count ##
### Use comma separated values (view name: cheque count)  ###
### e.g.: EncryptionFieldSampleView:5,Users:2, ... etc.  ####
#############################################################
custom.cheques.count.per.view=
#############################################################
#####    Specify view names that will have read only       ##
### Search Filter Use comma separated values (view name) ####
### e.g.: EncryptionFieldSampleView,Users, ... etc.      ####
#############################################################
read.only.filter.view.names=
#############################################################
#############################################################
######### Referer Exceptional Pages ###############
######### your web service white list #########
#############################################################
referer.exception.regex=^.*(\
  /jfw/unSecuredAppServiceRPC.rpc|\
  /jfw/gwteventservice|\
  /reportServlet|\
  /scanUploader|\
  /attachmentServlet/ATTACH_FILES|\
  /downloadUploadServlet/UPLOAD|\
  /viewServlet/IMPORT|\
  /attachmentServlet/DOWNLOAD_ALL_ATTACHMENT|\
  /viewServlet/DOWNLOAD_IMPORT_LOGS).*
#############################################################
#############################################################
######### Referer Exceptions URLs ###############
######### when try to access from external site #########
######### multiple values separated by comma #########
#############################################################
referer.exceptional.urls=https://achcntr.cbonet.cboroot.om
#############################################################
## Views enabled for security logging are comma seperated and has the values of view names from JFW_VIEWS table #
#################################################################################################################
views.enabled.for.security.logging=Login
security.audit.change.logging.enabled=false

