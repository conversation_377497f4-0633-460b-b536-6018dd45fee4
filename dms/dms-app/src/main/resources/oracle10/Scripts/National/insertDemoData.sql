BEGIN

INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807804200000, null, 'DMS_ReasonManagement_Workflow', 1, 1);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALL<PERSON>, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (74000000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:50:22.517000', '1', 3003, 292121807804200000);

INSERT INTO DMS_REASONMANAGEMENT (ID, CODE, NAME, DESCRIPTION, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, <PERSON>_CREATION_DATE, Z_DELETED_BY, <PERSON>_<PERSON><PERSON><PERSON>D_FLAG, <PERSON>_DELETED_ON, <PERSON>_EDITABLE, Z_LOCKED_BY, <PERSON>_<PERSON>OCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ISDISPUTE, ISREJECTION, ISREPRESENTMENT, ISREQADDITIONALINFO) VALUES (***********, '01', 'General', 'General', null, null, null, null, null, 'DMSMAKER@DMS', TIMESTAMP '2025-01-24 13:50:09.553000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='DMS'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 13:50:22.548000', 292121807804200000, null, null, null, 3003, 1, 1, 1, 1);

INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807804300000, null, 'WF_DMS_DisputeCase', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807804600000, null, 'WF_DMS_DisputeCase', 4, 2);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807804900000, null, 'WF_DMS_DisputeCase', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807805200000, null, 'WF_DMS_DisputeCase', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807816300000, null, 'WF_DMS_DisputeCase', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807816600000, null, 'WF_DMS_DisputeCase', 4, 2);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807816900000, null, 'WF_DMS_DisputeCase', 1, 1);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (74100000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:53:54.523000', '1', 406, 292121807804300000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (74700000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:54:09.137000', '1', 406, 292121807804900000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (75000000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:54:12.665000', '1', 406, 292121807805200000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (86100000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:02:54.904000', '1', 406, 292121807816300000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (86400000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:05:33.528000', '1', 714, 292121807816600000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (86700000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:03:02.807000', '1', 406, 292121807816900000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (74400000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:57:30.327000', '1', 714, 292121807804600000);

INSERT INTO DMS_DISPUTECASES (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, URGENCY, CASEREFERENCENUMBER, LASTACTIONBY, LASTACTION, LASTNOTE, TRANSACTIONREFERENCE, CREATIONDATETIME, TRANSACTIONDATE, TRANSACTIONAMOUNT, DISPUTEDAMOUNT, CURRENCYID, SENDERPARTICIPANTID, RECEIVERPARTICIPANTID, PAYMENTSYSTEM_ID, CLAIMANTBANKDISPUTE_ID, DEFENDANTBANKDISPUTE_ID, CLAIMANTNCBDISPUTE_ID, DEFENDANTNCBDISPUTE_ID, OPERATORDISPUTE_ID, DEFENDANTBANK_REJECTIONCOUNT, DEFENDANTNCB_REJECTIONCOUNT, CLAIMANTBANK_REPRESENTCOUNT, CLAIMANTNCB_REPRESENTCOUNT, NUMBEROFATT, CLAIMANTBANK_ID, DEFENDANTBANK_ID, BANKEXCEEDARBITRATIONNOTIFIED, NCBEXCEEDARBITRATIONNOTIFIED, REASON_ID, NOTES, REFDISPUTE_PAYMENTSYSTEMID, REFDISPUTE_DEFENDANT, REFDISPUTE_CLAIMANT, REFDISPUTE_CASENUMBER, DISPUTEOVERDISPUTE, REFDISPUTE_TRANSACTIONAMOUNT) VALUES (***********1, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:50:44.379000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:56:17.139000', 292121807804300000, null, null, null, **********, 'Normal', '***********1', 'Assignee Bank', 'Approve Dispute', 'Dispute Approved.', '***********', TIMESTAMP '2025-01-24 13:50:44.362000', DATE '2024-11-10', 190.00000, 100.00000, 414, 8, 6, 2, *********001, *********001, null, null, null, 0, 0, 0, 0, 0, 8, 6, 0, 0, ***********, null, null, null, null, null, 0, null);
INSERT INTO DMS_DISPUTECASES (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, URGENCY, CASEREFERENCENUMBER, LASTACTIONBY, LASTACTION, LASTNOTE, TRANSACTIONREFERENCE, CREATIONDATETIME, TRANSACTIONDATE, TRANSACTIONAMOUNT, DISPUTEDAMOUNT, CURRENCYID, SENDERPARTICIPANTID, RECEIVERPARTICIPANTID, PAYMENTSYSTEM_ID, CLAIMANTBANKDISPUTE_ID, DEFENDANTBANKDISPUTE_ID, CLAIMANTNCBDISPUTE_ID, DEFENDANTNCBDISPUTE_ID, OPERATORDISPUTE_ID, DEFENDANTBANK_REJECTIONCOUNT, DEFENDANTNCB_REJECTIONCOUNT, CLAIMANTBANK_REPRESENTCOUNT, CLAIMANTNCB_REPRESENTCOUNT, NUMBEROFATT, CLAIMANTBANK_ID, DEFENDANTBANK_ID, BANKEXCEEDARBITRATIONNOTIFIED, NCBEXCEEDARBITRATIONNOTIFIED, REASON_ID, NOTES, REFDISPUTE_PAYMENTSYSTEMID, REFDISPUTE_DEFENDANT, REFDISPUTE_CLAIMANT, REFDISPUTE_CASENUMBER, DISPUTEOVERDISPUTE, REFDISPUTE_TRANSACTIONAMOUNT) VALUES (***********2, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:37.228000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:57:30.379000', 292121807804600000, null, null, null, **********, 'Normal', '***********2', 'Assigner Bank', 'Close Dispute', 'Dispute Closed After Rejection.', '**********', TIMESTAMP '2025-01-24 13:51:37.222000', DATE '2024-11-11', 500.00000, 250.00000, 414, 8, 6, 2, *********003, *********003, null, null, null, 1, 0, 0, 0, 0, 8, 6, 0, 0, ***********, null, null, null, null, null, 0, null);
INSERT INTO DMS_DISPUTECASES (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, URGENCY, CASEREFERENCENUMBER, LASTACTIONBY, LASTACTION, LASTNOTE, TRANSACTIONREFERENCE, CREATIONDATETIME, TRANSACTIONDATE, TRANSACTIONAMOUNT, DISPUTEDAMOUNT, CURRENCYID, SENDERPARTICIPANTID, RECEIVERPARTICIPANTID, PAYMENTSYSTEM_ID, CLAIMANTBANKDISPUTE_ID, DEFENDANTBANKDISPUTE_ID, CLAIMANTNCBDISPUTE_ID, DEFENDANTNCBDISPUTE_ID, OPERATORDISPUTE_ID, DEFENDANTBANK_REJECTIONCOUNT, DEFENDANTNCB_REJECTIONCOUNT, CLAIMANTBANK_REPRESENTCOUNT, CLAIMANTNCB_REPRESENTCOUNT, NUMBEROFATT, CLAIMANTBANK_ID, DEFENDANTBANK_ID, BANKEXCEEDARBITRATIONNOTIFIED, NCBEXCEEDARBITRATIONNOTIFIED, REASON_ID, NOTES, REFDISPUTE_PAYMENTSYSTEMID, REFDISPUTE_DEFENDANT, REFDISPUTE_CLAIMANT, REFDISPUTE_CASENUMBER, DISPUTEOVERDISPUTE, REFDISPUTE_TRANSACTIONAMOUNT) VALUES (***********3, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:04.278000', null, null, null, 0, null, null, 0, 'SYSTEM', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:08:13.055000', 292121807804900000, null, null, null, **********, 'Normal', '***********3', 'Operator', 'Reject Dispute', 'Dispute Rejected.', '**********', TIMESTAMP '2025-01-24 13:52:04.272000', DATE '2024-11-11', 200.00000, 150.00000, 414, 6, 8, 2, *********004, *********004, null, null, *********001, 2, 0, 1, 0, 2, 8, 6, 0, 0, ***********, null, null, null, null, null, 0, null);
INSERT INTO DMS_DISPUTECASES (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, URGENCY, CASEREFERENCENUMBER, LASTACTIONBY, LASTACTION, LASTNOTE, TRANSACTIONREFERENCE, CREATIONDATETIME, TRANSACTIONDATE, TRANSACTIONAMOUNT, DISPUTEDAMOUNT, CURRENCYID, SENDERPARTICIPANTID, RECEIVERPARTICIPANTID, PAYMENTSYSTEM_ID, CLAIMANTBANKDISPUTE_ID, DEFENDANTBANKDISPUTE_ID, CLAIMANTNCBDISPUTE_ID, DEFENDANTNCBDISPUTE_ID, OPERATORDISPUTE_ID, DEFENDANTBANK_REJECTIONCOUNT, DEFENDANTNCB_REJECTIONCOUNT, CLAIMANTBANK_REPRESENTCOUNT, CLAIMANTNCB_REPRESENTCOUNT, NUMBEROFATT, CLAIMANTBANK_ID, DEFENDANTBANK_ID, BANKEXCEEDARBITRATIONNOTIFIED, NCBEXCEEDARBITRATIONNOTIFIED, REASON_ID, NOTES, REFDISPUTE_PAYMENTSYSTEMID, REFDISPUTE_DEFENDANT, REFDISPUTE_CLAIMANT, REFDISPUTE_CASENUMBER, DISPUTEOVERDISPUTE, REFDISPUTE_TRANSACTIONAMOUNT) VALUES (***********4, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:43.170000', null, null, null, 0, null, null, 0, 'SYSTEM', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:15.546000', 292121807805200000, null, null, null, **********, 'Normal', '***********4', 'Operator', 'Approve Dispute', 'Dispute Approved.', '***********', TIMESTAMP '2025-01-24 13:52:43.165000', DATE '2024-11-14', 190.00000, 150.00000, 414, 8, 6, 2, *********005, *********005, null, null, *********002, 1, 0, 0, 0, 0, 8, 6, 0, 0, ***********, null, null, null, null, null, 0, null);
INSERT INTO DMS_DISPUTECASES (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, URGENCY, CASEREFERENCENUMBER, LASTACTIONBY, LASTACTION, LASTNOTE, TRANSACTIONREFERENCE, CREATIONDATETIME, TRANSACTIONDATE, TRANSACTIONAMOUNT, DISPUTEDAMOUNT, CURRENCYID, SENDERPARTICIPANTID, RECEIVERPARTICIPANTID, PAYMENTSYSTEM_ID, CLAIMANTBANKDISPUTE_ID, DEFENDANTBANKDISPUTE_ID, CLAIMANTNCBDISPUTE_ID, DEFENDANTNCBDISPUTE_ID, OPERATORDISPUTE_ID, DEFENDANTBANK_REJECTIONCOUNT, DEFENDANTNCB_REJECTIONCOUNT, CLAIMANTBANK_REPRESENTCOUNT, CLAIMANTNCB_REPRESENTCOUNT, NUMBEROFATT, CLAIMANTBANK_ID, DEFENDANTBANK_ID, BANKEXCEEDARBITRATIONNOTIFIED, NCBEXCEEDARBITRATIONNOTIFIED, REASON_ID, NOTES, REFDISPUTE_PAYMENTSYSTEMID, REFDISPUTE_DEFENDANT, REFDISPUTE_CLAIMANT, REFDISPUTE_CASENUMBER, DISPUTEOVERDISPUTE, REFDISPUTE_TRANSACTIONAMOUNT) VALUES (***********5, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:33.426000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:04:13.474000', 292121807816300000, null, null, null, **********, 'Normal', '***********5', 'Assignee Bank', 'Approve Dispute', 'Dispute Approved.', '***********', TIMESTAMP '2025-01-24 14:01:33.421000', DATE '2024-11-15', 190.00000, 150.00000, 414, 8, 6, 2, *********006, *********006, null, null, null, 0, 0, 0, 0, 0, 6, 8, 0, 0, ***********, null, null, null, null, null, 0, null);
INSERT INTO DMS_DISPUTECASES (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, URGENCY, CASEREFERENCENUMBER, LASTACTIONBY, LASTACTION, LASTNOTE, TRANSACTIONREFERENCE, CREATIONDATETIME, TRANSACTIONDATE, TRANSACTIONAMOUNT, DISPUTEDAMOUNT, CURRENCYID, SENDERPARTICIPANTID, RECEIVERPARTICIPANTID, PAYMENTSYSTEM_ID, CLAIMANTBANKDISPUTE_ID, DEFENDANTBANKDISPUTE_ID, CLAIMANTNCBDISPUTE_ID, DEFENDANTNCBDISPUTE_ID, OPERATORDISPUTE_ID, DEFENDANTBANK_REJECTIONCOUNT, DEFENDANTNCB_REJECTIONCOUNT, CLAIMANTBANK_REPRESENTCOUNT, CLAIMANTNCB_REPRESENTCOUNT, NUMBEROFATT, CLAIMANTBANK_ID, DEFENDANTBANK_ID, BANKEXCEEDARBITRATIONNOTIFIED, NCBEXCEEDARBITRATIONNOTIFIED, REASON_ID, NOTES, REFDISPUTE_PAYMENTSYSTEMID, REFDISPUTE_DEFENDANT, REFDISPUTE_CLAIMANT, REFDISPUTE_CASENUMBER, DISPUTEOVERDISPUTE, REFDISPUTE_TRANSACTIONAMOUNT) VALUES (***********6, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:03.975000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:05:33.580000', 292121807816600000, null, null, null, **********, 'Normal', '***********6', 'Assigner Bank', 'Close Dispute', 'Dispute Closed After Rejection.', '***********', TIMESTAMP '2025-01-24 14:02:03.971000', DATE '2024-12-11', 190.00000, 130.00000, 414, 8, 6, 2, *********007, *********007, null, null, null, 1, 0, 0, 0, 0, 6, 8, 0, 0, ***********, null, null, null, null, null, 0, null);
INSERT INTO DMS_DISPUTECASES (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, URGENCY, CASEREFERENCENUMBER, LASTACTIONBY, LASTACTION, LASTNOTE, TRANSACTIONREFERENCE, CREATIONDATETIME, TRANSACTIONDATE, TRANSACTIONAMOUNT, DISPUTEDAMOUNT, CURRENCYID, SENDERPARTICIPANTID, RECEIVERPARTICIPANTID, PAYMENTSYSTEM_ID, CLAIMANTBANKDISPUTE_ID, DEFENDANTBANKDISPUTE_ID, CLAIMANTNCBDISPUTE_ID, DEFENDANTNCBDISPUTE_ID, OPERATORDISPUTE_ID, DEFENDANTBANK_REJECTIONCOUNT, DEFENDANTNCB_REJECTIONCOUNT, CLAIMANTBANK_REPRESENTCOUNT, CLAIMANTNCB_REPRESENTCOUNT, NUMBEROFATT, CLAIMANTBANK_ID, DEFENDANTBANK_ID, BANKEXCEEDARBITRATIONNOTIFIED, NCBEXCEEDARBITRATIONNOTIFIED, REASON_ID, NOTES, REFDISPUTE_PAYMENTSYSTEMID, REFDISPUTE_DEFENDANT, REFDISPUTE_CLAIMANT, REFDISPUTE_CASENUMBER, DISPUTEOVERDISPUTE, REFDISPUTE_TRANSACTIONAMOUNT) VALUES (***********7, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:33.137000', null, null, null, 0, null, null, 0, 'SYSTEM', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:27.339000', 292121807816900000, null, null, null, **********, 'Normal', '***********7', 'Operator', 'Approve Dispute', 'Dispute Approved.', '***********', TIMESTAMP '2025-01-24 14:02:33.133000', DATE '2024-12-14', 190.00000, 140.00000, 414, 8, 6, 2, *********008, *********008, null, null, *********003, 1, 0, 0, 0, 0, 6, 8, 0, 0, ***********, null, null, null, null, null, 0, null);

INSERT INTO DMS_DISPUTEATT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (100001933, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:24.537000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, '546573740A', null, 'DMS_DisputeCases.View', null, 'text/plain', 'com.progressoft.dms.entities.DMS_DisputeCase', null, 'text/plain', 'DMS-Att.txt', null, '***********1', null, null, 5.00);
INSERT INTO DMS_DISPUTEATT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (100001985, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:30.561000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, '546573740A', null, 'DMS_DisputeCases.View', null, 'text/plain', 'com.progressoft.dms.entities.DMS_DisputeCase', null, 'text/plain', 'DMS-Att.txt', null, '***********3', null, null, 5.00);
INSERT INTO DMS_DISPUTEATT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (100002797, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:10.032000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, '546573740A', null, 'DMS_DisputeCases.View', null, 'text/plain', 'com.progressoft.dms.entities.DMS_DisputeCase', null, 'text/plain', 'DMS-Att.txt', null, '***********6', null, null, 5.00);
INSERT INTO DMS_DISPUTEATT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (100002010, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:48.913000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, '546573740A', null, 'DMS_DisputeCases.View', null, 'text/plain', 'com.progressoft.dms.entities.DMS_DisputeCase', null, 'text/plain', 'DMS-Att.txt', null, '***********4', null, null, 5.00);
INSERT INTO DMS_DISPUTEATT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (*********, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:59:36.773000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='RJH'), 'DMS', 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:59:36.773000', null, null, '546573740A', null, 'DMS_ClaimantBankCaseManagement.View', null, 'text/plain', 'com.progressoft.dms.entities.DMS_DisputeCase', null, 'text/plain', 'DMS-Att (copy).txt', null, '***********3', null, null, 5.00);
INSERT INTO DMS_DISPUTEATT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (*********, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:38.973000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, '546573740A', null, 'DMS_DisputeCases.View', null, 'text/plain', 'com.progressoft.dms.entities.DMS_DisputeCase', null, 'text/plain', 'DMS-Att.txt', null, '***********5', null, null, 5.00);
INSERT INTO DMS_DISPUTEATT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (100001958, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:44.445000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.719000', null, null, '546573740A', null, 'DMS_DisputeCases.View', null, 'text/plain', 'com.progressoft.dms.entities.DMS_DisputeCase', null, 'text/plain', 'DMS-Att.txt', null, '***********2', null, null, 5.00);
INSERT INTO DMS_DISPUTEATT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, ATTFILE, ATTACHMENT_SOURCE, ATTACHMENT_TOKEN, COMMENTS, CONTENTTYPE, ENTITYID, IMAGE_THUMBNAIL, IMAGE_TYPE, NAME, ORIGINAL_MICR, RECORDID, REF_VALUE, REV, ATTACHMENT_SIZE) VALUES (100002823, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:38.541000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, '546573740A', null, 'DMS_DisputeCases.View', null, 'text/plain', 'com.progressoft.dms.entities.DMS_DisputeCase', null, 'text/plain', 'DMS-Att.txt', null, '***********7', null, null, 5.00);

INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807805500000, null, 'WF_DMS_ClaimantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807806700000, null, 'WF_DMS_ClaimantBankCaseManagement', 4, 2);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807807300000, null, 'WF_DMS_ClaimantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807807900000, null, 'WF_DMS_ClaimantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807817200000, null, 'WF_DMS_ClaimantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807817800000, null, 'WF_DMS_ClaimantBankCaseManagement', 4, 2);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807818400000, null, 'WF_DMS_ClaimantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:08:13.100000', '1', 8151, 292121807807300000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:09:15.588000', '1', 703, 292121807807900000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:09:27.395000', '1', 703, 292121807818400000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:04:13.522000', '1', 703, 292121807817200000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (87600000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:05:33.406000', '1', 714, 292121807817800000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:56:17.198000', '1', 703, 292121807805500000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:57:30.199000', '1', 714, 292121807806700000);

INSERT INTO DMS_CLAIMANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********001, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.697000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='RJH'), 'DMS', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:56:19.025000', 292121807805500000, null, null, null, **********, null, ***********1, null, null, null);
INSERT INTO DMS_CLAIMANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********003, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.611000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='RJH'), 'DMS', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:57:30.379000', 292121807806700000, null, null, null, **********, null, ***********2, null, null, null);
INSERT INTO DMS_CLAIMANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********004, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.192000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='RJH'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:08:13.184000', 292121807807300000, null, null, null, **********, null, ***********3, null, null, null);
INSERT INTO DMS_CLAIMANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********005, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.718000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='RJH'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:15.666000', 292121807807900000, null, null, null, **********, null, ***********4, null, null, null);
INSERT INTO DMS_CLAIMANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********006, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:54.957000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='NBK'), 'DMS', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:04:13.688000', 292121807817200000, null, null, null, **********, null, ***********5, null, null, null);
INSERT INTO DMS_CLAIMANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********007, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.681000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='NBK'), 'DMS', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:05:33.580000', 292121807817800000, null, null, null, **********, null, ***********6, null, null, null);
INSERT INTO DMS_CLAIMANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********008, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.869000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='NBK'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:27.494000', 292121807818400000, null, null, null, **********, null, ***********7, null, null, null);

INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807805600000, null, 'WF_DMS_DefendantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807806800000, null, 'WF_DMS_DefendantBankCaseManagement', 4, 2);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807807400000, null, 'WF_DMS_DefendantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807808000000, null, 'WF_DMS_DefendantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807817300000, null, 'WF_DMS_DefendantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807817900000, null, 'WF_DMS_DefendantBankCaseManagement', 4, 2);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807818500000, null, 'WF_DMS_DefendantBankCaseManagement', 1, 1);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:08:13.181000', '1', 706, 292121807807400000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:09:15.664000', '1', 703, 292121807808000000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:09:27.491000', '1', 703, 292121807818500000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:04:13.466000', '1', 703, 292121807817300000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (87700000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:05:33.462000', '1', 714, 292121807817900000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:56:17.131000', '1', 703, 292121807805600000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (********, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 13:57:30.256000', '1', 714, 292121807806800000);

INSERT INTO DMS_DEFENDANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********001, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.886000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='NBK'), 'DMS', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:56:19.025000', 292121807805600000, null, null, null, **********, null, ***********1, null, null, null);
INSERT INTO DMS_DEFENDANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********003, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.807000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='NBK'), 'DMS', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:57:30.379000', 292121807806800000, null, null, null, **********, null, ***********2, null, null, null);
INSERT INTO DMS_DEFENDANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********004, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.338000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='NBK'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:08:13.184000', 292121807807400000, null, null, null, **********, null, ***********3, null, null, null);
INSERT INTO DMS_DEFENDANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********005, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.834000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='NBK'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:15.666000', 292121807808000000, null, null, null, **********, null, ***********4, null, null, null);
INSERT INTO DMS_DEFENDANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********006, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.057000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='RJH'), 'DMS', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:04:13.688000', 292121807817300000, null, null, null, **********, null, ***********5, null, null, null);
INSERT INTO DMS_DEFENDANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********007, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.807000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='RJH'), 'DMS', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:05:33.580000', 292121807817900000, null, null, null, **********, null, ***********6, null, null, null);
INSERT INTO DMS_DEFENDANTBANKCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REPRESENTREASON, REQADDINFOREASON) VALUES (*********008, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.969000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='RJH'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:27.494000', 292121807818500000, null, null, null, **********, null, ***********7, null, null, null);

INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807814700000, null, 'WF_DMS_OperatorCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807816000000, null, 'WF_DMS_OperatorCaseManagement', 1, 1);
INSERT INTO JFW_OS_WFENTRY (ID, Z_ARCHIVE_STATUS, NAME, STATE, VERSION) VALUES (292121807823000000, null, 'WF_DMS_OperatorCaseManagement', 1, 1);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (85800000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:09:15.542000', '1', 9903, 292121807816000000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (84500000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:08:13.051000', '1', 9904, 292121807814700000);
INSERT INTO JFW_OS_CURRENTSTEP (ID, ACTION_ID, Z_ARCHIVE_STATUS, CALLER, DUE_DATE, FINISH_DATE, OWNER, START_DATE, STATUS, STEP_ID, ENTRY_ID) VALUES (92800000, 0, null, null, null, null, null, TIMESTAMP '2025-01-24 14:09:27.330000', '1', 9903, 292121807823000000);

INSERT INTO DMS_OPERATORCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REQADDINFOREASON) VALUES (*********001, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 14:00:26.104000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='DMS'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:08:13.184000', 292121807814700000, null, null, null, 811819904, null, ***********3, null, null);
INSERT INTO DMS_OPERATORCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REQADDINFOREASON) VALUES (*********002, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 14:01:17.944000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='DMS'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:15.666000', 292121807816000000, null, null, null, 811819903, null, ***********4, null, null);
INSERT INTO DMS_OPERATORCASEMANAGEMENT (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, NOTE, DISPUTECASE_ID, REJECTIONREASON, REQADDINFOREASON) VALUES (*********003, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:06:06.361000', null, null, null, 0, null, null, (select id from JFW_ORGS where ORG_SHORT_NAME='DMS'), 'DMS', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:27.494000', 292121807823000000, null, null, null, 811819903, null, ***********7, null, null);

INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 13:55:30.886000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:55:30.886000', null, null, null, null, null, 'Request Additional Info For Dispute', 'Assignee Bank', 'Please provide more info.', ***********1, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 13:55:55.347000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:55:55.347000', null, null, null, null, null, 'Additional Info Provided', 'Assigner Bank', 'More info provided.', ***********1, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 13:56:17.138000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:56:17.138000', null, null, null, null, null, 'Approve Dispute', 'Assignee Bank', 'Dispute Approved.', ***********1, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 13:57:02.627000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:57:02.627000', null, null, null, null, null, 'Reject Dispute', 'Assignee Bank', 'Dispute Rejected.', ***********2, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:57:30.204000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:57:30.204000', null, null, null, null, null, 'Close Dispute', 'Assigner Bank', 'Dispute Closed After Rejection.', ***********2, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 13:58:08.559000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:58:08.559000', null, null, null, null, null, 'Request Additional Info For Dispute', 'Assignee Bank', 'Please provide more info.', ***********3, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 13:58:22.794000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:58:22.794000', null, null, null, null, null, 'Additional Info Provided', 'Assigner Bank', 'More info provided.', ***********3, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 13:58:37.526000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:58:37.526000', null, null, null, null, null, 'Reject Dispute', 'Assignee Bank', 'Dispute Rejected.', ***********3, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 13:59:42.672000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 13:59:42.672000', null, null, null, null, null, 'Re-Present Dispute', 'Assigner Bank', 'Represented', ***********3, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********0, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:00:00.044000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:00:00.044000', null, null, null, null, null, 'Reject Dispute', 'Assignee Bank', 'Dispute Rejected.', ***********3, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (1*********, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 14:00:25.998000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 14:00:25.998000', null, null, null, null, null, 'Arbitrate Dispute Case', 'Assigner Bank', 'Arbitrated', ***********3, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (1*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:00:59.196000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:00:59.196000', null, null, null, null, null, 'Reject Dispute', 'Assignee Bank', 'Dispute Rejected.', ***********4, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (1*********, null, null, null, null, null, 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 14:01:17.896000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 14:01:17.896000', null, null, null, null, null, 'Arbitrate Dispute Case', 'Assigner Bank', 'Arbitrated', ***********4, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (1*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:03:32.076000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:03:32.076000', null, null, null, null, null, 'Request Additional Info For Dispute', 'Assignee Bank', 'Please provide more info.', ***********5, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (**********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:03:50.798000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:03:50.798000', null, null, null, null, null, 'Additional Info Provided', 'Assigner Bank', 'More info provided.', ***********5, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (1*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:04:13.473000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:04:13.473000', null, null, null, null, null, 'Approve Dispute', 'Assignee Bank', 'Dispute Approved.', ***********5, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (1*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:05:12.034000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:05:12.034000', null, null, null, null, null, 'Reject Dispute', 'Assignee Bank', 'Dispute Rejected.', ***********6, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (1*********, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:05:33.410000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:05:33.410000', null, null, null, null, null, 'Close Dispute', 'Assigner Bank', 'Dispute Closed After Rejection.', ***********6, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (1*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:05:53.980000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:05:53.980000', null, null, null, null, null, 'Reject Dispute', 'Assignee Bank', 'Dispute Rejected.', ***********7, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (*********0, null, null, null, null, null, 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:06:06.327000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:06:06.327000', null, null, null, null, null, 'Arbitrate Dispute Case', 'Assigner Bank', 'Arbitrated', ***********7, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (2*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:06:50.481000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:06:50.481000', null, null, null, null, null, 'Request More Info From Assigner', 'Operator', 'Please provide more info.', ***********3, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (2*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:07:13.040000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:07:13.040000', null, null, null, null, null, 'Additional Info Provided', 'Assigner Bank', 'More info provided', ***********3, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (2*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:07:32.796000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:07:32.796000', null, null, null, null, null, 'Request More Info From Assignee', 'Operator', 'Please provide more info.', ***********3, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (2*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:07:53.630000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:07:53.630000', null, null, null, null, null, 'Additional Info Provided', 'Assignee Bank', 'More info provided.', ***********3, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (2*********, null, null, null, null, null, 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:08:13.054000', null, null, null, 0, null, null, 0, 'SYSTEM', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:08:13.054000', null, null, null, null, null, 'Reject Dispute', 'Operator', 'Dispute Rejected.', ***********3, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (2*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:08:44.389000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:08:44.389000', null, null, null, null, null, 'Request More Info From Assigner', 'Operator', 'Please provide more info.', ***********4, ***********);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (2*********, null, null, null, null, null, 'SERVICE_USER', TIMESTAMP '2025-01-24 14:09:02.519000', null, null, null, 0, null, null, 0, 'SYSTEM', 'SERVICE_USER', TIMESTAMP '2025-01-24 14:09:02.519000', null, null, null, null, null, 'Additional Info Provided', 'Assigner Bank', 'More info provided.', ***********4, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (**********, null, null, null, null, null, 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:15.546000', null, null, null, 0, null, null, 0, 'SYSTEM', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:15.546000', null, null, null, null, null, 'Approve Dispute', 'Operator', 'Dispute Approved.', ***********4, null);
INSERT INTO DMS_CORRESPONDENCE (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ACTION, ACTIONBY, NOTE, DISPUTE_ID, REASON_ID) VALUES (2*********, null, null, null, null, null, 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:27.339000', null, null, null, 0, null, null, 0, 'SYSTEM', 'DMSCHECKER@DMS', TIMESTAMP '2025-01-24 14:09:27.339000', null, null, null, null, null, 'Approve Dispute', 'Operator', 'Dispute Approved.', ***********7, null);

INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.225000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'instgBranch', 'branchCode1 - branchName1', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.238000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'instdBranch', 'branchCode2 - branchName2', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.244000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'creditorIban', 'TEST', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.249000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'creditorRIB', '12345', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.254000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'creditorCountry', 'KWT', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.258000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'creditorCity', 'KWT', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.263000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'creditorPhone', '12345', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.269000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'creditorEmail', '<EMAIL>', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.274000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'debtorIban', 'TEST', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (***********, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.278000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'debtorRIB', '12345', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (1*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.284000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'debtorCountry', 'KWT', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (1*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.288000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'debtorCity', 'KWT', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (1*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.293000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'debtorPhone', '12345', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (1*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:13.298000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:53:54.775000', null, null, null, null, null, 'debtorEmail', '<EMAIL>', ***********1);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (**********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.651000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'instgBranch', 'branchCode1 - branchName1', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (1*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.659000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'instdBranch', 'branchCode2 - branchName2', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (1*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.664000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'creditorIban', 'TEST', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (1*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.672000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'creditorRIB', '12345', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (1*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.677000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'creditorCountry', 'KWT', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********00, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.683000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'creditorCity', 'KWT', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (2*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.691000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'creditorPhone', '12345', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (2*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.698000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'creditorEmail', '<EMAIL>', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (2*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.720000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'debtorIban', 'TEST', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (2*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.745000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'debtorRIB', '12345', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (2*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.754000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'debtorCountry', 'KWT', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (2*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.760000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'debtorCity', 'KWT', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (2*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.766000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.718000', null, null, null, null, null, 'debtorPhone', '12345', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (**********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:51:48.770000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:04.719000', null, null, null, null, null, 'debtorEmail', '<EMAIL>', ***********2);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (2*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.030000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'instgBranch', 'branchCode1 - branchName1', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********00, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.035000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'instdBranch', 'branchCode2 - branchName2', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.039000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'creditorIban', 'TEST', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.044000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'creditorRIB', '12345', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.048000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'creditorCountry', 'KWT', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.053000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'creditorCity', 'KWT', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.057000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'creditorPhone', '12345', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.061000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'creditorEmail', '<EMAIL>', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.066000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'debtorIban', 'TEST', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.071000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'debtorRIB', '12345', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (3*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.075000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'debtorCountry', 'KWT', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********00, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.081000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'debtorCity', 'KWT', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.086000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'debtorPhone', '12345', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:18.091000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:09.290000', null, null, null, null, null, 'debtorEmail', '<EMAIL>', ***********3);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.734000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'instgBranch', 'branchCode1 - branchName1', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.740000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'instdBranch', 'branchCode2 - branchName2', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.745000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'creditorIban', 'TEST', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.750000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'creditorRIB', '12345', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.755000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'creditorCountry', 'KWT', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.763000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'creditorCity', 'KWT', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (4*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.769000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'creditorPhone', '12345', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********00, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.774000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'creditorEmail', '<EMAIL>', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.782000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'debtorIban', 'TEST', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.808000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'debtorRIB', '12345', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.820000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'debtorCountry', 'KWT', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.828000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'debtorCity', 'KWT', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.834000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'debtorPhone', '12345', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'RJHMAKER@RJH', TIMESTAMP '2025-01-24 13:52:53.840000', null, null, null, 0, null, null, 0, 'SYSTEM', 'RJHCHECKER@RJH', TIMESTAMP '2025-01-24 13:54:12.796000', null, null, null, null, null, 'debtorEmail', '<EMAIL>', ***********4);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.379000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'instgBranch', 'branchCode1 - branchName1', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.383000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'instdBranch', 'branchCode2 - branchName2', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (5*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.388000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'creditorIban', 'TEST', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********00, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.391000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'creditorRIB', '12345', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.395000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'creditorCountry', 'KWT', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.400000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'creditorCity', 'KWT', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.404000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'creditorPhone', '12345', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.408000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'creditorEmail', '<EMAIL>', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.413000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'debtorIban', 'TEST', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.418000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'debtorRIB', '12345', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.422000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'debtorCountry', 'KWT', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.426000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'debtorCity', 'KWT', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (6*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.431000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'debtorPhone', '12345', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********00, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:01:50.435000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:55.016000', null, null, null, null, null, 'debtorEmail', '<EMAIL>', ***********5);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.781000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'instgBranch', 'branchCode1 - branchName1', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.792000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'instdBranch', 'branchCode2 - branchName2', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.807000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'creditorIban', 'TEST', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.811000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'creditorRIB', '12345', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.816000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'creditorCountry', 'KWT', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.823000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'creditorCity', 'KWT', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.828000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'creditorPhone', '12345', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.834000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'creditorEmail', '<EMAIL>', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (7*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.840000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'debtorIban', 'TEST', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********00, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.846000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'debtorRIB', '12345', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.852000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'debtorCountry', 'KWT', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.857000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'debtorCity', 'KWT', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.862000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'debtorPhone', '12345', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:15.866000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:02:59.764000', null, null, null, null, null, 'debtorEmail', '<EMAIL>', ***********6);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.716000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'instgBranch', 'branchCode1 - branchName1', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.723000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'instdBranch', 'branchCode2 - branchName2', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.727000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'creditorIban', 'TEST', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.732000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'creditorRIB', '12345', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (8*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.736000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'creditorCountry', 'KWT', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (*********00, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.741000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'creditorCity', 'KWT', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (9*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.745000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'creditorPhone', '12345', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (9*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.749000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'creditorEmail', '<EMAIL>', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (9*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.754000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'debtorIban', 'TEST', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (9*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.758000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'debtorRIB', '12345', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (9*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.762000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'debtorCountry', 'KWT', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (9*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.766000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'debtorCity', 'KWT', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (9*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.771000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'debtorPhone', '12345', ***********7);
INSERT INTO DMS_TRANSACTIONADDITIONALINFOS (ID, Z_ARCHIVE_ON, Z_ARCHIVE_QUEUED, Z_ARCHIVE_STATUS, Z_ASSIGNED_GROUP, Z_ASSIGNED_USER, Z_CREATED_BY, Z_CREATION_DATE, Z_DELETED_BY, Z_DELETED_FLAG, Z_DELETED_ON, Z_EDITABLE, Z_LOCKED_BY, Z_LOCKED_UNTIL, Z_ORG_ID, Z_TENANT_ID, Z_UPDATED_BY, Z_UPDATING_DATE, Z_WORKFLOW_ID, Z_WS_TOKEN, Z_DRAFT_STATUS, Z_DRAFT_ID, Z_STATUS_ID, ADDITIONALINFOKEY, ADDITIONALINFOVALUE, DISPUTE_ID) VALUES (9*********0, null, null, null, null, null, 'NBKMAKER@NBK', TIMESTAMP '2025-01-24 14:02:42.776000', null, null, null, 0, null, null, 0, 'SYSTEM', 'NBKCHECKER@NBK', TIMESTAMP '2025-01-24 14:03:02.937000', null, null, null, null, null, 'debtorEmail', '<EMAIL>', ***********7);

COMMIT;
END;