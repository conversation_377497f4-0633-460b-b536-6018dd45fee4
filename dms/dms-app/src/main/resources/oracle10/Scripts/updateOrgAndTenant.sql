DECLARE
vTenant VARCHAR(100);
vOrgId NUMBER;
BEGIN

SELECT NAME INTO vTenant FROM JFW_TENANT WHERE ID = 1;
SELECT ID INTO vOrgId FROM JFW_ORGS WHERE Z_TENANT_ID = vTenant AND PARENT_ID IS NULL AND ROWNUM = 1;

update DMS_SystemConfiguration set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_PAYMENTSYSTEMS set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update JFW_CALENDAR set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_ENDPOINTS set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_SLAConfigurationParty set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_SLAConfigAutomaticAction set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;
update DMS_SLAConfiguration set Z_ORG_ID = vOrgId, Z_TENANT_ID = vTenant;

COMMIT;
END;
