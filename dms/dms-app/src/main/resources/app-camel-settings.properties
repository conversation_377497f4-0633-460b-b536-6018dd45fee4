DefaultTenant=DMS
IIPSTransactionApiKey=DMS-secret
AppQueueBeanName=DMSAppAmq

#CRYPTO_STORE_PATH=/u01/keystore.jks
CRYPTO_STORE_PATH=keystore.jks
CRYPTO_STORE_PASS=P@ssw0rd
CRYPTO_FUNCTION=SHA256withRSA
CRYPTO_STALE_TIMEOUT_SECONDS=3000

DMS.ACTIVEMQ.URL=failover:(${env_broker_url:tcp://localhost:61616}?keepAlive=true)?randomize=false&maxReconnectAttempts=-1&jms.prefetchPolicy.all=1&timeout=10000
IIPS.ACTIVEMQ.URL=failover:(tcp://iips:61616?keepAlive=true)?randomize=false&maxReconnectAttempts=-1&jms.prefetchPolicy.all=1&timeout=10000

#**********************************SQL statements for oracle db *********************************************
calculate.the.number.of.days.script=TO_NUMBER(TO_CHAR(SYSDATE, 'DD')) -  TO_NUMBER(TO_CHAR(updatingDate, 'DD'))
#**********************************End SQL statements for oracle db *********************************************

#**********************************SQL statements for postgresSQL db *********************************************
#calculate.the.number.of.days.script=CAST(TO_CHAR(now(), 'DD') AS INTEGER) - CAST(TO_CHAR(updatingDate, 'DD') AS INTEGER)
#**********************************End SQL statements for postgresSQL db *********************************************