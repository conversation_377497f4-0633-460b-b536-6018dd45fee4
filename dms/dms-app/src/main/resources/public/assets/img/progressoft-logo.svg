<svg xmlns="http://www.w3.org/2000/svg" width="125" height="44" viewBox="0 0 125 44">
  <g id="ProgressSoft_Logo" data-name="ProgressSoft Logo" transform="translate(-2.9 -3.7)">
    <path id="Path_5" data-name="Path 5" d="M55.5,30h9.131v.655L55.5,35.736Z" transform="translate(-31.253 -15.525)" fill="#0d3f7a"/>
    <path id="Path_6" data-name="Path 6" d="M12.031,70.536H2.9V69.88l9.131-5.08Z" transform="translate(0 -36.068)" fill="#b7bdc4"/>
    <rect id="Rectangle_2" data-name="Rectangle 2" width="9.131" height="9.218" transform="translate(24.247 3.7)" fill="#0d3f7a"/>
    <path id="Path_7" data-name="Path 7" d="M29.2,56.3h1.583l-1.583.86Z" transform="translate(-15.626 -31.051)" fill="#0d3f7a"/>
    <rect id="Rectangle_3" data-name="Rectangle 3" width="9.131" height="9.218" transform="translate(2.9 14.475)" fill="#0d3f7a"/>
    <rect id="Rectangle_4" data-name="Rectangle 4" width="9.131" height="9.218" transform="translate(24.247 25.249)" fill="#b7bdc4"/>
    <rect id="Rectangle_5" data-name="Rectangle 5" width="9.131" height="9.218" transform="translate(13.574 3.7)" fill="#0d3f7a"/>
    <rect id="Rectangle_6" data-name="Rectangle 6" width="9.131" height="9.218" transform="translate(2.9 3.7)" fill="#0d3f7a"/>
    <path id="Path_8" data-name="Path 8" d="M2.9,56.3h9.131v1.721L2.9,63.1Z" transform="translate(0 -31.051)" fill="#0d3f7a"/>
    <path id="Path_9" data-name="Path 9" d="M64.632,42.7H55.5V40.98l9.131-5.08Z" transform="translate(-31.253 -19.008)" fill="#b7bdc4"/>
    <path id="Path_10" data-name="Path 10" d="M29.2,30v9.218h4.383L38.332,36.6V30Z" transform="translate(-15.626 -15.525)" fill="#0d3f7a"/>
    <path id="Path_11" data-name="Path 11" d="M33.908,56.3,29.2,58.922v6.6h9.131V56.3Z" transform="translate(-15.626 -31.051)" fill="#b7bdc4"/>
    <path id="Path_12" data-name="Path 12" d="M49.383,51.26H47.8l1.583-.86Z" transform="translate(-26.678 -27.568)" fill="#b7bdc4"/>
    <path id="Path_13" data-name="Path 13" d="M86.9,50.046a23.748,23.748,0,0,1,3.571-.246,5.528,5.528,0,0,1,3.612.983A3.437,3.437,0,0,1,95.3,53.528a3.787,3.787,0,0,1-1.055,2.827,5.447,5.447,0,0,1-3.815,1.27,2.881,2.881,0,0,1-.893-.082v4.22H86.9V50.046Zm2.638,5.408a4.744,4.744,0,0,0,.893.082c1.42,0,2.273-.737,2.273-1.926,0-1.106-.731-1.721-2.07-1.721a5.9,5.9,0,0,0-1.1.082Z" transform="translate(-49.909 -27.214)" fill="#0d3f7a"/>
    <path id="Path_14" data-name="Path 14" d="M110.081,60.532A27.11,27.11,0,0,0,110,57.7h2.273l.122,1.6h.081a2.5,2.5,0,0,1,2.313-1.8,2.472,2.472,0,0,1,.568.041v2.5a3.985,3.985,0,0,0-.731-.082A1.71,1.71,0,0,0,112.8,61.31a2.736,2.736,0,0,0-.041.615v4.384h-2.638Z" transform="translate(-63.634 -31.759)" fill="#0d3f7a"/>
    <path id="Path_15" data-name="Path 15" d="M128.324,66.572a4.2,4.2,0,0,1-4.424-4.425,4.281,4.281,0,0,1,4.586-4.547,4.163,4.163,0,0,1,4.342,4.425,4.313,4.313,0,0,1-4.5,4.547Zm.041-1.926c1.055,0,1.7-1.024,1.7-2.581,0-1.27-.487-2.581-1.7-2.581-1.258,0-1.745,1.311-1.745,2.581,0,1.475.609,2.581,1.745,2.581Z" transform="translate(-71.893 -31.818)" fill="#0d3f7a"/>
    <path id="Path_16" data-name="Path 16" d="M156.263,65.1a5.043,5.043,0,0,1-1.3,3.892,5.109,5.109,0,0,1-3.45,1.065,7.014,7.014,0,0,1-3.084-.655l.528-2.048a4.973,4.973,0,0,0,2.476.656,2.036,2.036,0,0,0,2.232-2.253V65.22h-.041a2.767,2.767,0,0,1-2.313,1.106c-2.11,0-3.612-1.721-3.612-4.179,0-2.786,1.745-4.547,3.856-4.547a2.541,2.541,0,0,1,2.354,1.229h.041l.081-1.024h2.313c-.041.574-.081,1.27-.081,2.581Zm-2.638-3.851a2.013,2.013,0,0,0-.041-.492,1.473,1.473,0,0,0-1.42-1.188c-.974,0-1.745.86-1.745,2.458,0,1.27.609,2.294,1.745,2.294a1.487,1.487,0,0,0,1.42-1.065,2.169,2.169,0,0,0,.081-.737v-1.27Z" transform="translate(-86.034 -31.818)" fill="#0d3f7a"/>
    <path id="Path_17" data-name="Path 17" d="M172.981,60.532A27.123,27.123,0,0,0,172.9,57.7h2.273l.122,1.6h.081a2.5,2.5,0,0,1,2.313-1.8,2.473,2.473,0,0,1,.568.041v2.5a3.985,3.985,0,0,0-.731-.082A1.71,1.71,0,0,0,175.7,61.31a2.739,2.739,0,0,0-.041.615v4.384h-2.719Z" transform="translate(-101.007 -31.759)" fill="#0d3f7a"/>
    <path id="Path_18" data-name="Path 18" d="M189.316,62.767c.081,1.147,1.177,1.639,2.394,1.639a7.1,7.1,0,0,0,2.354-.369l.365,1.8a7.989,7.989,0,0,1-3.084.533c-2.882,0-4.546-1.68-4.546-4.384a4.293,4.293,0,0,1,4.3-4.588c2.76,0,3.815,2.171,3.815,4.3a6.259,6.259,0,0,1-.081,1.065Zm3.125-1.8a1.586,1.586,0,0,0-1.5-1.762,1.772,1.772,0,0,0-1.664,1.762Z" transform="translate(-109.265 -31.7)" fill="#0d3f7a"/>
    <path id="Path_19" data-name="Path 19" d="M208.787,63.973a5.309,5.309,0,0,0,2.273.615c.812,0,1.136-.246,1.136-.7s-.243-.655-1.218-.983c-1.786-.574-2.476-1.557-2.435-2.581,0-1.639,1.38-2.827,3.45-2.827a6,6,0,0,1,2.394.492l-.446,1.844a4.265,4.265,0,0,0-1.867-.492c-.649,0-1.015.246-1.015.7,0,.41.325.615,1.38,1.024,1.623.533,2.273,1.393,2.313,2.622,0,1.639-1.258,2.786-3.693,2.786a5.747,5.747,0,0,1-2.76-.615Z" transform="translate(-122.04 -31.759)" fill="#0d3f7a"/>
    <path id="Path_20" data-name="Path 20" d="M226.387,63.973a5.309,5.309,0,0,0,2.273.615c.812,0,1.136-.246,1.136-.7s-.244-.655-1.258-.983c-1.786-.574-2.476-1.557-2.435-2.581,0-1.639,1.339-2.827,3.45-2.827a6,6,0,0,1,2.394.492l-.446,1.844a4.234,4.234,0,0,0-1.826-.492c-.649,0-1.015.246-1.015.7,0,.41.325.615,1.38,1.024,1.623.533,2.273,1.393,2.313,2.622,0,1.639-1.217,2.786-3.693,2.786a5.867,5.867,0,0,1-2.76-.615Z" transform="translate(-132.497 -31.759)" fill="#0d3f7a"/>
    <path id="Path_21" data-name="Path 21" d="M244.365,59.76a5.078,5.078,0,0,0,2.719.778c1.542,0,2.476-.819,2.476-2.007,0-1.106-.609-1.762-2.232-2.335-1.907-.7-3.084-1.721-3.084-3.359,0-1.844,1.5-3.237,3.815-3.237a5.551,5.551,0,0,1,2.6.574l-.406,1.27a4.116,4.116,0,0,0-2.232-.574c-1.623,0-2.232.983-2.232,1.762,0,1.106.731,1.639,2.354,2.294,1.989.778,2.963,1.721,2.963,3.482,0,1.844-1.339,3.441-4.1,3.441a6.235,6.235,0,0,1-3-.778Z" transform="translate(-143.251 -27.096)" fill="#0d3f7a"/>
    <path id="Path_22" data-name="Path 22" d="M267.958,66.69a4.044,4.044,0,0,1-4.058-4.384c0-2.868,1.867-4.507,4.18-4.507,2.435,0,4.058,1.762,4.058,4.384a4.12,4.12,0,0,1-4.18,4.507Zm.081-1.188c1.461,0,2.557-1.393,2.557-3.318,0-1.434-.731-3.237-2.516-3.237s-2.6,1.68-2.6,3.318c0,1.844,1.055,3.237,2.557,3.237Z" transform="translate(-155.075 -31.936)" fill="#0d3f7a"/>
    <path id="Path_23" data-name="Path 23" d="M286.577,60.7V53.367H285.4V52.179h1.177v-.41a4.061,4.061,0,0,1,1.015-2.991A2.825,2.825,0,0,1,289.621,48a3.318,3.318,0,0,1,1.3.246l-.2,1.188a2.1,2.1,0,0,0-1.015-.2c-1.3,0-1.623,1.147-1.623,2.458v.451h2.029v1.188h-2.029V60.7Z" transform="translate(-167.849 -26.151)" fill="#0d3f7a"/>
    <path id="Path_24" data-name="Path 24" d="M300.341,53.2v2.048h2.192v1.188h-2.192v4.588c0,1.065.284,1.639,1.136,1.639a2.6,2.6,0,0,0,.893-.123l.081,1.188a3.764,3.764,0,0,1-1.38.2,2.155,2.155,0,0,1-1.664-.656,3.466,3.466,0,0,1-.609-2.253V56.437h-1.3V55.248h1.3V53.692Z" transform="translate(-175.038 -29.221)" fill="#0d3f7a"/>
    <path id="Path_25" data-name="Path 25" d="M5.477,101.315,4.3,97.3h.69L5.6,99.594l.244.86c0-.041.081-.328.2-.819L6.654,97.3h.69l.609,2.335.2.778.244-.778.69-2.294h.649L8.48,101.356H7.79L7.181,98.98l-.162-.7-.812,3.073H5.477Z" transform="translate(-0.832 -55.254)" fill="#0d3f7a"/>
    <path id="Path_26" data-name="Path 26" d="M26.077,101.315,24.9,97.3h.69l.609,2.294.244.86c0-.041.081-.328.2-.819l.609-2.335h.69l.609,2.335.2.778L29,99.635l.69-2.294h.649l-1.258,4.015h-.69l-.609-2.376-.162-.7-.812,3.073h-.731Z" transform="translate(-13.071 -55.254)" fill="#0d3f7a"/>
    <path id="Path_27" data-name="Path 27" d="M46.677,101.315,45.5,97.3h.69l.609,2.294.244.86c0-.041.081-.328.2-.819l.609-2.335h.69l.609,2.335.2.778.243-.778.69-2.294h.649l-1.258,4.015h-.69l-.609-2.376-.162-.7-.812,3.073h-.731Z" transform="translate(-25.311 -55.254)" fill="#0d3f7a"/>
    <path id="Path_28" data-name="Path 28" d="M67.8,105.978V105.2h.771v.778Z" transform="translate(-38.561 -59.917)" fill="#0d3f7a"/>
    <path id="Path_29" data-name="Path 29" d="M79.6,102.713V97.182h.609v.533a1.63,1.63,0,0,1,.487-.451,1.518,1.518,0,0,1,.649-.164,1.625,1.625,0,0,1,.893.246,1.5,1.5,0,0,1,.568.737,2.925,2.925,0,0,1,.2,1.065,3.154,3.154,0,0,1-.2,1.106,1.648,1.648,0,0,1-.649.737,1.725,1.725,0,0,1-.893.246,1.049,1.049,0,0,1-.609-.164,1.271,1.271,0,0,1-.446-.369v1.925H79.6Zm.609-3.482a1.753,1.753,0,0,0,.325,1.147.919.919,0,0,0,.731.369,1,1,0,0,0,.771-.369,1.882,1.882,0,0,0,.325-1.188,1.834,1.834,0,0,0-.325-1.147.919.919,0,0,0-.731-.369.978.978,0,0,0-.771.41A1.753,1.753,0,0,0,80.209,99.23Z" transform="translate(-45.572 -55.136)" fill="#0d3f7a"/>
    <path id="Path_30" data-name="Path 30" d="M97.1,101.2V97.182h.609V97.8a2.611,2.611,0,0,1,.406-.574.812.812,0,0,1,.446-.123,1.485,1.485,0,0,1,.69.2l-.243.615a1.072,1.072,0,0,0-.487-.123.681.681,0,0,0-.406.123.6.6,0,0,0-.244.369,2.406,2.406,0,0,0-.122.778v2.089H97.1Z" transform="translate(-55.97 -55.136)" fill="#0d3f7a"/>
    <path id="Path_31" data-name="Path 31" d="M109.9,99.189a2.083,2.083,0,0,1,.609-1.639,1.892,1.892,0,0,1,1.258-.451,1.8,1.8,0,0,1,1.339.533,1.992,1.992,0,0,1,.528,1.475,2.64,2.64,0,0,1-.244,1.229,1.781,1.781,0,0,1-.649.7,1.914,1.914,0,0,1-.974.246,1.679,1.679,0,0,1-1.339-.533A2.067,2.067,0,0,1,109.9,99.189Zm.69,0a1.834,1.834,0,0,0,.325,1.147,1.044,1.044,0,0,0,.852.369,1.018,1.018,0,0,0,.812-.369,1.882,1.882,0,0,0,.325-1.188,1.706,1.706,0,0,0-.325-1.106,1.018,1.018,0,0,0-.812-.369,1.163,1.163,0,0,0-.852.369A1.612,1.612,0,0,0,110.59,99.189Z" transform="translate(-63.575 -55.136)" fill="#0d3f7a"/>
    <path id="Path_32" data-name="Path 32" d="M127.581,101.584l.649.082a.691.691,0,0,0,.243.451,1.153,1.153,0,0,0,.69.2,1.2,1.2,0,0,0,.731-.2,1.04,1.04,0,0,0,.365-.533,4.071,4.071,0,0,0,.041-.86,1.3,1.3,0,0,1-1.1.533,1.54,1.54,0,0,1-1.258-.574,2.407,2.407,0,0,1-.446-1.434,3.1,3.1,0,0,1,.2-1.065,1.371,1.371,0,0,1,.609-.737,1.669,1.669,0,0,1,.933-.246,1.5,1.5,0,0,1,1.177.574v-.492h.609v3.441a3.228,3.228,0,0,1-.2,1.311,1.471,1.471,0,0,1-.609.615,1.962,1.962,0,0,1-1.015.246,1.876,1.876,0,0,1-1.136-.328A1.078,1.078,0,0,1,127.581,101.584Zm.568-2.417a1.753,1.753,0,0,0,.325,1.147.991.991,0,0,0,1.542,0,1.753,1.753,0,0,0,.325-1.147,1.706,1.706,0,0,0-.325-1.106.991.991,0,0,0-1.542,0A1.631,1.631,0,0,0,128.149,99.167Z" transform="translate(-74.032 -55.195)" fill="#0d3f7a"/>
    <path id="Path_33" data-name="Path 33" d="M145.5,101.2V97.182h.609V97.8a2.613,2.613,0,0,1,.406-.574.812.812,0,0,1,.446-.123,1.485,1.485,0,0,1,.69.2l-.243.615a1.072,1.072,0,0,0-.487-.123.681.681,0,0,0-.406.123.6.6,0,0,0-.243.369,2.4,2.4,0,0,0-.122.778v2.089H145.5Z" transform="translate(-84.727 -55.136)" fill="#0d3f7a"/>
    <path id="Path_34" data-name="Path 34" d="M161.163,99.827l.69.082a1.737,1.737,0,0,1-.609.942,1.706,1.706,0,0,1-1.136.328,1.767,1.767,0,0,1-1.38-.533,2.205,2.205,0,0,1-.528-1.516,2.129,2.129,0,0,1,.528-1.557A1.706,1.706,0,0,1,160.067,97a1.648,1.648,0,0,1,1.3.533,2.191,2.191,0,0,1,.487,1.516v.164h-2.922a1.8,1.8,0,0,0,.365,1.024,1.018,1.018,0,0,0,.812.369,1.218,1.218,0,0,0,.649-.2C160.919,100.359,161.081,100.114,161.163,99.827Zm-2.192-1.065H161.2a1.566,1.566,0,0,0-.244-.737,1.075,1.075,0,0,0-.852-.41,1.034,1.034,0,0,0-.771.328A.98.98,0,0,0,158.971,98.762Z" transform="translate(-92.272 -55.077)" fill="#0d3f7a"/>
    <path id="Path_35" data-name="Path 35" d="M175.7,99.95l.649-.123a.769.769,0,0,0,.325.615,1.153,1.153,0,0,0,.69.2,1.068,1.068,0,0,0,.69-.2.524.524,0,0,0,.2-.451.431.431,0,0,0-.2-.369c-.081-.041-.325-.123-.69-.246a7.534,7.534,0,0,1-1.015-.328.994.994,0,0,1-.446-.369,1.17,1.17,0,0,1-.162-.533,1.1,1.1,0,0,1,.122-.492.964.964,0,0,1,.325-.369,1.137,1.137,0,0,1,.446-.2A2.374,2.374,0,0,1,177.2,97a2.419,2.419,0,0,1,.812.123,1.031,1.031,0,0,1,.528.369,1.4,1.4,0,0,1,.244.615l-.649.082a.938.938,0,0,0-.243-.492.837.837,0,0,0-.609-.164.888.888,0,0,0-.649.164.431.431,0,0,0-.2.369.586.586,0,0,0,.081.246.373.373,0,0,0,.244.164,3.754,3.754,0,0,0,.568.164,5.154,5.154,0,0,1,.974.328.994.994,0,0,1,.446.369.872.872,0,0,1,.162.574,1.246,1.246,0,0,1-.2.655,1.051,1.051,0,0,1-.568.451,1.867,1.867,0,0,1-.852.164,1.922,1.922,0,0,1-1.177-.328A1.857,1.857,0,0,1,175.7,99.95Z" transform="translate(-102.67 -55.077)" fill="#0d3f7a"/>
    <path id="Path_36" data-name="Path 36" d="M192.2,99.95l.649-.123a.769.769,0,0,0,.325.615,1.2,1.2,0,0,0,.731.2,1.068,1.068,0,0,0,.69-.2.568.568,0,0,0,.243-.451.431.431,0,0,0-.2-.369c-.081-.041-.325-.123-.69-.246a7.536,7.536,0,0,1-1.015-.328.994.994,0,0,1-.446-.369,1.17,1.17,0,0,1-.162-.533,1.1,1.1,0,0,1,.122-.492.964.964,0,0,1,.325-.369,1.265,1.265,0,0,1,.406-.2,2.375,2.375,0,0,1,.568-.082,2.419,2.419,0,0,1,.812.123,1.031,1.031,0,0,1,.528.369,1.4,1.4,0,0,1,.243.615l-.649.082a.937.937,0,0,0-.244-.492.837.837,0,0,0-.609-.164.889.889,0,0,0-.649.164.431.431,0,0,0-.2.369.586.586,0,0,0,.081.246.373.373,0,0,0,.244.164,3.43,3.43,0,0,0,.609.164,5.154,5.154,0,0,1,.974.328.994.994,0,0,1,.446.369.872.872,0,0,1,.162.574,1.246,1.246,0,0,1-.2.655,1.051,1.051,0,0,1-.568.451,1.8,1.8,0,0,1-.812.164,1.922,1.922,0,0,1-1.177-.328A1.264,1.264,0,0,1,192.2,99.95Z" transform="translate(-112.474 -55.077)" fill="#0d3f7a"/>
    <path id="Path_37" data-name="Path 37" d="M208.7,99.189a2.084,2.084,0,0,1,.609-1.639,1.893,1.893,0,0,1,1.258-.451,1.8,1.8,0,0,1,1.339.533,1.992,1.992,0,0,1,.528,1.475,2.64,2.64,0,0,1-.243,1.229,1.781,1.781,0,0,1-.649.7,1.914,1.914,0,0,1-.974.246,1.679,1.679,0,0,1-1.339-.533A2.067,2.067,0,0,1,208.7,99.189Zm.69,0a1.834,1.834,0,0,0,.325,1.147,1.044,1.044,0,0,0,.852.369,1.018,1.018,0,0,0,.812-.369,1.882,1.882,0,0,0,.325-1.188,1.706,1.706,0,0,0-.325-1.106,1.018,1.018,0,0,0-.812-.369,1.163,1.163,0,0,0-.852.369A1.612,1.612,0,0,0,209.39,99.189Z" transform="translate(-122.277 -55.136)" fill="#0d3f7a"/>
    <path id="Path_38" data-name="Path 38" d="M226.309,99.013V95.53H225.7V95h.609v-.41a2.5,2.5,0,0,1,.081-.615.817.817,0,0,1,.325-.41,1.153,1.153,0,0,1,.69-.164,3.106,3.106,0,0,1,.609.082l-.081.574a1.368,1.368,0,0,0-.406-.041.648.648,0,0,0-.446.123.717.717,0,0,0-.122.492V95h.771v.533h-.771v3.482Z" transform="translate(-132.378 -52.951)" fill="#0d3f7a"/>
    <path id="Path_39" data-name="Path 39" d="M240.026,98.634l.081.615a2.309,2.309,0,0,1-.487.041,1.067,1.067,0,0,1-.568-.123.567.567,0,0,1-.284-.328,3.089,3.089,0,0,1-.081-.819V95.726H238.2v-.533h.487V94.21l.69-.41v1.393h.69v.533h-.731v2.335c0,.2,0,.328.041.369l.122.123c.041.041.122.041.243.041A.239.239,0,0,0,240.026,98.634Z" transform="translate(-139.805 -53.188)" fill="#0d3f7a"/>
    <path id="Path_40" data-name="Path 40" d="M251.9,105.978V105.2h.771v.778Z" transform="translate(-147.945 -59.917)" fill="#0d3f7a"/>
    <path id="Path_41" data-name="Path 41" d="M265.9,99.663l.649.082a1.6,1.6,0,0,1-1.664,1.434,1.648,1.648,0,0,1-1.3-.533,2.19,2.19,0,0,1-.487-1.516,3.213,3.213,0,0,1,.2-1.147,1.441,1.441,0,0,1,.649-.737,1.871,1.871,0,0,1,.933-.246,1.709,1.709,0,0,1,1.055.328,1.451,1.451,0,0,1,.528.942l-.649.082a1.264,1.264,0,0,0-.325-.615.875.875,0,0,0-.568-.2,1.018,1.018,0,0,0-.812.369,1.753,1.753,0,0,0-.325,1.147,1.9,1.9,0,0,0,.284,1.188.939.939,0,0,0,.771.369.917.917,0,0,0,.649-.246C265.738,100.278,265.86,99.991,265.9,99.663Z" transform="translate(-154.599 -55.077)" fill="#0d3f7a"/>
    <path id="Path_42" data-name="Path 42" d="M279.5,99.189a2.083,2.083,0,0,1,.609-1.639,1.892,1.892,0,0,1,1.258-.451,1.8,1.8,0,0,1,1.339.533,1.992,1.992,0,0,1,.528,1.475,2.64,2.64,0,0,1-.243,1.229,1.781,1.781,0,0,1-.649.7,1.914,1.914,0,0,1-.974.246,1.679,1.679,0,0,1-1.339-.533A2.067,2.067,0,0,1,279.5,99.189Zm.69,0a1.834,1.834,0,0,0,.325,1.147,1.044,1.044,0,0,0,.852.369,1.018,1.018,0,0,0,.812-.369,1.882,1.882,0,0,0,.325-1.188,1.707,1.707,0,0,0-.325-1.106,1.018,1.018,0,0,0-.812-.369,1.163,1.163,0,0,0-.852.369A1.834,1.834,0,0,0,280.19,99.189Z" transform="translate(-164.344 -55.136)" fill="#0d3f7a"/>
    <path id="Path_43" data-name="Path 43" d="M297.7,101.2V97.182h.609v.574a1.406,1.406,0,0,1,.487-.492,1.324,1.324,0,0,1,.69-.164,1.391,1.391,0,0,1,.73.2.9.9,0,0,1,.406.533,1.476,1.476,0,0,1,1.218-.7,1.19,1.19,0,0,1,.893.328,1.468,1.468,0,0,1,.325,1.024v2.745h-.69V98.7a1.713,1.713,0,0,0-.081-.574.549.549,0,0,0-.244-.287.681.681,0,0,0-.406-.123.832.832,0,0,0-.69.287,1.2,1.2,0,0,0-.284.9v2.335h-.69V98.657a1.181,1.181,0,0,0-.162-.7.6.6,0,0,0-.528-.246.811.811,0,0,0-.528.164.823.823,0,0,0-.365.451,2.171,2.171,0,0,0-.122.819v2.089H297.7Z" transform="translate(-175.157 -55.136)" fill="#0d3f7a"/>
  </g>
</svg>
