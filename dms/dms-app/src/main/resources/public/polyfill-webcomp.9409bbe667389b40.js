(function(){"use strict";var y;function nr(t){var e=0;return function(){return e<t.length?{done:!1,value:t[e++]}:{done:!0}}}function Me(t){var e=typeof Symbol<"u"&&Symbol.iterator&&t[Symbol.iterator];return e?e.call(t):{next:nr(t)}}function rr(t){for(var e,n=[];!(e=t.next()).done;)n.push(e.value);return n}var st=typeof window<"u"&&window===this?this:typeof global<"u"&&null!=global?global:this,or="function"==typeof Object.defineProperties?Object.defineProperty:function(t,e,n){t!=Array.prototype&&t!=Object.prototype&&(t[e]=n.value)};function ir(){ir=function(){},st.Symbol||(st.Symbol=Ri)}function sr(t,e){this.a=t,or(this,"description",{configurable:!0,writable:!0,value:e})}sr.prototype.toString=function(){return this.a};var e,Pe,Ri=(e=0,function t(n){if(this instanceof t)throw new TypeError("Symbol is not a constructor");return new sr("jscomp_symbol_"+(n||"")+"_"+e++,n)});function Le(){ir();var t=st.Symbol.iterator;t||(t=st.Symbol.iterator=st.Symbol("Symbol.iterator")),"function"!=typeof Array.prototype[t]&&or(Array.prototype,t,{configurable:!0,writable:!0,value:function(){return function Fi(t){return Le(),(t={next:t})[st.Symbol.iterator]=function(){return this},t}(nr(this))}}),Le=function(){}}if("function"==typeof Object.setPrototypeOf)Pe=Object.setPrototypeOf;else{var Ae;t:{var ar={};try{ar.__proto__={Fa:!0},Ae=ar.Fa;break t}catch{}Ae=!1}Pe=Ae?function(t,e){if(t.__proto__=e,t.__proto__!==e)throw new TypeError(t+" is not extensible");return t}:null}var lr=Pe;function je(){this.f=!1,this.b=null,this.U=void 0,this.a=1,this.F=0,this.c=null}function Re(t){if(t.f)throw new TypeError("Generator is already running");t.f=!0}function Fe(t,e){t.c={Ia:e,Ma:!0},t.a=t.F}function He(t,e){return t.a=3,{value:e}}function Ii(t){this.a=new je,this.b=t}function Ie(t,e,n,r){try{var o=e.call(t.a.b,n);if(!(o instanceof Object))throw new TypeError("Iterator result "+o+" is not an object");if(!o.done)return t.a.f=!1,o;var i=o.value}catch(s){return t.a.b=null,Fe(t.a,s),Rt(t)}return t.a.b=null,r.call(t.a,i),Rt(t)}function Rt(t){for(;t.a.a;)try{var e=t.b(t.a);if(e)return t.a.f=!1,{value:e.value,done:!1}}catch(n){t.a.U=void 0,Fe(t.a,n)}if(t.a.f=!1,t.a.c){if(e=t.a.c,t.a.c=null,e.Ma)throw e.Ia;return{value:e.return,done:!0}}return{value:void 0,done:!0}}function ki(t){this.next=function(e){return Re(t.a),t.a.b?e=Ie(t,t.a.b.next,e,t.a.u):(t.a.u(e),e=Rt(t)),e},this.throw=function(e){return Re(t.a),t.a.b?e=Ie(t,t.a.b.throw,e,t.a.u):(Fe(t.a,e),e=Rt(t)),e},this.return=function(e){return function Ui(t,e){Re(t.a);var n=t.a.b;return n?Ie(t,"return"in n?n.return:function(r){return{value:r,done:!0}},e,t.a.return):(t.a.return(e),Rt(t))}(t,e)},Le(),this[Symbol.iterator]=function(){return this}}function Ue(t,e){return e=new ki(new Ii(e)),lr&&lr(e,t.prototype),e}je.prototype.u=function(t){this.U=t},je.prototype.return=function(t){this.c={return:t},this.a=this.F},Array.from||(Array.from=function(t){return[].slice.call(t)}),Object.assign||(Object.assign=function(t){for(var r,e=[].slice.call(arguments,1),n=0;n<e.length;n++)if(r=e[n])for(var o=t,i=r,s=Object.getOwnPropertyNames(i),a=0;a<s.length;a++)o[r=s[a]]=i[r];return t}),function(){if((i=document.createEvent("Event")).initEvent("foo",!0,!0),i.preventDefault(),!i.defaultPrevented){var t=Event.prototype.preventDefault;Event.prototype.preventDefault=function(){this.cancelable&&(t.call(this),Object.defineProperty(this,"defaultPrevented",{get:function(){return!0},configurable:!0}))}}var i,e=/Trident/.test(navigator.userAgent);if(!window.Event||e&&"function"!=typeof window.Event){var n=window.Event;if(window.Event=function(i,s){s=s||{};var a=document.createEvent("Event");return a.initEvent(i,!!s.bubbles,!!s.cancelable),a},n){for(var r in n)window.Event[r]=n[r];window.Event.prototype=n.prototype}}if((!window.CustomEvent||e&&"function"!=typeof window.CustomEvent)&&(window.CustomEvent=function(i,s){s=s||{};var a=document.createEvent("CustomEvent");return a.initCustomEvent(i,!!s.bubbles,!!s.cancelable,s.detail),a},window.CustomEvent.prototype=window.Event.prototype),!window.MouseEvent||e&&"function"!=typeof window.MouseEvent){if(e=window.MouseEvent,window.MouseEvent=function(i,s){s=s||{};var a=document.createEvent("MouseEvent");return a.initMouseEvent(i,!!s.bubbles,!!s.cancelable,s.view||window,s.detail,s.screenX,s.screenY,s.clientX,s.clientY,s.ctrlKey,s.altKey,s.shiftKey,s.metaKey,s.button,s.relatedTarget),a},e)for(var o in e)window.MouseEvent[o]=e[o];window.MouseEvent.prototype=e.prototype}}(),function(){function t(){}function e(d,_){if(!d.childNodes.length)return[];switch(d.nodeType){case Node.DOCUMENT_NODE:return O.call(d,_);case Node.DOCUMENT_FRAGMENT_NODE:return T.call(d,_);default:return p.call(d,_)}}var n=typeof HTMLTemplateElement>"u",r=!(document.createDocumentFragment().cloneNode()instanceof DocumentFragment),o=!1;/Trident/.test(navigator.userAgent)&&function(){function d(N,k){if(N instanceof DocumentFragment)for(var De;De=N.firstChild;)b.call(this,De,k);else b.call(this,N,k);return N}o=!0;var _=Node.prototype.cloneNode;Node.prototype.cloneNode=function(N){return N=_.call(this,N),this instanceof DocumentFragment&&(N.__proto__=DocumentFragment.prototype),N},DocumentFragment.prototype.querySelectorAll=HTMLElement.prototype.querySelectorAll,DocumentFragment.prototype.querySelector=HTMLElement.prototype.querySelector,Object.defineProperties(DocumentFragment.prototype,{nodeType:{get:function(){return Node.DOCUMENT_FRAGMENT_NODE},configurable:!0},localName:{get:function(){},configurable:!0},nodeName:{get:function(){return"#document-fragment"},configurable:!0}});var b=Node.prototype.insertBefore;Node.prototype.insertBefore=d;var P=Node.prototype.appendChild;Node.prototype.appendChild=function(N){return N instanceof DocumentFragment?d.call(this,N,null):P.call(this,N),N};var q=Node.prototype.removeChild,it=Node.prototype.replaceChild;Node.prototype.replaceChild=function(N,k){return N instanceof DocumentFragment?(d.call(this,N,k),q.call(this,k)):it.call(this,N,k),k},Document.prototype.createDocumentFragment=function(){var N=this.createElement("df");return N.__proto__=DocumentFragment.prototype,N};var ft=Document.prototype.importNode;Document.prototype.importNode=function(N,k){return k=ft.call(this,N,k||!1),N instanceof DocumentFragment&&(k.__proto__=DocumentFragment.prototype),k}}();var i=Node.prototype.cloneNode,s=Document.prototype.createElement,a=Document.prototype.importNode,l=Node.prototype.removeChild,u=Node.prototype.appendChild,c=Node.prototype.replaceChild,f=DOMParser.prototype.parseFromString,x=Object.getOwnPropertyDescriptor(window.HTMLElement.prototype,"innerHTML")||{get:function(){return this.innerHTML},set:function(d){this.innerHTML=d}},S=Object.getOwnPropertyDescriptor(window.Node.prototype,"childNodes")||{get:function(){return this.childNodes}},p=Element.prototype.querySelectorAll,O=Document.prototype.querySelectorAll,T=DocumentFragment.prototype.querySelectorAll,F=function(){if(!n){var d=document.createElement("template"),_=document.createElement("template");return _.content.appendChild(document.createElement("div")),d.content.appendChild(_),0===(d=d.cloneNode(!0)).content.childNodes.length||0===d.content.firstChild.content.childNodes.length||r}}();if(n){var w=document.implementation.createHTMLDocument("template"),Z=!0,m=document.createElement("style");m.textContent="template{display:none;}";var ht=document.head;ht.insertBefore(m,ht.firstElementChild),t.prototype=Object.create(HTMLElement.prototype);var nt=!document.createElement("div").hasOwnProperty("innerHTML");t.S=function(d){if(!d.content&&d.namespaceURI===document.documentElement.namespaceURI){d.content=w.createDocumentFragment();for(var _;_=d.firstChild;)u.call(d.content,_);if(nt)d.__proto__=t.prototype;else if(d.cloneNode=function(b){return t.b(this,b)},Z)try{h(d),M(d)}catch{Z=!1}t.a(d.content)}};var dt={option:["select"],thead:["table"],col:["colgroup","table"],tr:["tbody","table"],th:["tr","tbody","table"],td:["tr","tbody","table"]},h=function(d){Object.defineProperty(d,"innerHTML",{get:function(){return wt(this)},set:function(_){var b=dt[(/<([a-z][^/\0>\x20\t\r\n\f]+)/i.exec(_)||["",""])[1].toLowerCase()];if(b)for(var P=0;P<b.length;P++)_="<"+b[P]+">"+_+"</"+b[P]+">";for(w.body.innerHTML=_,t.a(w);this.content.firstChild;)l.call(this.content,this.content.firstChild);if(_=w.body,b)for(P=0;P<b.length;P++)_=_.lastChild;for(;_.firstChild;)u.call(this.content,_.firstChild)},configurable:!0})},M=function(d){Object.defineProperty(d,"outerHTML",{get:function(){return"<template>"+this.innerHTML+"</template>"},set:function(_){if(!this.parentNode)throw Error("Failed to set the 'outerHTML' property on 'Element': This element has no parent node.");for(w.body.innerHTML=_,_=this.ownerDocument.createDocumentFragment();w.body.firstChild;)u.call(_,w.body.firstChild);c.call(this.parentNode,_,this)},configurable:!0})};h(t.prototype),M(t.prototype),t.a=function(d){for(var P,_=0,b=(d=e(d,"template")).length;_<b&&(P=d[_]);_++)t.S(P)},document.addEventListener("DOMContentLoaded",function(){t.a(document)}),Document.prototype.createElement=function(){var d=s.apply(this,arguments);return"template"===d.localName&&t.S(d),d},DOMParser.prototype.parseFromString=function(){var d=f.apply(this,arguments);return t.a(d),d},Object.defineProperty(HTMLElement.prototype,"innerHTML",{get:function(){return wt(this)},set:function(d){x.set.call(this,d),t.a(this)},configurable:!0,enumerable:!0});var ot=/[&\u00A0"]/g,tr=/[&\u00A0<>]/g,be=function(d){switch(d){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";case"\xa0":return"&nbsp;"}},Xt=(m=function(d){for(var _={},b=0;b<d.length;b++)_[d[b]]=!0;return _})("area base br col command embed hr img input keygen link meta param source track wbr".split(" ")),Oe=m("style script xmp iframe noembed noframes plaintext noscript".split(" ")),wt=function(d,_){"template"===d.localName&&(d=d.content);for(var ft,b="",P=_?_(d):S.get.call(d),q=0,it=P.length;q<it&&(ft=P[q]);q++){t:{var N=ft,k=d,De=_;switch(N.nodeType){case Node.ELEMENT_NODE:for(var er=N.localName,xe="<"+er,Aa=N.attributes,ji=0;k=Aa[ji];ji++)xe+=" "+k.name+'="'+k.value.replace(ot,be)+'"';xe+=">",N=Xt[er]?xe:xe+wt(N,De)+"</"+er+">";break t;case Node.TEXT_NODE:N=N.data,N=k&&Oe[k.localName]?N:N.replace(tr,be);break t;case Node.COMMENT_NODE:N="\x3c!--"+N.data+"--\x3e";break t;default:throw window.console.error(N),Error("not implemented")}}b+=N}return b}}if(n||F){t.b=function(d,_){var b=i.call(d,!1);return this.S&&this.S(b),_&&(u.call(b.content,i.call(d.content,!0)),v(b.content,d.content)),b};var v=function(d,_){if(_.querySelectorAll&&0!==(_=e(_,"template")).length)for(var q,it,b=0,P=(d=e(d,"template")).length;b<P;b++)it=_[b],q=d[b],t&&t.S&&t.S(it),c.call(q.parentNode,D.call(it,!0),q)},D=Node.prototype.cloneNode=function(d){if(!o&&r&&this instanceof DocumentFragment){if(!d)return this.ownerDocument.createDocumentFragment();var _=L.call(this.ownerDocument,this,!0)}else _=this.nodeType===Node.ELEMENT_NODE&&"template"===this.localName&&this.namespaceURI==document.documentElement.namespaceURI?t.b(this,d):i.call(this,d);return d&&v(_,this),_},L=Document.prototype.importNode=function(d,_){if(_=_||!1,"template"===d.localName)return t.b(d,_);var b=a.call(this,d,_);if(_){v(b,d),d=e(b,'script:not([type]),script[type="application/javascript"],script[type="text/javascript"]');for(var P,q=0;q<d.length;q++){P=d[q],(_=s.call(document,"script")).textContent=P.textContent;for(var N,it=P.attributes,ft=0;ft<it.length;ft++)_.setAttribute((N=it[ft]).name,N.value);c.call(P.parentNode,_,P)}}return b}}n&&(window.HTMLTemplateElement=t)}();var Bi=setTimeout;function Vi(){}function B(t){if(!(this instanceof B))throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this.K=0,this.pa=!1,this.w=void 0,this.V=[],cr(t,this)}function ur(t,e){for(;3===t.K;)t=t.w;0===t.K?t.V.push(e):(t.pa=!0,Ve(function(){var n=1===t.K?e.Oa:e.Pa;if(null===n)(1===t.K?ke:Ft)(e.na,t.w);else{try{var r=n(t.w)}catch(o){return void Ft(e.na,o)}ke(e.na,r)}}))}function ke(t,e){try{if(e===t)throw new TypeError("A promise cannot be resolved with itself.");if(e&&("object"==typeof e||"function"==typeof e)){var n=e.then;if(e instanceof B)return t.K=3,t.w=e,void Be(t);if("function"==typeof n)return void cr(function Wi(t,e){return function(){t.apply(e,arguments)}}(n,e),t)}t.K=1,t.w=e,Be(t)}catch(r){Ft(t,r)}}function Ft(t,e){t.K=2,t.w=e,Be(t)}function Be(t){2===t.K&&0===t.V.length&&Ve(function(){t.pa||typeof console<"u"&&console&&console.warn("Possible Unhandled Promise Rejection:",t.w)});for(var e=0,n=t.V.length;e<n;e++)ur(t,t.V[e]);t.V=null}function Gi(t,e,n){this.Oa="function"==typeof t?t:null,this.Pa="function"==typeof e?e:null,this.na=n}function cr(t,e){var n=!1;try{t(function(r){n||(n=!0,ke(e,r))},function(r){n||(n=!0,Ft(e,r))})}catch(r){n||(n=!0,Ft(e,r))}}B.prototype.catch=function(t){return this.then(null,t)},B.prototype.then=function(t,e){var n=new this.constructor(Vi);return ur(this,new Gi(t,e,n)),n},B.prototype.finally=function(t){var e=this.constructor;return this.then(function(n){return e.resolve(t()).then(function(){return n})},function(n){return e.resolve(t()).then(function(){return e.reject(n)})})};var Ve="function"==typeof setImmediate&&function(t){setImmediate(t)}||function(t){Bi(t,0)};if(!window.Promise){window.Promise=B,B.prototype.then=B.prototype.then,B.all=function $i(t){return new B(function(e,n){function r(a,l){try{if(l&&("object"==typeof l||"function"==typeof l)){var u=l.then;if("function"==typeof u)return void u.call(l,function(c){r(a,c)},n)}o[a]=l,0==--i&&e(o)}catch(c){n(c)}}if(!t||typeof t.length>"u")throw new TypeError("Promise.all accepts an array");var o=Array.prototype.slice.call(t);if(0===o.length)return e([]);for(var i=o.length,s=0;s<o.length;s++)r(s,o[s])})},B.race=function Zi(t){return new B(function(e,n){for(var r=0,o=t.length;r<o;r++)t[r].then(e,n)})},B.resolve=function Ji(t){return t&&"object"==typeof t&&t.constructor===B?t:new B(function(e){e(t)})},B.reject=function Xi(t){return new B(function(e,n){n(t)})};var We=document.createTextNode(""),Zt=[];new MutationObserver(function(){for(var t=Zt.length,e=0;e<t;e++)Zt[e]();Zt.splice(0,t)}).observe(We,{characterData:!0}),Ve=function(t){Zt.push(t),We.textContent=0<We.textContent.length?"":"a"}}(function(t,e){if(!(e in t)){var n=typeof global==typeof n?window:global,r=0,o=""+Math.random(),i="__\x01symbol@@"+o,s=t.getOwnPropertyNames,a=t.getOwnPropertyDescriptor,l=t.create,u=t.keys,c=t.freeze||t,f=t.defineProperty,x=t.defineProperties,S=a(t,"getOwnPropertyNames"),p=t.prototype,O=p.hasOwnProperty,T=p.propertyIsEnumerable,F=p.toString,w=function(v,D,L){O.call(v,i)||f(v,i,{enumerable:!1,configurable:!1,writable:!1,value:{}}),v[i]["@@"+D]=L},Z=function(v,D){var L=l(v);return s(D).forEach(function(d){dt.call(D,d)&&Xt(L,d,D[d])}),L},m=function(){},ht=function(v){return v!=i&&!O.call(ot,v)},nt=function(v){return v!=i&&O.call(ot,v)},dt=function(v){var D=""+v;return nt(D)?O.call(this,D)&&this[i]["@@"+D]:T.call(this,v)},h=function(v){return f(p,v,{enumerable:!1,configurable:!0,get:m,set:function(D){wt(this,v,{enumerable:!1,configurable:!0,writable:!0,value:D}),w(this,v,!0)}}),c(ot[v]=f(t(v),"constructor",tr))},M=function(v){if(this&&this!==n)throw new TypeError("Symbol is not a constructor");return h("__\x01symbol:".concat(v||"",o,++r))},ot=l(null),tr={value:M},be=function(v){return ot[v]},Xt=function(v,D,L){var d=""+D;if(nt(d)){if(D=wt,L.enumerable){var _=l(L);_.enumerable=!1}else _=L;D(v,d,_),w(v,d,!!L.enumerable)}else f(v,D,L);return v},Oe=function(v){return s(v).filter(nt).map(be)};S.value=Xt,f(t,"defineProperty",S),S.value=Oe,f(t,e,S),S.value=function(v){return s(v).filter(ht)},f(t,"getOwnPropertyNames",S),S.value=function(v,D){var L=Oe(D);return L.length?u(D).concat(L).forEach(function(d){dt.call(D,d)&&Xt(v,d,D[d])}):x(v,D),v},f(t,"defineProperties",S),S.value=dt,f(p,"propertyIsEnumerable",S),S.value=M,f(n,"Symbol",S),S.value=function(v){return(v="__\x01symbol:".concat("__\x01symbol:",v,o))in p?ot[v]:h(v)},f(M,"for",S),S.value=function(v){if(ht(v))throw new TypeError(v+" is not a symbol");return O.call(ot,v)?v.slice(20,-o.length):void 0},f(M,"keyFor",S),S.value=function(v,D){var L=a(v,D);return L&&nt(D)&&(L.enumerable=dt.call(v,D)),L},f(t,"getOwnPropertyDescriptor",S),S.value=function(v,D){return 1===arguments.length?l(v):Z(v,D)},f(t,"create",S),S.value=function(){var v=F.call(this);return"[object String]"===v&&nt(this)?"[object Symbol]":v},f(p,"toString",S);try{var wt=l(f({},"__\x01symbol:",{get:function(){return f(this,"__\x01symbol:",{value:!1})["__\x01symbol:"]}}))["__\x01symbol:"]||f}catch{wt=function(D,L,d){var _=a(p,L);delete p[L],f(D,L,d),f(p,L,_)}}}})(Object,"getOwnPropertySymbols"),function(t){var o,e=t.defineProperty,n=t.prototype,r=n.toString;"iterator match replace search split hasInstance isConcatSpreadable unscopables species toPrimitive toStringTag".split(" ").forEach(function(i){i in Symbol||"toStringTag"!==(e(Symbol,i,{value:Symbol(i)}),i)||((o=t.getOwnPropertyDescriptor(n,"toString")).value=function(){var s=r.call(this),a=this[Symbol.toStringTag];return typeof a>"u"?s:"[object "+a+"]"},e(n,"toString",o))})}(Object,Symbol),function(t,e,n){function r(){return this}e[t]||(e[t]=function(){var o=0,i=this,s={next:function(){var a=i.length<=o;return a?{done:a}:{done:a,value:i[o++]}}};return s[t]=r,s}),n[t]||(n[t]=function(){var o=String.fromCodePoint,i=this,s=0,a=i.length,l={next:function(){var u=a<=s,c=u?"":o(i.codePointAt(s));return s+=c.length,u?{done:u}:{done:u,value:c}}};return l[t]=r,l})}(Symbol.iterator,Array.prototype,String.prototype);var Yi=Object.prototype.toString;Object.prototype.toString=function(){return void 0===this?"[object Undefined]":null===this?"[object Null]":Yi.call(this)},Object.keys=function(t){return Object.getOwnPropertyNames(t).filter(function(e){return(e=Object.getOwnPropertyDescriptor(t,e))&&e.enumerable})};var Et=window.Symbol.iterator;String.prototype[Et]&&String.prototype.codePointAt||(String.prototype[Et]=function t(){var e,n=this;return Ue(t,function(r){if(1==r.a&&(e=0),3!=r.a)return e<n.length?r=He(r,n[e]):(r.a=0,r=void 0),r;e++,r.a=2})}),Set.prototype[Et]||(Set.prototype[Et]=function t(){var e,r,n=this;return Ue(t,function(o){if(1==o.a&&(e=[],n.forEach(function(i){e.push(i)}),r=0),3!=o.a)return r<e.length?o=He(o,e[r]):(o.a=0,o=void 0),o;r++,o.a=2})}),Map.prototype[Et]||(Map.prototype[Et]=function t(){var e,r,n=this;return Ue(t,function(o){if(1==o.a&&(e=[],n.forEach(function(i,s){e.push([s,i])}),r=0),3!=o.a)return r<e.length?o=He(o,e[r]):(o.a=0,o=void 0),o;r++,o.a=2})}),window.WebComponents=window.WebComponents||{flags:{}};var hr=document.querySelector('script[src*="webcomponents-bundle"]'),Ki=/wc-(.+)/,$={};if(!$.noOpts){if(location.search.slice(1).split("&").forEach(function(t){var e;(t=t.split("="))[0]&&(e=t[0].match(Ki))&&($[e[1]]=t[1]||!0)}),hr)for(var dr=0,Yt=void 0;Yt=hr.attributes[dr];dr++)"src"!==Yt.name&&($[Yt.name]=Yt.value||!0);if($.log&&$.log.split){var Qi=$.log.split(",");$.log={},Qi.forEach(function(t){$.log[t]=!0})}else $.log={}}window.WebComponents.flags=$;var fr=$.shadydom;if(fr){window.ShadyDOM=window.ShadyDOM||{},window.ShadyDOM.force=fr;var pr=$.noPatch;window.ShadyDOM.noPatch="true"===pr||pr}var _r=$.register||$.ce;function vr(){}function R(t){return t.__shady||(t.__shady=new vr),t.__shady}function E(t){return t&&t.__shady}_r&&window.customElements&&(window.customElements.forcePolyfill=_r),vr.prototype.toJSON=function(){return{}};var g=window.ShadyDOM||{};g.Ka=!(!Element.prototype.attachShadow||!Node.prototype.getRootNode);var Ge=Object.getOwnPropertyDescriptor(Node.prototype,"firstChild");function pt(t){return(t=E(t))&&void 0!==t.firstChild}function V(t){return t instanceof ShadowRoot}function $e(t){return(t=(t=E(t))&&t.root)&&wo(t)}g.D=!!(Ge&&Ge.configurable&&Ge.get),g.ia=g.force||!g.Ka,g.G=g.noPatch||!1,g.ma=g.preferPerformance,g.la="on-demand"===g.G,g.ya=navigator.userAgent.match("Trident");var Nt=Element.prototype,mr=Nt.matches||Nt.matchesSelector||Nt.mozMatchesSelector||Nt.msMatchesSelector||Nt.oMatchesSelector||Nt.webkitMatchesSelector,Je=document.createTextNode(""),yr=0,Xe=[];function gr(t){Xe.push(t),Je.textContent=yr++}new MutationObserver(function(){for(;Xe.length;)try{Xe.shift()()}catch(t){throw Je.textContent=yr++,t}}).observe(Je,{characterData:!0});var qi=!!document.contains;function wr(t,e){for(;e;){if(e==t)return!0;e=e.__shady_parentNode}return!1}function Kt(t){for(var e=t.length-1;0<=e;e--){var n=t[e],r=n.getAttribute("id")||n.getAttribute("name");r&&"length"!==r&&isNaN(r)&&(t[r]=n)}return t.item=function(o){return t[o]},t.namedItem=function(o){if("length"!==o&&isNaN(o)&&t[o])return t[o];for(var i=Me(t),s=i.next();!s.done;s=i.next())if(((s=s.value).getAttribute("id")||s.getAttribute("name"))==o)return s;return null},t}function Er(t){var e=[];for(t=t.__shady_native_firstChild;t;t=t.__shady_native_nextSibling)e.push(t);return e}function Ze(t){var e=[];for(t=t.__shady_firstChild;t;t=t.__shady_nextSibling)e.push(t);return e}function Nr(t,e,n){if(n.configurable=!0,n.value)t[e]=n.value;else try{Object.defineProperty(t,e,n)}catch{}}function W(t,e,n,r){for(var o in n=void 0===n?"":n,e)r&&0<=r.indexOf(o)||Nr(t,n+o,e[o])}function Ye(t,e){for(var n in e)n in t&&Nr(t,n,e[n])}function H(t){var e={};return Object.getOwnPropertyNames(t).forEach(function(n){e[n]=Object.getOwnPropertyDescriptor(t,n)}),e}var Ke,Ht=[];function Cr(t){Ke||(Ke=!0,gr(Qt)),Ht.push(t)}function Qt(){Ke=!1;for(var t=!!Ht.length;Ht.length;)Ht.shift()();return t}function Qe(){this.a=!1,this.addedNodes=[],this.removedNodes=[],this.ba=new Set}Qt.list=Ht,Qe.prototype.flush=function(){if(this.a){this.a=!1;var t=this.takeRecords();t.length&&this.ba.forEach(function(e){e(t)})}},Qe.prototype.takeRecords=function(){if(this.addedNodes.length||this.removedNodes.length){var t=[{addedNodes:this.addedNodes,removedNodes:this.removedNodes}];return this.addedNodes=[],this.removedNodes=[],t}return[]};var rs=/[&\u00A0"]/g,os=/[&\u00A0<>]/g;function Sr(t){switch(t){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";case"\xa0":return"&nbsp;"}}function Tr(t){for(var e={},n=0;n<t.length;n++)e[t[n]]=!0;return e}var is=Tr("area base br col command embed hr img input keygen link meta param source track wbr".split(" ")),ss=Tr("style script xmp iframe noembed noframes plaintext noscript".split(" "));function qe(t,e){"template"===t.localName&&(t=t.content);for(var n="",r=e?e(t):t.childNodes,o=0,i=r.length,s=void 0;o<i&&(s=r[o]);o++){t:{var a=s,l=t,u=e;switch(a.nodeType){case Node.ELEMENT_NODE:for(var S,c="<"+(l=a.localName),f=a.attributes,x=0;S=f[x];x++)c+=" "+S.name+'="'+S.value.replace(rs,Sr)+'"';c+=">",a=is[l]?c:c+qe(a,u)+"</"+l+">";break t;case Node.TEXT_NODE:a=a.data,a=l&&ss[l.localName]?a:a.replace(os,Sr);break t;case Node.COMMENT_NODE:a="\x3c!--"+a.data+"--\x3e";break t;default:throw window.console.error(a),Error("not implemented")}}n+=a}return n}var qt=g.D,ze={querySelector:function(t){return this.__shady_native_querySelector(t)},querySelectorAll:function(t){return this.__shady_native_querySelectorAll(t)}},br={};function Or(t){br[t]=function(e){return e["__shady_native_"+t]}}function It(t,e){for(var n in W(t,e,"__shady_native_"),e)Or(n)}function I(t,e){e=void 0===e?[]:e;for(var n=0;n<e.length;n++){var r=e[n],o=Object.getOwnPropertyDescriptor(t,r);o&&(Object.defineProperty(t,"__shady_native_"+r,o),o.value?ze[r]||(ze[r]=o.value):Or(r))}}var Y=document.createTreeWalker(document,NodeFilter.SHOW_ALL,null,!1),K=document.createTreeWalker(document,NodeFilter.SHOW_ELEMENT,null,!1),tn=document.implementation.createHTMLDocument("inert");function Dr(t){for(var e;e=t.__shady_native_firstChild;)t.__shady_native_removeChild(e)}var en=["firstElementChild","lastElementChild","children","childElementCount"],nn=["querySelector","querySelectorAll"],rn=H({get childNodes(){return this.__shady_childNodes},get firstChild(){return this.__shady_firstChild},get lastChild(){return this.__shady_lastChild},get childElementCount(){return this.__shady_childElementCount},get children(){return this.__shady_children},get firstElementChild(){return this.__shady_firstElementChild},get lastElementChild(){return this.__shady_lastElementChild},get shadowRoot(){return this.__shady_shadowRoot}}),on=H({get textContent(){return this.__shady_textContent},set textContent(t){this.__shady_textContent=t},get innerHTML(){return this.__shady_innerHTML},set innerHTML(t){return this.__shady_innerHTML=t}}),sn=H({get parentElement(){return this.__shady_parentElement},get parentNode(){return this.__shady_parentNode},get nextSibling(){return this.__shady_nextSibling},get previousSibling(){return this.__shady_previousSibling},get nextElementSibling(){return this.__shady_nextElementSibling},get previousElementSibling(){return this.__shady_previousElementSibling},get className(){return this.__shady_className},set className(t){return this.__shady_className=t}});function an(t){for(var e in t){var n=t[e];n&&(n.enumerable=!1)}}an(rn),an(on),an(sn);var t,xr=g.D||!0===g.G,Mr=xr?function(){}:function(t){var e=R(t);e.Aa||(e.Aa=!0,Ye(t,sn))},Lr=xr?function(){}:function(t){var e=R(t);e.za||(e.za=!0,Ye(t,rn),window.customElements&&!g.G||Ye(t,on))},Ut="__eventWrappers"+Date.now(),ln=(t=Object.getOwnPropertyDescriptor(Event.prototype,"composed"))?function(e){return t.get.call(e)}:null,ls=function(){function t(){}var e=!1,n={get capture(){return e=!0,!1}};return window.addEventListener("test",t,n),window.removeEventListener("test",t,n),e}();function Pr(t){if(t&&"object"==typeof t)var e=!!t.capture,n=!!t.once,r=!!t.passive,o=t.O;else e=!!t,r=n=!1;return{wa:o,capture:e,once:n,passive:r,ua:ls?t:e}}var us={blur:!0,focus:!0,focusin:!0,focusout:!0,click:!0,dblclick:!0,mousedown:!0,mouseenter:!0,mouseleave:!0,mousemove:!0,mouseout:!0,mouseover:!0,mouseup:!0,wheel:!0,beforeinput:!0,input:!0,keydown:!0,keyup:!0,compositionstart:!0,compositionupdate:!0,compositionend:!0,touchstart:!0,touchend:!0,touchmove:!0,touchcancel:!0,pointerover:!0,pointerenter:!0,pointerdown:!0,pointermove:!0,pointerup:!0,pointercancel:!0,pointerout:!0,pointerleave:!0,gotpointercapture:!0,lostpointercapture:!0,dragstart:!0,drag:!0,dragenter:!0,dragleave:!0,dragover:!0,drop:!0,dragend:!0,DOMActivate:!0,DOMFocusIn:!0,DOMFocusOut:!0,keypress:!0},Ar={DOMAttrModified:!0,DOMAttributeNameChanged:!0,DOMCharacterDataModified:!0,DOMElementNameChanged:!0,DOMNodeInserted:!0,DOMNodeInsertedIntoDocument:!0,DOMNodeRemoved:!0,DOMNodeRemovedFromDocument:!0,DOMSubtreeModified:!0};function jr(t){return t instanceof Node?t.__shady_getRootNode():t}function zt(t,e){var n=[],r=t;for(t=jr(t);r;)n.push(r),r=r.__shady_assignedSlot?r.__shady_assignedSlot:r.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&r.host&&(e||r!==t)?r.host:r.__shady_parentNode;return n[n.length-1]===document&&n.push(window),n}function Rr(t,e){if(!V)return t;t=zt(t,!0);for(var r,i,n=0,o=void 0,s=void 0;n<e.length;n++)if((i=jr(r=e[n]))!==o&&(s=t.indexOf(i),o=i),!V(i)||-1<s)return r}function un(t){function e(n,r){return(n=new t(n,r)).__composed=r&&!!r.composed,n}return e.__proto__=t,e.prototype=t.prototype,e}var cn={focus:!0,blur:!0};function Fr(t){return t.__target!==t.target||t.__relatedTarget!==t.relatedTarget}function Hr(t,e,n){if(n=e.__handlers&&e.__handlers[t.type]&&e.__handlers[t.type][n])for(var o,r=0;(o=n[r])&&(!Fr(t)||t.target!==t.relatedTarget)&&(o.call(e,t),!t.__immediatePropagationStopped);r++);}function hs(t){var o,e=t.composedPath();Object.defineProperty(t,"currentTarget",{get:function(){return r},configurable:!0});for(var n=e.length-1;0<=n;n--){var r=e[n];if(Hr(t,r,"capture"),t.ea)return}for(Object.defineProperty(t,"eventPhase",{get:function(){return Event.AT_TARGET}}),n=0;n<e.length;n++){var i=E(r=e[n]);if(i=i&&i.root,(0===n||i&&i===o)&&(Hr(t,r,"bubble"),r!==window&&(o=r.__shady_getRootNode()),t.ea))break}}function Ir(t,e,n,r,o,i){for(var s=0;s<t.length;s++){var a=t[s];if(e===a.node&&n===a.type&&r===a.capture&&o===a.once&&i===a.passive)return s}return-1}function Ur(t){return Qt(),this.__shady_native_dispatchEvent(t)}function hn(t,e,n){var r=Pr(n),o=r.capture,i=r.once,s=r.passive,a=r.wa;if(r=r.ua,e){var l=typeof e;if(("function"===l||"object"===l)&&("object"!==l||e.handleEvent&&"function"==typeof e.handleEvent)){if(Ar[t])return this.__shady_native_addEventListener(t,e,r);var u=a||this;if(a=e[Ut]){if(-1<Ir(a,u,t,o,i,s))return}else e[Ut]=[];a=function(c){if(i&&this.__shady_removeEventListener(t,e,n),c.__target||Br(c),u!==this){var f=Object.getOwnPropertyDescriptor(c,"currentTarget");Object.defineProperty(c,"currentTarget",{get:function(){return u},configurable:!0})}if(c.__previousCurrentTarget=c.currentTarget,(!V(u)&&"slot"!==u.localName||-1!=c.composedPath().indexOf(u))&&(c.composed||-1<c.composedPath().indexOf(u)))if(Fr(c)&&c.target===c.relatedTarget)c.eventPhase===Event.BUBBLING_PHASE&&c.stopImmediatePropagation();else if(c.eventPhase===Event.CAPTURING_PHASE||c.bubbles||c.target===u||u instanceof Window){var x="function"===l?e.call(u,c):e.handleEvent&&e.handleEvent(c);return u!==this&&(f?(Object.defineProperty(c,"currentTarget",f),f=null):delete c.currentTarget),x}},e[Ut].push({node:u,type:t,capture:o,once:i,passive:s,$a:a}),cn[t]?(this.__handlers=this.__handlers||{},this.__handlers[t]=this.__handlers[t]||{capture:[],bubble:[]},this.__handlers[t][o?"capture":"bubble"].push(a)):this.__shady_native_addEventListener(t,a,r)}}}function dn(t,e,n){if(e){var r=Pr(n);n=r.capture;var o=r.once,i=r.passive,s=r.wa;if(r=r.ua,Ar[t])return this.__shady_native_removeEventListener(t,e,r);var a=s||this;s=void 0;var l=null;try{l=e[Ut]}catch{}l&&-1<(o=Ir(l,a,t,n,o,i))&&(s=l.splice(o,1)[0].$a,l.length||(e[Ut]=void 0)),this.__shady_native_removeEventListener(t,s||e,r),s&&cn[t]&&this.__handlers&&this.__handlers[t]&&-1<(e=(t=this.__handlers[t][n?"capture":"bubble"]).indexOf(s))&&t.splice(e,1)}}var kr=H({get composed(){return void 0===this.__composed&&(ln?this.__composed="focusin"===this.type||"focusout"===this.type||ln(this):!1!==this.isTrusted&&(this.__composed=us[this.type])),this.__composed||!1},composedPath:function(){return this.__composedPath||(this.__composedPath=zt(this.__target,this.composed)),this.__composedPath},get target(){return Rr(this.currentTarget||this.__previousCurrentTarget,this.composedPath())},get relatedTarget(){return this.__relatedTarget?(this.__relatedTargetComposedPath||(this.__relatedTargetComposedPath=zt(this.__relatedTarget,!0)),Rr(this.currentTarget||this.__previousCurrentTarget,this.__relatedTargetComposedPath)):null},stopPropagation:function(){Event.prototype.stopPropagation.call(this),this.ea=!0},stopImmediatePropagation:function(){Event.prototype.stopImmediatePropagation.call(this),this.ea=this.__immediatePropagationStopped=!0}});function Br(t){if(t.__target=t.target,t.__relatedTarget=t.relatedTarget,g.D){var e=Object.getPrototypeOf(t);if(!e.hasOwnProperty("__shady_patchedProto")){var n=Object.create(e);n.__shady_sourceProto=e,W(n,kr),e.__shady_patchedProto=n}t.__proto__=e.__shady_patchedProto}else W(t,kr)}var fs=un(Event),ps=un(CustomEvent),_s=un(MouseEvent),Vr=Object.getOwnPropertyNames(Document.prototype).filter(function(t){return"on"===t.substring(0,2)});function kt(t,e){return{index:t,X:[],aa:e}}function ms(t,e,n,r){var o=0,i=0,s=0,a=0,l=Math.min(e-o,r-i);if(0==o&&0==i)t:{for(s=0;s<l;s++)if(t[s]!==n[s])break t;s=l}if(e==t.length&&r==n.length){a=t.length;for(var u=n.length,c=0;c<l-s&&ys(t[--a],n[--u]);)c++;a=c}if(i+=s,r-=a,(e-=a)-(o+=s)==0&&r-i==0)return[];if(o==e){for(e=kt(o,0);i<r;)e.X.push(n[i++]);return[e]}if(i==r)return[kt(o,e-o)];for(r=r-(s=i)+1,a=e-(l=o)+1,e=Array(r),u=0;u<r;u++)e[u]=Array(a),e[u][0]=u;for(u=0;u<a;u++)e[0][u]=u;for(u=1;u<r;u++)for(c=1;c<a;c++)if(t[l+c-1]===n[s+u-1])e[u][c]=e[u-1][c-1];else{var f=e[u-1][c]+1,x=e[u][c-1]+1;e[u][c]=f<x?f:x}for(r=e[l=e.length-1][s=e[0].length-1],t=[];0<l||0<s;)0==l?(t.push(2),s--):0==s?(t.push(3),l--):(a=e[l-1][s-1],(f=(u=e[l-1][s])<(c=e[l][s-1])?u<a?u:a:c<a?c:a)==a?(a==r?t.push(0):(t.push(1),r=a),l--,s--):f==u?(t.push(3),l--,r=u):(t.push(2),s--,r=c));for(t.reverse(),e=void 0,l=[],s=0;s<t.length;s++)switch(t[s]){case 0:e&&(l.push(e),e=void 0),o++,i++;break;case 1:e||(e=kt(o,0)),e.aa++,o++,e.X.push(n[i]),i++;break;case 2:e||(e=kt(o,0)),e.aa++,o++;break;case 3:e||(e=kt(o,0)),e.X.push(n[i]),i++}return e&&l.push(e),l}function ys(t,e){return t===e}var Wr=H({dispatchEvent:Ur,addEventListener:hn,removeEventListener:dn}),fn=null;function _t(){return fn||(fn=window.ShadyCSS&&window.ShadyCSS.ScopingShim),fn||null}function Gr(t,e,n){var r=_t();return!(!r||"class"!==e||(r.setElementClass(t,n),0))}function $r(t,e){var n=_t();n&&n.unscopeNode(t,e)}function Jr(t,e){var n=_t();if(!n)return!0;if(t.nodeType===Node.DOCUMENT_FRAGMENT_NODE){for(n=!0,t=t.__shady_firstChild;t;t=t.__shady_nextSibling)n=n&&Jr(t,e);return n}return t.nodeType!==Node.ELEMENT_NODE||n.currentScopeForNode(t)===e}function te(t){if(t.nodeType!==Node.ELEMENT_NODE)return"";var e=_t();return e?e.currentScopeForNode(t):""}function pn(t,e){if(t)for(t.nodeType===Node.ELEMENT_NODE&&e(t),t=t.__shady_firstChild;t;t=t.__shady_nextSibling)t.nodeType===Node.ELEMENT_NODE&&pn(t,e)}var _n=window.document,vn=g.ma,Xr=Object.getOwnPropertyDescriptor(Node.prototype,"isConnected"),Zr=Xr&&Xr.get;function mn(t){for(var e;e=t.__shady_firstChild;)t.__shady_removeChild(e)}function Yr(t){var e=E(t);if(e&&void 0!==e.da)for(e=t.__shady_firstChild;e;e=e.__shady_nextSibling)Yr(e);(t=E(t))&&(t.da=void 0)}function Kr(t){var e=t;return t&&"slot"===t.localName&&(e=(e=(e=E(t))&&e.T)&&e.length?e[0]:Kr(t.__shady_nextSibling)),e}function Qr(t,e,n){if(t=(t=E(t))&&t.W){if(e)if(e.nodeType===Node.DOCUMENT_FRAGMENT_NODE)for(var r=0,o=e.childNodes.length;r<o;r++)t.addedNodes.push(e.childNodes[r]);else t.addedNodes.push(e);n&&t.removedNodes.push(n),function zi(t){t.a||(t.a=!0,gr(function(){t.flush()}))}(t)}}var ee=H({get parentNode(){var t=E(this);return void 0!==(t=t&&t.parentNode)?t:this.__shady_native_parentNode},get firstChild(){var t=E(this);return void 0!==(t=t&&t.firstChild)?t:this.__shady_native_firstChild},get lastChild(){var t=E(this);return void 0!==(t=t&&t.lastChild)?t:this.__shady_native_lastChild},get nextSibling(){var t=E(this);return void 0!==(t=t&&t.nextSibling)?t:this.__shady_native_nextSibling},get previousSibling(){var t=E(this);return void 0!==(t=t&&t.previousSibling)?t:this.__shady_native_previousSibling},get childNodes(){if(pt(this)){var t=E(this);if(!t.childNodes){t.childNodes=[];for(var e=this.__shady_firstChild;e;e=e.__shady_nextSibling)t.childNodes.push(e)}var n=t.childNodes}else n=this.__shady_native_childNodes;return n.item=function(r){return n[r]},n},get parentElement(){var t=E(this);return(t=t&&t.parentNode)&&t.nodeType!==Node.ELEMENT_NODE&&(t=null),void 0!==t?t:this.__shady_native_parentElement},get isConnected(){if(Zr&&Zr.call(this))return!0;if(this.nodeType==Node.DOCUMENT_FRAGMENT_NODE)return!1;var t=this.ownerDocument;if(qi){if(t.__shady_native_contains(this))return!0}else if(t.documentElement&&t.documentElement.__shady_native_contains(this))return!0;for(t=this;t&&!(t instanceof Document);)t=t.__shady_parentNode||(V(t)?t.host:void 0);return!!(t&&t instanceof Document)},get textContent(){if(pt(this)){for(var t=[],e=this.__shady_firstChild;e;e=e.__shady_nextSibling)e.nodeType!==Node.COMMENT_NODE&&t.push(e.__shady_textContent);return t.join("")}return this.__shady_native_textContent},set textContent(t){switch((typeof t>"u"||null===t)&&(t=""),this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:if(!pt(this)&&g.D){var e=this.__shady_firstChild;(e!=this.__shady_lastChild||e&&e.nodeType!=Node.TEXT_NODE)&&mn(this),this.__shady_native_textContent=t}else mn(this),(0<t.length||this.nodeType===Node.ELEMENT_NODE)&&this.__shady_insertBefore(document.createTextNode(t));break;default:this.nodeValue=t}},insertBefore:function(t,e){if(this.ownerDocument!==_n&&t.ownerDocument!==_n)return this.__shady_native_insertBefore(t,e),t;if(t===this)throw Error("Failed to execute 'appendChild' on 'Node': The new child element contains the parent.");if(e){var n=E(e);if(void 0!==(n=n&&n.parentNode)&&n!==this||void 0===n&&e.__shady_native_parentNode!==this)throw Error("Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.")}if(e===t)return t;Qr(this,t);var r=[],o=(n=Tt(this))?n.host.localName:te(this),i=t.__shady_parentNode;if(i){var s=te(t),a=!!n||!Tt(t)||vn&&void 0!==this.__noInsertionPoint;i.__shady_removeChild(t,a)}i=!0;var l=(!vn||void 0===t.__noInsertionPoint&&void 0===this.__noInsertionPoint)&&!Jr(t,o),u=n&&!t.__noInsertionPoint&&(!vn||t.nodeType===Node.DOCUMENT_FRAGMENT_NODE);return(u||l)&&(l&&(s=s||te(t)),pn(t,function(c){if(u&&"slot"===c.localName&&r.push(c),l){var f=s;_t()&&(f&&$r(c,f),(f=_t())&&f.scopeNode(c,o))}})),r.length&&(mo(n),n.c.push.apply(n.c,r instanceof Array?r:rr(Me(r))),ut(n)),pt(this)&&(function Ts(t,e,n){ne(e,2);var r=R(e);if(void 0!==r.firstChild&&(r.childNodes=null),t.nodeType===Node.DOCUMENT_FRAGMENT_NODE)for(t=t.__shady_native_firstChild;t;t=t.__shady_native_nextSibling)lo(t,e,r,n);else lo(t,e,r,n)}(t,this,e),n=E(this),$e(this)?(ut(n.root),i=!1):n.root&&(i=!1)),i?(n=V(this)?this.host:this,e?(e=Kr(e),n.__shady_native_insertBefore(t,e)):n.__shady_native_appendChild(t)):t.ownerDocument!==this.ownerDocument&&this.ownerDocument.adoptNode(t),t},appendChild:function(t){if(this!=t||!V(t))return this.__shady_insertBefore(t)},removeChild:function(t,e){if(e=void 0!==e&&e,this.ownerDocument!==_n)return this.__shady_native_removeChild(t);if(t.__shady_parentNode!==this)throw Error("The node to be removed is not a child of this node: "+t);Qr(this,null,t);var n=Tt(t),r=n&&function Ds(t,e){if(t.a){ae(t);var r,n=t.b;for(r in n)for(var o=n[r],i=0;i<o.length;i++){var s=o[i];if(wr(e,s)){o.splice(i,1);var a=t.a.indexOf(s);if(0<=a&&(t.a.splice(a,1),(a=E(s.__shady_parentNode))&&a.Z&&a.Z--),i--,a=(s=E(s)).T)for(var l=0;l<a.length;l++){var u=a[l],c=u.__shady_native_parentNode;c&&c.__shady_native_removeChild(u)}s.T=[],s.assignedNodes=[],a=!0}}return a}}(n,t),o=E(this);if(pt(this)&&(function bs(t,e){var n=R(t);t===(e=R(e)).firstChild&&(e.firstChild=n.nextSibling),t===e.lastChild&&(e.lastChild=n.previousSibling);var r=n.nextSibling;(t=n.previousSibling)&&(R(t).nextSibling=r),r&&(R(r).previousSibling=t),n.parentNode=n.previousSibling=n.nextSibling=void 0,void 0!==e.childNodes&&(e.childNodes=null)}(t,this),$e(this))){ut(o.root);var i=!0}if(_t()&&!e&&n&&t.nodeType!==Node.TEXT_NODE){var s=te(t);pn(t,function(a){$r(a,s)})}return Yr(t),n&&((e=this&&"slot"===this.localName)&&(i=!0),(r||e)&&ut(n)),i||(i=V(this)?this.host:this,(!o.root&&"slot"!==t.localName||i===t.__shady_native_parentNode)&&i.__shady_native_removeChild(t)),t},replaceChild:function(t,e){return this.__shady_insertBefore(t,e),this.__shady_removeChild(e),t},cloneNode:function(t){if("template"==this.localName)return this.__shady_native_cloneNode(t);var e=this.__shady_native_cloneNode(!1);if(t&&e.nodeType!==Node.ATTRIBUTE_NODE){t=this.__shady_firstChild;for(var n;t;t=t.__shady_nextSibling)n=t.__shady_cloneNode(!0),e.__shady_appendChild(n)}return e},getRootNode:function(t){if(this&&this.nodeType){var e=R(this),n=e.da;return void 0===n&&(V(this)?e.da=n=this:(n=(n=this.__shady_parentNode)?n.__shady_getRootNode(t):this,document.documentElement.__shady_native_contains(this)&&(e.da=n))),n}},contains:function(t){return wr(this,t)}}),Bt=H({get assignedSlot(){var t=this.__shady_parentNode;return(t=t&&t.__shady_shadowRoot)&&se(t),(t=E(this))&&t.assignedSlot||null}});function yn(t,e,n){var r=[];return qr(t,e,n,r),r}function qr(t,e,n,r){for(t=t.__shady_firstChild;t;t=t.__shady_nextSibling){var o;if(o=t.nodeType===Node.ELEMENT_NODE){var i=e,s=n,a=r,l=i(o=t);l&&a.push(o),s&&s(l)?o=l:(qr(o,i,s,a),o=void 0)}if(o)break}}var Ct=H({get firstElementChild(){var t=E(this);if(t&&void 0!==t.firstChild){for(t=this.__shady_firstChild;t&&t.nodeType!==Node.ELEMENT_NODE;)t=t.__shady_nextSibling;return t}return this.__shady_native_firstElementChild},get lastElementChild(){var t=E(this);if(t&&void 0!==t.lastChild){for(t=this.__shady_lastChild;t&&t.nodeType!==Node.ELEMENT_NODE;)t=t.__shady_previousSibling;return t}return this.__shady_native_lastElementChild},get children(){return pt(this)?Kt(Array.prototype.filter.call(Ze(this),function(t){return t.nodeType===Node.ELEMENT_NODE})):this.__shady_native_children},get childElementCount(){var t=this.__shady_children;return t?t.length:0}}),gs=H({querySelector:function(t){return yn(this,function(e){return mr.call(e,t)},function(e){return!!e})[0]||null},querySelectorAll:function(t,e){if(e){e=Array.prototype.slice.call(this.__shady_native_querySelectorAll(t));var n=this.__shady_getRootNode();return Kt(e.filter(function(r){return r.__shady_getRootNode()==n}))}return Kt(yn(this,function(r){return mr.call(r,t)}))}}),zr=g.ma&&!g.G?Object.assign({},Ct):Ct;Object.assign(Ct,gs);var to=window.document;function eo(t,e){if("slot"===e)$e(t=t.__shady_parentNode)&&ut(E(t).root);else if("slot"===t.localName&&"name"===e&&(e=Tt(t))){if(e.a){ae(e);var n=t.Ba,r=yo(t);if(r!==n){var o=(n=e.b[n]).indexOf(t);0<=o&&n.splice(o,1),(n=e.b[r]||(e.b[r]=[])).push(t),1<n.length&&(e.b[r]=go(n))}}ut(e)}}var gn=H({get previousElementSibling(){var t=E(this);if(t&&void 0!==t.previousSibling){for(t=this.__shady_previousSibling;t&&t.nodeType!==Node.ELEMENT_NODE;)t=t.__shady_previousSibling;return t}return this.__shady_native_previousElementSibling},get nextElementSibling(){var t=E(this);if(t&&void 0!==t.nextSibling){for(t=this.__shady_nextSibling;t&&t.nodeType!==Node.ELEMENT_NODE;)t=t.__shady_nextSibling;return t}return this.__shady_native_nextElementSibling},get slot(){return this.getAttribute("slot")},set slot(t){this.__shady_setAttribute("slot",t)},get className(){return this.getAttribute("class")||""},set className(t){this.__shady_setAttribute("class",t)},setAttribute:function(t,e){this.ownerDocument!==to?this.__shady_native_setAttribute(t,e):Gr(this,t,e)||(this.__shady_native_setAttribute(t,e),eo(this,t))},removeAttribute:function(t){this.ownerDocument!==to?this.__shady_native_removeAttribute(t):Gr(this,t,"")?""===this.getAttribute(t)&&this.__shady_native_removeAttribute(t):(this.__shady_native_removeAttribute(t),eo(this,t))}}),no=H({attachShadow:function(t){if(!this)throw Error("Must provide a host.");if(!t)throw Error("Not enough arguments.");if(t.shadyUpgradeFragment&&!g.ya){var e=t.shadyUpgradeFragment;if(e.__proto__=ShadowRoot.prototype,fo(e,this,t),re(e,e),t=e.__noInsertionPoint?null:e.querySelectorAll("slot"),e.__noInsertionPoint=void 0,t&&t.length){var n=e;mo(n),n.c.push.apply(n.c,t instanceof Array?t:rr(Me(t))),ut(e)}e.host.__shady_native_appendChild(e)}else e=new ie(co,this,t);return this.__CE_shadowRoot=e},get shadowRoot(){var t=E(this);return t&&t.Sa||null}});Object.assign(gn,no);var wn=document.implementation.createHTMLDocument("inert"),En=H({get innerHTML(){return pt(this)?qe("template"===this.localName?this.content:this,Ze):this.__shady_native_innerHTML},set innerHTML(t){if("template"===this.localName)this.__shady_native_innerHTML=t;else{mn(this);var e=this.localName||"div";for(e=this.namespaceURI&&this.namespaceURI!==wn.namespaceURI?wn.createElementNS(this.namespaceURI,e):wn.createElement(e),g.D?e.__shady_native_innerHTML=t:e.innerHTML=t;t=e.__shady_firstChild;)this.__shady_insertBefore(t)}}}),ro=H({blur:function(){var t=E(this);(t=(t=t&&t.root)&&t.activeElement)?t.__shady_blur():this.__shady_native_blur()}});g.ma||Vr.forEach(function(t){ro[t]={set:function(e){var n=R(this),r=t.substring(2);n.N||(n.N={}),n.N[t]&&this.removeEventListener(r,n.N[t]),this.__shady_addEventListener(r,e),n.N[t]=e},get:function(){var e=E(this);return e&&e.N&&e.N[t]},configurable:!0}});var oo=H({assignedNodes:function(t){if("slot"===this.localName){var e=this.__shady_getRootNode();return e&&V(e)&&se(e),(e=E(this))&&(t&&t.flatten?e.T:e.assignedNodes)||[]}},addEventListener:function(t,e,n){if("slot"!==this.localName||"slotchange"===t)hn.call(this,t,e,n);else{"object"!=typeof n&&(n={capture:!!n});var r=this.__shady_parentNode;if(!r)throw Error("ShadyDOM cannot attach event to slot unless it has a `parentNode`");n.O=this,r.__shady_addEventListener(t,e,n)}},removeEventListener:function(t,e,n){if("slot"!==this.localName||"slotchange"===t)dn.call(this,t,e,n);else{"object"!=typeof n&&(n={capture:!!n});var r=this.__shady_parentNode;if(!r)throw Error("ShadyDOM cannot attach event to slot unless it has a `parentNode`");n.O=this,r.__shady_removeEventListener(t,e,n)}}}),Nn=H({getElementById:function(t){return""===t?null:yn(this,function(e){return e.id==t},function(e){return!!e})[0]||null}}),Cn=H({get activeElement(){var t=g.D?document.__shady_native_activeElement:document.activeElement;if(!t||!t.nodeType)return null;var e=!!V(this);if(!(this===document||e&&this.host!==t&&this.host.__shady_native_contains(t)))return null;for(e=Tt(t);e&&e!==this;)e=Tt(t=e.host);return this===document?e?null:t:e===this?t:null}}),ws=window.document,Es=H({importNode:function(t,e){if(t.ownerDocument!==ws||"template"===t.localName)return this.__shady_native_importNode(t,e);var n=this.__shady_native_importNode(t,!1);if(e)for(t=t.__shady_firstChild;t;t=t.__shady_nextSibling)e=this.__shady_importNode(t,!0),n.__shady_appendChild(e);return n}}),io=H({dispatchEvent:Ur,addEventListener:hn.bind(window),removeEventListener:dn.bind(window)}),St={};Object.getOwnPropertyDescriptor(HTMLElement.prototype,"parentElement")&&(St.parentElement=ee.parentElement),Object.getOwnPropertyDescriptor(HTMLElement.prototype,"contains")&&(St.contains=ee.contains),Object.getOwnPropertyDescriptor(HTMLElement.prototype,"children")&&(St.children=Ct.children),Object.getOwnPropertyDescriptor(HTMLElement.prototype,"innerHTML")&&(St.innerHTML=En.innerHTML),Object.getOwnPropertyDescriptor(HTMLElement.prototype,"className")&&(St.className=gn.className);var z={EventTarget:[Wr],Node:[ee,window.EventTarget?null:Wr],Text:[Bt],Comment:[Bt],CDATASection:[Bt],ProcessingInstruction:[Bt],Element:[gn,Ct,Bt,!g.D||"innerHTML"in Element.prototype?En:null,window.HTMLSlotElement?null:oo],HTMLElement:[ro,St],HTMLSlotElement:[oo],DocumentFragment:[zr,Nn],Document:[Es,zr,Nn,Cn],Window:[io]},Ns=g.D?null:["innerHTML","textContent"];function at(t,e,n,r){e.forEach(function(o){return t&&o&&W(t,o,n,r)})}function so(t){var n,e=t?null:Ns;for(n in z)at(window[n]&&window[n].prototype,z[n],t,e)}function ao(t){return t.__shady_protoIsPatched=!0,at(t,z.EventTarget),at(t,z.Node),at(t,z.Element),at(t,z.HTMLElement),at(t,z.HTMLSlotElement),t}["Text","Comment","CDATASection","ProcessingInstruction"].forEach(function(t){var e=window[t],n=Object.create(e.prototype);n.__shady_protoIsPatched=!0,at(n,z.EventTarget),at(n,z.Node),z[t]&&at(n,z[t]),e.prototype.__shady_patchedProto=n});var Cs=g.la,Ss=g.D;function ne(t,e){if(Cs&&!t.__shady_protoIsPatched&&!V(t)){var n=Object.getPrototypeOf(t),r=n.hasOwnProperty("__shady_patchedProto")&&n.__shady_patchedProto;r||(ao(r=Object.create(n)),n.__shady_patchedProto=r),Object.setPrototypeOf(t,r)}Ss||(1===e?Mr(t):2===e&&Lr(t))}function lo(t,e,n,r){ne(t,1),r=r||null;var o=R(t),i=r?R(r):null;o.previousSibling=r?i.previousSibling:e.__shady_lastChild,(i=E(o.previousSibling))&&(i.nextSibling=t),(i=E(o.nextSibling=r))&&(i.previousSibling=t),o.parentNode=e,r?r===n.firstChild&&(n.firstChild=t):(n.lastChild=t,n.firstChild||(n.firstChild=t)),n.childNodes=null}function re(t,e){var n=R(t);if(e||void 0===n.firstChild){n.childNodes=null;var r=n.firstChild=t.__shady_native_firstChild;for(n.lastChild=t.__shady_native_lastChild,ne(t,2),n=r,r=void 0;n;n=n.__shady_native_nextSibling){var o=R(n);o.parentNode=e||t,o.nextSibling=n.__shady_native_nextSibling,o.previousSibling=r||null,r=n,ne(n,1)}}}var Os=H({addEventListener:function(t,e,n){"object"!=typeof n&&(n={capture:!!n}),n.O=n.O||this,this.host.__shady_addEventListener(t,e,n)},removeEventListener:function(t,e,n){"object"!=typeof n&&(n={capture:!!n}),n.O=n.O||this,this.host.__shady_removeEventListener(t,e,n)}});function uo(t,e){W(t,Os,e),W(t,Cn,e),W(t,En,e),W(t,Ct,e),g.G&&!e?(W(t,ee,e),W(t,Nn,e)):g.D||(W(t,sn),W(t,rn),W(t,on))}var oe,co={},vt=g.deferConnectionCallbacks&&"loading"===document.readyState;function ho(t){var e=[];do{e.unshift(t)}while(t=t.__shady_parentNode);return e}function ie(t,e,n){if(t!==co)throw new TypeError("Illegal constructor");this.a=null,fo(this,e,n)}function fo(t,e,n){if(t.host=e,t.mode=n&&n.mode,re(t.host),(e=R(t.host)).root=t,e.Sa="closed"!==t.mode?t:null,(e=R(t)).firstChild=e.lastChild=e.parentNode=e.nextSibling=e.previousSibling=null,g.preferPerformance)for(;e=t.host.__shady_native_firstChild;)t.host.__shady_native_removeChild(e);else ut(t)}function ut(t){t.R||(t.R=!0,Cr(function(){return se(t)}))}function se(t){var e;if(e=t.R){for(var n;t;)t.R&&(n=t),V(t=(e=t).host.__shady_getRootNode())&&(e=E(e.host))&&0<e.Z||(t=void 0);e=n}(n=e)&&n._renderSelf()}function po(t,e,n){var r=R(e),o=r.fa;r.fa=null,n||(n=(t=t.b[e.__shady_slot||"__catchall"])&&t[0]),n?(R(n).assignedNodes.push(e),r.assignedSlot=n):r.assignedSlot=void 0,o!==r.assignedSlot&&r.assignedSlot&&(R(r.assignedSlot).ha=!0)}function _o(t,e,n){for(var r=0,o=void 0;r<n.length&&(o=n[r]);r++)if("slot"==o.localName){var i=E(o).assignedNodes;i&&i.length&&_o(t,e,i)}else e.push(n[r])}function vo(t,e){e.__shady_native_dispatchEvent(new Event("slotchange")),(e=E(e)).assignedSlot&&vo(t,e.assignedSlot)}function mo(t){t.c=t.c||[],t.a=t.a||[],t.b=t.b||{}}function ae(t){if(t.c&&t.c.length){for(var n,e=t.c,r=0;r<e.length;r++){var o=e[r];re(o);var i=o.__shady_parentNode;re(i),(i=E(i)).Z=(i.Z||0)+1,i=yo(o),t.b[i]?((n=n||{})[i]=!0,t.b[i].push(o)):t.b[i]=[o],t.a.push(o)}if(n)for(var s in n)t.b[s]=go(t.b[s]);t.c=[]}}function yo(t){var e=t.name||t.getAttribute("name")||"__catchall";return t.Ba=e}function go(t){return t.sort(function(e,n){e=ho(e);for(var r=ho(n),o=0;o<e.length;o++){var i=r[o];if((n=e[o])!==i)return(e=Ze(n.__shady_parentNode)).indexOf(n)-e.indexOf(i)}})}function wo(t){return ae(t),!(!t.a||!t.a.length)}if(ie.prototype._renderSelf=function(){var t=vt;if(vt=!0,this.R=!1,this.a){ae(this);for(var n,e=0;e<this.a.length;e++){var r=E(n=this.a[e]),o=r.assignedNodes;if(r.assignedNodes=[],r.T=[],r.ra=o)for(r=0;r<o.length;r++){var i=E(o[r]);i.fa=i.assignedSlot,i.assignedSlot===n&&(i.assignedSlot=null)}}for(e=this.host.__shady_firstChild;e;e=e.__shady_nextSibling)po(this,e);for(e=0;e<this.a.length;e++){if(!(o=E(n=this.a[e])).assignedNodes.length)for(r=n.__shady_firstChild;r;r=r.__shady_nextSibling)po(this,r,n);if((r=(r=E(n.__shady_parentNode))&&r.root)&&(wo(r)||r.R)&&r._renderSelf(),_o(this,o.T,o.assignedNodes),r=o.ra){for(i=0;i<r.length;i++)E(r[i]).fa=null;o.ra=null,r.length>o.assignedNodes.length&&(o.ha=!0)}o.ha&&(o.ha=!1,vo(this,n))}for(n=this.a,e=[],o=0;o<n.length;o++)(i=E(r=n[o].__shady_parentNode))&&i.root||!(0>e.indexOf(r))||e.push(r);for(n=0;n<e.length;n++){for(o=(i=e[n])===this?this.host:i,r=[],i=i.__shady_firstChild;i;i=i.__shady_nextSibling)if("slot"==i.localName)for(var s=E(i).T,a=0;a<s.length;a++)r.push(s[a]);else r.push(i);i=Er(o),s=ms(r,r.length,i,i.length);for(var l=a=0,u=void 0;a<s.length&&(u=s[a]);a++){for(var c=0,f=void 0;c<u.X.length&&(f=u.X[c]);c++)f.__shady_native_parentNode===o&&o.__shady_native_removeChild(f),i.splice(u.index+l,1);l-=u.aa}for(l=0,u=void 0;l<s.length&&(u=s[l]);l++)for(a=i[u.index],c=u.index;c<u.index+u.aa;c++)o.__shady_native_insertBefore(f=r[c],a),i.splice(c,0,f)}}if(!g.preferPerformance&&!this.qa)for(e=this.host.__shady_firstChild;e;e=e.__shady_nextSibling)n=E(e),e.__shady_native_parentNode!==this.host||"slot"!==e.localName&&n.assignedSlot||this.host.__shady_native_removeChild(e);this.qa=!0,vt=t,oe&&oe()},function(t){t.__proto__=DocumentFragment.prototype,uo(t,"__shady_"),uo(t),Object.defineProperties(t,{nodeType:{value:Node.DOCUMENT_FRAGMENT_NODE,configurable:!0},nodeName:{value:"#document-fragment",configurable:!0},nodeValue:{value:null,configurable:!0}}),["localName","namespaceURI","prefix"].forEach(function(e){Object.defineProperty(t,e,{value:void 0,configurable:!0})}),["ownerDocument","baseURI","isConnected"].forEach(function(e){Object.defineProperty(t,e,{get:function(){return this.host[e]},configurable:!0})})}(ie.prototype),window.customElements&&g.ia&&!g.preferPerformance){var le=new Map;oe=function(){var t=[];le.forEach(function(r,o){t.push([o,r])}),le.clear();for(var e=0;e<t.length;e++){var n=t[e][0];t[e][1]?n.__shadydom_connectedCallback():n.__shadydom_disconnectedCallback()}},vt&&document.addEventListener("readystatechange",function(){vt=!1,oe()},{once:!0});var Ms=window.customElements.define,Eo=function(t,e){var n=e.prototype.connectedCallback,r=e.prototype.disconnectedCallback;Ms.call(window.customElements,t,function(t,e,n){var r=0,o="__isConnected"+r++;return(e||n)&&(t.prototype.connectedCallback=t.prototype.__shadydom_connectedCallback=function(){vt?le.set(this,!0):this[o]||(this[o]=!0,e&&e.call(this))},t.prototype.disconnectedCallback=t.prototype.__shadydom_disconnectedCallback=function(){vt?this.isConnected||le.set(this,!1):this[o]&&(this[o]=!1,n&&n.call(this))}),t}(e,n,r)),e.prototype.connectedCallback=n,e.prototype.disconnectedCallback=r};window.customElements.define=Eo,Object.defineProperty(window.CustomElementRegistry.prototype,"define",{value:Eo,configurable:!0})}function Tt(t){if(V(t=t.__shady_getRootNode()))return t}function bt(t){this.node=t}(y=bt.prototype).addEventListener=function(t,e,n){return this.node.__shady_addEventListener(t,e,n)},y.removeEventListener=function(t,e,n){return this.node.__shady_removeEventListener(t,e,n)},y.appendChild=function(t){return this.node.__shady_appendChild(t)},y.insertBefore=function(t,e){return this.node.__shady_insertBefore(t,e)},y.removeChild=function(t){return this.node.__shady_removeChild(t)},y.replaceChild=function(t,e){return this.node.__shady_replaceChild(t,e)},y.cloneNode=function(t){return this.node.__shady_cloneNode(t)},y.getRootNode=function(t){return this.node.__shady_getRootNode(t)},y.contains=function(t){return this.node.__shady_contains(t)},y.dispatchEvent=function(t){return this.node.__shady_dispatchEvent(t)},y.setAttribute=function(t,e){this.node.__shady_setAttribute(t,e)},y.getAttribute=function(t){return this.node.__shady_native_getAttribute(t)},y.hasAttribute=function(t){return this.node.__shady_native_hasAttribute(t)},y.removeAttribute=function(t){this.node.__shady_removeAttribute(t)},y.attachShadow=function(t){return this.node.__shady_attachShadow(t)},y.focus=function(){this.node.__shady_native_focus()},y.blur=function(){this.node.__shady_blur()},y.importNode=function(t,e){if(this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_importNode(t,e)},y.getElementById=function(t){if(this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_getElementById(t)},y.querySelector=function(t){return this.node.__shady_querySelector(t)},y.querySelectorAll=function(t,e){return this.node.__shady_querySelectorAll(t,e)},y.assignedNodes=function(t){if("slot"===this.node.localName)return this.node.__shady_assignedNodes(t)},st.Object.defineProperties(bt.prototype,{activeElement:{configurable:!0,enumerable:!0,get:function(){if(V(this.node)||this.node.nodeType===Node.DOCUMENT_NODE)return this.node.__shady_activeElement}},_activeElement:{configurable:!0,enumerable:!0,get:function(){return this.activeElement}},host:{configurable:!0,enumerable:!0,get:function(){if(V(this.node))return this.node.host}},parentNode:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_parentNode}},firstChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_firstChild}},lastChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_lastChild}},nextSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_nextSibling}},previousSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_previousSibling}},childNodes:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_childNodes}},parentElement:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_parentElement}},firstElementChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_firstElementChild}},lastElementChild:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_lastElementChild}},nextElementSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_nextElementSibling}},previousElementSibling:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_previousElementSibling}},children:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_children}},childElementCount:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_childElementCount}},shadowRoot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_shadowRoot}},assignedSlot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_assignedSlot}},isConnected:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_isConnected}},innerHTML:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_innerHTML},set:function(t){this.node.__shady_innerHTML=t}},textContent:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_textContent},set:function(t){this.node.__shady_textContent=t}},slot:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_slot},set:function(t){this.node.__shady_slot=t}},className:{configurable:!0,enumerable:!0,get:function(){return this.node.__shady_className},set:function(t){return this.node.__shady_className=t}}}),Vr.forEach(function(t){Object.defineProperty(bt.prototype,t,{get:function(){return this.node["__shady_"+t]},set:function(e){this.node["__shady_"+t]=e},configurable:!0})});var No=new WeakMap;function Co(t){if(V(t)||t instanceof bt)return t;var e=No.get(t);return e||(e=new bt(t),No.set(t,e)),e}if(g.ia){var So=g.D?function(t){return t}:function(t){return Lr(t),Mr(t),t},Ls={inUse:g.ia,patch:So,isShadyRoot:V,enqueue:Cr,flush:Qt,flushInitial:function(t){!t.qa&&t.R&&se(t)},settings:g,filterMutations:function ns(t,e){var n=e.getRootNode();return t.map(function(r){var o=n===r.target.getRootNode();if(o&&r.addedNodes){if((o=Array.from(r.addedNodes).filter(function(i){return n===i.getRootNode()})).length)return r=Object.create(r),Object.defineProperty(r,"addedNodes",{value:o,configurable:!0}),r}else if(o)return r}).filter(function(r){return r})},observeChildren:function ts(t,e){var n=R(t);n.W||(n.W=new Qe),n.W.ba.add(e);var r=n.W;return{Ca:e,P:r,Da:t,takeRecords:function(){return r.takeRecords()}}},unobserveChildren:function es(t){var e=t&&t.P;e&&(e.ba.delete(t.Ca),e.ba.size||(R(t.Da).W=null))},deferConnectionCallbacks:g.deferConnectionCallbacks,preferPerformance:g.preferPerformance,handlesDynamicScoping:!0,wrap:g.G?Co:So,wrapIfNeeded:!0===g.G?Co:function(t){return t},Wrapper:bt,composedPath:function cs(t){return t.__composedPath||(t.__composedPath=zt(t.target,!0)),t.__composedPath},noPatch:g.G,patchOnDemand:g.la,nativeMethods:ze,nativeTree:br,patchElementProto:ao};window.ShadyDOM=Ls,function as(){var t=["dispatchEvent","addEventListener","removeEventListener"];window.EventTarget?I(window.EventTarget.prototype,t):(I(Node.prototype,t),I(Window.prototype,t)),qt?I(Node.prototype,"parentNode firstChild lastChild previousSibling nextSibling childNodes parentElement textContent".split(" ")):It(Node.prototype,{parentNode:{get:function(){return Y.currentNode=this,Y.parentNode()}},firstChild:{get:function(){return Y.currentNode=this,Y.firstChild()}},lastChild:{get:function(){return Y.currentNode=this,Y.lastChild()}},previousSibling:{get:function(){return Y.currentNode=this,Y.previousSibling()}},nextSibling:{get:function(){return Y.currentNode=this,Y.nextSibling()}},childNodes:{get:function(){var e=[];Y.currentNode=this;for(var n=Y.firstChild();n;)e.push(n),n=Y.nextSibling();return e}},parentElement:{get:function(){return K.currentNode=this,K.parentNode()}},textContent:{get:function(){switch(this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:for(var r,e=document.createTreeWalker(this,NodeFilter.SHOW_TEXT,null,!1),n="";r=e.nextNode();)n+=r.nodeValue;return n;default:return this.nodeValue}},set:function(e){switch((typeof e>"u"||null===e)&&(e=""),this.nodeType){case Node.ELEMENT_NODE:case Node.DOCUMENT_FRAGMENT_NODE:Dr(this),(0<e.length||this.nodeType===Node.ELEMENT_NODE)&&this.__shady_native_insertBefore(document.createTextNode(e),void 0);break;default:this.nodeValue=e}}}}),I(Node.prototype,"appendChild insertBefore removeChild replaceChild cloneNode contains".split(" ")),I(HTMLElement.prototype,["parentElement","contains"]),t={firstElementChild:{get:function(){return K.currentNode=this,K.firstChild()}},lastElementChild:{get:function(){return K.currentNode=this,K.lastChild()}},children:{get:function(){var e=[];K.currentNode=this;for(var n=K.firstChild();n;)e.push(n),n=K.nextSibling();return Kt(e)}},childElementCount:{get:function(){return this.children?this.children.length:0}}},qt?(I(Element.prototype,en),I(Element.prototype,["previousElementSibling","nextElementSibling","innerHTML","className"]),I(HTMLElement.prototype,["children","innerHTML","className"])):(It(Element.prototype,t),It(Element.prototype,{previousElementSibling:{get:function(){return K.currentNode=this,K.previousSibling()}},nextElementSibling:{get:function(){return K.currentNode=this,K.nextSibling()}},innerHTML:{get:function(){return qe(this,Er)},set:function(e){var n="template"===this.localName?this.content:this;Dr(n);var r=this.localName||"div";for((r=this.namespaceURI&&this.namespaceURI!==tn.namespaceURI?tn.createElementNS(this.namespaceURI,r):tn.createElement(r)).innerHTML=e,e="template"===this.localName?r.content:r;r=e.__shady_native_firstChild;)n.__shady_native_insertBefore(r,void 0)}},className:{get:function(){return this.getAttribute("class")||""},set:function(e){this.setAttribute("class",e)}}})),I(Element.prototype,"setAttribute getAttribute hasAttribute removeAttribute focus blur".split(" ")),I(Element.prototype,nn),I(HTMLElement.prototype,["focus","blur"]),window.HTMLTemplateElement&&I(window.HTMLTemplateElement.prototype,["innerHTML"]),qt?I(DocumentFragment.prototype,en):It(DocumentFragment.prototype,t),I(DocumentFragment.prototype,nn),qt?(I(Document.prototype,en),I(Document.prototype,["activeElement"])):It(Document.prototype,t),I(Document.prototype,["importNode","getElementById"]),I(Document.prototype,nn)}(),so("__shady_"),Object.defineProperty(document,"_activeElement",Cn.activeElement),W(Window.prototype,io,"__shady_"),g.G?g.la&&W(Element.prototype,no):(so(),function vs(){if(!ln&&Object.getOwnPropertyDescriptor(Event.prototype,"isTrusted")){var t=function(){var e=new MouseEvent("click",{bubbles:!0,cancelable:!0,composed:!0});this.__shady_dispatchEvent(e)};Element.prototype.click?Element.prototype.click=t:HTMLElement.prototype.click&&(HTMLElement.prototype.click=t)}}()),function ds(){for(var t in cn)window.__shady_native_addEventListener(t,function(e){e.__target||(Br(e),hs(e))},!0)}(),window.Event=fs,window.CustomEvent=ps,window.MouseEvent=_s,window.ShadowRoot=ie}var Sn=window.Document.prototype.createElement,Tn=window.Document.prototype.createElementNS,Ps=window.Document.prototype.importNode,As=window.Document.prototype.prepend,js=window.Document.prototype.append,Rs=window.DocumentFragment.prototype.prepend,Fs=window.DocumentFragment.prototype.append,To=window.Node.prototype.cloneNode,ue=window.Node.prototype.appendChild,bo=window.Node.prototype.insertBefore,bn=window.Node.prototype.removeChild,Oo=window.Node.prototype.replaceChild,On=Object.getOwnPropertyDescriptor(window.Node.prototype,"textContent"),Do=window.Element.prototype.attachShadow,Dn=Object.getOwnPropertyDescriptor(window.Element.prototype,"innerHTML"),xn=window.Element.prototype.getAttribute,xo=window.Element.prototype.setAttribute,Mo=window.Element.prototype.removeAttribute,ce=window.Element.prototype.getAttributeNS,Lo=window.Element.prototype.setAttributeNS,Po=window.Element.prototype.removeAttributeNS,Ao=window.Element.prototype.insertAdjacentElement,jo=window.Element.prototype.insertAdjacentHTML,Hs=window.Element.prototype.prepend,Is=window.Element.prototype.append,Ro=window.Element.prototype.before,Fo=window.Element.prototype.after,Ho=window.Element.prototype.replaceWith,Io=window.Element.prototype.remove,Us=window.HTMLElement,Mn=Object.getOwnPropertyDescriptor(window.HTMLElement.prototype,"innerHTML"),Uo=window.HTMLElement.prototype.insertAdjacentElement,ko=window.HTMLElement.prototype.insertAdjacentHTML,Bo=new Set;function Vo(t){var e=Bo.has(t);return t=/^[a-z][.0-9_a-z]*-[\-.0-9_a-z]*$/.test(t),!e&&t}"annotation-xml color-profile font-face font-face-src font-face-uri font-face-format font-face-name missing-glyph".split(" ").forEach(function(t){return Bo.add(t)});var ks=document.contains?document.contains.bind(document):document.documentElement.contains.bind(document.documentElement);function A(t){var e=t.isConnected;if(void 0!==e)return e;if(ks(t))return!0;for(;t&&!(t.__CE_isImportDocument||t instanceof Document);)t=t.parentNode||(window.ShadowRoot&&t instanceof ShadowRoot?t.host:void 0);return!(!t||!(t.__CE_isImportDocument||t instanceof Document))}function Ln(t){var e=t.children;if(e)return Array.prototype.slice.call(e);for(e=[],t=t.firstChild;t;t=t.nextSibling)t.nodeType===Node.ELEMENT_NODE&&e.push(t);return e}function Pn(t,e){for(;e&&e!==t&&!e.nextSibling;)e=e.parentNode;return e&&e!==t?e.nextSibling:null}function An(t,e,n){for(var r=t;r;){if(r.nodeType===Node.ELEMENT_NODE){var o=r;e(o);var i=o.localName;if("link"===i&&"import"===o.getAttribute("rel")){if(r=o.import,void 0===n&&(n=new Set),r instanceof Node&&!n.has(r))for(n.add(r),r=r.firstChild;r;r=r.nextSibling)An(r,e,n);r=Pn(t,o);continue}if("template"===i){r=Pn(t,o);continue}if(o=o.__CE_shadowRoot)for(o=o.firstChild;o;o=o.nextSibling)An(o,e,n)}r=r.firstChild?r.firstChild:Pn(t,r)}}function j(t,e,n){t[e]=n}function Wo(t){var e=document;this.b=t,this.a=e,this.P=void 0,lt(this.b,this.a),"loading"===this.a.readyState&&(this.P=new MutationObserver(this.c.bind(this)),this.P.observe(this.a,{childList:!0,subtree:!0}))}function Go(t){t.P&&t.P.disconnect()}function $o(){var t=this;this.a=this.w=void 0,this.b=new Promise(function(e){t.a=e,t.w&&e(t.w)})}function G(t){this.f=new Map,this.u=new Map,this.ta=new Map,this.U=!1,this.b=t,this.ja=new Map,this.c=function(e){return e()},this.a=!1,this.F=[],this.va=t.f?new Wo(t):void 0}function Jo(t,e){if(!Vo(e))throw new SyntaxError("The element name '"+e+"' is not valid.");if(he(t,e))throw Error("A custom element with name '"+e+"' has already been defined.");if(t.U)throw Error("A custom element is already being defined.")}function Xo(t,e,n){var r;t.U=!0;try{var o=function(c){var f=i[c];if(void 0!==f&&!(f instanceof Function))throw Error("The '"+c+"' callback must be a function.");return f},i=n.prototype;if(!(i instanceof Object))throw new TypeError("The custom element constructor's prototype is not an object.");var s=o("connectedCallback"),a=o("disconnectedCallback"),l=o("adoptedCallback"),u=(r=o("attributeChangedCallback"))&&n.observedAttributes||[]}catch(c){throw c}finally{t.U=!1}return t.u.set(e,n={localName:e,constructorFunction:n,connectedCallback:s,disconnectedCallback:a,adoptedCallback:l,attributeChangedCallback:r,observedAttributes:u,constructionStack:[]}),t.ta.set(n.constructorFunction,n),n}function Zo(t){if(!1!==t.a){t.a=!1;for(var e=[],n=t.F,r=new Map,o=0;o<n.length;o++)r.set(n[o],[]);for(lt(t.b,document,{upgrade:function(l){if(void 0===l.__CE_state){var u=l.localName,c=r.get(u);c?c.push(l):t.u.has(u)&&e.push(l)}}}),o=0;o<e.length;o++)fe(t.b,e[o]);for(o=0;o<n.length;o++){for(var i=n[o],s=r.get(i),a=0;a<s.length;a++)fe(t.b,s[a]);(i=t.ja.get(i))&&i.resolve(void 0)}n.length=0}}function he(t,e){var n=t.u.get(e);if(n)return n;if(n=t.f.get(e)){t.f.delete(e);try{return Xo(t,e,n())}catch(r){Dt(r)}}}function de(){var t=ct&&ct.noDocumentConstructionObserver,e=ct&&ct.shadyDomFastWalk;this.b=[],this.c=[],this.a=!1,this.shadyDomFastWalk=e,this.f=!t}function Vt(t,e,n,r){var o=window.ShadyDOM;if(t.shadyDomFastWalk&&o&&o.inUse){if(e.nodeType===Node.ELEMENT_NODE&&n(e),e.querySelectorAll)for(t=o.nativeMethods.querySelectorAll.call(e,"*"),e=0;e<t.length;e++)n(t[e])}else An(e,n,r)}function jn(t,e){t.a&&Vt(t,e,function(n){return Ot(t,n)})}function Ot(t,e){if(t.a&&!e.__CE_patched){e.__CE_patched=!0;for(var n=0;n<t.b.length;n++)t.b[n](e);for(n=0;n<t.c.length;n++)t.c[n](e)}}function rt(t,e){var n=[];for(Vt(t,e,function(o){return n.push(o)}),e=0;e<n.length;e++){var r=n[e];1===r.__CE_state?t.connectedCallback(r):fe(t,r)}}function Q(t,e){var n=[];for(Vt(t,e,function(o){return n.push(o)}),e=0;e<n.length;e++){var r=n[e];1===r.__CE_state&&t.disconnectedCallback(r)}}function lt(t,e,n){var r=(n=void 0===n?{}:n).Za,o=n.upgrade||function(s){return fe(t,s)},i=[];for(Vt(t,e,function(s){if(t.a&&Ot(t,s),"link"===s.localName&&"import"===s.getAttribute("rel")){var a=s.import;a instanceof Node&&(a.__CE_isImportDocument=!0,a.__CE_registry=document.__CE_registry),a&&"complete"===a.readyState?a.__CE_documentLoadHandled=!0:s.addEventListener("load",function(){var l=s.import;if(!l.__CE_documentLoadHandled){l.__CE_documentLoadHandled=!0;var u=new Set;r&&(r.forEach(function(c){return u.add(c)}),u.delete(l)),lt(t,l,{Za:u,upgrade:o})}})}else i.push(s)},r),e=0;e<i.length;e++)o(i[e])}function fe(t,e){try{var n=e.ownerDocument,r=n.__CE_registry,o=r&&(n.defaultView||n.__CE_isImportDocument)?he(r,e.localName):void 0;if(o&&void 0===e.__CE_state){o.constructionStack.push(e);try{try{if(new o.constructorFunction!==e)throw Error("The custom element constructor did not produce the element being upgraded.")}finally{o.constructionStack.pop()}}catch(l){throw e.__CE_state=2,l}if(e.__CE_state=1,e.__CE_definition=o,o.attributeChangedCallback&&e.hasAttributes()){var i=o.observedAttributes;for(o=0;o<i.length;o++){var s=i[o],a=e.getAttribute(s);null!==a&&t.attributeChangedCallback(e,s,null,a,null)}}A(e)&&t.connectedCallback(e)}}catch(l){Dt(l)}}function Yo(t,e,n,r){var o=e.__CE_registry;if(o&&(null===r||"http://www.w3.org/1999/xhtml"===r)&&(o=he(o,n)))try{var i=new o.constructorFunction;if(void 0===i.__CE_state||void 0===i.__CE_definition)throw Error("Failed to construct '"+n+"': The returned value was not constructed with the HTMLElement constructor.");if("http://www.w3.org/1999/xhtml"!==i.namespaceURI)throw Error("Failed to construct '"+n+"': The constructed element's namespace must be the HTML namespace.");if(i.hasAttributes())throw Error("Failed to construct '"+n+"': The constructed element must not have any attributes.");if(null!==i.firstChild)throw Error("Failed to construct '"+n+"': The constructed element must not have any children.");if(null!==i.parentNode)throw Error("Failed to construct '"+n+"': The constructed element must not have a parent node.");if(i.ownerDocument!==e)throw Error("Failed to construct '"+n+"': The constructed element's owner document is incorrect.");if(i.localName!==n)throw Error("Failed to construct '"+n+"': The constructed element's local name is incorrect.");return i}catch(s){return Dt(s),e=null===r?Sn.call(e,n):Tn.call(e,r,n),Object.setPrototypeOf(e,HTMLUnknownElement.prototype),e.__CE_state=2,e.__CE_definition=void 0,Ot(t,e),e}return Ot(t,e=null===r?Sn.call(e,n):Tn.call(e,r,n)),e}function Dt(t){var e=t.message,n=t.sourceURL||t.fileName||"",r=t.line||t.lineNumber||0,i=void 0;void 0===ErrorEvent.prototype.initErrorEvent?i=new ErrorEvent("error",{cancelable:!0,message:e,filename:n,lineno:r,colno:t.column||t.columnNumber||0,error:t}):((i=document.createEvent("ErrorEvent")).initErrorEvent("error",!1,!0,e,n,r),i.preventDefault=function(){Object.defineProperty(this,"defaultPrevented",{configurable:!0,get:function(){return!0}})}),void 0===i.error&&Object.defineProperty(i,"error",{configurable:!0,enumerable:!0,get:function(){return t}}),window.dispatchEvent(i),i.defaultPrevented||console.error(t)}Wo.prototype.c=function(t){var e=this.a.readyState;for("interactive"!==e&&"complete"!==e||Go(this),e=0;e<t.length;e++)for(var n=t[e].addedNodes,r=0;r<n.length;r++)lt(this.b,n[r])},$o.prototype.resolve=function(t){if(this.w)throw Error("Already resolved.");this.w=t,this.a&&this.a(t)},(y=G.prototype).Qa=function(t,e){var n=this;if(!(e instanceof Function))throw new TypeError("Custom element constructor getters must be functions.");Jo(this,t),this.f.set(t,e),this.F.push(t),this.a||(this.a=!0,this.c(function(){return Zo(n)}))},y.define=function(t,e){var n=this;if(!(e instanceof Function))throw new TypeError("Custom element constructors must be functions.");Jo(this,t),Xo(this,t,e),this.F.push(t),this.a||(this.a=!0,this.c(function(){return Zo(n)}))},y.upgrade=function(t){lt(this.b,t)},y.get=function(t){if(t=he(this,t))return t.constructorFunction},y.whenDefined=function(t){if(!Vo(t))return Promise.reject(new SyntaxError("'"+t+"' is not a valid custom element name."));var e=this.ja.get(t);if(e)return e.b;e=new $o,this.ja.set(t,e);var n=this.u.has(t)||this.f.has(t);return t=-1===this.F.indexOf(t),n&&t&&e.resolve(void 0),e.b},y.polyfillWrapFlushCallback=function(t){this.va&&Go(this.va);var e=this.c;this.c=function(n){return t(function(){return e(n)})}},window.CustomElementRegistry=G,G.prototype.define=G.prototype.define,G.prototype.upgrade=G.prototype.upgrade,G.prototype.get=G.prototype.get,G.prototype.whenDefined=G.prototype.whenDefined,G.prototype.polyfillDefineLazy=G.prototype.Qa,G.prototype.polyfillWrapFlushCallback=G.prototype.polyfillWrapFlushCallback,de.prototype.connectedCallback=function(t){var e=t.__CE_definition;if(e.connectedCallback)try{e.connectedCallback.call(t)}catch(n){Dt(n)}},de.prototype.disconnectedCallback=function(t){var e=t.__CE_definition;if(e.disconnectedCallback)try{e.disconnectedCallback.call(t)}catch(n){Dt(n)}},de.prototype.attributeChangedCallback=function(t,e,n,r,o){var i=t.__CE_definition;if(i.attributeChangedCallback&&-1<i.observedAttributes.indexOf(e))try{i.attributeChangedCallback.call(t,e,n,r,o)}catch(s){Dt(s)}};var Ko=new function(){};function Rn(t,e,n){function r(o){return function(i){for(var s=[],a=0;a<arguments.length;++a)s[a]=arguments[a];a=[];for(var l=[],u=0;u<s.length;u++){var c=s[u];if(c instanceof Element&&A(c)&&l.push(c),c instanceof DocumentFragment)for(c=c.firstChild;c;c=c.nextSibling)a.push(c);else a.push(c)}for(o.apply(this,s),s=0;s<l.length;s++)Q(t,l[s]);if(A(this))for(s=0;s<a.length;s++)(l=a[s])instanceof Element&&rt(t,l)}}void 0!==n.prepend&&j(e,"prepend",r(n.prepend)),void 0!==n.append&&j(e,"append",r(n.append))}var ct=window.customElements;function Qo(){var t=new de;(function Ws(t){window.HTMLElement=function(){function e(){var n=this.constructor,r=document.__CE_registry.ta.get(n);if(!r)throw Error("Failed to construct a custom element: The constructor was not registered with `customElements`.");var o=r.constructionStack;if(0===o.length)return o=Sn.call(document,r.localName),Object.setPrototypeOf(o,n.prototype),o.__CE_state=1,o.__CE_definition=r,Ot(t,o),o;var i=o.length-1,s=o[i];if(s===Ko)throw Error("Failed to construct '"+r.localName+"': This element was already constructed.");return o[i]=Ko,Object.setPrototypeOf(s,n.prototype),Ot(t,s),s}return e.prototype=Us.prototype,Object.defineProperty(e.prototype,"constructor",{writable:!0,configurable:!0,enumerable:!1,value:e}),e}()})(t),function Gs(t){j(Document.prototype,"createElement",function(e){return Yo(t,this,e,null)}),j(Document.prototype,"importNode",function(e,n){return e=Ps.call(this,e,!!n),this.__CE_registry?lt(t,e):jn(t,e),e}),j(Document.prototype,"createElementNS",function(e,n){return Yo(t,this,n,e)}),Rn(t,Document.prototype,{prepend:As,append:js})}(t),Rn(t,DocumentFragment.prototype,{prepend:Rs,append:Fs}),function $s(t){function e(n,r){Object.defineProperty(n,"textContent",{enumerable:r.enumerable,configurable:!0,get:r.get,set:function(o){if(this.nodeType===Node.TEXT_NODE)r.set.call(this,o);else{var i=void 0;if(this.firstChild){var s=this.childNodes,a=s.length;if(0<a&&A(this)){i=Array(a);for(var l=0;l<a;l++)i[l]=s[l]}}if(r.set.call(this,o),i)for(o=0;o<i.length;o++)Q(t,i[o])}}})}j(Node.prototype,"insertBefore",function(n,r){if(n instanceof DocumentFragment){var o=Ln(n);if(n=bo.call(this,n,r),A(this))for(r=0;r<o.length;r++)rt(t,o[r]);return n}return o=n instanceof Element&&A(n),r=bo.call(this,n,r),o&&Q(t,n),A(this)&&rt(t,n),r}),j(Node.prototype,"appendChild",function(n){if(n instanceof DocumentFragment){var r=Ln(n);if(n=ue.call(this,n),A(this))for(var o=0;o<r.length;o++)rt(t,r[o]);return n}return r=n instanceof Element&&A(n),o=ue.call(this,n),r&&Q(t,n),A(this)&&rt(t,n),o}),j(Node.prototype,"cloneNode",function(n){return n=To.call(this,!!n),this.ownerDocument.__CE_registry?lt(t,n):jn(t,n),n}),j(Node.prototype,"removeChild",function(n){var r=n instanceof Element&&A(n),o=bn.call(this,n);return r&&Q(t,n),o}),j(Node.prototype,"replaceChild",function(n,r){if(n instanceof DocumentFragment){var o=Ln(n);if(n=Oo.call(this,n,r),A(this))for(Q(t,r),r=0;r<o.length;r++)rt(t,o[r]);return n}o=n instanceof Element&&A(n);var i=Oo.call(this,n,r),s=A(this);return s&&Q(t,r),o&&Q(t,n),s&&rt(t,n),i}),On&&On.get?e(Node.prototype,On):function Bs(t,e){t.a=!0,t.b.push(e)}(t,function(n){e(n,{enumerable:!0,configurable:!0,get:function(){for(var r=[],o=this.firstChild;o;o=o.nextSibling)o.nodeType!==Node.COMMENT_NODE&&r.push(o.textContent);return r.join("")},set:function(r){for(;this.firstChild;)bn.call(this,this.firstChild);null!=r&&""!==r&&ue.call(this,document.createTextNode(r))}})})}(t),function Xs(t){function e(o,i){Object.defineProperty(o,"innerHTML",{enumerable:i.enumerable,configurable:!0,get:i.get,set:function(s){var a=this,l=void 0;if(A(this)&&(l=[],Vt(t,this,function(f){f!==a&&l.push(f)})),i.set.call(this,s),l)for(var u=0;u<l.length;u++){var c=l[u];1===c.__CE_state&&t.disconnectedCallback(c)}return this.ownerDocument.__CE_registry?lt(t,this):jn(t,this),s}})}function n(o,i){j(o,"insertAdjacentElement",function(s,a){var l=A(a);return s=i.call(this,s,a),l&&Q(t,a),A(s)&&rt(t,a),s})}function r(o,i){function s(a,l){for(var u=[];a!==l;a=a.nextSibling)u.push(a);for(l=0;l<u.length;l++)lt(t,u[l])}j(o,"insertAdjacentHTML",function(a,l){if("beforebegin"===(a=a.toLowerCase())){var u=this.previousSibling;i.call(this,a,l),s(u||this.parentNode.firstChild,this)}else if("afterbegin"===a)u=this.firstChild,i.call(this,a,l),s(this.firstChild,u);else if("beforeend"===a)u=this.lastChild,i.call(this,a,l),s(u||this.firstChild,null);else{if("afterend"!==a)throw new SyntaxError("The value provided ("+String(a)+") is not one of 'beforebegin', 'afterbegin', 'beforeend', or 'afterend'.");u=this.nextSibling,i.call(this,a,l),s(this.nextSibling,u)}})}Do&&j(Element.prototype,"attachShadow",function(o){if(o=Do.call(this,o),t.a&&!o.__CE_patched){o.__CE_patched=!0;for(var i=0;i<t.b.length;i++)t.b[i](o)}return this.__CE_shadowRoot=o}),Dn&&Dn.get?e(Element.prototype,Dn):Mn&&Mn.get?e(HTMLElement.prototype,Mn):function Vs(t,e){t.a=!0,t.c.push(e)}(t,function(o){e(o,{enumerable:!0,configurable:!0,get:function(){return To.call(this,!0).innerHTML},set:function(i){var s="template"===this.localName,a=s?this.content:this,l=Tn.call(document,this.namespaceURI,this.localName);for(l.innerHTML=i;0<a.childNodes.length;)bn.call(a,a.childNodes[0]);for(i=s?l.content:l;0<i.childNodes.length;)ue.call(a,i.childNodes[0])}})}),j(Element.prototype,"setAttribute",function(o,i){if(1!==this.__CE_state)return xo.call(this,o,i);var s=xn.call(this,o);xo.call(this,o,i),i=xn.call(this,o),t.attributeChangedCallback(this,o,s,i,null)}),j(Element.prototype,"setAttributeNS",function(o,i,s){if(1!==this.__CE_state)return Lo.call(this,o,i,s);var a=ce.call(this,o,i);Lo.call(this,o,i,s),s=ce.call(this,o,i),t.attributeChangedCallback(this,i,a,s,o)}),j(Element.prototype,"removeAttribute",function(o){if(1!==this.__CE_state)return Mo.call(this,o);var i=xn.call(this,o);Mo.call(this,o),null!==i&&t.attributeChangedCallback(this,o,i,null,null)}),j(Element.prototype,"removeAttributeNS",function(o,i){if(1!==this.__CE_state)return Po.call(this,o,i);var s=ce.call(this,o,i);Po.call(this,o,i);var a=ce.call(this,o,i);s!==a&&t.attributeChangedCallback(this,i,s,a,o)}),Uo?n(HTMLElement.prototype,Uo):Ao&&n(Element.prototype,Ao),ko?r(HTMLElement.prototype,ko):jo&&r(Element.prototype,jo),Rn(t,Element.prototype,{prepend:Hs,append:Is}),function Js(t){function e(r){return function(o){for(var i=[],s=0;s<arguments.length;++s)i[s]=arguments[s];s=[];for(var a=[],l=0;l<i.length;l++){var u=i[l];if(u instanceof Element&&A(u)&&a.push(u),u instanceof DocumentFragment)for(u=u.firstChild;u;u=u.nextSibling)s.push(u);else s.push(u)}for(r.apply(this,i),i=0;i<a.length;i++)Q(t,a[i]);if(A(this))for(i=0;i<s.length;i++)(a=s[i])instanceof Element&&rt(t,a)}}var n=Element.prototype;void 0!==Ro&&j(n,"before",e(Ro)),void 0!==Fo&&j(n,"after",e(Fo)),void 0!==Ho&&j(n,"replaceWith",function(r){for(var o=[],i=0;i<arguments.length;++i)o[i]=arguments[i];i=[];for(var s=[],a=0;a<o.length;a++){var l=o[a];if(l instanceof Element&&A(l)&&s.push(l),l instanceof DocumentFragment)for(l=l.firstChild;l;l=l.nextSibling)i.push(l);else i.push(l)}for(a=A(this),Ho.apply(this,o),o=0;o<s.length;o++)Q(t,s[o]);if(a)for(Q(t,this),o=0;o<i.length;o++)(s=i[o])instanceof Element&&rt(t,s)}),void 0!==Io&&j(n,"remove",function(){var r=A(this);Io.call(this),r&&Q(t,this)})}(t)}(t),t=new G(t),document.__CE_registry=t,Object.defineProperty(window,"customElements",{configurable:!0,enumerable:!0,value:t})}function Fn(){this.end=this.start=0,this.rules=this.parent=this.previous=null,this.cssText=this.parsedCssText="",this.atRule=!1,this.type=0,this.parsedSelector=this.selector=this.keyframesName=""}function Hn(t){var e=t=t.replace(Ys,"").replace(Ks,""),n=new Fn;n.start=0,n.end=e.length;for(var r=n,o=0,i=e.length;o<i;o++)if("{"===e[o]){r.rules||(r.rules=[]);var s=r,a=s.rules[s.rules.length-1]||null;(r=new Fn).start=o+1,r.parent=s,r.previous=a,s.rules.push(r)}else"}"===e[o]&&(r.end=o+1,r=r.parent||n);return qo(n,t)}function qo(t,e){var n=e.substring(t.start,t.end-1);if(t.parsedCssText=t.cssText=n.trim(),t.parent&&(n=function Zs(t){return t.replace(/\\([0-9a-f]{1,6})\s/gi,function(e,n){for(n=6-(e=n).length;n--;)e="0"+e;return"\\"+e})}(n=e.substring(t.previous?t.previous.end:t.parent.start,t.start-1)),n=(n=n.replace(ii," ")).substring(n.lastIndexOf(";")+1),n=t.parsedSelector=t.selector=n.trim(),t.atRule=0===n.indexOf("@"),t.atRule?0===n.indexOf("@media")?t.type=ei:n.match(zs)&&(t.type=In,t.keyframesName=t.selector.split(ii).pop()):t.type=0===n.indexOf("--")?ni:ti),n=t.rules)for(var r=0,o=n.length,i=void 0;r<o&&(i=n[r]);r++)qo(i,e);return t}function zo(t,e,n){n=void 0===n?"":n;var r="";if(t.cssText||t.rules){var i,o=t.rules;if((i=o)&&(i=!((i=o[0])&&i.selector&&0===i.selector.indexOf("--"))),i){i=0;for(var s=o.length,a=void 0;i<s&&(a=o[i]);i++)r=zo(a,e,r)}else(r=(e=e?t.cssText:(e=(e=t.cssText).replace(ri,"").replace(oi,"")).replace(Qs,"").replace(qs,"")).trim())&&(r="  "+r+"\n")}return r&&(t.selector&&(n+=t.selector+" {\n"),n+=r,t.selector&&(n+="}\n\n")),n}ct&&!ct.forcePolyfill&&"function"==typeof ct.define&&"function"==typeof ct.get||Qo(),window.__CE_installPolyfill=Qo;var Un,pe,ti=1,In=7,ei=4,ni=1e3,Ys=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//gim,Ks=/@import[^;]*;/gim,ri=/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?(?:[;\n]|$)/gim,oi=/(?:^[^;\-\s}]+)?--[^;{}]*?:[^{};]*?{[^}]*?}(?:[;\n]|$)?/gim,Qs=/@apply\s*\(?[^);]*\)?\s*(?:[;\n]|$)?/gim,qs=/[^;:]*?:[^;]*?var\([^;]*\)(?:[;\n]|$)?/gim,zs=/^@[^\s]*keyframes/,ii=/\s+/g,U=!(window.ShadyDOM&&window.ShadyDOM.inUse);function si(t){Un=(!t||!t.shimcssproperties)&&(U||!(navigator.userAgent.match(/AppleWebKit\/601|Edge\/15/)||!window.CSS||!CSS.supports||!CSS.supports("box-shadow","0 0 0 var(--foo)")))}window.ShadyCSS&&void 0!==window.ShadyCSS.cssBuild&&(pe=window.ShadyCSS.cssBuild);var xt=!(!window.ShadyCSS||!window.ShadyCSS.disableRuntime);window.ShadyCSS&&void 0!==window.ShadyCSS.nativeCss?Un=window.ShadyCSS.nativeCss:window.ShadyCSS?(si(window.ShadyCSS),window.ShadyCSS=void 0):si(window.WebComponents&&window.WebComponents.flags);var J=Un,_e=/(?:^|[;\s{]\s*)(--[\w-]*?)\s*:\s*(?:((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};{])+)|\{([^}]*)\}(?:(?=[;\s}])|$))/gi,ve=/(?:^|\W+)@apply\s*\(?([^);\n]*)\)?/gi,ta=/(--[\w-]+)\s*([:,;)]|$)/gi,ea=/(animation\s*:)|(animation-name\s*:)/,na=/@media\s(.*)/,ra=/\{[^}]*\}/g,ai=new Set;function Mt(t,e){return t?("string"==typeof t&&(t=Hn(t)),e&&Lt(t,e),zo(t,J)):""}function Wt(t){return!t.__cssRules&&t.textContent&&(t.__cssRules=Hn(t.textContent)),t.__cssRules||null}function li(t){return!!t.parent&&t.parent.type===In}function Lt(t,e,n,r){if(t){var o=!1,i=t.type;if(r&&i===ei){var s=t.selector.match(na);s&&(window.matchMedia(s[1]).matches||(o=!0))}if(i===ti?e(t):n&&i===In?n(t):i===ni&&(o=!0),(t=t.rules)&&!o)for(o=0,i=t.length,s=void 0;o<i&&(s=t[o]);o++)Lt(s,e,n,r)}}function kn(t,e,n,r){var o=document.createElement("style");return e&&o.setAttribute("scope",e),o.textContent=t,ci(o,n,r),o}var mt=null;function ui(t){t=document.createComment(" Shady DOM styles for "+t+" ");var e=document.head;return e.insertBefore(t,(mt?mt.nextSibling:null)||e.firstChild),mt=t}function ci(t,e,n){(e=e||document.head).insertBefore(t,n&&n.nextSibling||e.firstChild),mt?t.compareDocumentPosition(mt)===Node.DOCUMENT_POSITION_PRECEDING&&(mt=t):mt=t}function Bn(t,e){for(var n=0,r=t.length;e<r;e++)if("("===t[e])n++;else if(")"===t[e]&&0==--n)return e;return-1}function hi(t,e){var n=t.indexOf("var(");if(-1===n)return e(t,"","","");var r=Bn(t,n+3),o=t.substring(n+4,r);return n=t.substring(0,n),t=hi(t.substring(r+1),e),-1===(r=o.indexOf(","))?e(n,o.trim(),"",t):e(n,o.substring(0,r).trim(),o.substring(r+1).trim(),t)}function me(t,e){U?t.setAttribute("class",e):window.ShadyDOM.nativeMethods.setAttribute.call(t,"class",e)}var Gt=window.ShadyDOM&&window.ShadyDOM.wrap||function(t){return t};function yt(t){var e=t.localName,n="";return e?-1<e.indexOf("-")||(n=e,e=t.getAttribute&&t.getAttribute("is")||""):(e=t.is,n=t.extends),{is:e,Y:n}}function di(t){for(var e=[],n="",r=0;0<=r&&r<t.length;r++)if("("===t[r]){var o=Bn(t,r);n+=t.slice(r,o+1),r=o}else","===t[r]?(e.push(n),n=""):n+=t[r];return n&&e.push(n),e}function Pt(t){if(void 0!==pe)return pe;if(void 0===t.__cssBuild){var e=t.getAttribute("css-build");if(e)t.__cssBuild=e;else{if(""!==(e=(e="template"===t.localName?t.content.firstChild:t.firstChild)instanceof Comment&&"css-build"===(e=e.textContent.trim().split(":"))[0]?e[1]:"")){var n="template"===t.localName?t.content.firstChild:t.firstChild;n.parentNode.removeChild(n)}t.__cssBuild=e}}return t.__cssBuild||""}function Vn(t){return!(""===(t=void 0===t?"":t)||!J)&&(U?"shadow"===t:"shady"===t)}function ye(){}function ge(t,e,n){var r;if(e.nodeType===Node.ELEMENT_NODE&&n(e),r="template"===e.localName?(e.content||e._content||e).childNodes:e.children||e.childNodes)for(e=0;e<r.length;e++)ge(t,r[e],n)}function gt(t,e,n){if(e)if(t.classList)n?(t.classList.remove("style-scope"),t.classList.remove(e)):(t.classList.add("style-scope"),t.classList.add(e));else if(t.getAttribute){var r=t.getAttribute("class");n?r&&me(t,e=r.replace("style-scope","").replace(e,"")):me(t,(r?r+" ":"")+"style-scope "+e)}}function ia(t,e,n){ge(tt,t,function(r){gt(r,e,!0),gt(r,n)})}function sa(t,e){ge(tt,t,function(n){gt(n,e||"",!0)})}function we(t,e,n,r,o){var i=tt;return""===(o=void 0===o?"":o)&&(o=U||"shady"===(void 0===r?"":r)?Mt(e,n):function aa(t,e,n,r,o){var i=Wn(n,r);return n=n?"."+n:"",Mt(e,function(s){s.c||(s.selector=s.B=Gn(t,s,t.b,n,i),s.c=!0),o&&o(s,n,i)})}(i,e,(t=yt(t)).is,t.Y,n)+"\n\n"),o.trim()}function Wn(t,e){return e?"[is="+t+"]":t}function Gn(t,e,n,r,o){var i=di(e.selector);if(!li(e)){e=0;for(var s=i.length,a=void 0;e<s&&(a=i[e]);e++)i[e]=n.call(t,a,r,o)}return i.filter(function(l){return!!l}).join(",")}function fi(t){return t.replace($n,function(e,n,r){return-1<r.indexOf("+")?r=r.replace(/\+/g,"___"):-1<r.indexOf("___")&&(r=r.replace(/___/g,"+")),":"+n+"("+r+")"})}function pi(t,e){t=t.split(/(\[.+?\])/);for(var n=[],r=0;r<t.length;r++)if(r%2==1)n.push(t[r]);else{var o=t[r];(""!==o||r!==t.length-1)&&((o=o.split(":"))[0]+=e,n.push(o.join(":")))}return n.join("")}function _i(t){":root"===t.selector&&(t.selector="html")}ye.prototype.b=function(t,e,n){var r=!1;t=t.trim();var o=$n.test(t);o&&(t=fi(t=t.replace($n,function(a,l,u){return":"+l+"("+u.replace(/\s/g,"")+")"})));var i=yi.test(t);if(i){var s=function la(t){for(var n,e=[];n=t.match(yi);){var r=n.index,o=Bn(t,r);if(-1===o)throw Error(n.input+" selector missing ')'");n=t.slice(r,o+1),t=t.replace(n,"\ue000"),e.push(n)}return{oa:t,matches:e}}(t);t=s.oa,s=s.matches}return t=(t=t.replace(fa,":host $1")).replace(da,function(a,l,u){return r||(a=function ca(t,e,n,r){var o=t.indexOf("::slotted");if(0<=t.indexOf(":host")?t=function ha(t,e){var n=t.match(mi);return(n=n&&n[2].trim()||"")?n[0].match(vi)?t.replace(mi,function(r,o,i){return e+i}):n.split(vi)[0]===e?n:"should_not_match":t.replace(":host",e)}(t,r):0!==o&&(t=n?pi(t,n):t),n=!1,0<=o&&(e="",n=!0),n){var i=!0;n&&(t=t.replace(pa,function(s,a){return" > "+a}))}return{value:t,Ga:e,stop:i}}(u,l,e,n),r=r||a.stop,l=a.Ga,u=a.value),l+u}),i&&(t=function ua(t,e){var n=t.split("\ue000");return e.reduce(function(r,o,i){return r+o+n[i+1]},n[0])}(t,s)),o&&(t=fi(t)),t.replace(_a,function(a,l,u,c){return'[dir="'+u+'"] '+l+c+", "+l+'[dir="'+u+'"]'+c})},ye.prototype.c=function(t){return t.match(":host")?"":t.match("::slotted")?this.b(t,":not(.style-scope)"):pi(t.trim(),":not(.style-scope)")},st.Object.defineProperties(ye.prototype,{a:{configurable:!0,enumerable:!0,get:function(){return"style-scope"}}});var $n=/:(nth[-\w]+)\(([^)]+)\)/,da=/(^|[\s>+~]+)((?:\[.+?\]|[^\s>+~=[])+)/g,vi=/[[.:#*]/,fa=/^(::slotted)/,mi=/(:host)(?:\(((?:\([^)(]*\)|[^)(]*)+?)\))/,pa=/(?:::slotted)(?:\(((?:\([^)(]*\)|[^)(]*)+?)\))/,_a=/(.*):dir\((?:(ltr|rtl))\)(.*)/,yi=/:(?:matches|any|-(?:webkit|moz)-any)/,tt=new ye;function At(t,e,n,r,o){this.J=t||null,this.b=e||null,this.ka=n||[],this.H=null,this.cssBuild=o||"",this.Y=r||"",this.a=this.I=this.M=null}function et(t){return t?t.__styleInfo:null}function Jn(t,e){return t.__styleInfo=e}function gi(t){var e=this.matches||this.matchesSelector||this.mozMatchesSelector||this.msMatchesSelector||this.oMatchesSelector||this.webkitMatchesSelector;return e&&e.call(this,t)}At.prototype._getStyleRules=At.prototype.c=function(){return this.J};var va=/:host\s*>\s*/,ma=navigator.userAgent.match("Trident");function wi(){}function Ee(t){if(!t.A){var e={},n={};Ne(t,n)&&(e.L=n,t.rules=null),e.cssText=t.parsedCssText.replace(ra,"").replace(_e,""),t.A=e}}function Ne(t,e){var n=t.A;if(!n){n=t.parsedCssText;for(var r;t=_e.exec(n);)("inherit"!==(r=(t[2]||t[3]).trim())||"unset"!==r)&&(e[t[1].trim()]=r),r=!0;return r}if(n.L)return Object.assign(e,n.L),!0}function $t(t,e,n){return e&&(e=0<=e.indexOf(";")?Xn(t,e,n):hi(e,function(r,o,i,s){return o?((o=$t(t,n[o],n))&&"initial"!==o?"apply-shim-inherit"===o&&(o="inherit"):o=$t(t,n[i]||i,n)||i,r+(o||"")+s):r+s})),e&&e.trim()||""}function Xn(t,e,n){e=e.split(";");for(var o,i,r=0;r<e.length;r++)if(o=e[r]){if(ve.lastIndex=0,i=ve.exec(o))o=$t(t,n[i[1]],n);else if(-1!==(i=o.indexOf(":"))){var s=o.substring(i);s=$t(t,s=s.trim(),n)||s,o=o.substring(0,i)+s}e[r]=o&&o.lastIndexOf(";")===o.length-1?o.slice(0,-1):o||""}return e.join(";")}function Sa(t){return function(e){return e.replace(t.f,t.a)}}function Ta(t,e){var n=Ce,r=Wt(t);t.textContent=Mt(r,function(o){var i=o.cssText=o.parsedCssText;o.A&&o.A.cssText&&(i=i.replace(ri,"").replace(oi,""),o.cssText=Xn(n,i,e))})}st.Object.defineProperties(wi.prototype,{a:{configurable:!0,enumerable:!0,get:function(){return"x-scope"}}});var Ce=new wi,jt={},Se=window.customElements;if(Se&&!U&&!xt){var ba=Se.define;Se.define=function(t,e,n){jt[t]||(jt[t]=ui(t)),ba.call(Se,t,e,n)}}function Ei(){this.cache={}}function Ni(){}Ei.prototype.store=function(t,e,n,r){var o=this.cache[t]||[];o.push({L:e,styleElement:n,I:r}),100<o.length&&o.shift(),this.cache[t]=o};var Oa=new RegExp(tt.a+"\\s*([^\\s]*)");function Ci(t){return(t=(t.classList&&t.classList.value?t.classList.value:t.getAttribute("class")||"").match(Oa))?t[1]:""}function Zn(t){var e=Gt(t).getRootNode();return e===t||e===t.ownerDocument?"":(t=e.host)?yt(t).is:""}function Si(t){for(var e=0;e<t.length;e++){var n=t[e];if(n.target!==document.documentElement&&n.target!==document.head)for(var r=0;r<n.addedNodes.length;r++){var o=n.addedNodes[r];if(o.nodeType===Node.ELEMENT_NODE){var i=o.getRootNode(),s=Ci(o);if(s&&i===o.ownerDocument&&("style"!==o.localName&&"template"!==o.localName||""===Pt(o)))sa(o,s);else if(i instanceof ShadowRoot)for((i=Zn(o))!==s&&ia(o,s,i),o=window.ShadyDOM.nativeMethods.querySelectorAll.call(o,":not(."+tt.a+")"),s=0;s<o.length;s++){var a=Zn(i=o[s]);a&&gt(i,a)}}}}}if(!(U||window.ShadyDOM&&window.ShadyDOM.handlesDynamicScoping)){var Ti=new MutationObserver(Si),bi=function(t){Ti.observe(t,{childList:!0,subtree:!0})};if(window.customElements&&!window.customElements.polyfillWrapFlushCallback)bi(document);else{var Yn=function(){bi(document.body)};window.HTMLImports?window.HTMLImports.whenReady(Yn):requestAnimationFrame(function(){if("loading"===document.readyState){var t=function(){Yn(),document.removeEventListener("readystatechange",t)};document.addEventListener("readystatechange",t)}else Yn()})}Ni=function(){Si(Ti.takeRecords())}}var Te={},Da=Promise.resolve();function xa(t){(t=Te[t])&&(t._applyShimCurrentVersion=t._applyShimCurrentVersion||0,t._applyShimValidatingVersion=t._applyShimValidatingVersion||0,t._applyShimNextVersion=(t._applyShimNextVersion||0)+1)}function Oi(t){return t._applyShimCurrentVersion===t._applyShimNextVersion}var Di={},xi=new Ei;function C(){this.F={},this.c=document.documentElement;var t=new Fn;t.rules=[],this.f=Jn(this.c,new At(t)),this.u=!1,this.a=this.b=null}function Mi(t){var e=yt(t),n=e.is;e=e.Y;var r=jt[n]||null,o=Te[n];if(o)return Jn(t,e=new At(n=o._styleAst,r,o.a,e,o=Pt(o))),e}function Jt(t){if(!t.b&&window.ShadyCSS&&window.ShadyCSS.ApplyShim){t.b=window.ShadyCSS.ApplyShim,t.b.invalidCallback=xa;var e=!0}else e=!1;return function La(t){!t.a&&window.ShadyCSS&&window.ShadyCSS.CustomStyleInterface&&(t.a=window.ShadyCSS.CustomStyleInterface,t.a.transformCallback=function(e){t.xa(e)},t.a.validateCallback=function(){requestAnimationFrame(function(){(t.a.enqueued||t.u)&&t.flushCustomStyles()})})}(t),e}function Li(t,e,n){var r=yt(e).is;if(n.H){var i,o=n.H;for(i in o)null===i?e.style.removeProperty(i):e.style.setProperty(i,o[i])}!(!(o=Te[r])&&e!==t.c||o&&""!==Pt(o))&&o&&o._style&&!Oi(o)&&((Oi(o)||o._applyShimValidatingVersion!==o._applyShimNextVersion)&&(Jt(t),t.b&&t.b.transformRules(o._styleAst,r),o._style.textContent=we(e,n.J),function Ma(t){t._applyShimValidatingVersion=t._applyShimNextVersion,t._validating||(t._validating=!0,Da.then(function(){t._applyShimCurrentVersion=t._applyShimNextVersion,t._validating=!1}))}(o)),U&&(t=e.shadowRoot)&&(t=t.querySelector("style"))&&(t.textContent=we(e,n.J)),n.J=o._styleAst)}function Kn(t,e){return(e=Gt(e).getRootNode().host)?et(e)||Mi(e)?e:Kn(t,e):t.c}function Qn(t,e,n){var r=Kn(t,e),o=et(r),i=o.M;for(var s in r===t.c||i||(Qn(t,r,o),i=o.M),t=Object.create(i||null),r=function Ea(t,e,n){var r={},o={};return Lt(e,function(i){!function wa(t,e,n,r){if(e.A||Ee(e),e.A.L){var o=yt(t);t=o.is,o=o.Y,o=t?Wn(t,o):"html";var i=e.parsedSelector,s=!!i.match(va)||"html"===o&&-1<i.indexOf("html"),a=0===i.indexOf(":host")&&!s;"shady"===n&&(a=!(s=i===o+" > *."+o||-1!==i.indexOf("html"))&&0===i.indexOf(o)),(s||a)&&(n=o,a&&(e.B||(e.B=Gn(tt,e,tt.b,t?"."+t:"",o)),n=e.B||o),s&&"html"===o&&(n=e.B||e.u),r({oa:n,Na:a,ab:s}))}}(t,i,n,function(s){gi.call(t._element||t,s.oa)&&Ne(i,s.Na?r:o)})},null,!0),{Ta:o,La:r}}(e,n.J,n.cssBuild),e=function ga(t,e){var n={},r=[];return Lt(t,function(o){o.A||Ee(o);var i=o.B||o.parsedSelector;e&&o.A.L&&i&&gi.call(e,i)&&(Ne(o,n),o=o.index,i=parseInt(o/32,10),r[i]=(r[i]||0)|1<<o%32)},null,!0),{L:n,key:r}}(o.J,e).L,Object.assign(t,r.La,e,r.Ta),e=n.H)((o=e[s])||0===o)&&(t[s]=o);for(s=Ce,e=Object.getOwnPropertyNames(t),o=0;o<e.length;o++)t[r=e[o]]=$t(s,t[r],t);n.M=t}(y=C.prototype).flush=function(){Ni()},y.Ja=function(t){return Wt(t)},y.Xa=function(t){return Mt(t)},y.prepareTemplate=function(t,e,n){this.prepareTemplateDom(t,e),this.prepareTemplateStyles(t,e,n)},y.prepareTemplateStyles=function(t,e,n){if(!t._prepared&&!xt){U||jt[e]||(jt[e]=ui(e)),t._prepared=!0,t.name=e,t.extends=n,Te[e]=t;var r=Pt(t),o=Vn(r);n={is:e,extends:n};for(var i=[],s=t.content.querySelectorAll("style"),a=0;a<s.length;a++){var l=s[a];if(l.hasAttribute("shady-unscoped")){if(!U){var u=l.textContent;ai.has(u)||(ai.add(u),u=l.cloneNode(!0),document.head.appendChild(u)),l.parentNode.removeChild(l)}}else i.push(l.textContent),l.parentNode.removeChild(l)}i=i.join("").trim()+(Di[e]||""),Jt(this),o||((s=!r)&&(s=ve.test(i)||_e.test(i),ve.lastIndex=0,_e.lastIndex=0),a=Hn(i),s&&J&&this.b&&this.b.transformRules(a,e),t._styleAst=a),s=[],J||(s=function ya(t){var e={},n=[],r=0;for(var o in Lt(t,function(i){Ee(i),i.index=r++,i=i.A.cssText;for(var s;s=ta.exec(i);)":"!==s[2]&&(e[s[1]]=!0)},function(i){n.push(i)}),t.b=n,t=[],e)t.push(o);return t}(t._styleAst)),(!s.length||J)&&(a=U?t.content:null,e=jt[e]||null,r=(r=we(n,t._styleAst,null,r,o?i:"")).length?kn(r,n.is,a,e):null,t._style=r),t.a=s}},y.Ra=function(t,e){Di[e]=t.join(" ")},y.prepareTemplateDom=function(t,e){if(!xt){var n=Pt(t);U||"shady"===n||t._domPrepared||(t._domPrepared=!0,function oa(t,e){ge(tt,t,function(n){gt(n,e||"")})}(t.content,e))}},y.flushCustomStyles=function(){if(!xt){var t=Jt(this);if(this.a){var e=this.a.processStyles();if((t||this.a.enqueued)&&!Vn(this.f.cssBuild)){if(J){if(!this.f.cssBuild)for(t=0;t<e.length;t++){var n=this.a.getStyleForCustomStyle(e[t]);if(n&&J&&this.b){var r=Wt(n);Jt(this),this.b.transformRules(r),n.textContent=Mt(r)}}}else{for(function Pa(t,e){e=e.map(function(n){return t.a.getStyleForCustomStyle(n)}).filter(function(n){return!!n}),e.sort(function(n,r){return(n=r.compareDocumentPosition(n))&Node.DOCUMENT_POSITION_FOLLOWING?1:n&Node.DOCUMENT_POSITION_PRECEDING?-1:0}),t.f.J.rules=e.map(function(n){return Wt(n)})}(this,e),Qn(this,this.c,this.f),t=0;t<e.length;t++)(n=this.a.getStyleForCustomStyle(e[t]))&&Ta(n,this.f.M);this.u&&this.styleDocument()}this.a.enqueued=!1}}}},y.styleElement=function(t,e){if(xt){if(e){et(t)||Jn(t,new At(null));var n=et(t);n.H=n.H||{},Object.assign(n.H,e),Li(this,t,n)}}else if(n=et(t)||Mi(t))if(t!==this.c&&(this.u=!0),e&&(n.H=n.H||{},Object.assign(n.H,e)),J)Li(this,t,n);else if(this.flush(),Qn(this,t,n),n.ka&&n.ka.length){var r;e=yt(t).is;t:{if(r=xi.cache[e])for(var o=r.length-1;0<=o;o--){var i=r[o];e:{for(var s=n.ka,a=0;a<s.length;a++){var l=s[a];if(i.L[l]!==n.M[l]){s=!1;break e}}s=!0}if(s){r=i;break t}}r=void 0}s=r?r.styleElement:null,o=n.I,(i=r&&r.I)||(i=e+"-"+(i=this.F[e]=(this.F[e]||0)+1)),n.I=i,i=n.I,a=Ce,a=s?s.textContent||"":function Na(t,e,n,r){var o=yt(e),i=Wn(o.is,o.Y),s=new RegExp("(?:^|[^.#[:])"+(e.extends?"\\"+i.slice(0,-1)+"\\]":i)+"($|[.:[\\s>+~])"),a=et(e);o=a.J,a=a.cssBuild;var l=function Ca(t,e){t=t.b;var n={};if(!U&&t)for(var r=0,o=t[r];r<t.length;o=t[++r]){var i=o,s=e;i.f=new RegExp("\\b"+i.keyframesName+"(?!\\B|-)","g"),i.a=i.keyframesName+"-"+s,i.B=i.B||i.selector,i.selector=i.B.replace(i.keyframesName,i.a),n[o.keyframesName]=Sa(o)}return n}(o,r);return we(e,o,function(u){var c="";if(u.A||Ee(u),u.A.cssText&&(c=Xn(t,u.A.cssText,n)),u.cssText=c,!U&&!li(u)&&u.cssText){var f=c=u.cssText;if(null==u.sa&&(u.sa=ea.test(c)),u.sa)if(null==u.ca)for(var x in u.ca=[],l)c!==(f=(f=l[x])(c))&&(c=f,u.ca.push(x));else{for(x=0;x<u.ca.length;++x)c=(f=l[u.ca[x]])(c);f=c}u.cssText=f,u.B=u.B||u.selector,c="."+r,f=0;for(var S=(x=di(u.B)).length,p=void 0;f<S&&(p=x[f]);f++)x[f]=p.match(s)?p.replace(i,c):c+" "+p;u.selector=x.join(",")}},a)}(a,t,n.M,i);var u=(l=et(t)).a;u&&!U&&u!==s&&(u._useCount--,0>=u._useCount&&u.parentNode&&u.parentNode.removeChild(u)),U?l.a?(l.a.textContent=a,s=l.a):a&&(s=kn(a,i,t.shadowRoot,l.b)):s?s.parentNode||(ma&&-1<a.indexOf("@media")&&(s.textContent=a),ci(s,null,l.b)):a&&(s=kn(a,i,null,l.b)),s&&(s._useCount=s._useCount||0,l.a!=s&&s._useCount++,l.a=s),i=s,U||(s=n.I,l=a=t.getAttribute("class")||"",o&&(l=a.replace(new RegExp("\\s*x-scope\\s*"+o+"\\s*","g")," ")),a!==(l+=(l?" ":"")+"x-scope "+s)&&me(t,l)),r||xi.store(e,n.M,i,n.I)}},y.styleDocument=function(t){this.styleSubtree(this.c,t)},y.styleSubtree=function(t,e){var n=Gt(t),r=n.shadowRoot,o=t===this.c;if((r||o)&&this.styleElement(t,e),t=o?n:r)for(t=Array.from(t.querySelectorAll("*")).filter(function(i){return Gt(i).shadowRoot}),e=0;e<t.length;e++)this.styleSubtree(t[e])},y.xa=function(t){var e=this,n=Pt(t);if(n!==this.f.cssBuild&&(this.f.cssBuild=n),!Vn(n)){var r=Wt(t);Lt(r,function(o){if(U)_i(o);else{var i=tt;o.selector=o.parsedSelector,_i(o),o.selector=o.B=Gn(i,o,i.c,void 0,void 0)}J&&""===n&&(Jt(e),e.b&&e.b.transformRule(o))}),J?t.textContent=Mt(r):this.f.J.rules.push(r)}},y.getComputedStyleValue=function(t,e){var n;return J||(n=(et(t)||et(Kn(this,t))).M[e]),(n=n||window.getComputedStyle(t).getPropertyValue(e))?n.trim():""},y.Wa=function(t,e){var r,n=Gt(t).getRootNode();if(r=e?("string"==typeof e?e:String(e)).split(/\s/):[],!(e=n.host&&n.host.localName)&&(n=t.getAttribute("class"))){n=n.split(/\s/);for(var o=0;o<n.length;o++)if(n[o]===tt.a){e=n[o+1];break}}e&&r.push(tt.a,e),J||(e=et(t))&&e.I&&r.push(Ce.a,e.I),me(t,r.join(" "))},y.Ea=function(t){return et(t)},y.Va=function(t,e){gt(t,e)},y.Ya=function(t,e){gt(t,e,!0)},y.Ua=function(t){return Zn(t)},y.Ha=function(t){return Ci(t)},C.prototype.flush=C.prototype.flush,C.prototype.prepareTemplate=C.prototype.prepareTemplate,C.prototype.styleElement=C.prototype.styleElement,C.prototype.styleDocument=C.prototype.styleDocument,C.prototype.styleSubtree=C.prototype.styleSubtree,C.prototype.getComputedStyleValue=C.prototype.getComputedStyleValue,C.prototype.setElementClass=C.prototype.Wa,C.prototype._styleInfoForNode=C.prototype.Ea,C.prototype.transformCustomStyleForDocument=C.prototype.xa,C.prototype.getStyleAst=C.prototype.Ja,C.prototype.styleAstToString=C.prototype.Xa,C.prototype.flushCustomStyles=C.prototype.flushCustomStyles,C.prototype.scopeNode=C.prototype.Va,C.prototype.unscopeNode=C.prototype.Ya,C.prototype.scopeForNode=C.prototype.Ua,C.prototype.currentScopeForNode=C.prototype.Ha,C.prototype.prepareAdoptedCssText=C.prototype.Ra,Object.defineProperties(C.prototype,{nativeShadow:{get:function(){return U}},nativeCss:{get:function(){return J}}});var qn,zn,X=new C;window.ShadyCSS&&(qn=window.ShadyCSS.ApplyShim,zn=window.ShadyCSS.CustomStyleInterface),window.ShadyCSS={ScopingShim:X,prepareTemplate:function(t,e,n){X.flushCustomStyles(),X.prepareTemplate(t,e,n)},prepareTemplateDom:function(t,e){X.prepareTemplateDom(t,e)},prepareTemplateStyles:function(t,e,n){X.flushCustomStyles(),X.prepareTemplateStyles(t,e,n)},styleSubtree:function(t,e){X.flushCustomStyles(),X.styleSubtree(t,e)},styleElement:function(t){X.flushCustomStyles(),X.styleElement(t)},styleDocument:function(t){X.flushCustomStyles(),X.styleDocument(t)},flushCustomStyles:function(){X.flushCustomStyles()},getComputedStyleValue:function(t,e){return X.getComputedStyleValue(t,e)},nativeCss:J,nativeShadow:U,cssBuild:pe,disableRuntime:xt},qn&&(window.ShadyCSS.ApplyShim=qn),zn&&(window.ShadyCSS.CustomStyleInterface=zn),function(t){function e(p){return""==p&&(i.call(this),this.i=!0),p.toLowerCase()}function n(p){var O=p.charCodeAt(0);return 32<O&&127>O&&-1==[34,35,60,62,63,96].indexOf(O)?p:encodeURIComponent(p)}function r(p){var O=p.charCodeAt(0);return 32<O&&127>O&&-1==[34,35,60,62,96].indexOf(O)?p:encodeURIComponent(p)}function o(p,O,T){function F(ot){dt.push(ot)}var w=O||"scheme start",Z=0,m="",ht=!1,nt=!1,dt=[];t:for(;(null!=p[Z-1]||0==Z)&&!this.i;){var h=p[Z];switch(w){case"scheme start":if(!h||!f.test(h)){if(O){F("Invalid scheme.");break t}m="",w="no scheme";continue}m+=h.toLowerCase(),w="scheme";break;case"scheme":if(h&&x.test(h))m+=h.toLowerCase();else{if(":"!=h){if(O){null!=h&&F("Code point not allowed in scheme: "+h);break t}m="",Z=0,w="no scheme";continue}if(this.h=m,m="",O)break t;void 0!==u[this.h]&&(this.C=!0),w="file"==this.h?"relative":this.C&&T&&T.h==this.h?"relative or authority":this.C?"authority first slash":"scheme data"}break;case"scheme data":"?"==h?(this.o="?",w="query"):"#"==h?(this.v="#",w="fragment"):null!=h&&"\t"!=h&&"\n"!=h&&"\r"!=h&&(this.ga+=n(h));break;case"no scheme":if(T&&void 0!==u[T.h]){w="relative";continue}F("Missing scheme."),i.call(this),this.i=!0;break;case"relative or authority":if("/"!=h||"/"!=p[Z+1]){F("Expected /, got: "+h),w="relative";continue}w="authority ignore slashes";break;case"relative":if(this.C=!0,"file"!=this.h&&(this.h=T.h),null==h){this.j=T.j,this.m=T.m,this.l=T.l.slice(),this.o=T.o,this.s=T.s,this.g=T.g;break t}if("/"==h||"\\"==h)"\\"==h&&F("\\ is an invalid code point."),w="relative slash";else if("?"==h)this.j=T.j,this.m=T.m,this.l=T.l.slice(),this.o="?",this.s=T.s,this.g=T.g,w="query";else{if("#"!=h){w=p[Z+1];var M=p[Z+2];("file"!=this.h||!f.test(h)||":"!=w&&"|"!=w||null!=M&&"/"!=M&&"\\"!=M&&"?"!=M&&"#"!=M)&&(this.j=T.j,this.m=T.m,this.s=T.s,this.g=T.g,this.l=T.l.slice(),this.l.pop()),w="relative path";continue}this.j=T.j,this.m=T.m,this.l=T.l.slice(),this.o=T.o,this.v="#",this.s=T.s,this.g=T.g,w="fragment"}break;case"relative slash":if("/"!=h&&"\\"!=h){"file"!=this.h&&(this.j=T.j,this.m=T.m,this.s=T.s,this.g=T.g),w="relative path";continue}"\\"==h&&F("\\ is an invalid code point."),w="file"==this.h?"file host":"authority ignore slashes";break;case"authority first slash":if("/"!=h){F("Expected '/', got: "+h),w="authority ignore slashes";continue}w="authority second slash";break;case"authority second slash":if(w="authority ignore slashes","/"!=h){F("Expected '/', got: "+h);continue}break;case"authority ignore slashes":if("/"!=h&&"\\"!=h){w="authority";continue}F("Expected authority, got: "+h);break;case"authority":if("@"==h){for(ht&&(F("@ already seen."),m+="%40"),ht=!0,h=0;h<m.length;h++)"\t"==(M=m[h])||"\n"==M||"\r"==M?F("Invalid whitespace in authority."):":"==M&&null===this.g?this.g="":(M=n(M),null!==this.g?this.g+=M:this.s+=M);m=""}else{if(null==h||"/"==h||"\\"==h||"?"==h||"#"==h){Z-=m.length,m="",w="host";continue}m+=h}break;case"file host":if(null==h||"/"==h||"\\"==h||"?"==h||"#"==h){2!=m.length||!f.test(m[0])||":"!=m[1]&&"|"!=m[1]?(0!=m.length&&(this.j=e.call(this,m),m=""),w="relative path start"):w="relative path";continue}"\t"==h||"\n"==h||"\r"==h?F("Invalid whitespace in file host."):m+=h;break;case"host":case"hostname":if(":"!=h||nt){if(null==h||"/"==h||"\\"==h||"?"==h||"#"==h){if(this.j=e.call(this,m),m="",w="relative path start",O)break t;continue}"\t"!=h&&"\n"!=h&&"\r"!=h?("["==h?nt=!0:"]"==h&&(nt=!1),m+=h):F("Invalid code point in host/hostname: "+h)}else if(this.j=e.call(this,m),m="",w="port","hostname"==O)break t;break;case"port":if(/[0-9]/.test(h))m+=h;else{if(null==h||"/"==h||"\\"==h||"?"==h||"#"==h||O){if(""!=m&&((m=parseInt(m,10))!=u[this.h]&&(this.m=m+""),m=""),O)break t;w="relative path start";continue}"\t"==h||"\n"==h||"\r"==h?F("Invalid code point in port: "+h):(i.call(this),this.i=!0)}break;case"relative path start":if("\\"==h&&F("'\\' not allowed in path."),w="relative path","/"!=h&&"\\"!=h)continue;break;case"relative path":null!=h&&"/"!=h&&"\\"!=h&&(O||"?"!=h&&"#"!=h)?"\t"!=h&&"\n"!=h&&"\r"!=h&&(m+=n(h)):("\\"==h&&F("\\ not allowed in relative path."),(M=c[m.toLowerCase()])&&(m=M),".."==m?(this.l.pop(),"/"!=h&&"\\"!=h&&this.l.push("")):"."==m&&"/"!=h&&"\\"!=h?this.l.push(""):"."!=m&&("file"==this.h&&0==this.l.length&&2==m.length&&f.test(m[0])&&"|"==m[1]&&(m=m[0]+":"),this.l.push(m)),m="","?"==h?(this.o="?",w="query"):"#"==h&&(this.v="#",w="fragment"));break;case"query":O||"#"!=h?null!=h&&"\t"!=h&&"\n"!=h&&"\r"!=h&&(this.o+=r(h)):(this.v="#",w="fragment");break;case"fragment":null!=h&&"\t"!=h&&"\n"!=h&&"\r"!=h&&(this.v+=h)}Z++}}function i(){this.s=this.ga=this.h="",this.g=null,this.m=this.j="",this.l=[],this.v=this.o="",this.C=this.i=!1}function s(p,O){void 0===O||O instanceof s||(O=new s(String(O))),this.a=p,i.call(this),o.call(this,this.a.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g,""),null,O)}var a=!1;try{var l=new URL("b","http://a");l.pathname="c%20d",a="http://a/c%20d"===l.href}catch{}if(!a){var u=Object.create(null);u.ftp=21,u.file=0,u.gopher=70,u.http=80,u.https=443,u.ws=80,u.wss=443;var c=Object.create(null);c["%2e"]=".",c[".%2e"]="..",c["%2e."]="..",c["%2e%2e"]="..";var f=/[a-zA-Z]/,x=/[a-zA-Z0-9\+\-\.]/;s.prototype={toString:function(){return this.href},get href(){if(this.i)return this.a;var p="";return(""!=this.s||null!=this.g)&&(p=this.s+(null!=this.g?":"+this.g:"")+"@"),this.protocol+(this.C?"//"+p+this.host:"")+this.pathname+this.o+this.v},set href(p){i.call(this),o.call(this,p)},get protocol(){return this.h+":"},set protocol(p){this.i||o.call(this,p+":","scheme start")},get host(){return this.i?"":this.m?this.j+":"+this.m:this.j},set host(p){!this.i&&this.C&&o.call(this,p,"host")},get hostname(){return this.j},set hostname(p){!this.i&&this.C&&o.call(this,p,"hostname")},get port(){return this.m},set port(p){!this.i&&this.C&&o.call(this,p,"port")},get pathname(){return this.i?"":this.C?"/"+this.l.join("/"):this.ga},set pathname(p){!this.i&&this.C&&(this.l=[],o.call(this,p,"relative path start"))},get search(){return this.i||!this.o||"?"==this.o?"":this.o},set search(p){!this.i&&this.C&&(this.o="?","?"==p[0]&&(p=p.slice(1)),o.call(this,p,"query"))},get hash(){return this.i||!this.v||"#"==this.v?"":this.v},set hash(p){this.i||(p?(this.v="#","#"==p[0]&&(p=p.slice(1)),o.call(this,p,"fragment")):this.v="")},get origin(){var p;if(this.i||!this.h)return"";switch(this.h){case"data":case"file":case"javascript":case"mailto":return"null"}return(p=this.host)?this.h+"://"+p:""}};var S=t.URL;S&&(s.createObjectURL=function(p){return S.createObjectURL.apply(S,arguments)},s.revokeObjectURL=function(p){S.revokeObjectURL(p)}),t.URL=s}}(window),Object.getOwnPropertyDescriptor(Node.prototype,"baseURI")||Object.defineProperty(Node.prototype,"baseURI",{get:function(){var t=(this.ownerDocument||this).querySelector("base[href]");return t&&t.href||window.location.href},configurable:!0,enumerable:!0});var Pi=document.createElement("style");Pi.textContent="body {transition: opacity ease-in 0.2s; } \nbody[unresolved] {opacity: 0; display: block; overflow: hidden; position: relative; } \n";var Ai=document.querySelector("head");Ai.insertBefore(Pi,Ai.firstChild)}).call(this);
//# sourceMappingURL=polyfill-webcomp.9409bbe667389b40.js.map