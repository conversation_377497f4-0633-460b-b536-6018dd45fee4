<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

	<bean id="attachmentsCategoryProviderFactory"
          class="com.progressoft.jfw.shared.attachment.AttachmentsCategoryProviderFactory">
        <property name="attachmentsCategoryProviders">
            <map>
                <entry>
                    <key>
                        <value>DEFAULT_ATTACHMENTS_CATEGORY_PROVIDER</value>
                    </key>
                    <bean id="defaultattachmentsCategoryProvider"
                          class="com.progressoft.jfw.util.DefaultAttachmentsCategoryProvider"></bean>
                </entry>
            </map>
        </property>
    </bean>

    <bean id="attachmentControlsModifierProviderFactory"
          class="com.progressoft.jfw.shared.attachment.AttachmentControlsModifierProviderFactory">
        <property name="attachmentControlsAuthorityModifierProvider">
            <map>
                <entry>
                    <key>
                        <value>DEFAULT_ATTACHMENTS_CONTROL_AUTHORITY_MODIFIER</value>
                    </key>
                    <bean id="defaultattachmentscontrolauthoritymodifier"
                          class="com.progressoft.jfw.shared.attachment.DefaultAttachmentControlsAuthorityModifier"/>
                </entry>
			</map>
		</property>
	</bean>
</beans>