{"id": "dms_realm", "realm": "dms_realm", "notBefore": 0, "defaultSignatureAlgorithm": "RS256", "revokeRefreshToken": false, "refreshTokenMaxReuse": 0, "accessTokenLifespan": 300, "accessTokenLifespanForImplicitFlow": 900, "ssoSessionIdleTimeout": 1800, "ssoSessionMaxLifespan": 36000, "ssoSessionIdleTimeoutRememberMe": 0, "ssoSessionMaxLifespanRememberMe": 0, "offlineSessionIdleTimeout": 2592000, "offlineSessionMaxLifespanEnabled": false, "offlineSessionMaxLifespan": 5184000, "clientSessionIdleTimeout": 0, "clientSessionMaxLifespan": 0, "clientOfflineSessionIdleTimeout": 0, "clientOfflineSessionMaxLifespan": 0, "accessCodeLifespan": 60, "accessCodeLifespanUserAction": 300, "accessCodeLifespanLogin": 1800, "actionTokenGeneratedByAdminLifespan": 43200, "actionTokenGeneratedByUserLifespan": 300, "oauth2DeviceCodeLifespan": 600, "oauth2DevicePollingInterval": 5, "enabled": true, "sslRequired": "external", "registrationAllowed": false, "registrationEmailAsUsername": false, "rememberMe": false, "verifyEmail": false, "loginWithEmailAllowed": true, "duplicateEmailsAllowed": false, "resetPasswordAllowed": false, "editUsernameAllowed": false, "bruteForceProtected": false, "permanentLockout": false, "maxTemporaryLockouts": 0, "maxFailureWaitSeconds": 900, "minimumQuickLoginWaitSeconds": 60, "waitIncrementSeconds": 60, "quickLoginCheckMilliSeconds": 1000, "maxDeltaTimeSeconds": 43200, "failureFactor": 30, "roles": {"realm": [{"id": "a4dfba5b-6891-4ef5-9e82-92cdfb7da6e6", "name": "offline_access", "description": "${role_offline-access}", "composite": false, "clientRole": false, "containerId": "dms_realm", "attributes": {}}, {"id": "38f706a2-25bc-4142-9baf-8eab60d8da71", "name": "uma_authorization", "description": "${role_uma_authorization}", "composite": false, "clientRole": false, "containerId": "dms_realm", "attributes": {}}, {"id": "130f461a-1a95-4601-81b3-b53bfcedcd5e", "name": "default-roles-dms_realm", "description": "${role_default-roles}", "composite": true, "composites": {"realm": ["offline_access", "uma_authorization"], "client": {"account": ["view-profile", "manage-account"]}}, "clientRole": false, "containerId": "dms_realm", "attributes": {}}], "client": {"realm-management": [{"id": "0512113e-e376-46fa-9174-41915914c437", "name": "manage-authorization", "description": "${role_manage-authorization}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "d0205cd3-3188-40c5-8807-82030b10d129", "name": "query-realms", "description": "${role_query-realms}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "f3fa324e-a9ec-4bcd-a3ce-df8fecd6dc62", "name": "create-client", "description": "${role_create-client}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "3dc38ee1-45b6-4ad5-aab3-7a38b688aad8", "name": "manage-clients", "description": "${role_manage-clients}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "1191d99c-f691-42b5-99e9-d30cc212cf1b", "name": "impersonation", "description": "${role_impersonation}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "f0e7e357-2078-4a01-9964-d7785de689b7", "name": "manage-identity-providers", "description": "${role_manage-identity-providers}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "45f92f15-2437-44bd-b4a9-79b11cc9494e", "name": "realm-admin", "description": "${role_realm-admin}", "composite": true, "composites": {"client": {"realm-management": ["manage-authorization", "query-realms", "manage-clients", "create-client", "impersonation", "manage-identity-providers", "view-authorization", "manage-users", "view-events", "manage-events", "query-groups", "manage-realm", "view-users", "query-users", "view-identity-providers", "query-clients", "view-realm", "view-clients"]}}, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "f4ec03ac-0b8e-4ac4-a8ca-dec6ec19845e", "name": "manage-users", "description": "${role_manage-users}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "be0c7455-e6ef-4cbb-87b9-6a0a82a46354", "name": "view-authorization", "description": "${role_view-authorization}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "f449e820-386d-4378-a96b-1df1f6f7548c", "name": "view-events", "description": "${role_view-events}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "26100328-0ce7-4876-b239-7c62dcd93350", "name": "manage-events", "description": "${role_manage-events}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "6a1a2ec1-71e5-4a8b-b466-1d9af1223af9", "name": "query-groups", "description": "${role_query-groups}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "b0f48d35-55e7-44a5-af49-c3cd14c6c8fb", "name": "manage-realm", "description": "${role_manage-realm}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "a48515d2-002f-401d-8ee3-5a61894fd730", "name": "view-users", "description": "${role_view-users}", "composite": true, "composites": {"client": {"realm-management": ["query-users", "query-groups"]}}, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "8be9ad0d-39e8-4c72-b504-70f1c155069c", "name": "query-users", "description": "${role_query-users}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "f5041421-41c4-42e1-8c2f-b29ee3a0be40", "name": "query-clients", "description": "${role_query-clients}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "2dbb9bc0-dbe8-460d-b103-8a52ef26315d", "name": "view-identity-providers", "description": "${role_view-identity-providers}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "8eb30de1-0cf7-4e39-bede-283532d25b7c", "name": "view-realm", "description": "${role_view-realm}", "composite": false, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}, {"id": "503642b7-03ac-4a09-b7ab-223db8601e89", "name": "view-clients", "description": "${role_view-clients}", "composite": true, "composites": {"client": {"realm-management": ["query-clients"]}}, "clientRole": true, "containerId": "d70bf464-f21f-4b20-860d-10f078182669", "attributes": {}}], "security-admin-console": [], "admin-cli": [], "account-console": [], "broker": [{"id": "866c7ab8-644e-4876-a145-32d71cc16eb9", "name": "read-token", "description": "${role_read-token}", "composite": false, "clientRole": true, "containerId": "d7efefba-bec7-4581-a49a-23f1e20b6183", "attributes": {}}], "dms_client": [{"id": "9176cbd5-1439-45f1-bfa8-5151d90279e2", "name": "DMSCHECKER", "description": "DMSChecker Role\t\n", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "6d53c2b6-e05e-415c-b8ec-58eeaed5a54c", "name": "DMSMAKER", "description": "DMSMaker Role", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "9e18174f-f82f-4ad3-9760-c5695dcc5843", "name": "BANKS CHECKER", "description": "Banks Checker Role", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "3cda112f-3d3c-47fb-a5ab-39d4f65163ea", "name": "BANKS MAKER", "description": "Banks Maker Role", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "b6553a99-6430-4b38-b6f0-bfe58883e656", "name": "NCBS MAKER", "description": "Ncbs Maker Role", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "151b3226-5d98-4f9a-b718-a7f1923be316", "name": "Generic Authorities", "description": "Generic Authorities", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "ddc7be14-538e-4015-b1fa-42cc96fd3135", "name": "Security Role - Maker", "description": "Security Role - Maker", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "185f67c9-924b-4811-ade4-619361ce55b2", "name": "Bussiness Role - Role Checker", "description": "Bussiness Role - Role Checker", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "a7c0f65b-dd14-4c09-bf91-08dae5d7116a", "name": "Security Role - Checker", "description": "Security Role - Checker", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "d87336cf-a2d4-4f60-b2d6-da9eadc0aacd", "name": "Bussiness Role - Role Maker", "description": "Bussiness Role - Role Maker", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}, {"id": "f8a465ff-2e85-432a-b416-79b94efb0b78", "name": "NCBS CHECKER", "description": "Ncbs Checker Role", "composite": false, "clientRole": true, "containerId": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "attributes": {}}], "account": [{"id": "d555b007-3add-43fa-9242-6cfc476b6aba", "name": "manage-consent", "description": "${role_manage-consent}", "composite": true, "composites": {"client": {"account": ["view-consent"]}}, "clientRole": true, "containerId": "4604987d-6313-49e4-b10e-3fd9d900fb59", "attributes": {}}, {"id": "e1a18136-e8b3-446a-8888-c6a7a3cc6b80", "name": "view-consent", "description": "${role_view-consent}", "composite": false, "clientRole": true, "containerId": "4604987d-6313-49e4-b10e-3fd9d900fb59", "attributes": {}}, {"id": "c1f4c619-b1f3-4945-9b56-6f8263debf91", "name": "view-profile", "description": "${role_view-profile}", "composite": false, "clientRole": true, "containerId": "4604987d-6313-49e4-b10e-3fd9d900fb59", "attributes": {}}, {"id": "e7ca61fb-ddaf-4a49-b4db-6a99a99a448f", "name": "view-groups", "description": "${role_view-groups}", "composite": false, "clientRole": true, "containerId": "4604987d-6313-49e4-b10e-3fd9d900fb59", "attributes": {}}, {"id": "738889cb-bbde-4270-8db4-8390ce3564de", "name": "manage-account", "description": "${role_manage-account}", "composite": true, "composites": {"client": {"account": ["manage-account-links"]}}, "clientRole": true, "containerId": "4604987d-6313-49e4-b10e-3fd9d900fb59", "attributes": {}}, {"id": "4ab9c1c4-d7f7-4db5-a444-a990deb2f977", "name": "manage-account-links", "description": "${role_manage-account-links}", "composite": false, "clientRole": true, "containerId": "4604987d-6313-49e4-b10e-3fd9d900fb59", "attributes": {}}, {"id": "bf5cce05-423b-4ac8-a0ea-331a32135a8d", "name": "view-applications", "description": "${role_view-applications}", "composite": false, "clientRole": true, "containerId": "4604987d-6313-49e4-b10e-3fd9d900fb59", "attributes": {}}, {"id": "********-13e8-41ea-a8ca-e4d8ae1e7bee", "name": "delete-account", "description": "${role_delete-account}", "composite": false, "clientRole": true, "containerId": "4604987d-6313-49e4-b10e-3fd9d900fb59", "attributes": {}}]}}, "groups": [{"id": "37aaf8b2-3c76-4411-b6bf-8870c58dafa1", "name": "AHLICHECKER", "path": "/AHLICHECKER", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["BANKS CHECKER"]}}, {"id": "bb7ed238-49a9-4667-8e89-f9aac8418c18", "name": "AHLICheckers Administrators Group", "path": "/AHLICheckers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Bussiness Role - Role Checker", "Security Role - Checker"]}}, {"id": "c11f4771-6663-4f68-8542-359b3e678ea9", "name": "AHLIMAKER", "path": "/AHLIMAKER", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["BANKS MAKER"]}}, {"id": "3abc4fe8-98de-497e-9a8c-74599c4c292a", "name": "AHLIMakers Administrators Group", "path": "/AHLIMakers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Security Role - Maker", "Bussiness Role - Role Maker"]}}, {"id": "6eba59e5-57db-4416-bbba-34764bfb887d", "name": "ARABCHECKER", "path": "/ARABCHECKER", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["BANKS CHECKER"]}}, {"id": "c38794d0-c9f4-4242-8861-1cee856beaa1", "name": "ARABCheckers Administrators Group", "path": "/ARABCheckers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Bussiness Role - Role Checker", "Security Role - Checker"]}}, {"id": "c798b46f-a286-4bab-9377-04458f353d5d", "name": "ARABMAKER", "path": "/ARABMAKER", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["BANKS MAKER"]}}, {"id": "9f8a613b-bff0-4811-a961-2ef4a155445b", "name": "ARABMakers Administrators Group", "path": "/ARABMakers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Security Role - Maker", "Bussiness Role - Role Maker"]}}, {"id": "398fad18-05ec-4a5e-913d-67c1d3c403c7", "name": "CBOMCHECKER", "path": "/CBOMCHECKER", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["NCBS CHECKER"]}}, {"id": "a2d73d7b-bdc7-409f-abc4-fc32aede826f", "name": "CBOMCheckers Administrators Group", "path": "/CBOMCheckers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Bussiness Role - Role Checker", "Security Role - Checker"]}}, {"id": "608fbe8c-caec-411d-9374-59b4909b443b", "name": "CBOMMAKER", "path": "/CBOMMAKER", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["NCBS MAKER"]}}, {"id": "683ddf13-7d63-45c2-882e-015f0ae66c03", "name": "CBOMMakers Administrators Group", "path": "/CBOMMakers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Security Role - Maker", "Bussiness Role - Role Maker"]}}, {"id": "8e4eb762-6861-4105-9422-7b7f2af175c6", "name": "DMSChecker Group", "path": "/DMSChecker Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["DMSCHECKER"]}}, {"id": "e6e63317-ed46-4022-8ae3-41200fc27b64", "name": "DMSCheckers Administrators Group", "path": "/DMSCheckers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Bussiness Role - Role Checker", "Security Role - Checker"]}}, {"id": "5c8b52ac-4dd7-4006-9b33-7fb212072c5b", "name": "DMSMaker Group", "path": "/DMSMaker Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["DMSMAKER"]}}, {"id": "75be58df-bb03-42ba-860a-ea41310dc435", "name": "DMSMakers Administrators Group", "path": "/DMSMakers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Security Role - Maker", "Bussiness Role - Role Maker"]}}, {"id": "445cbdc2-b476-4ab1-96c9-353b66836ec0", "name": "QMAGCHECKER", "path": "/QMAGCHECKER", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["NCBS CHECKER"]}}, {"id": "4e3142fb-0cc8-4d72-b77d-db627db938c2", "name": "QMAGCheckers Administrators Group", "path": "/QMAGCheckers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Bussiness Role - Role Checker", "Security Role - Checker"]}}, {"id": "30d309a8-0af1-45df-abe8-98be02763a0c", "name": "QMAGMAKER", "path": "/QMAGMAKER", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["NCBS MAKER"]}}, {"id": "f8d696ad-d903-4a92-8db1-e5181e12bc18", "name": "QMAGMakers Administrators Group", "path": "/QMAGMakers Administrators Group", "subGroups": [], "attributes": {}, "realmRoles": [], "clientRoles": {"dms_client": ["Generic Authorities", "Security Role - Maker", "Bussiness Role - Role Maker"]}}], "defaultRole": {"id": "130f461a-1a95-4601-81b3-b53bfcedcd5e", "name": "default-roles-dms_realm", "description": "${role_default-roles}", "composite": true, "clientRole": false, "containerId": "dms_realm"}, "users": [{"username": "DMSMAKERADMIN@DMS", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["DMSMakers Administrators Group"]}, {"username": "DMSCHECKERADMIN@DMS", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["DMSCheckers Administrators Group"]}, {"username": "DMSMAKER@DMS", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["DMSMaker Group"]}, {"username": "DMSCHECKER@DMS", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["DMSChecker Group"]}, {"username": "AHLIMAKER@AHLI", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["AHLIMAKER"]}, {"username": "AHLICHECKER@AHLI", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["AHLICHECKER"]}, {"username": "AhliMakerAdmin@AHLI", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["AHLIMakers Administrators Group"]}, {"username": "AHLICheckerAdmin@AHLI", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["AHLICheckers Administrators Group"]}, {"username": "ARABMakerAdmin@ARAB", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["ARABMakers Administrators Group"]}, {"username": "ARABCheckerAdmin@ARAB", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["ARABCheckers Administrators Group"]}, {"username": "ARABMAKER@ARAB", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["ARABMAKER"]}, {"username": "ARABCHECKER@ARAB", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["ARABCHECKER"]}, {"username": "QMAGMAKER@QMAG", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["QMAGMAKER"]}, {"username": "QMAGMakerAdmin@QMAG", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["QMAGMakers Administrators Group"]}, {"username": "QMAGCHECKER@QMAG", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["QMAGCHECKER"]}, {"username": "QMAGCheckerAdmin@QMAG", "email": "<EMAIL>", "enabled": true, "credentials": [{"type": "password", "value": "a"}], "groups": ["QMAGCheckers Administrators Group"]}], "requiredCredentials": ["password"], "otpPolicyType": "totp", "otpPolicyAlgorithm": "HmacSHA1", "otpPolicyInitialCounter": 0, "otpPolicyDigits": 6, "otpPolicyLookAheadWindow": 1, "otpPolicyPeriod": 30, "otpPolicyCodeReusable": false, "otpSupportedApplications": ["totpAppFreeOTPName", "totpAppGoogleName", "totpAppMicrosoftAuthenticatorName"], "localizationTexts": {}, "webAuthnPolicyRpEntityName": "keycloak", "webAuthnPolicySignatureAlgorithms": ["ES256"], "webAuthnPolicyRpId": "", "webAuthnPolicyAttestationConveyancePreference": "not specified", "webAuthnPolicyAuthenticatorAttachment": "not specified", "webAuthnPolicyRequireResidentKey": "not specified", "webAuthnPolicyUserVerificationRequirement": "not specified", "webAuthnPolicyCreateTimeout": 0, "webAuthnPolicyAvoidSameAuthenticatorRegister": false, "webAuthnPolicyAcceptableAaguids": [], "webAuthnPolicyExtraOrigins": [], "webAuthnPolicyPasswordlessRpEntityName": "keycloak", "webAuthnPolicyPasswordlessSignatureAlgorithms": ["ES256"], "webAuthnPolicyPasswordlessRpId": "", "webAuthnPolicyPasswordlessAttestationConveyancePreference": "not specified", "webAuthnPolicyPasswordlessAuthenticatorAttachment": "not specified", "webAuthnPolicyPasswordlessRequireResidentKey": "not specified", "webAuthnPolicyPasswordlessUserVerificationRequirement": "not specified", "webAuthnPolicyPasswordlessCreateTimeout": 0, "webAuthnPolicyPasswordlessAvoidSameAuthenticatorRegister": false, "webAuthnPolicyPasswordlessAcceptableAaguids": [], "webAuthnPolicyPasswordlessExtraOrigins": [], "scopeMappings": [{"clientScope": "offline_access", "roles": ["offline_access"]}], "clientScopeMappings": {"account": [{"client": "account-console", "roles": ["manage-account", "view-groups"]}]}, "clients": [{"id": "4604987d-6313-49e4-b10e-3fd9d900fb59", "clientId": "account", "name": "${client_account}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dms_realm/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/dms_realm/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "18e018a8-9acf-4b73-8ea8-b53313993e1b", "clientId": "account-console", "name": "${client_account-console}", "rootUrl": "${authBaseUrl}", "baseUrl": "/realms/dms_realm/account/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/realms/dms_realm/account/*"], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "825a2121-5591-46a0-813b-7aef56b7cf86", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "9c2a2bcc-49b7-44cf-ae84-ab8079e47365", "clientId": "admin-cli", "name": "${client_admin-cli}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": false, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "d7efefba-bec7-4581-a49a-23f1e20b6183", "clientId": "broker", "name": "${client_broker}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "60f6d25e-bfdc-4515-b5de-42497ff38a44", "clientId": "dms_client", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["*"], "webOrigins": ["*"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": true, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"id.token.as.detached.signature": "false", "saml.assertion.signature": "false", "saml.force.post.binding": "false", "saml.multivalued.roles": "false", "saml.encrypt": "false", "post.logout.redirect.uris": "+", "oauth2.device.authorization.grant.enabled": "false", "backchannel.logout.revoke.offline.tokens": "false", "saml.server.signature": "false", "saml.server.signature.keyinfo.ext": "false", "use.refresh.tokens": "true", "exclude.session.state.from.auth.response": "false", "oidc.ciba.grant.enabled": "false", "saml.artifact.binding": "false", "backchannel.logout.session.required": "true", "client_credentials.use_refresh_token": "false", "saml_force_name_id_format": "false", "require.pushed.authorization.requests": "false", "saml.client.signature": "false", "tls.client.certificate.bound.access.tokens": "false", "saml.authnstatement": "false", "display.on.consent.screen": "false", "saml.onetimeuse.condition": "false"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": true, "nodeReRegistrationTimeout": -1, "protocolMappers": [{"id": "ee14ec42-b4ff-4bb9-8c82-4d4472a57aed", "name": "roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "roles", "jsonType.label": "String", "usermodel.clientRoleMapping.clientId": "dms_client"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "d70bf464-f21f-4b20-860d-10f078182669", "clientId": "realm-management", "name": "${client_realm-management}", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": [], "webOrigins": [], "notBefore": 0, "bearerOnly": true, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": false, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}, {"id": "072cfe16-e6a2-4f46-a041-a5611b344068", "clientId": "security-admin-console", "name": "${client_security-admin-console}", "rootUrl": "${authAdminUrl}", "baseUrl": "/admin/dms_realm/console/", "surrogateAuthRequired": false, "enabled": true, "alwaysDisplayInConsole": false, "clientAuthenticatorType": "client-secret", "redirectUris": ["/admin/dms_realm/console/*"], "webOrigins": ["+"], "notBefore": 0, "bearerOnly": false, "consentRequired": false, "standardFlowEnabled": true, "implicitFlowEnabled": false, "directAccessGrantsEnabled": false, "serviceAccountsEnabled": false, "publicClient": true, "frontchannelLogout": false, "protocol": "openid-connect", "attributes": {"post.logout.redirect.uris": "+", "pkce.code.challenge.method": "S256"}, "authenticationFlowBindingOverrides": {}, "fullScopeAllowed": false, "nodeReRegistrationTimeout": 0, "protocolMappers": [{"id": "416faa63-70dc-4919-8c07-034ef8218882", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}], "defaultClientScopes": ["web-origins", "roles", "profile", "email"], "optionalClientScopes": ["address", "phone", "offline_access", "microprofile-jwt"]}], "clientScopes": [{"id": "********-0235-4ba5-9e6c-45b83ebcb07c", "name": "role_list", "description": "SAML role list", "protocol": "saml", "attributes": {"consent.screen.text": "${samlRoleListScopeConsentText}", "display.on.consent.screen": "true"}, "protocolMappers": [{"id": "1f783355-674d-4efb-b481-cf5825efa0fd", "name": "role list", "protocol": "saml", "protocolMapper": "saml-role-list-mapper", "consentRequired": false, "config": {"single": "false", "attribute.nameformat": "Basic", "attribute.name": "Role"}}]}, {"id": "b6f4bbd7-1cbd-45d7-9c3e-8ef4072c1d8a", "name": "web-origins", "description": "OpenID Connect scope for add allowed web origins to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false", "consent.screen.text": ""}, "protocolMappers": [{"id": "c379c6ad-9aae-415a-b981-3330faffaf0d", "name": "allowed web origins", "protocol": "openid-connect", "protocolMapper": "oidc-allowed-origins-mapper", "consentRequired": false, "config": {}}]}, {"id": "2066670b-882f-4333-a665-aacd631d7e09", "name": "phone", "description": "OpenID Connect built-in scope: phone", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${phoneScopeConsentText}"}, "protocolMappers": [{"id": "a6edf623-a71b-4238-98cf-0660debbc3fd", "name": "phone number verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumberVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number_verified", "jsonType.label": "boolean"}}, {"id": "4e15f6ea-d6a7-43a9-bb37-9fb763fd8f74", "name": "phone number", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "phoneNumber", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "phone_number", "jsonType.label": "String"}}]}, {"id": "445dba43-9fa0-4131-92e2-3bec19358e49", "name": "offline_access", "description": "OpenID Connect built-in scope: offline_access", "protocol": "openid-connect", "attributes": {"consent.screen.text": "${offlineAccessScopeConsentText}", "display.on.consent.screen": "true"}}, {"id": "b28ceae7-b184-40bb-88e1-014f55c195b3", "name": "acr", "description": "OpenID Connect scope for add acr (authentication context class reference) to the token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "c9c7a589-54e5-4f21-8028-f73fe8cbdcdf", "name": "acr loa level", "protocol": "openid-connect", "protocolMapper": "oidc-acr-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "introspection.token.claim": "true", "access.token.claim": "true"}}]}, {"id": "8ce04cce-5559-414b-a553-99e56bf27b84", "name": "microprofile-jwt", "description": "Microprofile - JWT built-in scope", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "false"}, "protocolMappers": [{"id": "7cf91fb3-6598-4067-a586-857c0ccce7cd", "name": "groups", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"multivalued": "true", "userinfo.token.claim": "true", "user.attribute": "foo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "groups", "jsonType.label": "String"}}, {"id": "c7661eb1-502b-475a-a899-2d98df7f9b49", "name": "upn", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "upn", "jsonType.label": "String"}}]}, {"id": "0f824f43-5820-4cc6-94c5-c22c1961794f", "name": "address", "description": "OpenID Connect built-in scope: address", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${addressScopeConsentText}"}, "protocolMappers": [{"id": "dea24140-bf4f-41a2-8298-bd2b683f73b0", "name": "address", "protocol": "openid-connect", "protocolMapper": "oidc-address-mapper", "consentRequired": false, "config": {"user.attribute.formatted": "formatted", "user.attribute.country": "country", "user.attribute.postal_code": "postal_code", "userinfo.token.claim": "true", "user.attribute.street": "street", "id.token.claim": "true", "user.attribute.region": "region", "access.token.claim": "true", "user.attribute.locality": "locality"}}]}, {"id": "70055691-9abb-4a1a-be03-72aadd8a3127", "name": "email", "description": "OpenID Connect built-in scope: email", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${emailScopeConsentText}"}, "protocolMappers": [{"id": "4d589111-d60d-4767-8ce4-6a72c5c6b4aa", "name": "email verified", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "emailVerified", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email_verified", "jsonType.label": "boolean"}}, {"id": "8ed6180c-b537-459b-aa3e-75b51d50d45f", "name": "email", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "email", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "email", "jsonType.label": "String"}}]}, {"id": "0249ca99-4f5e-40e8-a675-516d89f27252", "name": "roles", "description": "OpenID Connect scope for add user roles to the access token", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "false", "display.on.consent.screen": "true", "consent.screen.text": "${rolesScopeConsentText}"}, "protocolMappers": [{"id": "fa97507b-5ff4-455a-b1e7-a7c7086a01fb", "name": "audience resolve", "protocol": "openid-connect", "protocolMapper": "oidc-audience-resolve-mapper", "consentRequired": false, "config": {}}, {"id": "cd8c7b62-480d-4bdf-b914-0818f355e819", "name": "realm roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-realm-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "realm_access.roles", "jsonType.label": "String", "multivalued": "true"}}, {"id": "5c0a4d42-d2ce-4fbb-b218-b39d3531ddf2", "name": "client roles", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-client-role-mapper", "consentRequired": false, "config": {"user.attribute": "foo", "access.token.claim": "true", "claim.name": "resource_access.${client_id}.roles", "jsonType.label": "String", "multivalued": "true"}}]}, {"id": "12cef922-dd25-47e8-8b56-4207b0ad7763", "name": "profile", "description": "OpenID Connect built-in scope: profile", "protocol": "openid-connect", "attributes": {"include.in.token.scope": "true", "display.on.consent.screen": "true", "consent.screen.text": "${profileScopeConsentText}"}, "protocolMappers": [{"id": "5e29ca7a-ae72-4379-ad7f-f4dd1f6912b2", "name": "given name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "firstName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "given_name", "jsonType.label": "String"}}, {"id": "52f61c2e-844b-41f1-bd55-1e2665a92c46", "name": "username", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "username", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "preferred_username", "jsonType.label": "String"}}, {"id": "43f19526-1474-49c7-9938-6e5c2bd9c48e", "name": "gender", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "gender", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "gender", "jsonType.label": "String"}}, {"id": "ef4a777d-6f1b-4bd5-a071-43aae939f800", "name": "full name", "protocol": "openid-connect", "protocolMapper": "oidc-full-name-mapper", "consentRequired": false, "config": {"id.token.claim": "true", "access.token.claim": "true", "userinfo.token.claim": "true"}}, {"id": "d7b4361f-677b-4e4d-9efe-bf6d67883868", "name": "zoneinfo", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "zoneinfo", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "zoneinfo", "jsonType.label": "String"}}, {"id": "f8ee91bd-1d12-433b-968b-ebc6a6058ec1", "name": "birthdate", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "birthdate", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "birthdate", "jsonType.label": "String"}}, {"id": "f2546745-8884-44bb-b24d-5e94f750da58", "name": "profile", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "profile", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "profile", "jsonType.label": "String"}}, {"id": "b8910a80-f40a-4a60-a620-205b7f899fca", "name": "updated at", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "updatedAt", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "updated_at", "jsonType.label": "String"}}, {"id": "2cbd83fe-36d6-41c7-82ca-2243282fecaf", "name": "family name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-property-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "lastName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "family_name", "jsonType.label": "String"}}, {"id": "b5bd4494-5d33-4db3-a943-427ad9f4793f", "name": "locale", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "locale", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "locale", "jsonType.label": "String"}}, {"id": "07aadbde-9b42-4e4f-876b-59d49a487b41", "name": "middle name", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "middleName", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "middle_name", "jsonType.label": "String"}}, {"id": "2a6546bd-a107-4cad-b9d0-b67e1a5c7ca1", "name": "nickname", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "nickname", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "nickname", "jsonType.label": "String"}}, {"id": "4ec7ceca-5da8-47ea-9527-5c9e4e1fe12f", "name": "website", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "website", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "website", "jsonType.label": "String"}}, {"id": "94095bfc-7e02-49f3-9d4f-3cb182d48272", "name": "picture", "protocol": "openid-connect", "protocolMapper": "oidc-usermodel-attribute-mapper", "consentRequired": false, "config": {"userinfo.token.claim": "true", "user.attribute": "picture", "id.token.claim": "true", "access.token.claim": "true", "claim.name": "picture", "jsonType.label": "String"}}]}], "defaultDefaultClientScopes": ["role_list", "profile", "email", "roles", "web-origins", "acr"], "defaultOptionalClientScopes": ["offline_access", "address", "phone", "microprofile-jwt"], "browserSecurityHeaders": {"contentSecurityPolicyReportOnly": "", "xContentTypeOptions": "nosniff", "referrerPolicy": "no-referrer", "xRobotsTag": "none", "xFrameOptions": "SAMEORIGIN", "contentSecurityPolicy": "frame-src 'self'; frame-ancestors 'self'; object-src 'none';", "xXSSProtection": "1; mode=block", "strictTransportSecurity": "max-age=31536000; includeSubDomains"}, "smtpServer": {}, "eventsEnabled": false, "eventsListeners": ["jboss-logging"], "enabledEventTypes": [], "adminEventsEnabled": false, "adminEventsDetailsEnabled": false, "identityProviders": [], "identityProviderMappers": [], "components": {"org.keycloak.services.clientregistration.policy.ClientRegistrationPolicy": [{"id": "81dca80b-3140-4410-b742-2388c41fb7e8", "name": "Consent Required", "providerId": "consent-required", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "ff9cde3e-04cd-41e2-892d-3bca76697e7d", "name": "Full Scope Disabled", "providerId": "scope", "subType": "anonymous", "subComponents": {}, "config": {}}, {"id": "07d1541e-d702-42ed-a37a-b7153243fe54", "name": "Max Clients Limit", "providerId": "max-clients", "subType": "anonymous", "subComponents": {}, "config": {"max-clients": ["200"]}}, {"id": "f447b2e5-d7d0-485f-8488-5757e86aacac", "name": "Trusted Hosts", "providerId": "trusted-hosts", "subType": "anonymous", "subComponents": {}, "config": {"host-sending-registration-request-must-match": ["true"], "client-uris-must-match": ["true"]}}, {"id": "e3a098b6-b382-4890-ab82-cb00429c4a34", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "authenticated", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-full-name-mapper", "saml-user-attribute-mapper", "oidc-usermodel-attribute-mapper", "oidc-usermodel-property-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-property-mapper", "saml-role-list-mapper", "oidc-address-mapper"]}}, {"id": "1146a3c2-50c2-4c61-af73-5a663632129a", "name": "Allowed Protocol Mapper Types", "providerId": "allowed-protocol-mappers", "subType": "anonymous", "subComponents": {}, "config": {"allowed-protocol-mapper-types": ["oidc-address-mapper", "saml-role-list-mapper", "oidc-sha256-pairwise-sub-mapper", "saml-user-attribute-mapper", "oidc-full-name-mapper", "oidc-usermodel-attribute-mapper", "saml-user-property-mapper", "oidc-usermodel-property-mapper"]}}, {"id": "9fd58449-8d9c-4fef-8342-5fcd511afc77", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "anonymous", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}, {"id": "cf46d534-9052-4774-a1dd-9d40a3e8245e", "name": "Allowed C<PERSON>s", "providerId": "allowed-client-templates", "subType": "authenticated", "subComponents": {}, "config": {"allow-default-scopes": ["true"]}}], "org.keycloak.userprofile.UserProfileProvider": [{"id": "ddf85f1e-bd74-44f3-be21-0bfc3075f73f", "providerId": "declarative-user-profile", "subComponents": {}, "config": {"kc.user.profile.config": ["{\"attributes\":[{\"name\":\"username\",\"displayName\":\"${username}\",\"validations\":{\"length\":{\"min\":3,\"max\":255},\"username-prohibited-characters\":{},\"up-username-not-idn-homograph\":{}},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"email\",\"displayName\":\"${email}\",\"validations\":{\"email\":{},\"length\":{\"max\":255}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"firstName\",\"displayName\":\"${firstName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false},{\"name\":\"lastName\",\"displayName\":\"${lastName}\",\"validations\":{\"length\":{\"max\":255},\"person-name-prohibited-characters\":{}},\"required\":{\"roles\":[\"user\"]},\"permissions\":{\"view\":[\"admin\",\"user\"],\"edit\":[\"admin\",\"user\"]},\"multivalued\":false}],\"groups\":[{\"name\":\"user-metadata\",\"displayHeader\":\"User metadata\",\"displayDescription\":\"Attributes, which refer to user metadata\"}],\"unmanagedAttributePolicy\":\"ENABLED\"}"]}}], "org.keycloak.keys.KeyProvider": [{"id": "203680b8-4ab7-40bb-a569-e7c9e26ae85e", "name": "hmac-generated", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS256"]}}, {"id": "e62e2304-67f4-41ca-a0e5-28a9b52e1c85", "name": "hmac-generated-hs512", "providerId": "hmac-generated", "subComponents": {}, "config": {"priority": ["100"], "algorithm": ["HS512"]}}, {"id": "661d9821-8e37-483a-a4ca-effb9ea1ac72", "name": "aes-generated", "providerId": "aes-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "de158eba-bf43-458e-be72-bebd9139b2a5", "name": "rsa-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}, {"id": "3c3aee8e-b911-4c5f-8e42-6c51f346d061", "name": "rsa-enc-generated", "providerId": "rsa-generated", "subComponents": {}, "config": {"priority": ["100"]}}]}, "internationalizationEnabled": false, "supportedLocales": [], "authenticationFlows": [{"id": "df140f21-9ca6-4e8a-8f4f-395e711309ea", "alias": "Account verification options", "description": "Method with which to verity the existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-email-verification", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "Verify Existing Account by Re-authentication", "userSetupAllowed": false}]}, {"id": "9a2cbc90-db30-479d-a966-95677486990c", "alias": "Browser - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "b978b9d7-6aa9-44ff-be4d-3cb30e0b51ae", "alias": "Direct Grant - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "59e960f2-ba90-43c4-a19a-b6a3adbb7dd2", "alias": "First broker login - Conditional OTP", "description": "Flow to determine if the OTP is required for the authentication", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-otp-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "b8d329d2-7183-4b6d-b162-2cf02ffe73ca", "alias": "<PERSON><PERSON> Existing Account", "description": "Handle what to do if there is existing account with same email/username like authenticated identity provider", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-confirm-link", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "Account verification options", "userSetupAllowed": false}]}, {"id": "27e69d4b-6458-435e-ad94-ea3b55a96e18", "alias": "Reset - Conditional OTP", "description": "Flow to determine if the OTP should be reset or not. Set to REQUIRED to force.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "conditional-user-configured", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-otp", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "010c7ef5-574e-45d4-ad67-d33d27e98263", "alias": "User creation or linking", "description": "Flow for the existing/non-existing user alternatives", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "create unique user config", "authenticator": "idp-create-user-if-unique", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": true, "flowAlias": "<PERSON><PERSON> Existing Account", "userSetupAllowed": false}]}, {"id": "01d87854-99a2-4c22-b0fc-7b6d11cc976b", "alias": "Verify Existing Account by Re-authentication", "description": "Reauthentication of existing account", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "idp-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "First broker login - Conditional OTP", "userSetupAllowed": false}]}, {"id": "5602d613-2f6a-46ed-a600-56de3f7377b4", "alias": "browser", "description": "browser based authentication", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-cookie", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "auth-spnego", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "identity-provider-redirector", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 25, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": true, "flowAlias": "forms", "userSetupAllowed": false}]}, {"id": "144c5916-72a4-4f7c-866f-f0334679bf7b", "alias": "clients", "description": "Base authentication for clients", "providerId": "client-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "client-secret", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-secret-jwt", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "client-x509", "authenticatorFlow": false, "requirement": "ALTERNATIVE", "priority": 40, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "e95292f5-52b4-47ba-aad4-feb513e56728", "alias": "direct grant", "description": "OpenID Connect Resource Owner Grant", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "direct-grant-validate-username", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "direct-grant-validate-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 30, "autheticatorFlow": true, "flowAlias": "Direct Grant - Conditional OTP", "userSetupAllowed": false}]}, {"id": "7f85a45a-01ce-47cf-9bf5-5159c9be3841", "alias": "docker auth", "description": "Used by Docker clients to authenticate against the IDP", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "docker-http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "b1c56cda-0849-4430-b4a5-1f8d1f1faaba", "alias": "first broker login", "description": "Actions taken after first broker login with identity provider account, which is not yet linked to any Keycloak account", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticatorConfig": "review profile config", "authenticator": "idp-review-profile", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": true, "flowAlias": "User creation or linking", "userSetupAllowed": false}]}, {"id": "6036b95c-c042-487b-b936-66bdb3564cbc", "alias": "forms", "description": "Username, password, otp and other auth forms.", "providerId": "basic-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "auth-username-password-form", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 20, "autheticatorFlow": true, "flowAlias": "Browser - Conditional OTP", "userSetupAllowed": false}]}, {"id": "267a713d-58a1-4c47-8077-15f3cca984b9", "alias": "registration", "description": "registration flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-page-form", "authenticatorFlow": true, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": true, "flowAlias": "registration form", "userSetupAllowed": false}]}, {"id": "88e0ee35-1f6a-4fe2-85f8-4e09109e3c6d", "alias": "registration form", "description": "registration form", "providerId": "form-flow", "topLevel": false, "builtIn": true, "authenticationExecutions": [{"authenticator": "registration-user-creation", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-password-action", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 50, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "registration-recaptcha-action", "authenticatorFlow": false, "requirement": "DISABLED", "priority": 60, "autheticatorFlow": false, "userSetupAllowed": false}]}, {"id": "c83f54e8-0831-4b0e-99da-9f76becdb8dc", "alias": "reset credentials", "description": "Reset credentials for a user if they forgot their password or something", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "reset-credentials-choose-user", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-credential-email", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 20, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticator": "reset-password", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 30, "autheticatorFlow": false, "userSetupAllowed": false}, {"authenticatorFlow": true, "requirement": "CONDITIONAL", "priority": 40, "autheticatorFlow": true, "flowAlias": "Reset - Conditional OTP", "userSetupAllowed": false}]}, {"id": "a7d9a0c5-7a26-4098-8a29-5d02e25e61a4", "alias": "saml ecp", "description": "SAML ECP Profile Authentication Flow", "providerId": "basic-flow", "topLevel": true, "builtIn": true, "authenticationExecutions": [{"authenticator": "http-basic-authenticator", "authenticatorFlow": false, "requirement": "REQUIRED", "priority": 10, "autheticatorFlow": false, "userSetupAllowed": false}]}], "authenticatorConfig": [{"id": "7c511b44-e477-4de3-998a-3f199b57ed65", "alias": "create unique user config", "config": {"require.password.update.after.registration": "false"}}, {"id": "a72e5a36-949d-4336-86a7-a9be86f69879", "alias": "review profile config", "config": {"update.profile.on.first.login": "missing"}}], "requiredActions": [{"alias": "CONFIGURE_TOTP", "name": "Configure OTP", "providerId": "CONFIGURE_TOTP", "enabled": true, "defaultAction": false, "priority": 10, "config": {}}, {"alias": "TERMS_AND_CONDITIONS", "name": "Terms and Conditions", "providerId": "TERMS_AND_CONDITIONS", "enabled": false, "defaultAction": false, "priority": 20, "config": {}}, {"alias": "UPDATE_PASSWORD", "name": "Update Password", "providerId": "UPDATE_PASSWORD", "enabled": true, "defaultAction": false, "priority": 30, "config": {}}, {"alias": "UPDATE_PROFILE", "name": "Update Profile", "providerId": "UPDATE_PROFILE", "enabled": true, "defaultAction": false, "priority": 40, "config": {}}, {"alias": "VERIFY_EMAIL", "name": "<PERSON><PERSON><PERSON>", "providerId": "VERIFY_EMAIL", "enabled": true, "defaultAction": false, "priority": 50, "config": {}}, {"alias": "delete_account", "name": "Delete Account", "providerId": "delete_account", "enabled": false, "defaultAction": false, "priority": 60, "config": {}}, {"alias": "delete_credential", "name": "Delete Credential", "providerId": "delete_credential", "enabled": true, "defaultAction": false, "priority": 100, "config": {}}, {"alias": "update_user_locale", "name": "Update User Locale", "providerId": "update_user_locale", "enabled": true, "defaultAction": false, "priority": 1000, "config": {}}], "browserFlow": "browser", "registrationFlow": "registration", "directGrantFlow": "direct grant", "resetCredentialsFlow": "reset credentials", "clientAuthenticationFlow": "clients", "dockerAuthenticationFlow": "docker auth", "firstBrokerLoginFlow": "first broker login", "attributes": {"cibaBackchannelTokenDeliveryMode": "poll", "cibaExpiresIn": "120", "cibaAuthRequestedUserHint": "login_hint", "oauth2DeviceCodeLifespan": "600", "clientOfflineSessionMaxLifespan": "0", "oauth2DevicePollingInterval": "5", "clientSessionIdleTimeout": "0", "parRequestUriLifespan": "60", "clientSessionMaxLifespan": "0", "clientOfflineSessionIdleTimeout": "0", "cibaInterval": "5", "realmReusableOtpCode": "false"}, "keycloakVersion": "24.0.3", "userManagedAccessAllowed": false, "clientProfiles": {"profiles": []}, "clientPolicies": {"policies": []}}