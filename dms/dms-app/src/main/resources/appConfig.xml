<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-3.0.xsd">

    <context:component-scan base-package="com.progressoft.*"/>

    <bean id="jfw" class="com.progressoft.jfw.impl.JfwImpl"/>
    <bean id="lookups" class="com.progressoft.jfw.impl.DefaultLookupsImpl"/>
    <bean id="currencies" class="com.progressoft.jfw.impl.DefaultCurrenciesImpl"/>
    <bean id="systemDate" class="com.progressoft.jfw.impl.SystemDateImpl"/>
    <bean id="attachments" class="com.progressoft.jfw.Attachments"/>
    <bean id="duplicate" class="com.progressoft.jfw.impl.DuplicateImpl"/>
    <bean id="tenantProvider" class="com.progressoft.jfw.impl.DbTenantProvider"/>

    <bean id="startCamelContextTask" class="com.progressoft.jfw.startup.tasks.StartCamelContextTask"/>
    <bean id="executeRouteBuildersTask" class="com.progressoft.jfw.startup.tasks.ExecuteRouteBuildersTask" />

    <bean id="defaultApplicationStartupEntry" class="com.progressoft.jfw.startup.DefaultApplicationStartupEntry" primary="true"/>
    <bean id="setAppKeyTask" class="com.progressoft.jfw.startup.tasks.SetAppKeyTask"/>
    <bean id="createTenantsTask" class="com.progressoft.jfw.startup.tasks.CreateTenantsTask"/>
    <bean id="updateAccountPolicyTask" class="com.progressoft.jfw.startup.tasks.UpdateAccountPolicyTask"/>
    <bean id="activateWorkflowsTask" class="com.progressoft.jfw.startup.tasks.ActivateWorkflowsTask"/>
    <bean id="flushEhCacheTask" class="com.progressoft.jfw.startup.tasks.FlushEhCacheTask"/>

    <bean id="executeScriptsTask" class="com.progressoft.dms.startup.ExecuteScriptsTask"/>
    <bean id="executeDemoDataScriptTask" class="com.progressoft.dms.startup.ExecuteDemoDataScriptTask"/>

    <bean id="initEntitiesWorkflowsTask" class="com.progressoft.psdms.application.startuptasks.InitializeDMSWorkflowEntitiesTask">
        <property name="packageName" value="com.progressoft.dms.entities"/>
        <property name="actions" value="Request Approve,Approve"/>
        <property name="items">
            <list>
                <value>DMS_SLAConfiguration</value>
                <value>DMS_SystemConfiguration</value>
                <value>DMS_TermsAndConditions</value>
            </list>
        </property>
    </bean>


</beans>