<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:jee="http://www.springframework.org/schema/jee"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.1.xsd

		http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-4.1.xsd">

<beans 	profile="default">
	<bean id="jfwDataSource" class="org.apache.commons.dbcp.BasicDataSource">
		<property name="driverClassName" value="${db_driver}" />
		<property name="url" value="${db_url}" />
		<property name="username" value="${db_user}" />
		<property name="password" value="${db_password}" />
		<property name="validationQuery" value="${db_validation_query}" />
		<property name="maxWait" value="10000" />
		<property name="maxIdle" value="30" />
		<property name="testOnBorrow" value="true"/>
		<property name="testOnReturn" value="true"/>
		<property name="maxActive" value="100" />
	</bean>
</beans>

</beans>
