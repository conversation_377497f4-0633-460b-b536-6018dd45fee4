<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="monther (generated)" id="1659432012207-40">
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="1"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="Operation Mode"/>
            <column name="CONFIGVALUE" value="Regional"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="2"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="ACH_TRANSACTION_ENDPOINT"/>
            <column name="CONFIGVALUE" value="http://localhost:9090/ach-transactions"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="3"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="IIPS_TRANSACTION_ENDPOINT"/>
            <column name="CONFIGVALUE" value="http://localhost:9090/iips-transactions"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="4"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="MAX_REPRESENT_COUNT"/>
            <column name="CONFIGVALUE" value="2"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="5"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="ATTACHMENT_MANDATE_FLAG"/>
            <column name="CONFIGVALUE" value="true"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="7"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="MIN_REJECTION_COUNT_FOR_ARBITRATION"/>
            <column name="CONFIGVALUE" value="3"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="8"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="mail.smtp.enabled"/>
            <column name="CONFIGVALUE" value="true"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="9"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="mail.smtp.from"/>
            <column name="CONFIGVALUE" value="<EMAIL>"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="10"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="mail.smtp.user"/>
            <column name="CONFIGVALUE" value="username"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="11"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="mail.smtp.password"/>
            <column name="CONFIGVALUE" value="password"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="12"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="mail.smtp.auth"/>
            <column name="CONFIGVALUE" value="true"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="13"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="mail.smtp.starttls.enable"/>
            <column name="CONFIGVALUE" value="true"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="14"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="mail.smtp.host"/>
            <column name="CONFIGVALUE" value="localhost"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="15"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="mail.smtp.port"/>
            <column name="CONFIGVALUE" value="1234"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="16"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="Arbitration Notification Threshold in Days"/>
            <column name="CONFIGVALUE" value="2"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="17"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="Arbitration Threshold Exceeding E-Mail Address"/>
            <column name="CONFIGVALUE" value="<EMAIL>"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="18"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="SLA Exceeded Penalty"/>
            <column name="CONFIGVALUE" value="5"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="19"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="Urgent Dispute Fee"/>
            <column name="CONFIGVALUE" value="10"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="20"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="Attachments Maximum Number"/>
            <column name="CONFIGVALUE" value="5"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="21"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="Attachment Maximum Size"/>
            <column name="CONFIGVALUE" value="1020"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="22"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="Attachment Types"/>
            <column name="CONFIGVALUE" value="xml,txt,pdf,xls,xlsx,dat,csv,mt,mx,jpg,jpeg,png,gif,xls,doc,xlsx,docx"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="23"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="Dispute of a Dispute Fee"/>
            <column name="CONFIGVALUE" value="5"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" value="24"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="CONFIGKEY" value="operatorEmail"/>
            <column name="CONFIGVALUE" value="<EMAIL>"/>
        </insert>
    </changeSet>

    <changeSet  author="Nasief (updated)" id="1659432012207-41">
        <insert tableName="DMS_PAYMENTSYSTEMS">
            <column name="ID" value="1"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="IIPS"/>
            <column name="DESCRIPTION" value="Interoperable Instant Payments System"/>
            <column name="NAME" value="Interoperable Instant Payments System"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
            <column name="CHECKERCOMMENTS" />
        </insert>
        <insert tableName="DMS_PAYMENTSYSTEMS">
            <column name="ID" value="2"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="ACH"/>
            <column name="DESCRIPTION" value="Automated Clearing House"/>
            <column name="NAME" value="Automated Clearing House"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
            <column name="CHECKERCOMMENTS" />
        </insert>
    </changeSet>

    <changeSet author="Damilola (updated)" id="1659432012207-42">
        <insert tableName="DMS_ENDPOINTS">
            <column name="ID" value="1"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="NAME" value="IIPS_MESSAGE_URI"/>
            <column name="SOURCEURI" value="direct:IIPS_DISPUTE_APPROVED"/>
            <column name="DIRECTION" value="out"/>
            <column name="SYSTEM" value="IIPS"/>
            <column name="TARGETURI" value="jms:queue:IIPS_DISPUTE_APPROVED"/>
        </insert>
        <insert tableName="DMS_ENDPOINTS">
            <column name="ID" value="2"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="NAME" value="ACH_MESSAGE_URI"/>
            <column name="SOURCEURI" value="direct:ACH_DISPUTE_APPROVED"/>
            <column name="DIRECTION" value="out"/>
            <column name="SYSTEM" value="ACH"/>
            <column name="TARGETURI" value="jms:queue:ACH_DISPUTE_APPROVED"/>
        </insert>
    </changeSet>
    <changeSet author="Mary (generated)" id="1662364607810-3">
        <insert tableName="DMS_SLACONFIGURATIONPARTY">
            <column name="ID" value="1"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="ClaimantBank"/>
            <column name="DESCRIPTION" value="Claimant Bank"/>
            <column name="NAME" value="Claimant Bank"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATIONPARTY">
            <column name="ID" value="2"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="DefendantBank"/>
            <column name="DESCRIPTION" value="Defendant Bank"/>
            <column name="NAME" value="Defendant Bank"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATIONPARTY">
            <column name="ID" value="3"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="ClaimantNCB"/>
            <column name="DESCRIPTION" value="Claimant NCB"/>
            <column name="NAME" value="Claimant NCB"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATIONPARTY">
            <column name="ID" value="4"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="DefendantNCB"/>
            <column name="DESCRIPTION" value="Defendant NCB"/>
            <column name="NAME" value="Defendant NCB"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATIONPARTY">
            <column name="ID" value="5"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="Operator"/>
            <column name="DESCRIPTION" value="Operator"/>
            <column name="NAME" value="Operator"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
    </changeSet>
    <changeSet author="omololu (generated)" id="1662364607810-4">
        <insert tableName="DMS_SLACONFIGAUTOMATICACTION">
            <column name="ID" value="1"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="Accept"/>
            <column name="DESCRIPTION" value="Accept"/>
            <column name="NAME" value="SVC_SLA_Accept"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGAUTOMATICACTION">
            <column name="ID" value="2"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="Reject"/>
            <column name="DESCRIPTION" value="Reject"/>
            <column name="NAME" value="SVC_SLA_Reject"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
    </changeSet>
    <changeSet author="muna (generated)" id="1662364607654-1">
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="1"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="2"/>
            <column name="PAYMENTSYSTEM_ID" value="2"/>
            <column name="STAGE" value="New,Reply Repair,More Information Provided,Represented,Additional Info Required"/>
            <column name="URGENCY" value="Normal"/>
            <column name="MAXDAYS" value="4"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="2"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="2"/>
            <column name="PAYMENTSYSTEM_ID" value="2"/>
            <column name="STAGE" value="New,Reply Repair,More Information Provided,Represented,Additional Info Required"/>
            <column name="URGENCY" value="Urgent"/>
            <column name="MAXDAYS" value="3"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="3"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="4"/>
            <column name="PAYMENTSYSTEM_ID" value="2"/>
            <column name="STAGE" value="New Dispute,More Info Provided,Repair,Represented"/>
            <column name="URGENCY" value="Normal"/>
            <column name="MAXDAYS" value="4"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="4"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="4"/>
            <column name="PAYMENTSYSTEM_ID" value="2"/>
            <column name="STAGE" value="New Dispute,More Info Provided,Repair,Represented"/>
            <column name="URGENCY" value="Urgent"/>
            <column name="MAXDAYS" value="2"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="5"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="3"/>
            <column name="PAYMENTSYSTEM_ID" value="2"/>
            <column name="STAGE" value="Additional Info Required,Represented,Arbitrated Dispute"/>
            <column name="URGENCY" value="Normal"/>
            <column name="MAXDAYS" value="4"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="2"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="6"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="3"/>
            <column name="PAYMENTSYSTEM_ID" value="2"/>
            <column name="STAGE" value="Additional Info Required,Represented,Arbitrated Dispute"/>
            <column name="URGENCY" value="Urgent"/>
            <column name="MAXDAYS" value="2"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="2"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="7"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="2"/>
            <column name="PAYMENTSYSTEM_ID" value="1"/>
            <column name="STAGE" value="New,Reply Repair,More Information Provided,Represented,Additional Info Required"/>
            <column name="URGENCY" value="Normal"/>
            <column name="MAXDAYS" value="2"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="8"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="2"/>
            <column name="PAYMENTSYSTEM_ID" value="1"/>
            <column name="STAGE" value="New,Reply Repair,More Information Provided,Represented,Additional Info Required"/>
            <column name="URGENCY" value="Urgent"/>
            <column name="MAXDAYS" value="2"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="9"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="4"/>
            <column name="PAYMENTSYSTEM_ID" value="1"/>
            <column name="STAGE" value="New Dispute,More Info Provided,Repair,Represented"/>
            <column name="URGENCY" value="Normal"/>
            <column name="MAXDAYS" value="2"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLACONFIGURATION">
            <column name="ID" value="10"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" value="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" value="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" value="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" value="4"/>
            <column name="PAYMENTSYSTEM_ID" value="1"/>
            <column name="STAGE" value="New Dispute,More Info Provided,Repair,Represented"/>
            <column name="URGENCY" value="Urgent"/>
            <column name="MAXDAYS" value="2"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" value="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
    </changeSet>
</databaseChangeLog>
