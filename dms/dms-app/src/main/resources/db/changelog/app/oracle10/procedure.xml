<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="damilola-32a" author="damilola">
            <createSequence cacheSize="0" maxValue="9999999999999999999999999999" sequenceName="SEQ_DMS_DisputeCase_Case_ref" startValue="1"/>
    </changeSet>

    <changeSet id="damilola-32" author="damilola">
        <createProcedure procedureName="gen_dispute_id" dbms="oracle" >
            CREATE OR REPLACE PROCEDURE gen_dispute_case_ref (dispute_case_ref OUT NUMBER)
            AS
            BEGIN
                dispute_case_ref := SEQ_DMS_DisputeCase_Case_ref.NEXTVAL;
            END;
        </createProcedure>
    </changeSet>
</databaseChangeLog>