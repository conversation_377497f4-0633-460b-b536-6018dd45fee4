<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.2.xsd">

    <include relativeToChangelogFile="true" file="dms-tables.xml"/>
    <include relativeToChangelogFile="true" file="dms-jfw-data.xml"/>
    <include relativeToChangelogFile="true" file="dms-initial-data.xml"/>
    <include relativeToChangelogFile="true" file="reason-management-table.xml"/>
    <include relativeToChangelogFile="true" file="dms-wf-status.xml"/>
    <include file="../../../../postgressql/Scripts/postgresDBCast.sql" relativeToChangelogFile="true"/>
    <include file="../../../../postgressql/Scripts/generateDisputeCaseRef.sql" relativeToChangelogFile="true"/>
    <include file="/changes/90-change-oid-to-bytea.xml" relativeToChangelogFile="true"/>
</databaseChangeLog>
