<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="monther (generated)" id="SEQ_DMS_PARTICIPANT">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_PARTICIPANT" startValue="1"/>
    </changeSet>
    <changeSet author="monther (generated)" id="SEQ_DMS_NATIONALCENTRALBANKS">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_NATIONALCENTRALBANKS" startValue="10"/>
    </changeSet>
    <changeSet author="monther (generated)" id="SEQ_DMS_SYS_CONF">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_SYS_CONF" startValue="1"/>
    </changeSet>
    <changeSet author="monther (generated)" id="SEQ_DMS_PAYMENTSYSTEMS">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_PAYMENTSYSTEMS" startValue="1"/>
    </changeSet>
    <changeSet author="monther (generated)" id="SEQ_DMS_CLAIMANTBANKCASEMANAGEMENT">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_CLAIMANTBANKCASEMANAGEMENT" startValue="1"/>
    </changeSet>
    <changeSet author="monther (generated)" id="SEQ_DMS_DEFENDANTBANKCASEMANAGEMENT">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_DEFENDANTBANKCASEMANAGEMENT" startValue="1"/>
    </changeSet>
    <changeSet author="monther (generated)" id="SEQ_DMS_DEFENDANTNCBCASEMANAGEMENT">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_DEFENDANTNCBCASEMANAGEMENT" startValue="1"/>
    </changeSet>
    <changeSet author="monther (generated)" id="SEQ_DMS_CLAIMANTNCBCASEMANAGEMENT">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_CLAIMANTNCBCASEMANAGEMENT" startValue="1"/>
    </changeSet>
    <changeSet author="satanai (generated)" id="SEQ_DMS_OPERATORCASEMANAGEMENT">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_OPERATORCASEMANAGEMENT" startValue="1"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-1">
        <createTable tableName="DMS_PARTICIPANTS">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859181"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="CODE" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="EMAIL" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(255)"/>
            <column name="NAME" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="FULLNAME" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="CHECKERCOMMENTS" type="VARCHAR(258)"/>
            <column name="NCB_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="COUNTRY_ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-2">
        <createTable tableName="DMS_NCB">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859182"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="CODE" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="EMAIL" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(255)"/>
            <column name="NAME" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="COUNTRYID" type="BIGINT"/>
            <column name="CHECKERCOMMENTS" type="VARCHAR(258)"/>
        </createTable>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-3">
        <createIndex indexName="UK_395Y8X6KX7TQQMPS8VOG7LRHA" tableName="DMS_NCB" unique="true">
            <column name="NAME"/>
            <column name="Z_TENANT_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-4">
        <addForeignKeyConstraint baseColumnNames="COUNTRYID" baseTableName="DMS_NCB" constraintName="FK_L6746C99XI60X6GFL5N4SO491" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="JFW_COUNTRIES" validate="true"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-5">
        <addForeignKeyConstraint baseColumnNames="NCB_ID" baseTableName="DMS_PARTICIPANTS" constraintName="FK_L6746C99XI60X6GFL5N4SO492" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_NCB" validate="true"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-6">
        <addForeignKeyConstraint baseColumnNames="Z_DRAFT_ID" baseTableName="DMS_NCB" constraintName="FK_RIE9TCOLY0TCR2XM6GBI4XE5Q" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="JFW_DRAFTS" validate="true"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-7">
        <addForeignKeyConstraint baseColumnNames="Z_STATUS_ID" baseTableName="DMS_NCB" constraintName="FK_TPK5VD2DOLRX8HEG4J9KQWY9Q" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="JFW_WF_STATUS" validate="true"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-8">
        <createIndex indexName="UK_QJI8V2UT7GI6PLLT0KVIJB45E" tableName="DMS_PARTICIPANTS" unique="true">
            <column name="NAME"/>
            <column name="Z_TENANT_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-9">
        <addUniqueConstraint columnNames="NAME, Z_TENANT_ID" constraintName="UK_QJI8V2UT7GI6PLLT0KVIJB45E2" tableName="DMS_PARTICIPANTS"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-10">
        <createIndex indexName="UK_QJI8V2UT7GI6PLLT0KVIJB45D" tableName="DMS_PARTICIPANTS" unique="true">
            <column name="FULLNAME"/>
            <column name="Z_TENANT_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-11">
        <addUniqueConstraint columnNames="FULLNAME, Z_TENANT_ID" constraintName="UK_QJI8V2UT7GI6PLLT0KVIJB45D2" tableName="DMS_PARTICIPANTS"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-12">
        <createTable tableName="DMS_PAYMENTSYSTEMS">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859123"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="CODE" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(255)"/>
            <column name="NAME" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="CHECKERCOMMENTS" type="VARCHAR(258)"/>
        </createTable>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-13">
        <createIndex indexName="UK_QJI8V2UT7GI6PLLT0KVIJB454T" tableName="DMS_PAYMENTSYSTEMS" unique="true">
            <column name="NAME"/>
            <column name="Z_TENANT_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-14">
        <addUniqueConstraint columnNames="NAME, Z_TENANT_ID" constraintName="UK_QJI8V2UT7GI6PLLT0KVIJB454T2" tableName="DMS_PAYMENTSYSTEMS"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-15">
        <createTable tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859183"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="CONFIGKEY" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="CONFIGVALUE" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-16">
        <createIndex indexName="UK_QJI8V2UT7GI6PLLT0KVIJB45F" tableName="DMS_SYSTEMCONFIGURATION" unique="true">
            <column name="CONFIGKEY"/>
            <column name="Z_TENANT_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-17">
        <addUniqueConstraint columnNames="CONFIGKEY, Z_TENANT_ID" constraintName="UK_QJI8V2UT7GI6PLLT0KVIJB45F2" tableName="DMS_SYSTEMCONFIGURATION"/>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-18">
        <createTable tableName="DMS_DEFENDANTBANKCASEMANAGEMENT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859125"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="NOTE" type="VARCHAR(4000)"/>
            <column name="DISPUTECASE_ID" type="BIGINT"/>
            <column name="REJECTIONREASON" type="BIGINT"/>
            <column name="REPRESENTREASON" type="BIGINT"/>
            <column name="REQADDINFOREASON" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet author="satanai (generated)" id="*************-25">
        <createTable tableName="DMS_CLAIMANTNCBCASEMANAGEMENT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859126"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="NOTE" type="VARCHAR(4000)"/>
            <column name="DISPUTECASE_ID" type="BIGINT"/>
            <column name="REJECTIONREASON" type="BIGINT"/>
            <column name="REPRESENTREASON" type="BIGINT"/>
            <column name="REQADDINFOREASON" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="monther (generated)" id="*************-32">
        <createTable tableName="DMS_DEFENDANTNCBCASEMANAGEMENT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859128"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="NOTE" type="VARCHAR(4000)"/>
            <column name="DISPUTECASE_ID" type="BIGINT"/>
            <column name="REJECTIONREASON" type="BIGINT"/>
            <column name="REPRESENTREASON" type="BIGINT"/>
            <column name="REQADDINFOREASON" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet author="monther (generated)" id="*************-39">
        <createTable tableName="DMS_CLAIMANTBANKCASEMANAGEMENT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859139"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="NOTE" type="VARCHAR(4000)"/>
            <column name="DISPUTECASE_ID" type="BIGINT"/>
            <column name="REJECTIONREASON" type="BIGINT"/>
            <column name="REPRESENTREASON" type="BIGINT"/>
            <column name="REQADDINFOREASON" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="satanai (generated)" id="*************-47">
        <createTable tableName="DMS_OPERATORCASEMANAGEMENT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859127"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="NOTE" type="VARCHAR(4000)"/>
            <column name="DISPUTECASE_ID" type="BIGINT"/>
            <column name="REJECTIONREASON" type="BIGINT"/>
            <column name="REQADDINFOREASON" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="Nasief (generated)" id="SEQ_DMS_TRN_INFO">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_TRN_INFO" startValue="1"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-54">
        <createTable tableName="DMS_TRANSACTIONADDITIONALINFOS">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859154"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="ADDITIONALINFOKEY" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="ADDITIONALINFOVALUE" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="DISPUTE_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="satanai (generated)" id="1660240078562-55">
        <createTable tableName="DMS_DISPUTEATT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C00196255"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="ATTFILE" type="OID"/>
            <column name="ATTACHMENT_SOURCE" type="VARCHAR(255)"/>
            <column name="ATTACHMENT_TOKEN" type="VARCHAR(255)"/>
            <column name="COMMENTS" type="VARCHAR(255)"/>
            <column name="CONTENTTYPE" type="VARCHAR(255)"/>
            <column name="ENTITYID" type="VARCHAR(255)"/>
            <column name="IMAGE_THUMBNAIL" type="OID"/>
            <column name="IMAGE_TYPE" type="VARCHAR(255)"/>
            <column name="NAME" type="VARCHAR(255)"/>
            <column name="ORIGINAL_MICR" type="VARCHAR(255)"/>
            <column name="RECORDID" type="VARCHAR(255)"/>
            <column name="REF_VALUE" type="VARCHAR(255)"/>
            <column name="REV" type="NUMBER(10, 0)"/>
            <column name="ATTACHMENT_SIZE" type="NUMBER(19, 2)"/>
        </createTable>
    </changeSet>
    <changeSet author="damilola (generated)" id="SEQ_DMS_ENDPOINT">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_ENDPOINT" startValue="2"/>
    </changeSet>
    <changeSet author="damilola (generated)" id="*************-56">
        <createTable tableName="DMS_ENDPOINTS">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859155"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="SOURCEURI" type="VARCHAR(500)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="TARGETURI" type="VARCHAR(500)">
                <constraints nullable="false"/>
            </column>
            <column name="SYSTEM" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="NAME" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="DIRECTION" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="damilola (generated)" id="*************-57">
        <createIndex indexName="UK_QJI8V2UT7GI6PLLT0KVIJB57F" tableName="DMS_ENDPOINTS" unique="true">
            <column name="NAME"/>
            <column name="Z_TENANT_ID"/>
        </createIndex>
    </changeSet>
    <changeSet author="damilola (generated)" id="*************-58">
        <addUniqueConstraint columnNames="NAME, Z_TENANT_ID" constraintName="UK_QJI8V2UT7GI6PLLT0KVIJB57F2" tableName="DMS_ENDPOINTS"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-59">
        <createTable tableName="DMS_DISPUTECASES">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859158"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="URGENCY" type="VARCHAR(10)"/>
            <column name="CASEREFERENCENUMBER" type="VARCHAR(255)"/>
            <column name="LASTACTIONBY" type="VARCHAR(100)"/>
            <column name="LASTACTION" type="VARCHAR(100)"/>
            <column name="LASTNOTE" type="VARCHAR(4000)"/>
            <column name="TRANSACTIONREFERENCE" type="VARCHAR(258)"/>
            <column name="CREATIONDATETIME" type="TIMESTAMP(6)">
                <constraints nullable="true"/>
            </column>
            <column name="TRANSACTIONDATE" type="TIMESTAMP(6)"/>
            <column name="TRANSACTIONAMOUNT" type="NUMBER(14, 5)"/>
            <column name="DISPUTEDAMOUNT" type="NUMBER(14, 5)"/>
            <column name="CURRENCYID" type="BIGINT"/>
            <column name="SENDERPARTICIPANTID" type="BIGINT"/>
            <column name="RECEIVERPARTICIPANTID" type="BIGINT"/>
            <column name="PAYMENTSYSTEM_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="CLAIMANTBANKDISPUTE_ID" type="BIGINT"/>
            <column name="DEFENDANTBANKDISPUTE_ID" type="BIGINT"/>
            <column name="CLAIMANTNCBDISPUTE_ID" type="BIGINT"/>
            <column name="DEFENDANTNCBDISPUTE_ID" type="BIGINT"/>
            <column name="OPERATORDISPUTE_ID" type="BIGINT"/>
            <column name="DEFENDANTBANK_REJECTIONCOUNT" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="DEFENDANTNCB_REJECTIONCOUNT" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="CLAIMANTBANK_REPRESENTCOUNT" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="CLAIMANTNCB_REPRESENTCOUNT" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="NUMBEROFATT" type="BIGINT" defaultValue="0"/>
            <column name="CLAIMANTBANK_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="DEFENDANTBANK_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="BANKEXCEEDARBITRATIONNOTIFIED" type="BOOLEAN" defaultValue="0"/>
            <column name="NCBEXCEEDARBITRATIONNOTIFIED" type="BOOLEAN" defaultValue="0"/>
            <column name="REASON_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="NOTES" type="VARCHAR(4000)">
                <constraints nullable="true"/>
            </column>
            <column name="REFDISPUTE_PAYMENTSYSTEMID" type="BIGINT"/>
            <column name="REFDISPUTE_DEFENDANT" type="BIGINT"/>
            <column name="REFDISPUTE_CLAIMANT" type="BIGINT"/>
            <column name="REFDISPUTE_CASENUMBER" type="VARCHAR(255)"/>
            <column name="DISPUTEOVERDISPUTE"  type="BOOLEAN" defaultValue="0"/>
            <column name="REFDISPUTE_TRANSACTIONAMOUNT" type="NUMBER(14, 5)"/>
        </createTable>
    </changeSet>
      <changeSet id="NEW_COULMNS_FK_DISPUTE_CASE" author="Satanai">
          <addForeignKeyConstraint baseColumnNames="REFDISPUTE_CLAIMANT" baseTableName="DMS_DISPUTECASES"
                                   constraintName="FK_DISPUTE_REF_CLAIMANT" deferrable="false"
                                   initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                   referencedColumnNames="ID" referencedTableName="DMS_PARTICIPANTS" validate="true"/>

          <addForeignKeyConstraint baseColumnNames="REFDISPUTE_DEFENDANT" baseTableName="DMS_DISPUTECASES"
                                   constraintName="FK_DISPUTE_REF_DEFENDANT" deferrable="false"
                                   initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                   referencedColumnNames="ID" referencedTableName="DMS_PARTICIPANTS" validate="true"/>

          <addForeignKeyConstraint baseColumnNames="REFDISPUTE_PAYMENTSYSTEMID" baseTableName="DMS_DISPUTECASES"
                                   constraintName="FK_DISPUTE_REF_PAYMENT_SYS" deferrable="false"
                                   initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                   referencedColumnNames="ID" referencedTableName="DMS_PAYMENTSYSTEMS" validate="true"/>

      </changeSet>
    <changeSet author="Nasief (generated)" id="SEQ_DMS_DISPUTECASE">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_DISPUTECASE" startValue="1"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-60">
        <addForeignKeyConstraint baseColumnNames="CURRENCYID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO459" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="JFW_CURRENCIES" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-61">
        <addForeignKeyConstraint baseColumnNames="SENDERPARTICIPANTID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO460" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PARTICIPANTS" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-62">
        <addForeignKeyConstraint baseColumnNames="RECEIVERPARTICIPANTID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO461" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PARTICIPANTS" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-63">
        <addForeignKeyConstraint baseColumnNames="PAYMENTSYSTEM_ID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO462" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PAYMENTSYSTEMS" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-64">
        <createIndex indexName="UK_QJI8V2UT7GI6PLLT0KVIJB463" tableName="DMS_DISPUTECASES" unique="true">
            <column name="CASEREFERENCENUMBER"/>
        </createIndex>
    </changeSet>
    <changeSet author="Damilola (generated)" id="*************-40">
        <addForeignKeyConstraint baseColumnNames="DISPUTECASE_ID" baseTableName="DMS_CLAIMANTBANKCASEMANAGEMENT" constraintName="FK_L6746C99XI60X6GFL5N4SO439" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DISPUTECASES" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-19">
        <addForeignKeyConstraint baseColumnNames="DISPUTECASE_ID" baseTableName="DMS_DEFENDANTBANKCASEMANAGEMENT" constraintName="FK_L6746C99XI60X6GFL5N4SO493" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DISPUTECASES" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-65">
        <addForeignKeyConstraint baseColumnNames="CLAIMANTBANKDISPUTE_ID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO464" deferrable="true" initiallyDeferred="true" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_CLAIMANTBANKCASEMANAGEMENT" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-66">
        <addForeignKeyConstraint baseColumnNames="DEFENDANTBANKDISPUTE_ID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO465" deferrable="true" initiallyDeferred="true" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DEFENDANTBANKCASEMANAGEMENT" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-67">
        <addForeignKeyConstraint baseColumnNames="CLAIMANTNCBDISPUTE_ID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO466" deferrable="true" initiallyDeferred="true" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_CLAIMANTNCBCASEMANAGEMENT" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-26">
        <addForeignKeyConstraint baseColumnNames="DISPUTECASE_ID" baseTableName="DMS_CLAIMANTNCBCASEMANAGEMENT" constraintName="FK_L6746C99XI60X6GFL5N4SO497" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DISPUTECASES" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-33">
        <addForeignKeyConstraint baseColumnNames="DISPUTECASE_ID" baseTableName="DMS_DEFENDANTNCBCASEMANAGEMENT" constraintName="FK_L6746C99XI60X6GFL5N4SO401" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DISPUTECASES" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-68">
        <addForeignKeyConstraint baseColumnNames="DEFENDANTNCBDISPUTE_ID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO467" deferrable="true" initiallyDeferred="true" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DEFENDANTNCBCASEMANAGEMENT" validate="true"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-48">
        <addForeignKeyConstraint baseColumnNames="DISPUTECASE_ID" baseTableName="DMS_OPERATORCASEMANAGEMENT" constraintName="FK_L6746C99XI60X6GFL5N4SO4048" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DISPUTECASES" validate="true"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-69">
        <addForeignKeyConstraint baseColumnNames="OPERATORDISPUTE_ID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO468" deferrable="true" initiallyDeferred="true" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_OPERATORCASEMANAGEMENT" validate="true"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="SEQ_DMS_CORRESPONDENCE">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_CORRESPONDENCE" startValue="1"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-70">
        <createTable tableName="DMS_CORRESPONDENCE">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859169"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="ACTION" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="ACTIONBY" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="NOTE" type="VARCHAR(4000)">
                <constraints nullable="true"/>
            </column>
            <column name="DISPUTE_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="REASON_ID" type="BIGINT"/>
        </createTable>
    </changeSet>

    <changeSet author="omololu (generated)" id="SEQ_DMS_SLA_CONF">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_SLA_CONF" startValue="1"/>
    </changeSet>
    <changeSet author="omololu (generated)" id="*************-70">
        <createTable tableName="DMS_SLACONFIGURATION">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859184"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="SLACONFIGURATIONPARTY_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="PAYMENTSYSTEM_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="STAGE" type="VARCHAR(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="URGENCY" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="MAXDAYS" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="SLACONFIGAUTOMATICACTION_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Mary (generated)" id="SEQ_DMS_SLA_Configuration_Party">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_SLA_Configuration_Party" startValue="1"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-71">
        <createTable tableName="DMS_SLACONFIGURATIONPARTY">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859171"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="CODE" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(255)"/>
            <column name="NAME" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="omololu (generated)" id="*************-72">
        <addForeignKeyConstraint baseColumnNames="SLACONFIGURATIONPARTY_ID" baseTableName="DMS_SLACONFIGURATION" constraintName="FK_L6746C99XI60X6GFL5N4SO472" deferrable="true" initiallyDeferred="true" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_SLACONFIGURATIONPARTY" validate="true"/>
    </changeSet>
    <changeSet author="omololu (generated)" id="*************-73">
        <addForeignKeyConstraint baseColumnNames="PAYMENTSYSTEM_ID" baseTableName="DMS_SLACONFIGURATION" constraintName="FK_L6746C99XI60X6GFL5N4SO473" deferrable="true" initiallyDeferred="true" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PAYMENTSYSTEMS" validate="true"/>
    </changeSet>

    <changeSet author="omololu (generated)" id="SEQ_DMS_SLACONFIGAUTOMATICACTION">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_SLACONFIGAUTOMATICACTION" startValue="1"/>
    </changeSet>
    <changeSet author="omololu (generated)" id="*************-74">
        <createTable tableName="DMS_SLACONFIGAUTOMATICACTION">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859174"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="CODE" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(255)"/>
            <column name="NAME" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet author="omololu (generated)" id="*************-75">
        <addForeignKeyConstraint baseColumnNames="SLACONFIGAUTOMATICACTION_ID" baseTableName="DMS_SLACONFIGURATION" constraintName="FK_L6746C99XI60X6GFL5N4SO475" deferrable="true" initiallyDeferred="true" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_SLACONFIGAUTOMATICACTION" validate="true"/>
    </changeSet>
    <changeSet author="user (generated)" id="*************-76">
        <createTable tableName="QRTZ_BLOB_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_BLOB_TRIG_PK"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_BLOB_TRIG_PK"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_BLOB_TRIG_PK"/>
            </column>
            <column name="BLOB_DATA" type="OID"/>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-77">
        <createTable tableName="QRTZ_CALENDARS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_CALENDARS_PK"/>
            </column>
            <column name="CALENDAR_NAME" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_CALENDARS_PK"/>
            </column>
            <column name="CALENDAR" type="OID">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-78">
        <createTable tableName="QRTZ_CRON_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_CRON_TRIG_PK"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_CRON_TRIG_PK"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_CRON_TRIG_PK"/>
            </column>
            <column name="CRON_EXPRESSION" type="VARCHAR(120)">
                <constraints nullable="false"/>
            </column>
            <column name="TIME_ZONE_ID" type="VARCHAR(80)"/>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-79">
        <createTable tableName="QRTZ_FIRED_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_FIRED_TRIGGER_PK"/>
            </column>
            <column name="ENTRY_ID" type="VARCHAR(95)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_FIRED_TRIGGER_PK"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="INSTANCE_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="FIRED_TIME" type="NUMBER(13, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="SCHED_TIME" type="NUMBER(13, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="PRIORITY" type="NUMBER(13, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="STATE" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_NAME" type="VARCHAR(200)"/>
            <column name="JOB_GROUP" type="VARCHAR(200)"/>
            <column name="IS_NONCONCURRENT" type="BOOLEAN"/>
            <column name="REQUESTS_RECOVERY" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-80">
        <createTable tableName="QRTZ_JOB_DETAILS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_JOB_DETAILS_PK"/>
            </column>
            <column name="JOB_NAME" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_JOB_DETAILS_PK"/>
            </column>
            <column name="JOB_GROUP" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_JOB_DETAILS_PK"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(250)"/>
            <column name="JOB_CLASS_NAME" type="VARCHAR(250)">
                <constraints nullable="false"/>
            </column>
            <column name="IS_DURABLE" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="IS_NONCONCURRENT" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="IS_UPDATE_DATA" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="REQUESTS_RECOVERY" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_DATA" type="BYTEA"/>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-81">
        <createTable tableName="QRTZ_LOCKS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_LOCKS_PK"/>
            </column>
            <column name="LOCK_NAME" type="VARCHAR(40)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_LOCKS_PK"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-82">
        <createTable tableName="QRTZ_PAUSED_TRIGGER_GRPS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_PAUSED_TRIG_GRPS_PK"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_PAUSED_TRIG_GRPS_PK"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-83">
        <createTable tableName="QRTZ_SCHEDULER_STATE">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_SCHEDULER_STATE_PK"/>
            </column>
            <column name="INSTANCE_NAME" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_SCHEDULER_STATE_PK"/>
            </column>
            <column name="LAST_CHECKIN_TIME" type="NUMBER(13, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="CHECKIN_INTERVAL" type="NUMBER(13, 0)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-84">
        <createTable tableName="QRTZ_SIMPLE_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_SIMPLE_TRIG_PK"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_SIMPLE_TRIG_PK"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_SIMPLE_TRIG_PK"/>
            </column>
            <column name="REPEAT_COUNT" type="NUMBER(7, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="REPEAT_INTERVAL" type="NUMBER(12, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="TIMES_TRIGGERED" type="NUMBER(10, 0)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-85">
        <createTable tableName="QRTZ_SIMPROP_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_SIMPROP_TRIG_PK"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_SIMPROP_TRIG_PK"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_SIMPROP_TRIG_PK"/>
            </column>
            <column name="STR_PROP_1" type="VARCHAR(512)"/>
            <column name="STR_PROP_2" type="VARCHAR(512)"/>
            <column name="STR_PROP_3" type="VARCHAR(512)"/>
            <column name="INT_PROP_1" type="NUMBER(10, 0)"/>
            <column name="INT_PROP_2" type="NUMBER(10, 0)"/>
            <column name="LONG_PROP_1" type="NUMBER(13, 0)"/>
            <column name="LONG_PROP_2" type="NUMBER(13, 0)"/>
            <column name="DEC_PROP_1" type="NUMBER(13, 4)"/>
            <column name="DEC_PROP_2" type="NUMBER(13, 4)"/>
            <column name="BOOL_PROP_1" type="BOOLEAN"/>
            <column name="BOOL_PROP_2" type="BOOLEAN"/>
        </createTable>
    </changeSet>
    <changeSet author="user (generated)" id="*************-86">
        <createTable tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME" type="VARCHAR(120)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_TRIGGERS_PK"/>
            </column>
            <column name="TRIGGER_NAME" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_TRIGGERS_PK"/>
            </column>
            <column name="TRIGGER_GROUP" type="VARCHAR(200)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="QRTZ_TRIGGERS_PK"/>
            </column>
            <column name="JOB_NAME" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="JOB_GROUP" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(250)"/>
            <column name="NEXT_FIRE_TIME" type="NUMBER(13, 0)"/>
            <column name="PREV_FIRE_TIME" type="NUMBER(13, 0)"/>
            <column name="PRIORITY" type="NUMBER(13, 0)"/>
            <column name="TRIGGER_STATE" type="VARCHAR(16)">
                <constraints nullable="false"/>
            </column>
            <column name="TRIGGER_TYPE" type="VARCHAR(8)">
                <constraints nullable="false"/>
            </column>
            <column name="START_TIME" type="NUMBER(13, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="END_TIME" type="NUMBER(13, 0)"/>
            <column name="CALENDAR_NAME" type="VARCHAR(200)"/>
            <column name="MISFIRE_INSTR" type="NUMBER(2, 0)"/>
            <column name="JOB_DATA" type="BYTEA"/>
        </createTable>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-87">
        <createIndex indexName="IDX_QRTZ_FT_INST_JOB_REQ_RCVRY" tableName="QRTZ_FIRED_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="INSTANCE_NAME"/>
            <column name="REQUESTS_RECOVERY"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-88">
        <createIndex indexName="IDX_QRTZ_FT_JG" tableName="QRTZ_FIRED_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-89">
        <createIndex indexName="IDX_QRTZ_FT_J_G" tableName="QRTZ_FIRED_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="JOB_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-90">
        <createIndex indexName="IDX_QRTZ_FT_TG" tableName="QRTZ_FIRED_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_GROUP"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-91">
        <createIndex indexName="IDX_QRTZ_FT_TRIG_INST_NAME" tableName="QRTZ_FIRED_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="INSTANCE_NAME"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-92">
        <createIndex indexName="IDX_QRTZ_FT_T_G" tableName="QRTZ_FIRED_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_NAME"/>
            <column name="TRIGGER_GROUP"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-93">
        <createIndex indexName="IDX_QRTZ_J_GRP" tableName="QRTZ_JOB_DETAILS">
            <column name="SCHED_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-94">
        <createIndex indexName="IDX_QRTZ_J_REQ_RECOVERY" tableName="QRTZ_JOB_DETAILS">
            <column name="SCHED_NAME"/>
            <column name="REQUESTS_RECOVERY"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-95">
        <createIndex indexName="IDX_QRTZ_T_C" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="CALENDAR_NAME"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-96">
        <createIndex indexName="IDX_QRTZ_T_G" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_GROUP"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-97">
        <createIndex indexName="IDX_QRTZ_T_J" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="JOB_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-98">
        <createIndex indexName="IDX_QRTZ_T_JG" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="JOB_GROUP"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-99">
        <createIndex indexName="IDX_QRTZ_T_NEXT_FIRE_TIME" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="NEXT_FIRE_TIME"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-100">
        <createIndex indexName="IDX_QRTZ_T_NFT_MISFIRE" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="MISFIRE_INSTR"/>
            <column name="NEXT_FIRE_TIME"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-101">
        <createIndex indexName="IDX_QRTZ_T_NFT_ST" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_STATE"/>
            <column name="NEXT_FIRE_TIME"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-102">
        <createIndex indexName="IDX_QRTZ_T_NFT_ST_MISFIRE" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="MISFIRE_INSTR"/>
            <column name="NEXT_FIRE_TIME"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-103">
        <createIndex indexName="IDX_QRTZ_T_NFT_ST_MISFIRE_GRP" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="MISFIRE_INSTR"/>
            <column name="NEXT_FIRE_TIME"/>
            <column name="TRIGGER_GROUP"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-104">
        <createIndex indexName="IDX_QRTZ_T_N_G_STATE" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_GROUP"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-105">
        <createIndex indexName="IDX_QRTZ_T_N_STATE" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_NAME"/>
            <column name="TRIGGER_GROUP"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-106">
        <createIndex indexName="IDX_QRTZ_T_STATE" tableName="QRTZ_TRIGGERS">
            <column name="SCHED_NAME"/>
            <column name="TRIGGER_STATE"/>
        </createIndex>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-107">
        <addForeignKeyConstraint baseColumnNames="SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP" baseTableName="QRTZ_BLOB_TRIGGERS" constraintName="QRTZ_BLOB_TRIG_TO_TRIG_FK" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP" referencedTableName="QRTZ_TRIGGERS" validate="true"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-108">
        <addForeignKeyConstraint baseColumnNames="SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP" baseTableName="QRTZ_CRON_TRIGGERS" constraintName="QRTZ_CRON_TRIG_TO_TRIG_FK" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP" referencedTableName="QRTZ_TRIGGERS" validate="true"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-109">
        <addForeignKeyConstraint baseColumnNames="SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP" baseTableName="QRTZ_SIMPLE_TRIGGERS" constraintName="QRTZ_SIMPLE_TRIG_TO_TRIG_FK" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP" referencedTableName="QRTZ_TRIGGERS" validate="true"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-110">
        <addForeignKeyConstraint baseColumnNames="SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP" baseTableName="QRTZ_SIMPROP_TRIGGERS" constraintName="QRTZ_SIMPROP_TRIG_TO_TRIG_FK" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="SCHED_NAME,TRIGGER_NAME,TRIGGER_GROUP" referencedTableName="QRTZ_TRIGGERS" validate="true"/>
    </changeSet>
    <changeSet author="Mary (generated)" id="*************-111">
        <addForeignKeyConstraint baseColumnNames="SCHED_NAME,JOB_NAME,JOB_GROUP" baseTableName="QRTZ_TRIGGERS" constraintName="QRTZ_TRIGGER_TO_JOBS_FK" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="SCHED_NAME,JOB_NAME,JOB_GROUP" referencedTableName="QRTZ_JOB_DETAILS" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-112">
        <addForeignKeyConstraint baseColumnNames="CLAIMANTBANK_ID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO561" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PARTICIPANTS" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-113">
        <addForeignKeyConstraint baseColumnNames="DEFENDANTBANK_ID" baseTableName="DMS_DISPUTECASES" constraintName="FK_L6746C99XI60X6GFL5N4SO562" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PARTICIPANTS" validate="true"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-114">
        <createTable tableName="DMS_FEEMESSAGES">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859172"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="AMOUNT" type="NUMBER(14, 5)">
                <constraints nullable="false"/>
            </column>
            <column name="TYPE" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="PARTICIPANTCODE" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="DISPUTECASE_ID" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Nasief (generated)" id="SEQ_DMS_FEE_MESSAGES">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_FEE_MESSAGES" startValue="1"/>
    </changeSet>
    <changeSet author="Nasief (generated)" id="*************-115">
        <addForeignKeyConstraint baseColumnNames="DISPUTECASE_ID" baseTableName="DMS_FEEMESSAGES" constraintName="FK_L6746C99XI60X6GFL5N4SO115" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DISPUTECASES" validate="true"/>
    </changeSet>
    <changeSet id="Dispue_Summary_Participants_Table" author="Satanai">
        <createTable tableName="DMS_DISPUTESUMMARYRPRTPARTICIPANTS" >
            <column name="report_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="participant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
       </changeSet>
    <changeSet id="Dispute_Summary_Report_Table" author="Satanai">
        <createTable tableName="DMS_DISPUTESUMMARYREPORT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="Dispute_Summary_Report_PK"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="country_id" type="BIGINT"/>
            <column name="reason_id" type="BIGINT"/>
            <column name="paymentsys_id" type="BIGINT"/>
            <column name="nature" type="VARCHAR(9)"/>
            <column name="report_type" type="VARCHAR(5)"/>
            <column name="type" type="VARCHAR(5)"/>
            <column name="date_from" type="TIMESTAMP(6)"/>
            <column name="date_to" type="TIMESTAMP(6)"/>
            <column name="ATTACHMENTUUID" type="VARCHAR(50)"/>
        </createTable>
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_DISPUTESUMMARYREPORT" startValue="1"/>

    </changeSet>
    <changeSet id="Dispute_Summary_Participant_Table_FK" author="Satanai">
        <addForeignKeyConstraint baseColumnNames="participant_id" baseTableName="DMS_DISPUTESUMMARYRPRTPARTICIPANTS" constraintName="FK_DisputeSummaryParticipant_ParticipantID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PARTICIPANTS" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="report_id" baseTableName="DMS_DISPUTESUMMARYRPRTPARTICIPANTS" constraintName="FK_DisputeSummaryParticipant_ReportID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DISPUTESUMMARYREPORT" validate="true"/>
    </changeSet>
    <changeSet id="Dispute_Summary_Report_Table_FK" author="Satanai">
        <addForeignKeyConstraint baseColumnNames="country_id" baseTableName="DMS_DISPUTESUMMARYREPORT" constraintName="FK_DisputeSummaryReport_CountryID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="JFW_COUNTRIES" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="paymentsys_id" baseTableName="DMS_DISPUTESUMMARYREPORT" constraintName="FK_DisputeSummaryReport_PaymentSysID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PAYMENTSYSTEMS" validate="true"/>
    </changeSet>
    <changeSet id="Participant_Country_FK" author="Satanai">
        <addForeignKeyConstraint baseColumnNames="COUNTRY_ID" baseTableName="DMS_PARTICIPANTS" constraintName="FK_Participants_CounrtyID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="JFW_COUNTRIES" validate="true"/>
    </changeSet>

    <changeSet id="Dispute_Details_Participants_Table" author="Ebube">
        <createTable tableName="DMS_DISPUTEDETAILSRPRTPARTICIPANTS" >
            <column name="report_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="participant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="Dispute_Details_Report_Table" author="Ebube">
        <createTable tableName="DMS_DISPUTEDETAILSREPORT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="Dispute_Details_Report_PK"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="country_id" type="BIGINT"/>
            <column name="reason_id" type="BIGINT"/>
            <column name="dispute_status" type="VARCHAR(255)"/>
            <column name="paymentsys_id" type="BIGINT"/>
            <column name="nature" type="VARCHAR(9)"/>
            <column name="date_from" type="TIMESTAMP(6)"/>
            <column name="date_to" type="TIMESTAMP(6)"/>
            <column name="report_type" type="VARCHAR(5)"/>
            <column name="type" type="VARCHAR(5)"/>
            <column name="amount_from" type="BIGINT"/>
            <column name="amount_to" type="BIGINT"/>
            <column name="ATTACHMENTUUID" type="VARCHAR(50)"/>
        </createTable>
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_DISPUTEDETAILSRPRT" startValue="1"/>

    </changeSet>
    <changeSet id="Dispute_Details_Participant_Table_FK" author="Ebube">
        <addForeignKeyConstraint baseColumnNames="participant_id" baseTableName="DMS_DISPUTEDETAILSRPRTPARTICIPANTS" constraintName="FK_DisputeDetailsParticipant_ParticipantID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PARTICIPANTS" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="report_id" baseTableName="DMS_DISPUTEDETAILSRPRTPARTICIPANTS" constraintName="FK_DisputeDetailsParticipant_ReportID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_DISPUTEDETAILSREPORT" validate="true"/>
    </changeSet>
    <changeSet id="Dispute_Details_Report_Table_FK" author="Ebube">
        <addForeignKeyConstraint baseColumnNames="country_id" baseTableName="DMS_DISPUTEDETAILSREPORT" constraintName="FK_DisputeDetailsReport_CountryID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="JFW_COUNTRIES" validate="true"/>
        <addForeignKeyConstraint baseColumnNames="paymentsys_id" baseTableName="DMS_DISPUTEDETAILSREPORT" constraintName="FK_DisputeDetailsReport_PaymentSysID" deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="ID" referencedTableName="DMS_PAYMENTSYSTEMS" validate="true"/>
    </changeSet>
    <changeSet id="damilola-32a" author="damilola">
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_DISPUTECASE_CASE_REF" startValue="1"/>
    </changeSet>
</databaseChangeLog>
