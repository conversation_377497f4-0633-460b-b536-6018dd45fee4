<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="Nasief" id="104-update-participant-countries">
        <sql>
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=187 WHERE ID=15;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=187 WHERE ID=16;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=187 WHERE ID=17;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=222 WHERE ID=7;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=222 WHERE ID=8;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=212 WHERE ID=9;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=212 WHERE ID=10;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=17 WHERE ID=13;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=17 WHERE ID=14;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=17 WHERE ID=18;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=171 WHERE ID=11;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=171 WHERE ID=12;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=159 WHERE ID=1;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=159 WHERE ID=2;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=159 WHERE ID=3;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=159 WHERE ID=4;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=159 WHERE ID=5;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=159 WHERE ID=6;
        </sql>
        <rollback>
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=15;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=16;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=17;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=7;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=8;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=9;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=10;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=13;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=14;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=18;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=11;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=12;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=1;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=2;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=3;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=4;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=5;
            UPDATE DMS_PARTICIPANTS SET COUNTRY_ID=NULL WHERE ID=6;
        </rollback>
    </changeSet>
</databaseChangeLog>