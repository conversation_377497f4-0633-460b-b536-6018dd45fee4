<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="monther (generated)" id="1659432012207-40">
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="1"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="Operation Mode"/>
            <column name="configValue" value="Regional"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="2"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="ACH_TRANSACTION_ENDPOINT"/>
            <column name="configValue" value="http://localhost:9090/ach-transactions"/>
        </insert>
<!--        <insert tableName="DMS_SYSTEMCONFIGURATION">-->
<!--            <column name="ID" valueNumeric="3"/>-->
<!--            <column name="Z_ARCHIVE_ON"/>-->
<!--            <column name="Z_ARCHIVE_QUEUED"/>-->
<!--            <column name="Z_ARCHIVE_STATUS"/>-->
<!--            <column name="Z_ASSIGNED_GROUP"/>-->
<!--            <column name="Z_ASSIGNED_USER"/>-->
<!--            <column name="Z_CREATED_BY" value="SERVICE_USER"/>-->
<!--            <column name="Z_CREATION_DATE" />-->
<!--            <column name="Z_DELETED_BY"/>-->
<!--            <column name="Z_DELETED_FLAG" valueNumeric="0"/>-->
<!--            <column name="Z_DELETED_ON"/>-->
<!--            <column name="Z_EDITABLE" valueNumeric="0"/>-->
<!--            <column name="Z_LOCKED_BY"/>-->
<!--            <column name="Z_LOCKED_UNTIL"/>-->
<!--            <column name="Z_ORG_ID" valueNumeric="*********"/>-->
<!--            <column name="Z_TENANT_ID" value="DMS"/>-->
<!--            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>-->
<!--            <column name="Z_UPDATING_DATE"/>-->
<!--            <column name="Z_WORKFLOW_ID"/>-->
<!--            <column name="Z_WS_TOKEN"/>-->
<!--            <column name="Z_DRAFT_STATUS"/>-->
<!--            <column name="Z_DRAFT_ID"/>-->
<!--            <column name="Z_STATUS_ID"/>-->
<!--            <column name="configkey" value="IIPS_TRANSACTION_ENDPOINT"/>-->
<!--            <column name="configValue" value="http://localhost:9090/iips-transactions"/>-->
<!--        </insert>-->
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="4"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="MAX_REPRESENT_COUNT"/>
            <column name="configValue" value="2"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="5"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="ATTACHMENT_MANDATE_FLAG"/>
            <column name="configValue" value="true"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="7"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="MIN_REJECTION_COUNT_FOR_ARBITRATION"/>
            <column name="configValue" value="3"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="8"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="mail.smtp.enabled"/>
            <column name="configValue" value="true"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="9"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="mail.smtp.from"/>
            <column name="configValue" value="<EMAIL>"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="10"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="mail.smtp.user"/>
            <column name="configValue" value="username"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="11"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="mail.smtp.password"/>
            <column name="configValue" value="password"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="12"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="mail.smtp.auth"/>
            <column name="configValue" value="true"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="13"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="mail.smtp.starttls.enable"/>
            <column name="configValue" value="true"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="14"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="mail.smtp.host"/>
            <column name="configValue" value="localhost"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="15"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="mail.smtp.port"/>
            <column name="configValue" value="1234"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="16"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="Arbitration Notification Threshold in Days"/>
            <column name="configValue" value="2"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="17"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="Arbitration Threshold Exceeding E-Mail Address"/>
            <column name="configValue" value="<EMAIL>"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="18"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="SLA Exceeded Penalty"/>
            <column name="configValue" value="5"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="19"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="Urgent Dispute Fee"/>
            <column name="configValue" value="10"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="20"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="Attachments Maximum Number"/>
            <column name="configValue" value="5"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="21"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="Attachment Maximum Size"/>
            <column name="configValue" value="1020"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="22"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="Attachment Types"/>
            <column name="configValue" value="xml,txt,pdf,xls,xlsx,dat,csv,mt,mx,jpg,jpeg,png,gif,xls,doc,xlsx,docx"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="23"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="Dispute of a Dispute Fee"/>
            <column name="configValue" value="5"/>
        </insert>
        <insert tableName="DMS_SYSTEMCONFIGURATION">
            <column name="ID" valueNumeric="24"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="configkey" value="operatorEmail"/>
            <column name="configValue" value="<EMAIL>"/>
        </insert>
    </changeSet>

    <changeSet  author="Nasief (updated)" id="1659432012207-41">
<!--        <insert tableName="DMS_PAYMENTSYSTEMS">-->
<!--            <column name="ID" valueNumeric="1"/>-->
<!--            <column name="Z_ARCHIVE_ON" />-->
<!--            <column name="Z_ARCHIVE_QUEUED" />-->
<!--            <column name="Z_ARCHIVE_STATUS" />-->
<!--            <column name="Z_ASSIGNED_GROUP" />-->
<!--            <column name="Z_ASSIGNED_USER" />-->
<!--            <column name="Z_CREATED_BY" value="SERVICE_USER"/>-->
<!--            <column name="Z_CREATION_DATE" />-->
<!--            <column name="Z_DELETED_BY" />-->
<!--            <column name="Z_DELETED_FLAG" valueNumeric="0"/>-->
<!--            <column name="Z_DELETED_ON" />-->
<!--            <column name="Z_EDITABLE" valueNumeric="0"/>-->
<!--            <column name="Z_LOCKED_BY" />-->
<!--            <column name="Z_LOCKED_UNTIL" />-->
<!--            <column name="Z_ORG_ID" valueNumeric="*********"/>-->
<!--            <column name="Z_TENANT_ID" value="DMS"/>-->
<!--            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>-->
<!--            <column name="Z_UPDATING_DATE" />-->
<!--            <column name="Z_WORKFLOW_ID" />-->
<!--            <column name="Z_WS_TOKEN" />-->
<!--            <column name="Z_DRAFT_STATUS" />-->
<!--            <column name="CODE" value="IIPS"/>-->
<!--            <column name="DESCRIPTION" value="Interoperable Instant Payments System"/>-->
<!--            <column name="NAME" value="Interoperable Instant Payments System"/>-->
<!--            <column name="Z_DRAFT_ID" />-->
<!--            <column name="Z_STATUS_ID" />-->
<!--            <column name="CHECKERCOMMENTS" />-->
<!--        </insert>-->
        <insert tableName="DMS_PAYMENTSYSTEMS">
            <column name="ID" valueNumeric="2"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="ACH"/>
            <column name="DESCRIPTION" value="Automated Clearing House"/>
            <column name="NAME" value="Automated Clearing House"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
            <column name="CHECKERCOMMENTS" />
        </insert>
    </changeSet>

    <changeSet author="Damilola (updated)" id="1659432012207-42">
        <insert tableName="DMS_ENDPOINTS">
            <column name="ID" valueNumeric="1"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="NAME" value="IIPS_MESSAGE_URI"/>
            <column name="SOURCEURI" value="direct:IIPS_DISPUTE_APPROVED"/>
            <column name="DIRECTION" value="out"/>
            <column name="SYSTEM" value="IIPS"/>
            <column name="TARGETURI" value="jms:queue:IIPS_DISPUTE_APPROVED"/>
        </insert>
        <insert tableName="DMS_ENDPOINTS">
            <column name="ID" valueNumeric="2"/>
            <column name="Z_ARCHIVE_ON"/>
            <column name="Z_ARCHIVE_QUEUED"/>
            <column name="Z_ARCHIVE_STATUS"/>
            <column name="Z_ASSIGNED_GROUP"/>
            <column name="Z_ASSIGNED_USER"/>
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY"/>
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON"/>
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY"/>
            <column name="Z_LOCKED_UNTIL"/>
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE"/>
            <column name="Z_WORKFLOW_ID"/>
            <column name="Z_WS_TOKEN"/>
            <column name="Z_DRAFT_STATUS"/>
            <column name="Z_DRAFT_ID"/>
            <column name="Z_STATUS_ID"/>
            <column name="NAME" value="ACH_MESSAGE_URI"/>
            <column name="SOURCEURI" value="direct:ACH_DISPUTE_APPROVED"/>
            <column name="DIRECTION" value="out"/>
            <column name="SYSTEM" value="ACH"/>
            <column name="TARGETURI" value="jms:queue:ACH_DISPUTE_APPROVED"/>
        </insert>
    </changeSet>
    <changeSet author="Mary (generated)" id="1662364607810-3">
        <insert tableName="DMS_SLAConfigurationParty">
            <column name="ID" valueNumeric="1"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="ClaimantBank"/>
            <column name="DESCRIPTION" value="Assigner Bank"/>
            <column name="NAME" value="Assigner Bank"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLAConfigurationParty">
            <column name="ID" valueNumeric="2"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="DefendantBank"/>
            <column name="DESCRIPTION" value="Assignee Bank"/>
            <column name="NAME" value="Assignee Bank"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLAConfigurationParty">
            <column name="ID" valueNumeric="5"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="Operator"/>
            <column name="DESCRIPTION" value="Operator"/>
            <column name="NAME" value="Operator"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
    </changeSet>
    <changeSet author="omololu (generated)" id="1662364607810-4">
        <insert tableName="DMS_SLAConfigAutomaticAction">
            <column name="ID" valueNumeric="1"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="Accept"/>
            <column name="DESCRIPTION" value="Accept"/>
            <column name="NAME" value="SVC_SLA_Accept"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLAConfigAutomaticAction">
            <column name="ID" valueNumeric="2"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="CODE" value="Reject"/>
            <column name="DESCRIPTION" value="Reject"/>
            <column name="NAME" value="SVC_SLA_Reject"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
    </changeSet>
    <changeSet author="muna (generated)" id="1662364607654-1">
        <insert tableName="DMS_SLAConfiguration">
            <column name="ID" valueNumeric="1"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" valueNumeric="2"/>
            <column name="PAYMENTSYSTEM_ID" valueNumeric="2"/>
            <column name="STAGE" value="New,Reply Repair,More Information Provided,Represented,Additional Info Required"/>
            <column name="URGENCY" value="Normal"/>
            <column name="MAXDAYS" valueNumeric="4"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" valueNumeric="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
        <insert tableName="DMS_SLAConfiguration">
            <column name="ID" valueNumeric="2"/>
            <column name="Z_ARCHIVE_ON" />
            <column name="Z_ARCHIVE_QUEUED" />
            <column name="Z_ARCHIVE_STATUS" />
            <column name="Z_ASSIGNED_GROUP" />
            <column name="Z_ASSIGNED_USER" />
            <column name="Z_CREATED_BY" value="SERVICE_USER"/>
            <column name="Z_CREATION_DATE" />
            <column name="Z_DELETED_BY" />
            <column name="Z_DELETED_FLAG" valueNumeric="0"/>
            <column name="Z_DELETED_ON" />
            <column name="Z_EDITABLE" valueNumeric="0"/>
            <column name="Z_LOCKED_BY" />
            <column name="Z_LOCKED_UNTIL" />
            <column name="Z_ORG_ID" valueNumeric="*********"/>
            <column name="Z_TENANT_ID" value="DMS"/>
            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>
            <column name="Z_UPDATING_DATE" />
            <column name="Z_WORKFLOW_ID" />
            <column name="Z_WS_TOKEN" />
            <column name="Z_DRAFT_STATUS" />
            <column name="SLACONFIGURATIONPARTY_ID" valueNumeric="2"/>
            <column name="PAYMENTSYSTEM_ID" valueNumeric="2"/>
            <column name="STAGE" value="New,Reply Repair,More Information Provided,Represented,Additional Info Required"/>
            <column name="URGENCY" value="Urgent"/>
            <column name="MAXDAYS" valueNumeric="3"/>
            <column name="SLACONFIGAUTOMATICACTION_ID" valueNumeric="1"/>
            <column name="Z_DRAFT_ID" />
            <column name="Z_STATUS_ID" />
        </insert>
<!--        <insert tableName="DMS_SLAConfiguration">-->
<!--            <column name="ID" valueNumeric="7"/>-->
<!--            <column name="Z_ARCHIVE_ON" />-->
<!--            <column name="Z_ARCHIVE_QUEUED" />-->
<!--            <column name="Z_ARCHIVE_STATUS" />-->
<!--            <column name="Z_ASSIGNED_GROUP" />-->
<!--            <column name="Z_ASSIGNED_USER" />-->
<!--            <column name="Z_CREATED_BY" value="SERVICE_USER"/>-->
<!--            <column name="Z_CREATION_DATE" />-->
<!--            <column name="Z_DELETED_BY" />-->
<!--            <column name="Z_DELETED_FLAG" valueNumeric="0"/>-->
<!--            <column name="Z_DELETED_ON" />-->
<!--            <column name="Z_EDITABLE" valueNumeric="0"/>-->
<!--            <column name="Z_LOCKED_BY" />-->
<!--            <column name="Z_LOCKED_UNTIL" />-->
<!--            <column name="Z_ORG_ID" valueNumeric="*********"/>-->
<!--            <column name="Z_TENANT_ID" value="DMS"/>-->
<!--            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>-->
<!--            <column name="Z_UPDATING_DATE" />-->
<!--            <column name="Z_WORKFLOW_ID" />-->
<!--            <column name="Z_WS_TOKEN" />-->
<!--            <column name="Z_DRAFT_STATUS" />-->
<!--            <column name="SLACONFIGURATIONPARTY_ID" valueNumeric="2"/>-->
<!--            <column name="PAYMENTSYSTEM_ID" valueNumeric="1"/>-->
<!--            <column name="STAGE" value="New,Reply Repair,More Information Provided,Represented,Additional Info Required"/>-->
<!--            <column name="URGENCY" value="Normal"/>-->
<!--            <column name="MAXDAYS" valueNumeric="2"/>-->
<!--            <column name="SLACONFIGAUTOMATICACTION_ID" valueNumeric="1"/>-->
<!--            <column name="Z_DRAFT_ID" />-->
<!--            <column name="Z_STATUS_ID" />-->
<!--        </insert>-->
<!--        <insert tableName="DMS_SLAConfiguration">-->
<!--            <column name="ID" valueNumeric="8"/>-->
<!--            <column name="Z_ARCHIVE_ON" />-->
<!--            <column name="Z_ARCHIVE_QUEUED" />-->
<!--            <column name="Z_ARCHIVE_STATUS" />-->
<!--            <column name="Z_ASSIGNED_GROUP" />-->
<!--            <column name="Z_ASSIGNED_USER" />-->
<!--            <column name="Z_CREATED_BY" value="SERVICE_USER"/>-->
<!--            <column name="Z_CREATION_DATE" />-->
<!--            <column name="Z_DELETED_BY" />-->
<!--            <column name="Z_DELETED_FLAG" valueNumeric="0"/>-->
<!--            <column name="Z_DELETED_ON" />-->
<!--            <column name="Z_EDITABLE" valueNumeric="0"/>-->
<!--            <column name="Z_LOCKED_BY" />-->
<!--            <column name="Z_LOCKED_UNTIL" />-->
<!--            <column name="Z_ORG_ID" valueNumeric="*********"/>-->
<!--            <column name="Z_TENANT_ID" value="DMS"/>-->
<!--            <column name="Z_UPDATED_BY" value="SERVICE_USER"/>-->
<!--            <column name="Z_UPDATING_DATE" />-->
<!--            <column name="Z_WORKFLOW_ID" />-->
<!--            <column name="Z_WS_TOKEN" />-->
<!--            <column name="Z_DRAFT_STATUS" />-->
<!--            <column name="SLACONFIGURATIONPARTY_ID" valueNumeric="2"/>-->
<!--            <column name="PAYMENTSYSTEM_ID" valueNumeric="1"/>-->
<!--            <column name="STAGE" value="New,Reply Repair,More Information Provided,Represented,Additional Info Required"/>-->
<!--            <column name="URGENCY" value="Urgent"/>-->
<!--            <column name="MAXDAYS" valueNumeric="2"/>-->
<!--            <column name="SLACONFIGAUTOMATICACTION_ID" valueNumeric="1"/>-->
<!--            <column name="Z_DRAFT_ID" />-->
<!--            <column name="Z_STATUS_ID" />-->
<!--        </insert>-->
    </changeSet>
</databaseChangeLog>
