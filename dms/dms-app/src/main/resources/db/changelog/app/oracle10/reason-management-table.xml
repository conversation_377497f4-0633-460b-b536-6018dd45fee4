<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="reason-management-table" author="Satanai">
        <createTable tableName="DMS_ReasonManagement">
            <column name="ID" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_REASON_MNGMNT"/>
            </column>
            <column name="CODE" type="VARCHAR2(50 CHAR)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="NAME" type="VARCHAR2(50 CHAR)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR2(100 CHAR)"/>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_ASSIGNED_GROUP" type="NUMBER(19, 0)"/>
            <column name="Z_ASSIGNED_USER" type="NUMBER(19, 0)"/>
            <column name="Z_CREATED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DELETED_FLAG" type="NUMBER(1, 0)"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="NUMBER(1, 0)"/>
            <column name="Z_LOCKED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="NUMBER(19, 0)"/>
            <column name="Z_TENANT_ID" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="NUMBER(19, 0)"/>
            <column name="Z_WS_TOKEN" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DRAFT_ID" type="NUMBER(19, 0)"/>
            <column name="Z_STATUS_ID" type="NUMBER(19, 0)"/>
            <column name="ISDISPUTE" type="NUMBER(1, 0)"/>
            <column name="ISREJECTION" type="NUMBER(1, 0)"/>
            <column name="ISREPRESENTMENT" type="NUMBER(1, 0)"/>
            <column name="ISREQADDITIONALINFO" type="NUMBER(1, 0)"/>
        </createTable>
        <createSequence cacheSize="0" maxValue="9999999999999999999999999999" sequenceName="SEQ_DMS_REASONMANAGEMENT" startValue="1"/>
        <rollback>
            <sql>
                DROP TABLE IF EXISTS DMS_ReasonManagement CASCADE;
            </sql>
        </rollback>
    </changeSet>

    <changeSet id="Dispute-reason-FK-Constraint" author="Yadirichukwu">
        <addForeignKeyConstraint baseTableName="DMS_DisputeCases" baseColumnNames="REASON_ID" constraintName="FK_L6746C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>

    <changeSet id="def-bnk-0-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DefendantBankCaseManagement"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_M6846C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="def-bnk-1-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DefendantBankCaseManagement"
                                 baseColumnNames="REPRESENTREASON" constraintName="FK_M6846C99XI60X6GFZ5N4ZO059" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="def-bnk-2-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DefendantBankCaseManagement"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M6846C99XI60X6GFZ5N4ZO859" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
<!--    claimant   -->
    <changeSet id="clmt-1-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_ClaimantNCBCaseManagement"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_M7I46C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="clmt-2-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_ClaimantNCBCaseManagement"
                                 baseColumnNames="REPRESENTREASON" constraintName="FK_M7J46C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="clmt-3-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_ClaimantNCBCaseManagement"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M7K46C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
<!--    -->
    <changeSet id="def-0-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DefendantNCBCaseManagement"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_M6746D99XI60X6IFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="def-1-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DefendantNCBCaseManagement"
                                 baseColumnNames="REPRESENTREASON" constraintName="FK_M6746D99XI60X6HFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="def-2-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DefendantNCBCaseManagement"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M6746D99XI60X6GFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
<!--    -->
    <changeSet id="clmt-bnk-2-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_ClaimantBankCaseManagement"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M6746F99XI60X6GFZ5N4XO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="clmt-bnk-1-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_ClaimantBankCaseManagement"
                                 baseColumnNames="REPRESENTREASON" constraintName="FK_M6746F99XI60X6GFZ5N4YO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="clmt-bnk-0-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_ClaimantBankCaseManagement"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_M6746F99XI60X6GFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="opr-0-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_OperatorCaseManagement"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_P6846C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="opr-2-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_OperatorCaseManagement"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M6846C95XI60X6GFZ5N4ZO859" referencedTableName="DMS_ReasonManagement"
                                 referencedColumnNames="ID"/>
    </changeSet>

</databaseChangeLog>
