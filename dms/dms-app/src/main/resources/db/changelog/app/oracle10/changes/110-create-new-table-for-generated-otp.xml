<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="Satanai" id="110-create-new-table-for-generated-otp">
        <createTable tableName="DMS_OTP">
            <column name="ID" type="NUMBER(19,0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C0062859177"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_ASSIGNED_GROUP" type="NUMBER(19, 0)"/>
            <column name="Z_ASSIGNED_USER" type="NUMBER(19, 0)"/>
            <column name="Z_CREATED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DELETED_FLAG" type="NUMBER(1,0)"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="NUMBER(1,0)"/>
            <column name="Z_LOCKED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="NUMBER(19, 0)"/>
            <column name="Z_TENANT_ID" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="NUMBER(19, 0)"/>
            <column name="Z_WS_TOKEN" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DRAFT_ID" type="NUMBER(19, 0)"/>
            <column name="Z_STATUS_ID" type="NUMBER(19, 0)"/>
            <column name="SESSIONID" type="VARCHAR2(100 CHAR)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="OTP" type="VARCHAR2(10 CHAR)">
                <constraints nullable="false"/>
            </column>
            <column name="USERNAME" type="VARCHAR2(255 CHAR)">
                <constraints nullable="false"/>
            </column>
            <column name="RESENDATTEMPTS" type="NUMBER(10,0)">
                <constraints nullable="false"/>
            </column>
            <column name="VALIDATEATTEMPTS" type="NUMBER(10,0)">
                <constraints nullable="false"/>
            </column>
            <column name="EXPIARYTIME" type="TIMESTAMP(6)"/>
        </createTable>

    </changeSet>

</databaseChangeLog>