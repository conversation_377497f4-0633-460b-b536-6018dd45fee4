<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.24.xsd">
    <changeSet author="Satanai" id="90-change-oid-to-bytea" failOnError="true">
        <sql>
            ALTER TABLE DMS_DISPUTEATT ALTER COLUMN ATTFILE TYPE BYTEA USING ATTFILE::TEXT::BYTEA;;
            ALTER TABLE DMS_DISPUTEATT ALTER COLUMN IMAGE_THUMBNAIL TYPE BYTEA USING IMAGE_THUMBNAIL::TEXT::BYTEA;;
            ALTER TABLE QRTZ_BLOB_TRIGGERS ALTER COLUMN BLOB_DATA TYPE BYTEA USING BLOB_DATA::TEXT::BYTEA;;
            ALTER TABLE QRTZ_CALENDARS ALTER COLUMN CALENDAR TYPE BYTEA USING CALENDAR::TEXT::BYTEA;;
        </sql>
    </changeSet>
</databaseChangeLog>