<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="Nasief" id="remove-country-from-DMS_DisputeDetailsReport">
        <dropColumn tableName="DMS_DisputeDetailsReport">
            <column name="country_id"/>
        </dropColumn>
        <rollback>
            ALTER TABLE DMS_DisputeDetailsReport ADD COLUMN country_id NUMBER(19, 0);
        </rollback>
    </changeSet>
    <changeSet author="Nasief" id="remove-country-from-DMS_DisputeSummaryReport">
        <dropColumn tableName="DMS_DisputeSummaryReport">
            <column name="country_id"/>
        </dropColumn>
        <rollback>
            ALTER TABLE DMS_DisputeSummaryReport ADD COLUMN country_id NUMBER(19, 0);
        </rollback>
    </changeSet>
</databaseChangeLog>