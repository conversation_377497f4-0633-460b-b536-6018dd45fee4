<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.8.xsd">
    <changeSet author="Mary" id="3895727271242-236">
        <createTable tableName="DMS_ACLCONFIGSPARTICIPANTS">
            <column name="ACL_CONFIGS_ID" type="NUMBER(19, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="PARTICIPANTS_ID" type="NUMBER(19, 0)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Mary" id="3455727271242-236">
        <createTable tableName="DMS_ACLCONFIGSUSERS">
            <column name="ACL_CONFIGS_ID" type="NUMBER(19, 0)">
                <constraints nullable="false"/>
            </column>
            <column name="USERS_ID" type="NUMBER(19, 0)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet author="Mary" id="SEQ_DMS_ACLCONFIG">
        <createSequence cacheSize="0" maxValue="9999999999999999999999999999" sequenceName="SEQ_DMS_ACLCONFIG" startValue="1"/>
    </changeSet>
    <changeSet author="Mary" id="add-acl-configuration-table">
        <createTable tableName="DMS_AclConfigs">
            <column name="ID" type="NUMBER(19, 0)">
                <constraints nullable="false" primaryKey="true" primaryKeyName="SYS_C5KUGKFR798"/>
            </column>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_ASSIGNED_GROUP" type="NUMBER(19, 0)"/>
            <column name="Z_ASSIGNED_USER" type="NUMBER(19, 0)"/>
            <column name="Z_CREATED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DELETED_FLAG" type="NUMBER(1, 0)"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="NUMBER(1, 0)"/>
            <column name="Z_LOCKED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="NUMBER(19, 0)"/>
            <column name="Z_TENANT_ID" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="NUMBER(19, 0)"/>
            <column name="Z_WS_TOKEN" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR2(255 CHAR)"/>
            <column name="Z_DRAFT_ID" type="NUMBER(19, 0)"/>
            <column name="Z_STATUS_ID" type="NUMBER(19, 0)"/>
            <column name="MAXAMOUNT" type="NUMBER(14, 5)"/>
        </createTable>
    </changeSet>
    <changeSet author="Mary" id="3455727271242-235">
        <addForeignKeyConstraint baseColumnNames="ACL_CONFIGS_ID" baseTableName="DMS_ACLCONFIGSPARTICIPANTS"
                                 constraintName="FK_STCBAVDLVY8VULERTNSM3G09JA" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="ID" referencedTableName="DMS_AclConfigs" validate="true"/>
    </changeSet>
    <changeSet author="Mary" id="3455727271242-237">
        <addForeignKeyConstraint baseColumnNames="PARTICIPANTS_ID" baseTableName="DMS_ACLCONFIGSPARTICIPANTS"
                                 constraintName="FK_GRAL8OEIY0GGEVIQBW0WRGB48" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="ID" referencedTableName="DMS_Participants" validate="true"/>
    </changeSet>
    <changeSet author="Mary" id="3455456271242-235">
        <addForeignKeyConstraint baseColumnNames="ACL_CONFIGS_ID" baseTableName="DMS_ACLCONFIGSUSERS"
                                 constraintName="FK_STCBAVDLVY8VULERTNSM3GNIJA" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="ID" referencedTableName="DMS_AclConfigs" validate="true"/>
    </changeSet>
    <changeSet author="Mary" id="34556797271242-237">
        <addForeignKeyConstraint baseColumnNames="USERS_ID" baseTableName="DMS_ACLCONFIGSUSERS"
                                 constraintName="FK_GRAL8OEIY0GGETSYBW0WRGB48" deferrable="false"
                                 initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"
                                 referencedColumnNames="ID" referencedTableName="JFW_USERS" validate="true"/>
    </changeSet>
</databaseChangeLog>