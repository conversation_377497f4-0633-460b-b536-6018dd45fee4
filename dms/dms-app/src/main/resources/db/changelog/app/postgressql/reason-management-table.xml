<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="reason-management-table" author="Satanai">
        <createTable tableName="DMS_REASONMANAGEMENT">
            <column name="ID" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_REASON_MNGMNT"/>
            </column>
            <column name="CODE" type="VARCHAR(50)">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="NAME" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="DESCRIPTION" type="VARCHAR(100)"/>
            <column name="Z_ARCHIVE_ON" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_QUEUED" type="TIMESTAMP(6)"/>
            <column name="Z_ARCHIVE_STATUS" type="VARCHAR(255)"/>
            <column name="Z_ASSIGNED_GROUP" type="BIGINT"/>
            <column name="Z_ASSIGNED_USER" type="BIGINT"/>
            <column name="Z_CREATED_BY" type="VARCHAR(255)"/>
            <column name="Z_CREATION_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_DELETED_BY" type="VARCHAR(255)"/>
            <column name="Z_DELETED_FLAG" type="BOOLEAN"/>
            <column name="Z_DELETED_ON" type="TIMESTAMP(6)"/>
            <column name="Z_EDITABLE" type="BOOLEAN"/>
            <column name="Z_LOCKED_BY" type="VARCHAR(255)"/>
            <column name="Z_LOCKED_UNTIL" type="TIMESTAMP(6)"/>
            <column name="Z_ORG_ID" type="BIGINT"/>
            <column name="Z_TENANT_ID" type="VARCHAR(255)"/>
            <column name="Z_UPDATED_BY" type="VARCHAR(255)"/>
            <column name="Z_UPDATING_DATE" type="TIMESTAMP(6)"/>
            <column name="Z_WORKFLOW_ID" type="BIGINT"/>
            <column name="Z_WS_TOKEN" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_STATUS" type="VARCHAR(255)"/>
            <column name="Z_DRAFT_ID" type="BIGINT"/>
            <column name="Z_STATUS_ID" type="BIGINT"/>
            <column name="ISDISPUTE" type="BOOLEAN"/>
            <column name="ISREJECTION" type="BOOLEAN"/>
            <column name="ISREPRESENTMENT" type="BOOLEAN"/>
            <column name="ISREQADDITIONALINFO" type="BOOLEAN"/>
        </createTable>
        <createSequence cacheSize="0" maxValue="9223372036854775807" sequenceName="SEQ_DMS_REASONMANAGEMENT" startValue="1"/>
        <rollback>
            <sql>
                DROP TABLE IF EXISTS DMS_REASONMANAGEMENT CASCADE;
            </sql>
        </rollback>
    </changeSet>

    <changeSet id="Dispute-reason-FK-Constraint" author="Yadirichukwu">
        <addForeignKeyConstraint baseTableName="DMS_DISPUTECASES" baseColumnNames="REASON_ID" constraintName="FK_L6746C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>

    <changeSet id="def-bnk-0-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DEFENDANTBANKCASEMANAGEMENT"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_M6846C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="def-bnk-1-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DEFENDANTBANKCASEMANAGEMENT"
                                 baseColumnNames="REPRESENTREASON" constraintName="FK_M6846C99XI60X6GFZ5N4ZO059" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="def-bnk-2-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DEFENDANTBANKCASEMANAGEMENT"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M6846C99XI60X6GFZ5N4ZO859" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
<!--    claimant   -->
    <changeSet id="clmt-1-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_CLAIMANTNCBCASEMANAGEMENT"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_M7I46C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="clmt-2-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_CLAIMANTNCBCASEMANAGEMENT"
                                 baseColumnNames="REPRESENTREASON" constraintName="FK_M7J46C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="clmt-3-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_CLAIMANTNCBCASEMANAGEMENT"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M7K46C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
<!--    -->
    <changeSet id="def-0-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DEFENDANTNCBCASEMANAGEMENT"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_M6746D99XI60X6IFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="def-1-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DEFENDANTNCBCASEMANAGEMENT"
                                 baseColumnNames="REPRESENTREASON" constraintName="FK_M6746D99XI60X6HFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="def-2-ncb-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_DEFENDANTNCBCASEMANAGEMENT"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M6746D99XI60X6GFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
<!--    -->
    <changeSet id="clmt-bnk-2-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_CLAIMANTBANKCASEMANAGEMENT"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M6746F99XI60X6GFZ5N4XO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="clmt-bnk-1-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_CLAIMANTBANKCASEMANAGEMENT"
                                 baseColumnNames="REPRESENTREASON" constraintName="FK_M6746F99XI60X6GFZ5N4YO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="clmt-bnk-0-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_CLAIMANTBANKCASEMANAGEMENT"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_M6746F99XI60X6GFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="opr-0-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_OPERATORCASEMANAGEMENT"
                                 baseColumnNames="REJECTIONREASON" constraintName="FK_P6846C99XI60X6GFZ5N4ZO459" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>
    <changeSet id="opr-2-reason-FK-Constraint" author="Damilola">
        <addForeignKeyConstraint baseTableName="DMS_OPERATORCASEMANAGEMENT"
                                 baseColumnNames="REQADDINFOREASON" constraintName="FK_M6846C95XI60X6GFZ5N4ZO859" referencedTableName="DMS_REASONMANAGEMENT"
                                 referencedColumnNames="ID"/>
    </changeSet>

</databaseChangeLog>
