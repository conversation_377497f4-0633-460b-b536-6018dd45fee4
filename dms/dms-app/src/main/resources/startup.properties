setup.enabled=true
setup.tasks=setAppKeyTask,dmsCreateViewSchemeTask,createBusinessRolesTask,updateAccountPolicyTask,createTenantsTask,activateWorkflowsTask,executeScriptsTask,createNCBsTask,createParticipantsTask,prepareBanksAndNCBsUsersTask,flushEhCacheTask,initEntitiesWorkflowsTask,executeDemoDataScriptTask
startup.tasks=executeRouteBuildersTask,startCamelContextTask

app.key=(DMS).*

routeBuilders.contexts=defaultCamelContext
routeBuilders.defaultCamelContext=dmsRouteBuilder
route.names=DMS
camel.contexts=defaultCamelContext


views.schemes=Operator,^(DMS_DisputeCases|DMS_OperatorCaseManagement|DMS_Correspondence|DMS_TransactionAdditionalInfo|DMS_Participants|DMS_PaymentSystems|DMS_SystemConfiguration|DMS_SLAConfiguration|DMS_ReasonManagement|DMS_TermsAndConditions|DMS_AclConfigs).*:\
  Banks,^(DMS_DisputeCases|DMS_ClaimantBankCaseManagement|DMS_DefendantBankCaseManagement|DMS_Correspondence|DMS_TransactionAdditionalInfo).*


##Role name,view scheme name,views regex from view scheme,show to childs
## NotificationScheme|NotificationWFResultView
business.roles=Operator ,Operator,^(DMS_DisputeCases|DMS_OperatorCaseManagement|DMS_Correspondence|DMS_TransactionAdditionalInfo|DMS_Participants|DMS_PaymentSystems|DMS_SystemConfiguration|DMS_SLAConfiguration|DMS_ReasonManagement|DMS_TermsAndConditions|DMS_AclConfigs).*,false,Maker:\
  Banks ,Banks,^(DMS_DisputeCases|DMS_ClaimantBankCaseManagement|DMS_DefendantBankCaseManagement|DMS_Correspondence|DMS_TransactionAdditionalInfo).*,false,Maker:\
  Operator ,Operator,^(DMS_DisputeCases|DMS_OperatorCaseManagement|DMS_Correspondence|DMS_TransactionAdditionalInfo|DMS_Participants|DMS_PaymentSystems|DMS_SystemConfiguration|DMS_SLAConfiguration|DMS_ReasonManagement|DMS_TermsAndConditions|DMS_AclConfigs).*,false,Checker:\
  Banks ,Banks,^(DMS_DisputeCases|DMS_ClaimantBankCaseManagement|DMS_DefendantBankCaseManagement|DMS_Correspondence|DMS_TransactionAdditionalInfo).*,false,Checker

workflow.filter=^WF_.*
workflow.skip=

accountPolicy.description=Default Account Policy
accountPolicy.removeSecretQuestions=true
accountPolicy.oneTimePasswordAge=1000
accountPolicy.minPasswordLength=1
accountPolicy.minPasswordStrength=50
accountPolicy.channel=JFW_MESSAGE

tenants.names=DMS
tenants.orgs=
tenants.viewScheme=Operator
tenants.resetPasswords=true
tenants.defaultPassword=a
