package com.progressoft.dms.startup

import com.progressoft.jfw.startup.OptionsProvider
import com.progressoft.jfw.startup.StartupTask
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
open class DmsCreateViewSchemeTask(@Autowired private val createViewSchemeTask: CreateViewSchemeTask) : StartupTask() {

    companion object {
        private const val VIEW_SCHEMAS = "views.schemes"
    }

    @Transactional
    override fun execute(optionsProvider: OptionsProvider) {
        val viewSchemes: String = optionsProvider[VIEW_SCHEMAS]
        LOG.info("Start building views schemes")
        jfw.enableServiceUser("SYSTEM").use {
            val viewSchemeConfigs = viewSchemes.split(":".toRegex()).dropLastWhile { it.isEmpty() }
                .toTypedArray()
            for (viewSchemeConfig in viewSchemeConfigs) {
                createViewSchemeTask.execute(getOptionsProvider(optionsProvider, viewSchemeConfig))
                LOG.info(
                    "done creating views scheme names [{}], with filter [{}]",
                    optionsProvider["viewScheme.description"], optionsProvider["viewScheme.viewsFilter"]
                )
            }
            optionsProvider.load()
            LOG.info("Done building views schemes")
        }
    }

    private fun getOptionsProvider(optionsProvider: OptionsProvider, viewSchemeConfig: String): OptionsProvider {
        val rawDataConfig = viewSchemeConfig.split(",".toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        optionsProvider["viewScheme.description"] = rawDataConfig[0]
        optionsProvider["viewScheme.viewsFilter"] = rawDataConfig[1]
        return optionsProvider
    }

}