package com.progressoft.dms.startup

import com.progressoft.dms.entities.DMS_NationalCentralBank
import com.progressoft.jfw.AppUnhandledException
import com.progressoft.jfw.model.bussinessobject.core.JfwCountry
import com.progressoft.jfw.startup.OptionsProvider
import com.progressoft.jfw.startup.StartupTask
import com.progressoft.psdms.application.constants.WF_Bank
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
open class CreateNCBsTask : StartupTask() {

    @Value("\${ncb.names}")
    var ncbsNames: String = ""

    @Value("\${ncb.shortnames}")
    var ncbsShortnames: String= ""

    @Value("\${ncb.bic}")
    var ncbsBic: String= ""

    @Value("\${ncb.country}")
    var ncbsCountry: String= ""

    @Value("\${ncb.email}")
    var ncbsEmail: String= ""

    override fun execute(options: OptionsProvider) {
        try {
            jfw.enableServiceUser("DMS").use {
                val ncbNames = ncbsNames.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                val shortNames = ncbsShortnames.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                val bicCodes = ncbsBic.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                val country = ncbsCountry.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                val emails = ncbsEmail.split(",".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                for (i in ncbNames.indices) create(
                    createNewNcb(
                        ncbNames[i],
                        shortNames[i],
                        bicCodes[i],
                        country[i],
                        emails[i]
                    )
                )
                itemDao.flush()
            }

        } catch (e: Exception) {
            throw AppUnhandledException(e)
        }
    }

    private fun createNewNcb(
        bankName: String,
        shortName: String,
        bicCode: String,
        country: String,
        email: String
    ): DMS_NationalCentralBank {
        val ncb = DMS_NationalCentralBank()
        ncb.name = shortName
        ncb.code = bicCode
        ncb.description = bankName
        ncb.country = itemDao.getItem(JfwCountry::class.java, "countryStringIsoCode", country)
        ncb.email = email
        return ncb
    }

    private fun create(ncb: DMS_NationalCentralBank) {
        jfw.createEntity(ncb)
        jfw.executeAction(ncb, WF_Bank.ACTION_NAME_RequestApprove)
        jfw.executeAction(ncb, WF_Bank.ACTION_NAME_Approve)
    }
}