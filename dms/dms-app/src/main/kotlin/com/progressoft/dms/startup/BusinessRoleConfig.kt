package com.progressoft.dms.startup

import org.apache.commons.lang3.StringUtils

class BusinessRoleConfig(rawObject: String) {

    private val FIELDS_SPLITTER = ","
    private var roleName: String? = null
    private var viewSchemeName: String? = null
    private var viewNameRegex: String? = null
    private var showToChilds = false
    private var createMaker = false
    private var createChecker = false

    init {
        val singleLookupItem = rawObject.split(FIELDS_SPLITTER.toRegex()).dropLastWhile { it.isEmpty() }
            .toTypedArray()
        roleName = singleLookupItem[0]
        viewSchemeName = singleLookupItem[1]
        viewNameRegex = singleLookupItem[2]
        showToChilds = java.lang.Boolean.valueOf(singleLookupItem[3])
        createMaker = isEnabled(singleLookupItem, "Maker")
        createChecker = isEnabled(singleLookupItem, "Checker")
    }

    fun getRoleName(): String? {
        return roleName
    }

    fun getViewSchemeName(): String? {
        return viewSchemeName
    }

    fun getViewNameRegex(): String? {
        return viewNameRegex
    }

    fun isShowToChilds(): Boolean {
        return showToChilds
    }

    fun isCreateMaker(): Boolean {
        return createMaker
    }

    fun isCreateChecker(): Boolean {
        return createChecker
    }

    override fun toString(): String {
        return "BusinessRoleConfig{" +
                "roleName='" + roleName + '\'' +
                ", viewSchemeName='" + viewSchemeName + '\'' +
                ", viewNameRegex='" + viewNameRegex + '\'' +
                ", showToChilds=" + showToChilds +
                ", createMaker=" + createMaker +
                ", createChecker=" + createChecker +
                '}'
    }

    private fun isEnabled(singleLookupItem: Array<String>, filter: String): Boolean {
        return singleLookupItem.size == 4 || StringUtils.containsIgnoreCase(singleLookupItem[4], filter)
    }
}