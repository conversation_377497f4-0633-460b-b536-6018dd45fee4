package com.progressoft.dms.startup

import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.core.JfwSchmViews
import com.progressoft.jfw.model.bussinessobject.core.JfwViewsScheme
import com.progressoft.jfw.model.service.utils.RootAppContext
import com.progressoft.jfw.startup.DependsOnTasks
import com.progressoft.jfw.startup.OptionsProvider
import com.progressoft.jfw.startup.StartupTask
import com.progressoft.repository.view.ViewRepository
import com.progressoft.repository.view.dto.ViewDto
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Component
@Primary
@DependsOnTasks("flushEhCacheTask")
open class CreateViewSchemeTask : StartupTask() {

    companion object {
        private const val VIEW_SCHEMES_VIEW_NAME = "ViewsScheme"
        private const val VIEWS_FILTER = "viewScheme.viewsFilter"
        private const val SCHEME_DESCRIPTION = "viewScheme.description"
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun execute(options: OptionsProvider) {
        createViewScheme(
            options,
            VIEWS_FILTER,
            SCHEME_DESCRIPTION
        )
    }

    private fun createViewScheme(options: OptionsProvider, viewsFilter: String, schemeDescription: String) {
        val schemeName = options[schemeDescription]
        LOG.info("Creating scheme {}..", schemeName)
        if (viewSchemeAlreadyExists(schemeName)) {
            LOG.warn("A view scheme with the description {} already exists.", schemeName)
        } else {
            val views = RootAppContext.getApplicationContext().getBean(
                ViewRepository::class.java
            ).listAll()
            val filteredViews = filterViews(views, options[viewsFilter])
            LOG.info("{} views found..", filteredViews.size)
            this.createViewScheme(schemeName, filteredViews)
            LOG.info("View scheme {} created successfully!", schemeName)
        }
    }

    private fun createViewScheme(schemeDescription: String, views: List<ViewDto>) {
        val viewScheme = JfwViewsScheme()
        viewScheme.description = schemeDescription
        for (view: ViewDto in views) {
            val jfwSchmViews = JfwSchmViews()
            jfwSchmViews.viewId = view.name
            jfwSchmViews.viewsSchemeId = viewScheme
            viewScheme.schemeViews.add(jfwSchmViews)
        }
        jfw.createEntity(VIEW_SCHEMES_VIEW_NAME, viewScheme)
    }

    private fun filterViews(views: List<ViewDto>, filter: String): List<ViewDto> {
        val filtered: MutableList<ViewDto> = ArrayList()
        for (view in views) {
            if (viewNameMatchesFilter(view, filter)) {
                filtered.add(view)
            }
        }
        return filtered
    }

    private fun viewNameMatchesFilter(view: ViewDto, filter: String): Boolean {
        return view.name != null && (view.name!!.matches(filter.toRegex()) || isInboxMessageView(view))
    }

    private fun isInboxMessageView(view: ViewDto): Boolean {
        return Objects.requireNonNull(view.name).equals("InboxMessagesView", ignoreCase = true)
    }

    private fun viewSchemeAlreadyExists(schemeDescription: String): Boolean {
        return Query(itemDao).equals("description", schemeDescription).doesExist(
            JfwViewsScheme::class.java
        )
    }

}