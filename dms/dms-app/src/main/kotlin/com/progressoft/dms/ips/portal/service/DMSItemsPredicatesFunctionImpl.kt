package com.progressoft.dms.ips.portal.service

import com.github.wenhao.jpa.PredicateBuilder
import com.github.wenhao.jpa.Specifications.and
import com.github.wenhao.jpa.Specifications.or
import com.progressoft.dms.entities.DMS_AclConfig
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.dao.item.ItemDao
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class DMSItemsPredicatesFunctionImpl @Autowired constructor(private val itemDao: ItemDao) : DMSItemsPredicatesFunction {
    @Value("\${DefaultTenant}")
    val operatorAccount: String = ""

    override fun setPPSItemsPredicates(viewName: String, specBuilder: PredicateBuilder<Any?>?, orgShortName: String) {
        if (orgShortName != operatorAccount && viewName == DISPUTE_CASE_VIEW) {
            getNonACLConfigPredicate(specBuilder, viewName, orgShortName)
            val aclConfigs = Query(itemDao)
                .equals(STATUSID_DESCRIPTION, ACTIVE)
                .items(DMS_AclConfig::class.java)

            if (aclConfigs.isNotEmpty()) {
                filterConfigsByUser(aclConfigs, orgShortName).forEach {
                    getPredicate(viewName, specBuilder, it)
                }
            }
        }
    }

    private fun filterConfigsByUser(aclConfigs: MutableList<DMS_AclConfig>, orgShortName: String) = aclConfigs.filter {
        it.users.map { user ->
            user.rootOrg.shortName
        }.toList().contains(orgShortName)
    }

    private fun getPredicate(viewName: String, specBuilder: PredicateBuilder<Any?>?, config: DMS_AclConfig) {
        if (config.participants.isNotEmpty()) {
            specBuilder!!
                .predicate(
                    viewName == DISPUTE_CASE_VIEW,
                    or<Any>()
                        .`in`(CLAIMANT_BANK_CODE, getParticipantsCode(config.participants))
                        .`in`(DEFENDANT_BANK_CODE, getParticipantsCode(config.participants))
                        .build()
                )
        }
        if (config.maxAmount != null) {
            specBuilder!!
                .predicate(
                    viewName == DISPUTE_CASE_VIEW,
                    and<Any>()
                        .lt(AMOUNT, config.maxAmount)
                        .build()
                )
        }
    }

    private fun getNonACLConfigPredicate(specBuilder: PredicateBuilder<Any?>?, viewName: String, orgShortName: String) {
        specBuilder!!
            .predicate(
                viewName == DISPUTE_CASE_VIEW,
                or<Any>()
                    .eq(CLAIMANT_BANK_NAME, orgShortName)
                    .eq(DEFENDANT_BANK_NAME, orgShortName)
                    .eq(CLAIMANT_NCB_BANK_NAME, orgShortName)
                    .eq(DEFENDANT_NCB_BANK_NAME, orgShortName)
                    .build()
            )
    }

    private fun getParticipantsCode(prts: List<DMS_Participant>) =
        prts.map(DMS_Participant::getCode)

    companion object {
        private const val ACTIVE = "Active"
        private const val AMOUNT = "transactionAmount"
        private const val CLAIMANT_BANK_CODE = "claimantBank.code"
        private const val DEFENDANT_BANK_CODE = "defendantBank.code"
        private const val CLAIMANT_BANK_NAME = "claimantBank.name"
        private const val DEFENDANT_BANK_NAME = "defendantBank.name"
        private const val STATUSID_DESCRIPTION = "statusId.description"
        private const val DISPUTE_CASE_VIEW = "DMS_DisputeCases.View"
        private const val CLAIMANT_NCB_BANK_NAME = "claimantBank.nationalCentralBank.name"
        private const val DEFENDANT_NCB_BANK_NAME = "defendantBank.nationalCentralBank.name"
    }
}