package com.progressoft.dms.startup

import com.progressoft.jfw.model.bussinessobject.core.JfwViewsScheme
import com.progressoft.jfw.model.bussinessobject.security.BusinessRoles
import com.progressoft.jfw.model.bussinessobject.security.Org
import com.progressoft.jfw.model.bussinessobject.security.Tenant
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.workflow.function.security.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
open class DmsSecurityConfigurator : AppSecurityConfigurator() {

    @Autowired
    private val roleSecurityConfigurator: RoleSecurityConfigurator? = null

    @Autowired
    private val groupSecurityConfigurator: GroupSecurityConfigurator? = null

    @Autowired
    private val userSecurityConfigurator: UserSecurityConfigurator? = null

    @Autowired
    private val itemDao: ItemDao? = null

    override fun setup(tenant: Tenant?, tenantSuperOrg: Org?) {
        setGroupSecurityConfigurator(groupSecurityConfigurator)
        setRoleSecurityConfigurator(roleSecurityConfigurator)
        setUserSecurityConfigurator(userSecurityConfigurator)
        setItemDao(itemDao)
    }

    open fun createDmsBusinessRoleFromViewScheme(
        tenant: Tenant?,
        org: Org?,
        viewScheme: JfwViewsScheme?,
        authLevel: AuthorizationLevel?,
        potentialPrefix: String?
    ): BusinessRoles {
        return createBusinessRoleFromViewScheme(tenant, org, viewScheme, authLevel, potentialPrefix)
    }
}