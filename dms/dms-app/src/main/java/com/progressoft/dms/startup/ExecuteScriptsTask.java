package com.progressoft.dms.startup;

import com.progressoft.jfw.ServiceUserEnabled;
import com.progressoft.jfw.startup.OptionsProvider;
import com.progressoft.jfw.startup.StartupTask;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.io.InputStream;

import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Objects.requireNonNull;


@Transactional
public class ExecuteScriptsTask extends StartupTask {

    @Value("${scripts}")
    String scripts;


    @Override
    public void execute(OptionsProvider optionsProvider) {
        for (String script : scripts.split(",")) executeScript(optionsProvider, script);
        LOG.info("Scripts Executed Successfully!");
    }

    private void executeScript(OptionsProvider options, String scriptName) {
        LOG.info("Start Execute {} ..", scriptName);
        try (ServiceUserEnabled ignored = jfw.enableSystemServiceUser();
             InputStream resource = this.getClass().getResourceAsStream("/" + options.get("db_type").toLowerCase() + "/" + scriptName)) {
            itemDao.getEntityManager().createNativeQuery(IOUtils.toString(requireNonNull(resource), UTF_8)).executeUpdate();
        } catch (IOException e) {
            LOG.error("Could Not execute {} ..", scriptName);
        }
    }
}

