package com.progressoft.dmskuwait.controllers

import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.dms.entities.DMS_PaymentSystems
import com.progressoft.dmskuwait.helper.TestHelper
import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency
import com.progressoft.psdms.application.exceptions.FetchDisputesException
import com.progressoft.psdms.application.utils.Constants.Companion.ACTIVE_DISPUTE_STATUS_CODE
import com.progressoft.psdms.application.utils.Constants.Companion.APPROVED_CLOSED_DISPUTE_STATUS_CODE
import com.progressoft.psdms.application.utils.Constants.Companion.MODIFICATION_WF_STATUSES
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.http.HttpStatus
import java.math.BigDecimal
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class DisputeCaseReferenceControllerTest : TestHelper() {
    private val controller: DisputeCaseReferenceController = DisputeCaseReferenceController(itemDao)
    private lateinit var arabBank: DMS_Participant
    private lateinit var ahliBank: DMS_Participant
    private lateinit var paymentSystem: DMS_PaymentSystems
    private lateinit var usd: JFWCurrency

    @BeforeEach
    fun setUp() {
        arabBank = createNewParticipant(1, "ARAB")
        ahliBank = createNewParticipant(2, "AHLI")
        paymentSystem = createPaymentSystem(1, "ACH")
        usd = createNewCurrency(1, "USD")
        itemDao.affectItems()
        itemDao.filterItems()
    }

    @Test
    fun givenNumberOfFilters_whenCallingTheController_thenShouldReturnDisputedCasesMatchedWithTheFilters() {
        val currentDispute = createDisputeCase(
            1,
            "1",
            true,
            ahliBank,
            arabBank,
            paymentSystem,
            usd,
            APPROVED_CLOSED_DISPUTE_STATUS_CODE,
            BigDecimal.valueOf(100)
        )
        createDisputeCase(
            2,
            "2",
            false,
            ahliBank,
            arabBank,
            paymentSystem,
            usd,
            APPROVED_CLOSED_DISPUTE_STATUS_CODE,
            BigDecimal.valueOf(70)
        )
        val disputeCase = createDisputeCase(
            3,
            "3",
            false,
            arabBank,
            ahliBank,
            paymentSystem,
            usd,
            APPROVED_CLOSED_DISPUTE_STATUS_CODE,
            BigDecimal.valueOf(50)
        )
        createDisputeCase(
            4,
            "4",
            false,
            ahliBank,
            arabBank,
            paymentSystem,
            createNewCurrency(2, "JOD"),
            APPROVED_CLOSED_DISPUTE_STATUS_CODE,
            BigDecimal.valueOf(20)
        )
        val pageable = MockPageable(2, 2, null)
        val filters = getDisputeCaseFilters(currentDispute.id.toString(), "", usd.code, "5", "100")
        val response = controller.listActiveDisputes(pageable, filters)
        assertEquals(HttpStatus.OK, response.statusCode)
        val retrievedDisputeCases = response.body.content
        assertFalse(retrievedDisputeCases.isEmpty())
        assertEquals(1, retrievedDisputeCases.size)
        assertEquals(retrievedDisputeCases[0].caseReferenceNumber, disputeCase.caseReferenceNumber)
        assertEquals(retrievedDisputeCases[0].claimantBank, disputeCase.claimantBank!!.code)
        assertEquals(retrievedDisputeCases[0].defendantBank, disputeCase.defendantBank!!.code)
        assertEquals(retrievedDisputeCases[0].transactionCurrency, disputeCase.transactionCurrency?.codeNamePair)
        assertEquals(retrievedDisputeCases[0].disputedAmount, disputeCase.disputedAmount)
        assertEquals(retrievedDisputeCases[0].paymentSystem, disputeCase.paymentSystem!!.code)
    }

    @Test
    fun givenNotExistItemId_whenCallingTheController_thenShouldThrowException() {
        createDisputeCase(
            1,
            "1",
            true,
            ahliBank,
            arabBank,
            paymentSystem,
            usd,
            MODIFICATION_WF_STATUSES[0],
            BigDecimal.valueOf(100)
        )
        createDisputeCase(
            2,
            "2",
            false,
            ahliBank,
            arabBank,
            paymentSystem,
            usd,
            ACTIVE_DISPUTE_STATUS_CODE,
            BigDecimal.valueOf(70)
        )
        val pageable = MockPageable(2, 2, null)
        val filters = getDisputeCaseFilters("10", "2", usd.code, "5", "100")
        Assertions.assertThrows(FetchDisputesException::class.java) {
            controller.listActiveDisputes(
                pageable,
                filters
            )
        }
    }

    @Test
    fun givenDisputeCaseNotMatchedWithPaymentSystem_whenCallingTheController_thenShouldReturnNothing() {
        val paymentSystem2 = createPaymentSystem(2, "MPC")
        val currentDispute = createDisputeCase(
            1,
            "1",
            true,
            ahliBank,
            arabBank,
            paymentSystem,
            usd,
            MODIFICATION_WF_STATUSES[0],
            BigDecimal.valueOf(100)
        )
        createDisputeCase(
            2,
            "2",
            false,
            ahliBank,
            arabBank,
            paymentSystem2,
            usd,
            ACTIVE_DISPUTE_STATUS_CODE,
            BigDecimal.valueOf(70)
        )
        val pageable = MockPageable(2, 2, null)
        val filters = getDisputeCaseFilters(currentDispute.id.toString(), "2", usd.code, "5", "100")
        val response = controller.listActiveDisputes(pageable, filters)
        assertEquals(HttpStatus.OK, response.statusCode)
        val retrievedDisputeCases = response.body.content
        assertTrue(retrievedDisputeCases.isEmpty())
    }
}