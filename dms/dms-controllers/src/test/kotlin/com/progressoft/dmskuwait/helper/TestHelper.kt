package com.progressoft.dmskuwait.helper

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.dms.entities.DMS_PaymentSystems
import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency
import com.progressoft.jfw.model.bussinessobject.workflow.WorkflowStatus
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import utils.FakeItemDao
import java.math.BigDecimal

open class TestHelper {
    protected val itemDao = FakeItemDao()

    protected fun getDisputeCaseFilters(
        itemId: String,
        caseRefNo: String,
        currency: String,
        fromAmount: String,
        toAmount: String
    ): MutableMap<String, String?> {
        val parameters = mutableMapOf<String, String?>()
        parameters["itemId"] = itemId
        parameters["caseReferenceNumber"] = caseRefNo
        parameters["currency"] = currency
        parameters["fromAmount"] = fromAmount
        parameters["toAmount"] = toAmount
        return parameters
    }

    protected fun createDisputeCase(
        id: Long,
        refNo: String,
        isDisputeOverDispute: <PERSON><PERSON><PERSON>,
        claimant: DMS_Participant,
        defendant: DMS_Participant,
        paymentSystems: DMS_PaymentSystems,
        currency: JFWCurrency,
        statusId: String,
        disputedAmount: BigDecimal
    ): DMS_DisputeCase {
        val disputeCase = DMS_DisputeCase()
        disputeCase.id = id
        disputeCase.caseReferenceNumber = refNo
        disputeCase.disputeOverDispute = isDisputeOverDispute
        disputeCase.claimantBank = claimant
        disputeCase.defendantBank = defendant
        disputeCase.paymentSystem = paymentSystems
        disputeCase.transactionCurrency = currency
        disputeCase.disputedAmount = disputedAmount
        disputeCase.transactionAmount = BigDecimal.valueOf(150)
        disputeCase.statusId = getWorkflowStatus(statusId)
        itemDao.add(disputeCase)
        return disputeCase
    }

    protected fun getWorkflowStatus(statusCode: String): WorkflowStatus {
        val workflowStatus = WorkflowStatus()
        workflowStatus.id = 1
        workflowStatus.code = statusCode
        workflowStatus.active = true
        itemDao.add(workflowStatus)
        return workflowStatus
    }

    protected fun createNewParticipant(id: Long, code: String): DMS_Participant {
        val participant = DMS_Participant()
        participant.id = id
        participant.name = code
        participant.fullName = code
        participant.code = code
        itemDao.add(participant)
        return participant
    }

    protected fun createPaymentSystem(id: Long, code: String): DMS_PaymentSystems {
        val paymentSystems = DMS_PaymentSystems()
        paymentSystems.id = id
        paymentSystems.name = code
        paymentSystems.code = code
        paymentSystems.description = code
        itemDao.add(paymentSystems)
        return paymentSystems
    }

    protected fun createNewCurrency(id: Long, code: String): JFWCurrency {
        val currency = JFWCurrency()
        currency.id = id
        currency.code = code
        currency.name = code
        currency.active = true
        currency.stringISOCode = code
        itemDao.add(currency)
        return currency

    }

    protected open class MockPageable(
        private val pageNumber: Int,
        private val pageSize: Int,
        sort: Sort?,
    ) : Pageable {
        override fun getPageNumber(): Int {
            return pageNumber
        }

        override fun getPageSize(): Int {
            return pageSize
        }

        override fun getOffset(): Long {
            return (pageNumber - 1) * pageSize.toLong()
        }

        override fun getSort(): Sort? {
            return null;
        }

        override fun next(): Pageable {
            return MockPageable(pageNumber + 1, pageSize, sort)
        }

        override fun previousOrFirst(): Pageable {
            return if (pageNumber > 1) MockPageable(pageNumber - 1, pageSize, sort) else this
        }

        override fun first(): Pageable {
            return MockPageable(1, pageSize, sort)
        }

        override fun withPage(pageNumber: Int): Pageable? {
            return null
        }

        override fun hasPrevious(): Boolean {
            return pageNumber > 1
        }
    }
}