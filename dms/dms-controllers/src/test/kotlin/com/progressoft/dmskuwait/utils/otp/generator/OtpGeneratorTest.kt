package com.progressoft.dmskuwait.utils.otp.generator


import com.progressoft.dms.entities.DMS_OTP
import com.progressoft.dmskuwait.utils.otp.generator.OtpGenerator
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import utils.FakeItemDao
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*
import kotlin.test.assertEquals

class OtpGeneratorTest {
    private lateinit var itemDao: FakeItemDao

    @BeforeEach
    fun setUp() {
        itemDao = FakeItemDao()
    }

    @Test
    fun givenSessionIdAndUserName_WhenGenerateOtp_ThenGeneratedOtpShouldBePersistedAndReturned() {
        val otpGenerator = OtpGenerator(itemDao, 5)
        val sessionId = UUID.randomUUID().toString()
        val userName = "DMSMAKER@DMS"
        val otp: DMS_OTP = otpGenerator.generateOtp(sessionId, userName)
        assertOtp(otp, sessionId, userName, 0)
        val first: Optional<DMS_OTP> = itemDao.persistedItems
            .stream()
            .filter { item -> item is DMS_OTP }
            .map { item -> item as DMS_OTP }
            .filter { item -> sessionId == item.sessionId }
            .filter { item -> userName == item.username }
            .findFirst()
        assertTrue(first.isPresent)
        assertOtp(first.get(), sessionId, userName, 0)
    }

    @Test
    fun givenSessionIdAndUserName_WhenGenerateAndUpdateOtp_ThenShouldUpdateGeneratedOtpEntity() {
        val otpGenerator = OtpGenerator(itemDao, 2)
        val sessionId = UUID.randomUUID().toString()
        val userName = "DMSMAKER@DMS"
        val otp: DMS_OTP = createOtp(sessionId, userName)
        itemDao.add(otp)
        val newOtp: DMS_OTP = otpGenerator.generateAndUpdateOtp(otp)
        assertOtp(newOtp, sessionId, userName, 1)
        val first: Optional<DMS_OTP> = itemDao.getItems(DMS_OTP::class.java)
            .stream()
            .filter { item -> sessionId == item.sessionId }
            .filter { item -> userName == item.username }
            .findFirst()
        assertTrue(first.isPresent())
        assertOtp(first.get(), sessionId, userName, 1)
    }

    private fun assertOtp(otp: DMS_OTP, sessionId: String, userName: String, resendAttempts: Int) {
        Assertions.assertNotNull(otp)
        assertEquals(sessionId, otp.sessionId)
        assertEquals(userName, otp.username)
        assertEquals(resendAttempts, otp.reSendAttempts)
        assertEquals(0, otp.validateAttempts)
        assertTrue(otp.expiryTime.after(Timestamp.valueOf(LocalDateTime.now())))
    }

    private fun createOtp(sessionId: String, userName: String): DMS_OTP {
        val entity = DMS_OTP()
        entity.sessionId = sessionId
        entity.username = userName
        entity.otp = "OTP123"
        entity.reSendAttempts = 0
        entity.validateAttempts = 0
        entity.expiryTime = Timestamp.valueOf(LocalDateTime.now().plusMinutes(5))
        itemDao.persist(entity)
        return entity
    }
}
