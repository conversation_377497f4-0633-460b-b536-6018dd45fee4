package com.progressoft.dmskuwait.utils


import com.progressoft.jfw.notifications.entity.JFWGenericConfiguration
import com.progressoft.jupiter.security.rsa.RSA
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import utils.FakeItemDao
import java.math.BigInteger
import java.security.PrivateKey
import java.security.interfaces.RSAPrivateKey

class RSADecryptorTest {

    @Test
    fun givenEncryptedValue_whenCallingDecrypt_thenTheValueShouldBeDecrypted() {
        val itemDao = FakeItemDao()
        addRequiredConfig(itemDao)
        val rsaDecryptor = RSADecryptor(MockRsa(), itemDao)
        val decrypted: String = rsaDecryptor.decrypt("1212TEST")
        Assertions.assertEquals("SATANAI", decrypted)
    }

    private fun addRequiredConfig(itemDao: FakeItemDao) {
        val configuration = JFWGenericConfiguration()
        configuration.configKey = "private.key"
        configuration.configValue = "123a"
        itemDao.add(configuration)
    }

    private class MockRsa : RSA {
        override fun decrypt(bytes: ByteArray, privateKey: PrivateKey): String {
            return "SATANAI"
        }

        override fun readPrivateKey(s: String): RSAPrivateKey {
            return MockRSAPrivateKey()
        }
    }


    private class MockRSAPrivateKey : RSAPrivateKey {
        override fun getAlgorithm(): String {
            TODO("Not yet implemented")
        }

        override fun getFormat(): String {
            TODO("Not yet implemented")
        }

        override fun getEncoded(): ByteArray {
            TODO("Not yet implemented")
        }

        override fun getModulus(): BigInteger {
            TODO("Not yet implemented")
        }

        override fun getPrivateExponent(): BigInteger {
            TODO("Not yet implemented")
        }

    }
}