package com.progressoft.dmskuwait.services

import com.progressoft.dms.entities.DMS_TermsAndConditions
import com.progressoft.dmskuwait.services.TermsAndConditionService
import com.progressoft.jfw.model.bussinessobject.security.User
import org.junit.Before
import org.junit.Test
import utils.FakeItemDao
import kotlin.test.assertEquals

class TermsAndConditionServiceTest {

    private lateinit var itemDao: FakeItemDao
    private lateinit var service: TermsAndConditionService

    @Before
    fun setUp() {
        itemDao = FakeItemDao()
        service = TermsAndConditionService(itemDao)
    }

    @Test
    fun givenValidRequest_whenCallGetTermsAndConditions_thenShouldReturnTermsAndConditions() {
        val terms = getAtsTermsAndConditions()
        itemDao.add(terms)
        val termsAndConditions = service.termsAndConditions
        assertEquals(terms.termsAndConditions, termsAndConditions)
    }

    @Test
    fun givenValidRequest_whenCallAcceptTermsAndConditions_thenShouldUpdateTermsAcceptedToTrue() {
        val user = getUser()
        itemDao.add(user)
        service.acceptTermsAndConditions(user.username)
        assertEquals(true, user.termsAccepted)
    }

    @Test
    fun givenValidRequest_whenCallCheckTermsAndConditions_thenShouldReturnTermsAccepted() {
        val user = getUser()
        itemDao.add(user)
        val termsAccepted = service.checkTermsAndConditions(user.username)
        assertEquals(user.termsAccepted, termsAccepted)
    }

    private fun getAtsTermsAndConditions() = DMS_TermsAndConditions().apply {
        termsAndConditions = "This is test"
    }

    private fun getUser() = User().apply {
        id = 1L
        termsAccepted = false
    }
}
