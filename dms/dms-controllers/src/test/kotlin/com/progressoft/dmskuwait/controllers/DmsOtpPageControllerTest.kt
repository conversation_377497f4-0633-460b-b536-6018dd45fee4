package com.progressoft.dmskuwait.controllers


import com.progressoft.dmskuwait.dto.UIOTPPageResponse
import com.progressoft.dmskuwait.services.DmsOtpPageService
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class DmsOtpPageControllerTest {
    @Test
    fun givenLanguageCode_whenGetLoginPage_thenReturnExpectedResponse() {
        val service = MockEbppOtpPageService()
        val ebppOtpPageController = DmsOtpPageController(service)
        val response: UIOTPPageResponse = ebppOtpPageController.getLoginPage("en")
        Assertions.assertTrue(service.isCalled)
        assertEquals(OTP_FORM_TITLE, response.otpFormTitle)
        assertEquals(OTP_RESEND_TIMER_MESSAGE, response.resendTimerMessage)
        assertEquals(OTP_RESEND_TIMER_MESSAGE_SECONDS, response.resendTimerMessageSeconds)
        assertEquals(OTP_RESEND_BUTTON, response.resendButton)
        assertEquals(OTP_SUBMIT_BUTTON, response.submitButton)
        assertEquals(OTP_REQUIRED_MESSAGE, response.otpRequiredMessage)
    }

    private class MockEbppOtpPageService : DmsOtpPageService(null) {
        var isCalled: Boolean = false
            private set

        override fun getUIOTPPageResponse(languageCode: String?): UIOTPPageResponse {
            isCalled = true
            val response = UIOTPPageResponse()
            response.otpFormTitle = OTP_FORM_TITLE
            response.resendTimerMessage = OTP_RESEND_TIMER_MESSAGE
            response.resendTimerMessageSeconds = OTP_RESEND_TIMER_MESSAGE_SECONDS
            response.resendButton = OTP_RESEND_BUTTON
            response.submitButton = OTP_SUBMIT_BUTTON
            response.otpRequiredMessage = OTP_REQUIRED_MESSAGE
            return response
        }
    }

    companion object {
        private const val OTP_FORM_TITLE = "otp.form.title"
        private const val OTP_RESEND_TIMER_MESSAGE = "otp.resend.timer.message"
        private const val OTP_RESEND_TIMER_MESSAGE_SECONDS = "otp.resend.timer.message.seconds"
        private const val OTP_RESEND_BUTTON = "otp.resend.button"
        private const val OTP_SUBMIT_BUTTON = "otp.submit.button"
        private const val OTP_REQUIRED_MESSAGE = "otp.required.message"
    }
}
