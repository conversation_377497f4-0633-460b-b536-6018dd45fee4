//package com.progressoft.dms.services
//
//
//import com.progressoft.dms.entities.DMS_OTP
//import com.progressoft.dms.mock.MockOtpEmailSender
//import com.progressoft.dmskuwait.dto.DmsAuthenticationResponse
//import com.progressoft.dmskuwait.services.DmsLoginService
//import com.progressoft.dmskuwait.utils.RSADecryptor
//import com.progressoft.dmskuwait.utils.otp.generator.OtpGenerator
//import com.progressoft.jfw.labels.Labels
//import com.progressoft.jfw.model.bussinessobject.security.Tenant
//import com.progressoft.jfw.model.bussinessobject.security.User
//import com.progressoft.jfw.model.bussinessobject.workflow.WorkflowStatus
//import com.progressoft.jfw.model.dao.security.SecurityDao
//import com.progressoft.jupiter.authentication.strategy.api.AuthenticationStatus
//import com.progressoft.jupiter.authentication.strategy.api.AuthenticationStatus.FAILD
//import com.progressoft.jupiter.authentication.strategy.api.JupiterAuthenticator
//import com.progressoft.jupiter.authentication.strategy.api.request.AuthenticationRequest
//import com.progressoft.jupiter.authentication.strategy.api.response.AuthenticationResponse
//import com.progressoft.jupiter.authentication.strategy.api.response.AuthenticationResponseImpl
//import com.progressoft.jupiter.authentication.strategy.provider.DefaultAuthenticationStrategyProvider
//import org.junit.jupiter.api.Assertions.*
//import org.junit.jupiter.api.BeforeEach
//import org.junit.jupiter.api.Test
//import org.mockito.ArgumentMatchers
//import org.mockito.MockedStatic
//import org.mockito.Mockito
//import org.mockito.Mockito.mock
//import org.mockito.Mockito.`when`
//import utils.FakeItemDao
//import java.util.*
//
//internal class DmsLoginServiceTest {
//    private var itemDao: FakeItemDao = FakeItemDao()
//    private var otpEmailSender = MockOtpEmailSender()
//
//    @BeforeEach
//    @Throws(NoSuchFieldException::class, ClassNotFoundException::class, IllegalAccessException::class)
//    fun setUp() {
//        itemDao.filterItems()
//        addUsers(USER_NAME, 1)
//    }
//
//    @Test
//    fun givenValidAuthRequest_whenAuthenticate_thenReturnsSuccessResponseWithProperMessage() {
//        val sessionId = UUID.randomUUID().toString()
//        val request = com.progressoft.jupiter.rest.security.authenticate.request.AuthenticationRequest()
//        request.username = USER_NAME
//        request.password = "a"
//        request.tenant = TENANT
//        request.language = "en"
//        val otpGenerator = MockOtpGenerator(true)
//        val rsaDecryptor = mock(
//            RSADecryptor::class.java
//        )
//        `when`(rsaDecryptor.decrypt(sessionId)).thenReturn(sessionId)
//        mockLabelsClass().use {
//            val dmsLoginService = DmsLoginService(
//                mockSecurityDao(22L), mockStrategyProvider(true),
//                rsaDecryptor,
//                otpGenerator, otpEmailSender
//            )
//            val authenticate: DmsAuthenticationResponse =
//                dmsLoginService.authenticate(request, REMOTE_ADDR, sessionId)
//            assertTrue(otpGenerator.isCalled)
//            assertEquals(AuthenticationStatus.SUCCESS.name, authenticate.status)
//            assertEquals(SUCCESS_MSG, authenticate.message)
//            assertTrue(otpEmailSender.isCalled)
//        }
//    }
//
//    @Test
//    fun givenAuthRequestAndInactiveTenant_whenAuthenticate_thenReturnsFailureResponseWithProperMessage() {
//        val sessionId = UUID.randomUUID().toString()
//        val request = com.progressoft.jupiter.rest.security.authenticate.request.AuthenticationRequest()
//        request.username = USER_NAME
//        request.password = "a"
//        request.tenant = TENANT
//        request.language = "en"
//        val otpGenerator = MockOtpGenerator(true)
//        val rsaDecryptor = mock(
//            RSADecryptor::class.java
//        )
//        mockLabelsClass().use {
//            val dmsLoginService = DmsLoginService(
//                mockSecurityDao(21L),
//                mock(
//                    DefaultAuthenticationStrategyProvider::class.java
//                ),
//                rsaDecryptor, otpGenerator, otpEmailSender
//            )
//            val authenticate: DmsAuthenticationResponse =
//                dmsLoginService.authenticate(request, REMOTE_ADDR, sessionId)
//            assertEquals(FAILD.name, authenticate.status)
//            assertEquals(INACTIVE_TENANT, authenticate.message)
//            assertFalse(otpGenerator.isCalled)
//            assertFalse(otpEmailSender.isCalled)
//        }
//    }
//
//    @Test
//    fun givenAuthRequestWithInvalidUser_whenAuthenticate_thenReturnsFailureResponseWithProperMessage() {
//        val sessionId = UUID.randomUUID().toString()
//        val request = com.progressoft.jupiter.rest.security.authenticate.request.AuthenticationRequest()
//        request.username = "INVALID"
//        request.password = "a"
//        request.tenant = TENANT
//        request.language = "en"
//        val otpGenerator = MockOtpGenerator(true)
//        val rsaDecryptor = mock(
//            RSADecryptor::class.java
//        )
//        `when`(rsaDecryptor.decrypt(sessionId)).thenReturn(sessionId)
//
//        mockLabelsClass().use {
//            val dmsLoginService = DmsLoginService(
//                mockSecurityDao(22L), mockStrategyProvider(false),
//                rsaDecryptor,
//                otpGenerator, otpEmailSender
//            )
//            val authenticate: DmsAuthenticationResponse =
//                dmsLoginService.authenticate(request, REMOTE_ADDR, sessionId)
//            assertEquals(FAILD.name, authenticate.status)
//            assertEquals("Disabled User", authenticate.message)
//            assertFalse(otpGenerator.isCalled)
//            assertFalse(otpEmailSender.isCalled)
//        }
//    }
//
//    @Test
//    fun givenAuthRequest_whenAuthenticateAndOtpGeneratorThrowsException_thenReturnsFailureResponseWithProperMessage() {
//        val sessionId = UUID.randomUUID().toString()
//        val request = com.progressoft.jupiter.rest.security.authenticate.request.AuthenticationRequest()
//        request.username = USER_NAME
//        request.password = "a"
//        request.tenant = "INVALID"
//        request.language = "en"
//        val otpGenerator = MockOtpGenerator(false)
//        val rsaDecryptor = mock(
//            RSADecryptor::class.java
//        )
//        `when`(rsaDecryptor.decrypt(sessionId)).thenReturn(sessionId)
//
//        mockLabelsClass().use {
//            val dmsLoginService = DmsLoginService(
//                mockSecurityDao(22L), mockStrategyProvider(true),
//                rsaDecryptor,
//                otpGenerator, otpEmailSender
//            )
//            val authenticate: DmsAuthenticationResponse =
//                dmsLoginService.authenticate(request, REMOTE_ADDR, sessionId)
//            assertEquals(FAILD.name, authenticate.status)
//            assertEquals(INTERNAL_SYSTEM_ERROR, authenticate.message)
//            assertFalse(otpGenerator.isCalled)
//            assertFalse(otpEmailSender.isCalled)
//        }
//    }
//
//    private fun mockStrategyProvider(isSuccess: Boolean): DefaultAuthenticationStrategyProvider {
//        val strategyProvider = mock(
//            DefaultAuthenticationStrategyProvider::class.java
//        )
//        `when`(strategyProvider.strategy()).thenReturn(MockJupiterAuthenticator(isSuccess))
//        return strategyProvider
//    }
//
//    private fun mockSecurityDao(id: Long): SecurityDao {
//        val securityDao = mock(SecurityDao::class.java)
//        val tenant = Tenant()
//        tenant.name = TENANT
//        val statusId = WorkflowStatus()
//        statusId.id = id
//        tenant.statusId = statusId
//        `when`(securityDao.getTenant(ArgumentMatchers.anyString())).thenReturn(tenant)
//        return securityDao
//    }
//
//    @Throws(ClassNotFoundException::class, NoSuchFieldException::class, IllegalAccessException::class)
//    private fun addUsers(username: String, id: Long) {
//        val user = mock(
//            User::class.java
//        )
//        val mockClass = Class.forName("com.progressoft.jfw.model.bussinessobject.security.AbstractUser")
//        val mockUserClass = Class.forName("com.progressoft.jfw.model.bussinessobject.security.User")
//        val mockAbstractJfwEntity = Class.forName("com.progressoft.jfw.model.bussinessobject.core.AbstractJFWEntity")
//        val idfield = mockUserClass.getDeclaredField("id")
//        val usernameField = mockClass.getDeclaredField("username")
//        val passwordField = mockClass.getDeclaredField("password")
//        val tenantIdField = mockAbstractJfwEntity.getDeclaredField("tenantId")
//        idfield.isAccessible = true
//        idfield[user] = id
//        usernameField.isAccessible = true
//        usernameField[user] = username
//        passwordField.isAccessible = true
//        passwordField[user] = "a"
//        tenantIdField.isAccessible = true
//        tenantIdField[user] = TENANT
//        user.id = id
//        `when`(user.displayName).thenReturn(username)
//        `when`(user.username).thenReturn(username)
//        `when`(user.id).thenReturn(id)
//        itemDao.add(user)
//    }
//
//    private fun mockLabelsClass(): MockedStatic<Labels> {
//        val labelsMockedStatic = Mockito.mockStatic(
//            Labels::class.java
//        )
//        labelsMockedStatic.`when`<Any> { Labels.description("login.inactive.tenant") }
//            .thenReturn(INACTIVE_TENANT)
//        labelsMockedStatic.`when`<Any> { Labels.description("otp.internal.system.error") }
//            .thenReturn(INTERNAL_SYSTEM_ERROR)
//        return labelsMockedStatic
//    }
//
//    private class MockJupiterAuthenticator(private val isSuccess: Boolean) : JupiterAuthenticator {
//        override fun authenticate(authenticationRequest: AuthenticationRequest): AuthenticationResponse {
//            val response = AuthenticationResponseImpl()
//            response.setStatus(if (isSuccess) AuthenticationStatus.SUCCESS else FAILD)
//            response.setMessage(if (isSuccess) "Success" else "Disabled User")
//            return response
//        }
//
//        override fun key(): String {
//            return "MOCK"
//        }
//    }
//
//    private class MockOtpGenerator(private val isSuccess: Boolean) : OtpGenerator(FakeItemDao(), 5) {
//        var isCalled: Boolean = false
//
//        override fun generateOtp(sessionId: String, userName: String): DMS_OTP {
//            if (isSuccess) {
//                isCalled = true
//                return DMS_OTP()
//            } else throw RuntimeException("Error While Generate OTP")
//        }
//    }
//
//    companion object {
//        private const val USER_NAME = "user123"
//        private const val REMOTE_ADDR = "127.0.0.1"
//        private const val TENANT = "tenant"
//        private const val INACTIVE_TENANT = "Inactive Tenant"
//        private const val SUCCESS_MSG = "Success"
//        private const val INTERNAL_SYSTEM_ERROR = "INTERNAL SYSTEM ERROR"
//
//    }
//}
