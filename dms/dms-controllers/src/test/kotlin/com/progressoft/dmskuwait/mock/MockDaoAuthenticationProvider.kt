package com.progressoft.dmskuwait.mock

import com.progressoft.jfw.model.bussinessobject.security.*
import com.progressoft.jfw.model.service.security.JfwAuthentication
import org.springframework.security.authentication.BadCredentialsException
import org.springframework.security.authentication.DisabledException
import org.springframework.security.authentication.dao.DaoAuthenticationProvider
import org.springframework.security.core.Authentication
import org.springframework.security.core.AuthenticationException
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.web.authentication.session.SessionAuthenticationException


class MockDaoAuthenticationProvider : DaoAuthenticationProvider {
    private val throwsException: Boolean
    private val returnsUserDetails: Boolean
    private val isMaxConcurrentSessions: Boolean
    private val isDisabledUser: Boolean
    private val isBadCredentials: Boolean
    private var user: User? = null

    constructor(throwsException: Boolean) {
        this.throwsException = throwsException
        this.isMaxConcurrentSessions = false
        this.returnsUserDetails = false
        this.isDisabledUser = false
        this.isBadCredentials = false
    }

    @Throws(AuthenticationException::class)
    override fun authenticate(authentication: Authentication): Authentication {
        if (!throwsException) {
            if (returnsUserDetails) return object : Authentication {
                override fun getAuthorities(): Collection<GrantedAuthority> {
                    return ArrayList()
                }

                override fun getCredentials(): Any? {
                    return null
                }

                override fun getDetails(): Any? {
                    return null
                }

                override fun getPrincipal(): Any {
                    val accountPolicy = AccountPolicy()
                    accountPolicy.maxUnsuccessfulAttempts = 100
                    return DefaultExtendedUserDetails(
                        user, Tenant(), Org(), Org(), accountPolicy,
                        this.authorities, ArrayList(), ArrayList()
                    )
                }

                override fun isAuthenticated(): Boolean {
                    return true
                }

                @Throws(IllegalArgumentException::class)
                override fun setAuthenticated(b: Boolean) {
                }

                override fun getName(): String? {
                    return null
                }
            }
            else return JfwAuthentication()
        } else if (isDisabledUser) {
            throw DisabledException("User is disabled")
        } else if (isMaxConcurrentSessions) {
            throw SessionAuthenticationException("User is already logged in")
        } else if (isBadCredentials) {
            throw BadCredentialsException("Wrong Password")
        } else {
            throw RuntimeException("An Error Occurred")
        }
    }
}
