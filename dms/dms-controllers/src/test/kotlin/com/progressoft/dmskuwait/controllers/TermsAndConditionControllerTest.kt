package com.progressoft.dmskuwait.controllers

import com.progressoft.dms.entities.DMS_TermsAndConditions
import com.progressoft.dmskuwait.dto.TermsAndConditionsResponse
import com.progressoft.dmskuwait.services.TermsAndConditionService
import jakarta.servlet.ServletException
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.Part
import org.junit.Assert.assertEquals
import org.junit.Before
import org.junit.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.`when`
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import utils.FakeItemDao
import java.io.ByteArrayInputStream
import java.io.IOException
import java.util.*

class TermsAndConditionControllerTest {
    private var controller: TermsAndConditionController? = null
    private lateinit var itemDao: FakeItemDao
    private var service: TermsAndConditionService = mock(TermsAndConditionService::class.java)

    @Before
    fun setUp() {
        itemDao = FakeItemDao()
        val service = TermsAndConditionService(itemDao)
        controller = TermsAndConditionController(service)
    }

    @Test
    fun givenTermAndConditionsExist_whenCallGetTermsAndConditionsApi_thenShouldReturn200Response() {
        val terms = getAtsTermsAndConditions()
        itemDao = FakeItemDao()
        val service = TermsAndConditionService(itemDao)
        controller = TermsAndConditionController(service)
        itemDao.add(terms)

        val termsAndConditions = TermsAndConditionsResponse(service.termsAndConditions, null)

        val response: ResponseEntity<TermsAndConditionsResponse> = controller!!.termsAndConditions
        assertEquals(HttpStatus.OK, response.statusCode)
        assertEquals(termsAndConditions.content, Objects.requireNonNull(response.body).content)
    }

    @Test
    fun givenTermAndConditionsDoesNotExist_whenCallGetTermsAndConditionsApi_thenShouldThrowException() {
        val response: ResponseEntity<TermsAndConditionsResponse> = controller!!.termsAndConditions
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.statusCode)
    }

    @Test
    @Throws(ServletException::class, IOException::class)
    fun givenValidRequest_whenCallAcceptTermsAndConditionsApi_thenShouldReturn200Response() {
        val mockRequest = httpServletRequest
        val controller = TermsAndConditionController(service)
        val response: ResponseEntity<TermsAndConditionsResponse> = controller.acceptTermsAndConditions(mockRequest)
        assertEquals(HttpStatus.OK, response.statusCode)
    }

    @Test
    fun givenInvalidRequest_whenCallAcceptTermsAndConditionsApi_thenShouldReturn500Response() {
        val mockRequest = mock(HttpServletRequest::class.java)
        `when`(mockRequest.getPart("username")).thenThrow(ServletException("Missing part"))
        val response: ResponseEntity<TermsAndConditionsResponse> = controller!!.acceptTermsAndConditions(mockRequest)
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.statusCode)
    }

    @Test
    fun givenValidRequest_whenCallCheckTermsAndConditionsApi_thenShouldReturn200Response() {
        val mockRequest = httpServletRequest
        val controller = TermsAndConditionController(service)
        val response: ResponseEntity<TermsAndConditionsResponse> = controller.checkTermsAndConditions(mockRequest)
        assertEquals(HttpStatus.OK, response.statusCode)
    }

    @Test
    @Throws(ServletException::class, IOException::class)
    fun givenInvalidRequest_whenCallCheckTermsAndConditionsApi_thenShouldThrowException() {
        val mockRequest = mock(HttpServletRequest::class.java)
        `when`(mockRequest.getPart("username")).thenThrow(ServletException("Missing part"))
        val controller = TermsAndConditionController(service)
        val response: ResponseEntity<TermsAndConditionsResponse> = controller.checkTermsAndConditions(mockRequest)
        assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.statusCode)
    }

    private fun getAtsTermsAndConditions() = DMS_TermsAndConditions().apply{
        termsAndConditions = "This is test"
    }

    companion object {
        @get:Throws(IOException::class, ServletException::class)
        private val httpServletRequest: HttpServletRequest
            get() {
                val username = "testUser"
                val mockRequest = mock(HttpServletRequest::class.java)
                val mockPart = mock(Part::class.java)

                `when`(mockRequest.getPart("username")).thenReturn(mockPart)
                `when`(mockPart.inputStream).thenReturn(ByteArrayInputStream(username.toByteArray()))
                return mockRequest
            }
    }
}