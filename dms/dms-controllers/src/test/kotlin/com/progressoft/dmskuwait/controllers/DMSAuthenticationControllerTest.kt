//package com.progressoft.dms.controllers
//
//import com.progressoft.dms.mock.MockOtpEmailSender
//import com.progressoft.dmskuwait.controllers.DMSAuthenticationController
//import com.progressoft.dmskuwait.dto.*
//import com.progressoft.dmskuwait.services.DmsLoginService
//import com.progressoft.dmskuwait.services.ResendOtpService
//import com.progressoft.dmskuwait.services.ValidateOtpService
//import com.progressoft.jupiter.rest.security.authenticate.request.AuthenticationRequest
//import jakarta.servlet.ServletException
//import jakarta.servlet.http.HttpServletRequest
//import jakarta.servlet.http.Part
//import org.junit.jupiter.api.Assertions
//import org.junit.jupiter.api.Test
//import org.mockito.Mockito
//import org.springframework.http.HttpStatus
//import org.springframework.http.ResponseEntity
//import utils.FakeItemDao
//import java.io.ByteArrayInputStream
//import java.io.IOException
//import java.io.InputStream
//import java.util.*
//import kotlin.test.assertEquals
//import kotlin.test.assertFalse
//import kotlin.test.assertTrue
//
//class DMSAuthenticationControllerTest {
//    private var otpEmailSender = MockOtpEmailSender()
//
//    @Test
//    @Throws(ServletException::class, IOException::class)
//    fun givenValidHttpRequest_whenCallingValidateOtp_ThenShouldReturnExpectedResponse() {
//        val validateOtpService = MockValidateOtpService()
//        val expectedResponse: ValidateOtpResponse = prepareMockValidateOtpRs()
//        val controller = DMSAuthenticationController(
//            validateOtpService,
//            MockDmsLoginService(otpEmailSender),
//            MockResendOtpService(otpEmailSender),
//            FakeItemDao()
//        )
//        val request: HttpServletRequest = Mockito.spy(HttpServletRequest::class.java)
//        Mockito.`when`(request.getPart("sessionId"))
//            .thenReturn(getPart("sessionId", UUID.randomUUID().toString()))
//        Mockito.`when`(request.getPart("username")).thenReturn(getPart("username", "DVID"))
//        Mockito.`when`(request.getPart("otp")).thenReturn(getPart("otp", "123456"))
//        Mockito.`when`(request.getPart("password")).thenReturn(getPart("password", "123456789"))
//        Mockito.`when`(request.getPart("tenant")).thenReturn(getPart("tenant", "EBPP"))
//        Mockito.`when`(request.getPart("language")).thenReturn(getPart("language", "ar"))
//        Mockito.`when`(request.remoteAddr).thenReturn("DMS")
//        val responseEntity: ResponseEntity<ValidateOtpResponse> = controller.validateOtp(request)
//        Assertions.assertTrue(validateOtpService.isCalled)
//        val body: ValidateOtpResponse = responseEntity.body
//        Assertions.assertNotNull(body)
//        Assertions.assertEquals(HttpStatus.OK, responseEntity.statusCode)
//        assertEquals(expectedResponse.jwtExp, body.jwtExp)
//        assertEquals(expectedResponse.jwt, body.jwt)
//        assertEquals(expectedResponse.refreshTokenExp, body.refreshTokenExp)
//        assertEquals(expectedResponse.refreshToken, body.refreshToken)
//        assertEquals(expectedResponse.username, body.username)
//        assertEquals(expectedResponse.message, body.message)
//        assertFalse(otpEmailSender.isCalled)
//    }
//
//    @Test
//    @Throws(ServletException::class, IOException::class)
//    fun givenValidHttpRequest_whenCallingAuthenticate_ThenShouldReturnExpectedResponse() {
//        val ebppLoginService = MockDmsLoginService(otpEmailSender)
//        val controller = DMSAuthenticationController(
//            MockValidateOtpService(),
//            ebppLoginService,
//            MockResendOtpService(otpEmailSender),
//            FakeItemDao()
//        )
//        val request: HttpServletRequest = Mockito.spy(HttpServletRequest::class.java)
//        Mockito.`when`(request.getPart("sessionId"))
//            .thenReturn(getPart("sessionId", UUID.randomUUID().toString()))
//        Mockito.`when`(request.getPart("username")).thenReturn(getPart("username", "DVID"))
//        Mockito.`when`(request.getPart("password")).thenReturn(getPart("password", "123456789"))
//        Mockito.`when`(request.getPart("tenant")).thenReturn(getPart("tenant", "EBPP"))
//        Mockito.`when`(request.getPart("language")).thenReturn(getPart("language", "ar"))
//        Mockito.`when`(request.remoteAddr).thenReturn("DMS")
//        val authenticate: DmsAuthenticationResponse = controller.authenticate(request)
//        Assertions.assertTrue(ebppLoginService.isCalled)
//        assertEquals(STATUS, authenticate.status)
//        assertEquals(MESSAGE, authenticate.message)
//        assertTrue(otpEmailSender.isCalled)
//    }
//
//    @Test
//    @Throws(ServletException::class, IOException::class)
//    fun givenValidHttpRequest_whenCallingResendOtp_ThenShouldReturnExpectedResponse() {
//        val mockResendOtpService = MockResendOtpService(otpEmailSender)
//        val expectedResponse: ResendOtpResponse = prepareMockResendOtpRs()
//        val controller =
//            DMSAuthenticationController(
//                MockValidateOtpService(),
//                MockDmsLoginService(otpEmailSender),
//                mockResendOtpService,
//                FakeItemDao()
//            )
//        val request: HttpServletRequest = Mockito.spy(HttpServletRequest::class.java)
//        Mockito.`when`(request.getPart("sessionId"))
//            .thenReturn(getPart("sessionId", UUID.randomUUID().toString()))
//        Mockito.`when`(request.getPart("username")).thenReturn(getPart("username", "DVID"))
//        val responseEntity: ResponseEntity<ResendOtpResponse> = controller.resendOtp(request)
//        Assertions.assertTrue(mockResendOtpService.isCalled)
//        val body: ResendOtpResponse = responseEntity.body
//        Assertions.assertNotNull(body)
//        Assertions.assertEquals(responseEntity.getStatusCode(), HttpStatus.OK)
//        assertEquals(expectedResponse.message, body.message)
//        assertEquals(expectedResponse.status, body.status)
//    }
//
//    private class MockDmsLoginService(val emailUtil: MockOtpEmailSender) :
//        DmsLoginService(null, null, null, null, emailUtil) {
//        var isCalled: Boolean = false
//
//        override fun authenticate(
//            request: AuthenticationRequest,
//            remoteAddr: String,
//            sessionId: String
//        ): DmsAuthenticationResponse {
//            emailUtil.send("", "")
//            isCalled = true
//            val response = DmsAuthenticationResponse()
//            response.status = STATUS
//            response.message = MESSAGE
//            return response
//        }
//
//    }
//
//    private class MockValidateOtpService : ValidateOtpService(null, null, null, null, null, 1) {
//        var isCalled: Boolean = false
//        override fun validateOtp(request: ValidateOtpRequest, remoteAddr: String): ResponseEntity<ValidateOtpResponse> {
//            isCalled = true
//            return ResponseEntity<ValidateOtpResponse>(prepareMockValidateOtpRs(), HttpStatus.OK)
//        }
//    }
//
//    private class MockResendOtpService(mockOtpEmailSender: MockOtpEmailSender) :
//        ResendOtpService(null, null, null, null, mockOtpEmailSender) {
//        var isCalled: Boolean = false
//        override fun resendOtp(request: ResendOtpRequest): ResponseEntity<ResendOtpResponse> {
//            isCalled = true
//            return ResponseEntity<ResendOtpResponse>(prepareMockResendOtpRs(), HttpStatus.OK)
//        }
//
//    }
//
//    private fun getPart(name: String, value: String): Part {
//        return object : Part {
//            override fun getInputStream(): InputStream {
//                val bytes = value.toByteArray()
//                return ByteArrayInputStream(bytes, 1, bytes.size)
//            }
//
//            override fun getContentType(): String? {
//                return null
//            }
//
//            override fun getName(): String {
//                return name
//            }
//
//            override fun getSubmittedFileName(): String? {
//                return null
//            }
//
//            override fun getSize(): Long {
//                return 0
//            }
//
//            override fun write(s: String) {
//            }
//
//            override fun delete() {
//            }
//
//            override fun getHeader(s: String): String? {
//                return null
//            }
//
//            override fun getHeaders(s: String): Collection<String>? {
//                return null
//            }
//
//            override fun getHeaderNames(): Collection<String>? {
//                return null
//            }
//        }
//    }
//
//    companion object {
//        private const val STATUS = "SUCCESS"
//        private const val MESSAGE = "SUCCESS"
//
//        private fun prepareMockValidateOtpRs(): ValidateOtpResponse {
//            val response = ValidateOtpResponse()
//            response.message = MESSAGE
//            response.jwt = "JWT"
//            response.username = "DVID"
//            response.jwtExp = 1L
//            response.status = STATUS
//            response.refreshToken = "TOKEN"
//            response.refreshTokenExp = 2L
//            return response
//        }
//
//        private fun prepareMockResendOtpRs(): ResendOtpResponse {
//            val response = ResendOtpResponse()
//            response.message = MESSAGE
//            response.status = STATUS
//            return response
//        }
//    }
//}