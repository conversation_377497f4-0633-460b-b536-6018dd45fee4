package com.progressoft.dmskuwait.services

import com.progressoft.dms.entities.DMS_OTP
import com.progressoft.dmskuwait.mock.MockDaoAuthenticationProvider
import com.progressoft.dmskuwait.mock.MockRSADecryptor
import com.progressoft.dmskuwait.mock.MockSecretsLoader
import com.progressoft.dmskuwait.dto.ValidateOtpRequest
import com.progressoft.dmskuwait.dto.ValidateOtpResponse
import com.progressoft.dmskuwait.services.ValidateOtpService
import com.progressoft.jfw.labels.Labels
import com.progressoft.jfw.model.bussinessobject.security.ExtendedUserDetails
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.model.service.security.JfwAuthentication
import com.progressoft.jupiter.authentication.strategy.api.AuthenticationStatus
import com.progressoft.jupiter.authentication.strategy.api.JwtHelper
import com.progressoft.jupiter.authentication.strategy.api.complete.CompleteAuthentication
import com.progressoft.jupiter.authentication.strategy.api.request.AuthenticationRequest
import com.progressoft.jupiter.authentication.strategy.api.response.AuthenticationResponse
import com.progressoft.jupiter.authentication.strategy.api.response.AuthenticationResponseImpl
import io.jsonwebtoken.impl.DefaultClaims
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.ArgumentMatchers
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import utils.FakeItemDao
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

internal class ValidateOtpServiceTest {
    private lateinit var itemDao: FakeItemDao

    @BeforeEach
    @Throws(NoSuchFieldException::class, ClassNotFoundException::class, IllegalAccessException::class)
    fun setUp() {
        itemDao = FakeItemDao()
        itemDao.filterItems()
        addUsers(USER_NAME, 1)
    }

    @Test
    fun givenValidOtpRequestAndMockDaoAuthenticationProviderThrowException_whenValidateOtp_thenShouldReturnFailedWithInternalSystemError() {
        val sessionId = UUID.randomUUID().toString()
        val request: ValidateOtpRequest = buildRequest(sessionId, USER_NAME, OTP)
        val existingOtp: DMS_OTP =
            createOtpEntity(sessionId, USER_NAME, OTP, Timestamp.valueOf(LocalDateTime.now().plusMinutes(5)), 0)
        itemDao.add(existingOtp)
        Mockito.mockStatic(JwtHelper::class.java).use { jwtHelper ->
            mockLabelsClass().use {
                val claims: MutableMap<String, Any> = HashMap()
                claims["exp"] = Date()
                jwtHelper.`when`<Any> {
                    JwtHelper.getJwtClaims(
                        ArgumentMatchers.anyString(),
                        ArgumentMatchers.any(),
                        ArgumentMatchers.anyString()
                    )
                }.thenReturn(DefaultClaims(claims))
                val mockOtpService = ValidateOtpService(
                    itemDao,
                    MockCompleteAuthentication(true),
                    MockDaoAuthenticationProvider(true),
                    MockRSADecryptor(),
                    MockSecretsLoader(),
                    2
                )
                val response: ResponseEntity<ValidateOtpResponse> = mockOtpService.validateOtp(request, REMOTE_ADDR)
                assertEquals(HttpStatus.INTERNAL_SERVER_ERROR, response.statusCode)
                Assertions.assertNotNull(response.body)
                val validateOtpResponse: ValidateOtpResponse = response.body
                assertEquals(AuthenticationStatus.FAILD.name, validateOtpResponse.status)
                Assertions.assertNull(validateOtpResponse.jwt)
                Assertions.assertNull(validateOtpResponse.refreshToken)
                assertEquals(INTERNAL_SYSTEM_ERROR, validateOtpResponse.message)
                assertEquals(0, itemDao.getItems(DMS_OTP::class.java).size)
            }
        }
    }

    @Test
    fun givenOtpRequestWithNotMatchedOtp_whenValidateOtp_thenReturnsFailureResponseWithProperMessage() {
        val sessionId = UUID.randomUUID().toString()
        val request: ValidateOtpRequest = buildRequest(sessionId, USER_NAME, "INVALID")
        val existingOtp: DMS_OTP =
            createOtpEntity(sessionId, USER_NAME, OTP, Timestamp.valueOf(LocalDateTime.now().plusMinutes(5)), 0)
        itemDao.add(existingOtp)
        mockLabelsClass().use { mockLabelsClass ->
            val mockOtpService = ValidateOtpService(
                itemDao,
                MockCompleteAuthentication(true),
                MockDaoAuthenticationProvider(false),
                MockRSADecryptor(),
                MockSecretsLoader(),
                2
            )
            val response: ResponseEntity<ValidateOtpResponse> = mockOtpService.validateOtp(request, REMOTE_ADDR)
            assertEquals(HttpStatus.UNAUTHORIZED, response.statusCode)
            Assertions.assertNotNull(response.body)
            val validateOtpResponse: ValidateOtpResponse = response.body
            assertEquals(AuthenticationStatus.FAILD.name, validateOtpResponse.status)
            assertEquals(OTP_NOT_MATCHED, validateOtpResponse.message)
        }
    }

    @Test
    fun givenOtpRequestWithExpiredOtp_whenValidateOtp_thenReturnsFailureResponseWithProperMessage() {
        val sessionId = UUID.randomUUID().toString()
        val request: ValidateOtpRequest = buildRequest(sessionId, USER_NAME, OTP)
        val existingOtp: DMS_OTP =
            createOtpEntity(sessionId, USER_NAME, OTP, Timestamp.valueOf(LocalDateTime.now().minusMinutes(5)), 0)
        itemDao.add(existingOtp)
        mockLabelsClass().use { mockLabelsClass ->
            val mockOtpService = ValidateOtpService(
                itemDao,
                MockCompleteAuthentication(true),
                MockDaoAuthenticationProvider(false),
                MockRSADecryptor(),
                MockSecretsLoader(),
                2
            )
            val response: ResponseEntity<ValidateOtpResponse> = mockOtpService.validateOtp(request, REMOTE_ADDR)
            assertEquals(HttpStatus.GONE, response.statusCode)
            Assertions.assertNotNull(response.body)
            val validateOtpResponse: ValidateOtpResponse = response.body
            assertEquals(AuthenticationStatus.FAILD.name, validateOtpResponse.status)
            assertEquals(OTP_EXPIRED, validateOtpResponse.message)
        }
    }

    @Test
    fun givenOtpRequestWithMaxValidateAttemptsReached_whenValidateOtp_thenReturnsFailureResponseWithProperMessage() {
        val sessionId = UUID.randomUUID().toString()
        val request: ValidateOtpRequest = buildRequest(sessionId, USER_NAME, OTP)
        val existingOtp: DMS_OTP =
            createOtpEntity(sessionId, USER_NAME, OTP, Timestamp.valueOf(LocalDateTime.now().plusMinutes(5)), 2)
        itemDao.add(existingOtp)
        mockLabelsClass().use { mockLabelsClass ->
            val mockOtpService = ValidateOtpService(
                itemDao,
                MockCompleteAuthentication(true),
                MockDaoAuthenticationProvider(false),
                MockRSADecryptor(),
                MockSecretsLoader(),
                2
            )
            val response: ResponseEntity<ValidateOtpResponse> = mockOtpService.validateOtp(request, REMOTE_ADDR)
            assertEquals(HttpStatus.TOO_MANY_REQUESTS, response.statusCode)
            assertNotNull(response.body)
            val validateOtpResponse: ValidateOtpResponse = response.body
            assertEquals(AuthenticationStatus.FAILD.name, validateOtpResponse.status)
            assertEquals(OTP_MAX_VALIDATION_ATTEMPTS_REACHED, validateOtpResponse.message)
        }
    }

    private fun buildRequest(sessionId: String, userName: String, otp: String): ValidateOtpRequest {
        val request = ValidateOtpRequest()
        request.sessionId = sessionId
        request.userName = userName
        request.otp = otp
        request.password = "123"
        return request
    }

    private fun createOtpEntity(
        sessionId: String,
        userName: String,
        otp: String,
        expiryTime: Timestamp,
        validateAttempts: Int
    ): DMS_OTP {
        val existingOtp = DMS_OTP()
        existingOtp.sessionId = sessionId
        existingOtp.username = userName
        existingOtp.otp = otp
        existingOtp.expiryTime = expiryTime
        existingOtp.validateAttempts = validateAttempts
        return existingOtp
    }

    @Throws(ClassNotFoundException::class, NoSuchFieldException::class, IllegalAccessException::class)
    private fun addUsers(username: String, id: Long) {
        val user = Mockito.mock(
            User::class.java
        )
        val mockClass = Class.forName("com.progressoft.jfw.model.bussinessobject.security.AbstractUser")
        val mockUserClass = Class.forName("com.progressoft.jfw.model.bussinessobject.security.User")
        val nameField = mockClass.getDeclaredField("displayName")
        val idfield = mockUserClass.getDeclaredField("id")
        val usernameField = mockClass.getDeclaredField("username")
        val passwordField = mockClass.getDeclaredField("password")
        nameField.isAccessible = true
        nameField[user] = username
        idfield.isAccessible = true
        idfield[user] = id
        usernameField.isAccessible = true
        usernameField[user] = username
        passwordField.isAccessible = true
        passwordField[user] = "a"
        user.id = id
        Mockito.`when`(user.displayName).thenReturn(username)
        Mockito.`when`(user.id).thenReturn(id)
        itemDao.add(user)
    }

    private fun mockLabelsClass(): MockedStatic<Labels> {
        val labelsMockedStatic = Mockito.mockStatic(
            Labels::class.java
        )
        labelsMockedStatic.`when`<Any> { Labels.description("otp.not.found") }.thenReturn(
            OTP_NOT_FOUND
        )
        labelsMockedStatic.`when`<Any> { Labels.description("otp.max.validation.attempts.reached") }
            .thenReturn(OTP_MAX_VALIDATION_ATTEMPTS_REACHED)
        labelsMockedStatic.`when`<Any> { Labels.description("otp.expired") }.thenReturn(
            OTP_EXPIRED
        )
        labelsMockedStatic.`when`<Any> { Labels.description("otp.not.matched") }.thenReturn(
            OTP_NOT_MATCHED
        )
        labelsMockedStatic.`when`<Any> { Labels.description("otp.internal.system.error") }
            .thenReturn(INTERNAL_SYSTEM_ERROR)
        return labelsMockedStatic
    }

    private class MockCompleteAuthentication(private val isSuccess: Boolean) : CompleteAuthentication {
        override fun complete(
            jfwAuthentication: JfwAuthentication,
            extendedUserDetails: ExtendedUserDetails,
            authenticationRequest: AuthenticationRequest
        ): AuthenticationResponse {
            val authenticationResponse = AuthenticationResponseImpl()
            authenticationResponse.setStatus(if (isSuccess) AuthenticationStatus.SUCCESS else AuthenticationStatus.FAILD)
            authenticationResponse.setMessage(if (isSuccess) SUCCESS_MSG else "Failed")
            authenticationResponse.setToken(if (isSuccess) "token" else null)
            authenticationResponse.setRefreshToken(if (isSuccess) "refreshToken" else null)
            return authenticationResponse
        }
    }

    companion object {
        private const val USER_NAME = "user123"
        private const val OTP = "123456"
        private const val REMOTE_ADDR = "192.168.1.1"
        private const val OTP_NOT_MATCHED = "OTP Not Matched"
        private const val SUCCESS_MSG = "Success"
        private const val OTP_NOT_FOUND = "OTP Not Found"
        private const val OTP_EXPIRED = "OTP Expired"
        private const val OTP_MAX_VALIDATION_ATTEMPTS_REACHED = "OTP Max Validation Attempts Reached"
        private const val INTERNAL_SYSTEM_ERROR = "INTERNAL SYSTEM ERROR"

    }
}
