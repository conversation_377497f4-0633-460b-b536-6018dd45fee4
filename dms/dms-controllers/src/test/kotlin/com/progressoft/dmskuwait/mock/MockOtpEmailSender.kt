//package com.progressoft.dms.mock
//
//import com.progressoft.dmskuwait.utils.otp.generator.emailsender.OtpEmailSender
//import com.progressoft.psdms.email.Email
//import com.progressoft.psdms.email.EmailSenderImpl
//import com.progressoft.psdms.util.EmailUtils
//import utils.FakeItemDao
//
//class MockOtpEmailSender : OtpEmailSender(MockEmailUtils(),FakeItemDao(),"","") {
//    var isCalled: Boolean = false
//    override fun send(userName: String, otp: String) {
//        isCalled = true
//    }
//}
//
//private class MockEmailSender : EmailSenderImpl(true, "", null, null) {
//    override fun send(email: Email) {
//    }
//}
//
//private class MockEmailUtils : EmailUtils(FakeItemDao()) {
//    override fun getEmailSender(): EmailSenderImpl {
//        return MockEmailSender()
//    }
//}
