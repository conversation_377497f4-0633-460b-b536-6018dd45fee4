package com.progressoft.dmskuwait.functions

import com.progressoft.dms.entities.DMS_TermsAndConditions
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfContextBuilder
import org.junit.Assert
import org.junit.Test
import utils.FakeItemDao

class ResetAcceptTermsFunctionTest {

    private lateinit var itemDao: FakeItemDao
    private lateinit var context: WfContext<DMS_TermsAndConditions?>
    private lateinit var resetAcceptTermsFunction: ResetAcceptTermsFunction

    @Test
    fun givenValidRequest_whenCallResetAcceptTermsFunction_thenSetTermsAcceptedToFalseForAllUsers() {
        itemDao = FakeItemDao()
        resetAcceptTermsFunction = ResetAcceptTermsFunction().apply { setItemDao(itemDao) }
        val userA = User().apply { id = 1L; termsAccepted = true }
        val userB = User().apply { id = 2L; termsAccepted = true }
        itemDao.add(userA)
        itemDao.add(userB)
        context = WfContextBuilder(DMS_TermsAndConditions()).context

        resetAcceptTermsFunction.execute(context)

        itemDao.getItems(User::class.java).forEach { user -> Assert.assertEquals(user.termsAccepted, false) }
    }
}
