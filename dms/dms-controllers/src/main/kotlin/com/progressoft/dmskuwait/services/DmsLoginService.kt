package com.progressoft.dmskuwait.services


import com.progressoft.ach.application.dto.ValidateUserRequest
import com.progressoft.ach.application.dto.ValidateUserResponse
import com.progressoft.dmskuwait.dto.DmsAuthenticationResponse
import com.progressoft.dmskuwait.dto.TokenUserInfoResponse
import com.progressoft.dmskuwait.exceptions.ValidationException
import com.progressoft.dmskuwait.repository.UserRepository
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.LOCALE
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.USERNAME
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.getClaimStringFromToken
import com.progressoft.dmskuwait.utils.RSADecryptor
import com.progressoft.jfw.Query
import com.progressoft.jfw.labels.Labels
import com.progressoft.jfw.labels.LocaleHolder
import com.progressoft.jfw.model.bussinessobject.security.DefaultExtendedUserDetails
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jfw.model.dao.security.SecurityDao
import com.progressoft.jfw.model.service.security.JfwAuthentication
import com.progressoft.jupiter.authentication.strategy.api.AuthenticationStatus
import com.progressoft.jupiter.authentication.strategy.api.AuthenticationStatus.FAILD
import com.progressoft.jupiter.authentication.strategy.api.JwtHelper
import com.progressoft.jupiter.authentication.strategy.api.complete.CompleteAuthentication
import com.progressoft.jupiter.authentication.strategy.api.complete.Constants.BEARER
import com.progressoft.jupiter.authentication.strategy.api.complete.Constants.REFRESH
import com.progressoft.jupiter.authentication.strategy.api.request.AuthenticationRequestImpl
import com.progressoft.jupiter.authentication.strategy.api.response.AuthenticationResponse
import com.progressoft.jupiter.authentication.strategy.provider.AuthenticationStrategyProvider
import com.progressoft.jupiter.rest.security.authenticate.request.AuthenticationRequest
import com.progressoft.jupiter.secrurity.secrets.SecretsLoader
import com.progressoft.jupiter.secrurity.secrets.impl.SecretName.JWT_SECRET
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatus.*
import org.springframework.http.ResponseEntity
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.authentication.dao.DaoAuthenticationProvider
import org.springframework.stereotype.Component
import java.util.*

@Component
open class DmsLoginService @Autowired constructor(
    private val securityDao: SecurityDao,
    private val authenticationStrategyProvider: AuthenticationStrategyProvider,
    private val decryptor: RSADecryptor,
    private val itemDao: ItemDao,
    private val authenticationCompleter: CompleteAuthentication,
    private val daoAuthenticationProvider: DaoAuthenticationProvider,
    private val secretsLoader: SecretsLoader,
    private val userRepository: UserRepository,
    private val tokenService: JwtTokenService
) {

    open fun loginUsingToken(token: String, remoteAddr: String): ResponseEntity<TokenUserInfoResponse> {
        val userName = getClaimStringFromToken(token, USERNAME)
        val userInfo = userRepository.findByUsername(userName).get()
        val usernameTenant: String = userName + "/" + userInfo.tenantId
        val authentication = daoAuthenticationProvider.authenticate(
            UsernamePasswordAuthenticationToken(usernameTenant, tokenService.getUsersPassword())
        )
        val jfwAuth = JfwAuthentication(authentication, userInfo.tenantId)
        val lang = getClaimStringFromToken(token, LOCALE)
        val authRequest = AuthenticationRequestImpl(
            userName,
            tokenService.getUsersPassword(),
            userInfo.tenantId,
            lang,
            remoteAddr
        )
        val completeResponse = authenticationCompleter.complete(
            jfwAuth,
            authentication.principal as DefaultExtendedUserDetails,
            authRequest
        )
        return buildUserInfoResponse(completeResponse, userName, userInfo.tenantId, lang)
    }

    open fun authenticate(
        request: AuthenticationRequest,
        remoteAddr: String,
        sessionId: String,
        isTokenAuth: Boolean
    ):DmsAuthenticationResponse {
        try {
            LocaleHolder.setLocale(Locale(request.language))
            return processAuthenticationRequest(request, remoteAddr,isTokenAuth)
        } catch (e: Exception) {
            LOG.error(
                "Exception occurs while Process Authentication Request | username: {}, session : {}, Error message: {}",
                request.username,
                sessionId,
                e.message,
                e
            )
            return buildAuthenticationResponse(
                FAILD,
                Labels.description("validation.internal.system.error")
            )
        }
    }

    open fun validateUser(request: ValidateUserRequest, remoteAddr: String): ResponseEntity<ValidateUserResponse> {
        try {
            return processValidateUser(request, remoteAddr)
        } catch (ex: ValidationException) {
            LOG.warn(
                "Validation Exception Occurred | Username {} |  Exception Message {}",
                request.userName,
                ex.exceptionMessage
            )
            return buildResponse(FAILD.name, ex.responseMessage, ex.httpStatus)
        } catch (exception: Exception) {
            LOG.error(
                "Exception Occurs In User-Validation | Username {} | ErrorMessage {}",
                request.userName,
                exception.message,
                exception
            )
            return buildResponse(
                FAILD.name,
                Labels.description("validation.internal.system.error"),
                INTERNAL_SERVER_ERROR
            )
        }
    }

    private fun processValidateUser(
        request: ValidateUserRequest,
        remoteAddr: String,
    ): ResponseEntity<ValidateUserResponse> {
        return completeAuthentication(request, remoteAddr)

    }

    private fun completeAuthentication(
        request: ValidateUserRequest,
        remoteAddr: String
    ): ResponseEntity<ValidateUserResponse> {
        val usernameTenant: String = request.userName + "/" + request.tenant
        val decryptedPassword: String = decryptor.decrypt(request.password!!)
        val authentication = daoAuthenticationProvider.authenticate(
            UsernamePasswordAuthenticationToken(
                usernameTenant,
                decryptedPassword
            )
        )
        val jfwAuthentication = JfwAuthentication(authentication, request.tenant)
        val user =
            Query(itemDao).equals("username", request.userName).equals("tenantId", request.tenant).first(
                User::class.java
            )
        user.failedLoginAttempts = 0
        itemDao.merge(user)
        val authenticationRequest = AuthenticationRequestImpl(
            request.userName,
            decryptedPassword,
            request.tenant,
            request.language,
            remoteAddr
        )
        val completeResponse = authenticationCompleter.complete(
            jfwAuthentication,
            authentication.principal as DefaultExtendedUserDetails,
            authenticationRequest
        )
        return buildResponse(completeResponse, request.userName!!)
    }

    private fun buildResponse(
        status: String,
        message: String,
        httpStatus: HttpStatus
    ): ResponseEntity<ValidateUserResponse> {
        val response = ValidateUserResponse()
        response.status = status
        response.message = message
        return ResponseEntity.status(httpStatus).body(response)
    }

    private fun buildResponse(
        completeResponse: AuthenticationResponse,
        userName: String
    ): ResponseEntity<ValidateUserResponse> {
        if (completeResponse.status() == FAILD) {
            return buildResponse(completeResponse.status().name, completeResponse.message(), UNAUTHORIZED)
        } else {
            val response = ValidateUserResponse()
            response.status = completeResponse.status().name
            response.message = completeResponse.message()
            response.jwt = completeResponse.token()
            response.refreshToken = completeResponse.refreshToken()
            response.jwtExp = getJwtExp(completeResponse)
            response.refreshTokenExp = getRefreshExp(completeResponse)
            response.username = userName
            return ResponseEntity.status(OK).body(response)
        }
    }

    private fun buildUserInfoResponse(
        authResponse: AuthenticationResponse, userName: String, tenantId: String, lang: String
    ): ResponseEntity<TokenUserInfoResponse> {
        if (authResponse.status() == FAILD) {
            return ResponseEntity.status(UNAUTHORIZED).body(TokenUserInfoResponse().apply {
                status = authResponse.status().name
                message = authResponse.message()
            })
        } else {
            return ResponseEntity.status(OK).body(TokenUserInfoResponse().apply {
                status = authResponse.status().name
                message = authResponse.message()
                jwt = authResponse.token()
                refreshToken = authResponse.refreshToken()
                jwtExp = getJwtExp(authResponse)
                refreshTokenExp = getRefreshExp(authResponse)
                username = userName
                tenant = tenantId
                language = lang
            })
        }
    }

    private fun getRefreshExp(completeResponse: AuthenticationResponse): Long {
        return JwtHelper.getJwtClaims(completeResponse.refreshToken(), secretsLoader.loadSecret(JWT_SECRET), REFRESH)
            .expiration.toInstant().toEpochMilli()
    }

    private fun getJwtExp(completeResponse: AuthenticationResponse): Long {
        return JwtHelper.getJwtClaims(completeResponse.token(), secretsLoader.loadSecret(JWT_SECRET), BEARER)
            .expiration.toInstant().toEpochMilli()
    }

    private fun processAuthenticationRequest(
        request: AuthenticationRequest,
        remoteAddr: String,
        isTokenAuth: Boolean,
        ):DmsAuthenticationResponse {
        if (isActiveTenant(request.tenant)) {
            val authenticationRequest = buildAuthenticateRequest(request, remoteAddr, isTokenAuth)
            val authenticationResponse = authenticationStrategyProvider.strategy().authenticate(authenticationRequest)
            return handleAuthenticateResponse(authenticationResponse)
        } else return buildAuthenticationResponse(FAILD, Labels.description(("login.inactive.tenant"))
        )
    }

    private fun handleAuthenticateResponse(
        authenticationResponse: AuthenticationResponse
    ): DmsAuthenticationResponse {
        return buildAuthenticationResponse(authenticationResponse.status(), authenticationResponse.message())
    }


    private fun buildAuthenticateRequest(
        request: AuthenticationRequest,
        remoteAddr: String,
        isTokenAuth: Boolean
    ): AuthenticationRequestImpl {
        return AuthenticationRequestImpl(
            request.username,
            if(isTokenAuth) request.password else decryptor.decrypt(request.password),
            request.tenant,
            request.language,
            remoteAddr
        )
    }

    private fun buildAuthenticationResponse(
        authenticate: AuthenticationStatus,
        authenticate1: String?
    ): DmsAuthenticationResponse {
        return DmsAuthenticationResponse(authenticate.name, authenticate1)
    }

    private fun isActiveTenant(tenant: String): Boolean {
        val theTenant = securityDao.getTenant(tenant)
        return Objects.nonNull(theTenant) && theTenant.statusId.id != 21L
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DmsLoginService::class.java)
    }
}