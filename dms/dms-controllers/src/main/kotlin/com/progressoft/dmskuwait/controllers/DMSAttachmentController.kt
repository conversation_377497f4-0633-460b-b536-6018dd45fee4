package com.progressoft.dmskuwait.controllers

import com.progressoft.dms.entities.*
import com.progressoft.dmskuwait.dto.AttachmentDto
import com.progressoft.dmskuwait.response.LookupResponse
import com.progressoft.dmskuwait.services.AttachmentService
import com.progressoft.dmskuwait.services.DisputeCaseManagementService
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jfw.model.service.attachment.AttachmentsServiceImpl
import jakarta.servlet.ServletException
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.Part
import org.apache.commons.lang.StringEscapeUtils
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.StringUtils.EMPTY
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.io.IOException
import java.lang.reflect.InvocationTargetException
import java.math.BigDecimal
import java.util.*

@RestController
@Transactional
open class DMSAttachmentController @Autowired constructor(
    private val jfwAttachmentService: AttachmentsServiceImpl,
    private val attachmentService: AttachmentService,
    private val disputeCaseService: DisputeCaseManagementService,
    private val itemDao: ItemDao) {

    @GetMapping("/api/dms/list-attachments/{viewName}/{entityId}")
    @ResponseBody
    open fun getAttachment(
        @PageableDefault pageable: Pageable,
        @PathVariable viewName: String,
        @PathVariable entityId: String
    ): ResponseEntity<out LookupResponse<out Any?>> {
        return attachmentService.getAttachment(pageable, viewName, entityId, itemDao)
    }

    @GetMapping("/api/dms/list-attachments/dispute-case/{viewName}/{entityId}")
    @ResponseBody
    open fun getDisputeAttachment(
        @PageableDefault pageable: Pageable,
        @PathVariable entityId: String,
        @PathVariable viewName: String
    ): ResponseEntity<LookupResponse<AttachmentDto>> {
        return attachmentService.getDisputeCaseAttachment(pageable, viewName, entityId, disputeCaseService,itemDao)
    }

    @GetMapping("/api/dms/attachment-type")
    @ResponseBody
    open fun getAttachmentTypes(): ResponseEntity<LookupResponse<*>?> {
        return ResponseEntity<LookupResponse<*>?>(HttpStatus.OK)
    }

    @GetMapping("/api/dms/download-file/{viewName}/{attachmentId}")
    @ResponseBody
    @Transactional
    open fun downloadAttachment(
        @PathVariable viewName: String,
        @PathVariable attachmentId: String
    ): ResponseEntity<ByteArray> {
        return attachmentService.downloadAttachment(viewName, attachmentId, itemDao)
    }

    private fun getBadRequestResponse(): ResponseEntity<*> {
        return ResponseEntity<Any>(HttpStatus.BAD_REQUEST)
    }

    @PostMapping("/api/dms/upload-file/{viewName}/{itemId}")
    @Throws(
        IOException::class,
        ServletException::class,
        ClassNotFoundException::class,
        InstantiationException::class,
        IllegalAccessException::class,
        InvocationTargetException::class,
        NoSuchMethodException::class
    )
    open fun uploadFile(
        @PathVariable("viewName") viewName: String,
        @PathVariable("itemId") itemId: String,
        @RequestParam("comments") comments: String,
        request: HttpServletRequest
    ): ResponseEntity<*>? {
        if (StringUtils.isEmpty(viewName) || StringUtils.isEmpty(itemId) || Objects.isNull(request.getPart("files"))) {
            return getBadRequestResponse()
        }
        uploadAttachment(request.getPart("files"), itemId, viewName, comments)
        return ResponseEntity.ok("{\"status\":\"success\",\"attachmentId\":\"$itemId\"}")
    }

    @PostMapping("/api/dms/delete-file/{viewName}/{attachmentId}")
    @ResponseStatus(HttpStatus.OK)
    open fun deleteAttachment(
        @PathVariable("viewName") viewName: String,
        @PathVariable("attachmentId") attachmentId: String
    ) {
        val itemType = attachmentService.getItemType(viewName)
        itemType.ifPresent {
            itemDao.removeItemById(
                it, attachmentId.toLong()
            )
            itemDao.flush()
        }
    }

    @Throws(IOException::class, InstantiationException::class, IllegalAccessException::class)
    private fun uploadAttachment(uploadedFile: Part, itemId: String, viewName: String, comments: String) {
        if (StringUtils.isEmpty(viewName) || StringUtils.isEmpty(itemId) || Objects.isNull(uploadedFile)) {
            return
        }
        val itemType = attachmentService.getItemType(viewName)
        itemType.ifPresent {
            val attachmentItem = it.newInstance()
            val entityType = attachmentService.getEntityType(viewName)
            attachmentItem.recordId = resolveEntityId(itemId, viewName)
            attachmentItem.entityId = entityType.map { it.name }.orElse(EMPTY)
            attachmentItem.attFile = uploadedFile.inputStream.readAllBytes()
            attachmentItem.contentType = uploadedFile.contentType
            attachmentItem.name = StringEscapeUtils.unescapeHtml(uploadedFile.submittedFileName)
            attachmentItem.comments = comments
            attachmentItem.size = BigDecimal(uploadedFile.size)
            attachmentItem.imageType = uploadedFile.contentType
            attachmentItem.attachmentToken = viewName
            jfwAttachmentService.attach(attachmentItem)
        }
    }

    private fun resolveEntityId(itemId: String, viewName: String): String {
        if (viewName == "DMS_DisputeCases.View") return itemId
        val baseCaseManagement = itemDao.getItem(viewNameToMap[viewName], itemId) as DMS_BaseCaseManagement
        return baseCaseManagement.disputeCase?.id.toString()
    }

    companion object {
        val viewNameToMap = mapOf(
            "DMS_ClaimantBankCaseManagement.View" to DMS_ClaimantBankCaseManagement::class.java,
            "DMS_DefendantBankCaseManagement.View" to DMS_DefendantBankCaseManagement::class.java,
            "DMS_ClaimantNCBCaseManagement.View" to DMS_ClaimantNCBCaseManagement::class.java,
            "DMS_DefendantNCBCaseManagement.View" to DMS_DefendantNCBCaseManagement::class.java
        )
    }
}