package com.progressoft.dmskuwait.controllers

import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.psdms.application.exceptions.FetchTransactionException
import com.progressoft.psdms.application.models.Transaction
import com.progressoft.psdms.application.services.ApiService
import com.progressoft.jfw.model.dao.item.ItemDao
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import jakarta.annotation.Resource

@RestController
class PaymentTransactionController {

    @Resource
    private val itemDao: ItemDao? = null

    @Resource
    private val apiService = ApiService()

    @GetMapping("/api/views/fetch-transactions/lookup-grid-data")
    fun fetchTransactions(
        @PageableDefault pageable: Pageable, @RequestParam parameters: MutableMap<String?, String?>
    ): ResponseEntity<Page<out Transaction?>>? {
        try {
            val itemId = parameters["itemId"].toString().toLong()
            val disputeCase = itemDao?.getItem(DMS_DisputeCase::class.java, "id", itemId)
            parameters["claimantBank"] = disputeCase?.claimantBank?.code
            parameters["defendantBank"] = disputeCase?.defendantBank?.code
            return ResponseEntity.ok().body(getTransactions(parameters, pageable, disputeCase?.paymentSystem?.code!!))
        } catch (ex: Exception) {
            throw FetchTransactionException(ex.message, ex)
        }
    }

    private fun getTransactions(
        parameters: Map<String?, String?>,
        pageable: Pageable,
        paymentSystem: String
    ): Page<out Transaction?> {
        val result = apiService.fetchTransactionApi(parameters, paymentSystem,pageable.pageSize,pageable.pageNumber)
        val sortedTransactions = result.transactions
            .sortedBy { it.transactionDate }
        return PageImpl(sortedTransactions, pageable, result.dataSize)
    }

}