package com.progressoft.dmskuwait.services

import com.progressoft.dmskuwait.utils.AESUtil.Companion.decrypt
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jfw.notifications.entity.JFWGenericConfiguration
import com.progressoft.jupiter.secrurity.secrets.impl.SecretName.JWT_SECRET
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.Base64.getDecoder

@Service
class JwtTokenService @Autowired constructor(private val itemDao: ItemDao) {

    fun getSecretValue(): ByteArray {
        val secretBase64Encoded = itemDao.getItem(
            JFWGenericConfiguration::class.java,
            "configKey",
            JWT_SECRET.secretName
        ).configValue
        return getDecoder().decode(secretBase64Encoded)
    }

    fun getUsersPassword(): String {
        val usersPass = itemDao.getItem(
            JFWGenericConfiguration::class.java,
            "configKey",
            "users.password",
        ).configValue
        return decrypt(usersPass, getSecretValue())
    }
}