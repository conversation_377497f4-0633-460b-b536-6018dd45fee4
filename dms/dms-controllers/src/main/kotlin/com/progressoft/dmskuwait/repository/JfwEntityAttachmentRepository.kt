package com.progressoft.dmskuwait.repository

import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.repository.NoRepositoryBean
import java.util.*

@NoRepositoryBean
interface JfwEntityAttachmentRepository<T, S> : GenericAttachmentRepository<T, S> {
    fun findByRecordId(recordId: String, pageable: Pageable): Page<T>
    override fun findOneById(id: S): Optional<T>?
}
