package com.progressoft.dmskuwait.utils.otp.generator

import com.progressoft.dms.entities.DMS_OTP
import com.progressoft.jfw.model.dao.item.ItemDao
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.security.SecureRandom
import java.sql.Timestamp
import java.time.LocalDateTime

@Component
open class OtpGenerator(
    @Autowired private val itemDao: ItemDao,
    @Value("#{\${dms.otp.expiary.in.minutes}}") private val otpExpiryInMinutes: Int
) {
    private val secureRandom = SecureRandom()

    open fun generateOtp(sessionId: String, userName: String): DMS_OTP {
        val entity = DMS_OTP()
        entity.sessionId = sessionId
        entity.username = userName
        entity.otp = generate()
        entity.reSendAttempts = 0
        entity.validateAttempts = 0
        entity.expiryTime = Timestamp.valueOf(LocalDateTime.now().plusMinutes(otpExpiryInMinutes.toLong()))
        itemDao.persist(entity)
        return entity
    }

    fun generateAndUpdateOtp(otp: DMS_OTP): DMS_OTP {
        otp.otp = generate()
        otp.reSendAttempts += 1
        otp.expiryTime = Timestamp.valueOf(LocalDateTime.now().plusMinutes(otpExpiryInMinutes.toLong()))
        otp.validateAttempts = 0
        itemDao.persist(otp)
        return otp
    }

    private fun generate(): String {
        val length = 6
        val numbers = "0123456789"
        val otp = CharArray(length)
        for (i in 0 until length) {
            otp[i] = numbers[secureRandom.nextInt(numbers.length)]
        }
        return String(otp)
    }
}
