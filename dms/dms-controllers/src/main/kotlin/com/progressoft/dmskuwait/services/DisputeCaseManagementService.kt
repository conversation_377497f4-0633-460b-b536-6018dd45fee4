package com.progressoft.dmskuwait.services

import com.progressoft.dmskuwait.repository.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class DisputeCaseManagementService @Autowired constructor(
    private val claimantBankCaseRepo: ClaimantBankCaseManagementRepository,
    private val claimantNCBCaseRepo: ClaimantNCBCaseManagementRepository,
    private val defendantBankCaseRepo: DefendantBankCaseManagementRepository,
    private val defendantNCBCaseRepo: DefendantNCBCaseManagementRepository,
    private val operatorCaseRepo: OperatorCaseManagementRepository
) {

    fun getDisputeCaseId(entityId: String, viewName: String): Long? =
        if (viewName == "DMS_ClaimantBankCaseManagement.View") {
            claimantBankCaseRepo.findById(entityId.toLong()).map { it.disputeCase?.id }.orElse(0)
        } else if (viewName == "DMS_ClaimantNCBCaseManagement.View") {
            claimantNCBCaseRepo.findById(entityId.toLong()).map { it.disputeCase?.id }.orElse(0)
        } else if (viewName == "DMS_DefendantBankCaseManagement.View") {
            defendantBankCaseRepo.findById(entityId.toLong()).map { it.disputeCase?.id }.orElse(0)
        } else if (viewName == "DMS_DefendantNCBCaseManagement.View") {
            defendantNCBCaseRepo.findById(entityId.toLong()).map { it.disputeCase?.id }.orElse(0)
        } else {
            operatorCaseRepo.findById(entityId.toLong()).map { it.disputeCase?.id }.orElse(0)
        }

}