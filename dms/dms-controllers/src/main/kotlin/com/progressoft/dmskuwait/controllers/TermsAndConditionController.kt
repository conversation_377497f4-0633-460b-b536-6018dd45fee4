package com.progressoft.dmskuwait.controllers

import com.progressoft.dmskuwait.dto.TermsAndConditionsRequest
import com.progressoft.dmskuwait.dto.TermsAndConditionsResponse
import com.progressoft.dmskuwait.services.TermsAndConditionService
import jakarta.servlet.http.HttpServletRequest
import lombok.AllArgsConstructor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@AllArgsConstructor
class TermsAndConditionController @Autowired constructor(private val service: TermsAndConditionService)  {

    @get:GetMapping("/api/terms-and-conditions")
    val termsAndConditions: ResponseEntity<TermsAndConditionsResponse>
        get() {
            try {
                val content = service.termsAndConditions
                val termsAndConditions = TermsAndConditionsResponse(content, null)
                return ResponseEntity.ok(termsAndConditions)
            } catch (e: Exception) {
                return ResponseEntity.internalServerError().build()
            }
        }

    @PostMapping("/api/accept-terms-and-conditions")
    fun acceptTermsAndConditions(httpRequest: HttpServletRequest): ResponseEntity<TermsAndConditionsResponse> {
        try {
            val username = String(httpRequest.getPart("username").inputStream.readAllBytes())
            val request = TermsAndConditionsRequest(null, username)
            service.acceptTermsAndConditions(request.userName)
            return ResponseEntity.ok(TermsAndConditionsResponse(null, null))
        } catch (e: Exception) {
            return ResponseEntity.internalServerError().build()
        }
    }

    @PostMapping("/api/check-terms-and-conditions")
    fun checkTermsAndConditions(httpRequest: HttpServletRequest): ResponseEntity<TermsAndConditionsResponse> {
        try {
            val username = String(httpRequest.getPart("username").inputStream.readAllBytes())
            val request = TermsAndConditionsRequest(null, username)
            val checkTermsAndConditions = service.checkTermsAndConditions(request.userName)
            val termsAndConditions = TermsAndConditionsResponse(null, checkTermsAndConditions)
            return ResponseEntity.ok(termsAndConditions)
        } catch (e: Exception) {
            return ResponseEntity.internalServerError().build()
        }
    }
}
