package com.progressoft.dmskuwait.controllers

import com.progressoft.dmskuwait.dto.UIOTPPageResponse
import com.progressoft.dmskuwait.services.DmsOtpPageService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
class DmsOtpPageController @Autowired constructor(private val dmsOtpPageService: DmsOtpPageService) {
    @GetMapping("/public/api/otp")
    fun getLoginPage(
        @RequestParam(
            required = false,
            defaultValue = "en",
            name = "language"
        ) languageCode: String?
    ): UIOTPPageResponse {
        return dmsOtpPageService.getUIOTPPageResponse(languageCode)
    }
}