package com.progressoft.dmskuwait.services

import com.progressoft.dms.entities.DMS_TermsAndConditions
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.model.dao.item.ItemDao
import lombok.AllArgsConstructor
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
@AllArgsConstructor
class TermsAndConditionService @Autowired constructor(private val itemDao: ItemDao) {

    val termsAndConditions: String
        get() = Query(itemDao).first(DMS_TermsAndConditions::class.java).termsAndConditions.toString()

    fun acceptTermsAndConditions(loggedInUser: String?) {
        val user = Query(itemDao).equals("username", loggedInUser).first(User::class.java)
        user.termsAccepted = true
        itemDao.merge(user)
    }

    fun checkTermsAndConditions(loggedInUser: String?): Boolean {
        return Query(itemDao).equals("username", loggedInUser).first(User::class.java).termsAccepted
    }
}
