package com.progressoft.dmskuwait.controllers

import com.progressoft.dms.entities.DMS_Participant
import com.progressoft.psdms.application.models.Participant
import com.progressoft.jfw.model.dao.item.ItemDao
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RestController
import jakarta.annotation.Resource

@RestController
class PaymentParticipantsController {

    @Resource
    private val itemDao: ItemDao? = null

    @GetMapping("/api/misc/filter/participants")
    fun getAllParticipants(): ResponseEntity<List<Participant>>? {
        return ResponseEntity.ok(getParticipants())
    }

    private fun getParticipants(): List<Participant> {
        return itemDao!!.getItems(DMS_Participant::class.java)
            .stream()
            .map { item: DMS_Participant ->
                mapParticipant(
                    item
                )
            }
            .toList()
    }

    private fun mapParticipant(item: DMS_Participant): Participant {
        return Participant(item.id, item.name, item.code, item.codeNamePair)
    }
}