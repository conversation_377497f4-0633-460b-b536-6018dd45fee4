package com.progressoft.dmskuwait.controllers

import com.progressoft.ach.application.dto.ValidateUserRequest
import com.progressoft.ach.application.dto.ValidateUserResponse
import com.progressoft.dmskuwait.dto.DmsAuthenticationResponse
import com.progressoft.dmskuwait.services.DmsLoginService
import com.progressoft.jupiter.rest.security.authenticate.request.AuthenticationRequest
import jakarta.servlet.ServletException
import jakarta.servlet.http.HttpServletRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RestController
import java.io.IOException


@RestController
class AchAuthenticationController @Autowired constructor(
    private val loginService: DmsLoginService,
) {
    @PostMapping("/auth/login/authenticate")
    @Throws(ServletException::class, IOException::class)
    fun authenticate(httpRequest: HttpServletRequest): DmsAuthenticationResponse {
        val username = String(httpRequest.getPart("username").inputStream.readAllBytes())
        val password = String(httpRequest.getPart("password").inputStream.readAllBytes())
        val tenant = String(httpRequest.getPart("tenant").inputStream.readAllBytes())
        val language = String(httpRequest.getPart("language").inputStream.readAllBytes())
        val sessionId = String(httpRequest.getPart("sessionId").inputStream.readAllBytes())
        val request = AuthenticationRequest()
        request.username = username
        request.password = password
        request.tenant = tenant
        request.language = language
        return loginService.authenticate(request, httpRequest.remoteAddr, sessionId,false)
    }

    @PostMapping("/auth/user/validate")
    @Throws(ServletException::class, IOException::class)
    fun validateUser(request: HttpServletRequest?): ResponseEntity<ValidateUserResponse> {
        val sessionId = String(request!!.getPart("sessionId").inputStream.readAllBytes())
        val username = String(request.getPart("username").inputStream.readAllBytes())
        val password = String(request.getPart("password").inputStream.readAllBytes())
        val tenant = String(request.getPart("tenant").inputStream.readAllBytes())
        val language = String(request.getPart("language").inputStream.readAllBytes())
        val validateUserRequest = ValidateUserRequest()
        validateUserRequest.sessionId = sessionId
        validateUserRequest.userName = username
        validateUserRequest.password = password
        validateUserRequest.tenant = tenant
        validateUserRequest.language = language
        return loginService.validateUser(validateUserRequest, request.remoteAddr)
    }
}
