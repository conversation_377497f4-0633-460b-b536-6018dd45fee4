package com.progressoft.dmskuwait.repository

import com.progressoft.jfw.model.bussinessobject.security.User
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.stereotype.Repository
import java.util.*

@Repository
interface UserRepository : JpaRepository<User?, Long?>, JpaSpecificationExecutor<User?> {
    fun findByUsername(username: String?): Optional<User?>
}
