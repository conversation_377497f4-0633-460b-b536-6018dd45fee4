package com.progressoft.dmskuwait.utils

import com.auth0.jwt.JWT
import com.auth0.jwt.interfaces.Claim

class JwtTokenUtil {

    companion object {
        const val USERNAME = "username"
        const val TENANT = "tenant"
        const val LANGUAGE = "language"
        const val EXPIRY = "expiry"
        const val JWT_TOKEN = "authorization-token"
        const val JWT_TOKEN_EXP = "authorization-exp"
        const val REFRESH_TOKEN = "refresh-token"
        const val REFRESH_TOKEN_EXP = "refresh-exp"
        const val APP_LINK = "appLink"
        const val LOCALE = "locale"
        const val CIVIL_ID_CLAIM = "civilIdClaim"
        const val SMART_CARD_ID_CLAIM = "smartCardIdClaim"

        fun getClaimFromToken(jwtToken: String, key: String): Claim = JWT.decode(jwtToken).getClaim(key)
        fun getClaimStringFromToken(jwtToken: String, key: String) = getClaimFromToken(jwtToken, key).asString().trim()
    }
}