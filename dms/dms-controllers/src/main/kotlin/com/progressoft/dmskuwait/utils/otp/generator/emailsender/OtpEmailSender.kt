//package com.progressoft.dmskuwait.utils.otp.generator.emailsender
//
//import com.progressoft.jfw.Query
//import com.progressoft.jfw.model.bussinessobject.security.User
//import com.progressoft.jfw.model.dao.item.ItemDao
//import com.progressoft.psdms.email.Email
//import com.progressoft.psdms.email.EmailRecipients
//import com.progressoft.psdms.util.EmailUtils
//import org.slf4j.LoggerFactory
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.beans.factory.annotation.Value
//import org.springframework.stereotype.Component
//
//@Component
//open class OtpEmailSender @Autowired constructor(
//    private val emailUtil: EmailUtils,
//    private val itemDao: ItemDao,
//    @Value("\${dms.otp.email.subject}") private val subject: String?,
//    @Value("\${dms.otp.email.body}") private val body: String?
//) {
//    open fun send(userName: String, otp: String) {
//        LOG.debug("About To Generate New Email For User: {}", userName)
//        val email = pullReceiverEmail(userName).email
//        emailUtil.getEmailSender().send(getEmail(email, otp))
//        LOG.debug("New Email Was Generated For User: {}", userName)
//    }
//
//    private fun pullReceiverEmail(userName: String) = Query(itemDao)
//        .equals("username", userName)
//        .first(User::class.java)
//
//
//    private fun getEmail(email: String, otp: String) = Email(
//        EmailRecipients.to(email),
//        subject!!,
//        java.lang.String.format(body!!, otp),
//        null
//    )
//
//    companion object {
//        private val LOG = LoggerFactory.getLogger(OtpEmailSender::class.java)
//    }
//}