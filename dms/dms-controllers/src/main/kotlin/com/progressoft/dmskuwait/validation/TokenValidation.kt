package com.progressoft.dmskuwait.validation


import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm.HMAC256
import com.progressoft.dmskuwait.repository.UserRepository
import com.progressoft.dmskuwait.services.JwtTokenService
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.APP_LINK
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.CIVIL_ID_CLAIM
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.EXPIRY
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.JWT_TOKEN
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.LANGUAGE
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.LOCALE
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.REFRESH_TOKEN
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.SMART_CARD_ID_CLAIM
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.TENANT
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.USERNAME
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.getClaimFromToken
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.getClaimStringFromToken
import com.progressoft.jfw.model.bussinessobject.security.User
import org.apache.commons.lang.StringUtils.isEmpty
import org.apache.commons.validator.routines.UrlValidator
import org.apache.commons.validator.routines.UrlValidator.ALLOW_LOCAL_URLS
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.*
import java.util.Objects.isNull

@Component
class TokenValidation @Autowired constructor(
    private val userRepository: UserRepository,
    private val tokenService: JwtTokenService,
) {

    fun validate(jwtToken: String) = !(isNull(jwtToken) || !isReceivedJwtTokenValid(jwtToken))

    fun validateUserInfoToken(jwtToken: String) =
        !(isNull(jwtToken) || !validUserInfoToken(jwtToken) || isExpired(jwtToken))

    private fun validUserInfoToken(jwtToken: String) = validToken(jwtToken) &&
            isFieldExist(jwtToken, USERNAME) &&
            isFieldExist(jwtToken, TENANT) &&
            isFieldExist(jwtToken, LANGUAGE) &&
            isFieldExist(jwtToken, JWT_TOKEN) &&
            isFieldExist(jwtToken, REFRESH_TOKEN)

    private fun isReceivedJwtTokenValid(jwtToken: String) = validToken(jwtToken) &&
            isFieldExist(jwtToken, USERNAME) &&
            verifyApplicationLink(jwtToken) &&
            isFieldExist(jwtToken, LOCALE) &&
            validateUserInfo(jwtToken)

    private fun validateUserInfo(jwtToken: String): Boolean {
        val usernameWithTenant = getClaimStringFromToken(jwtToken, USERNAME)
        val user = bringUser(usernameWithTenant)
        return user.isPresent && isCorrectCivilId(jwtToken, user.get()) && isCorrectSmartCardId(jwtToken, user.get())
    }

    private fun validToken(jwtToken: String) = !isEmpty(jwtToken) && verified(jwtToken)

    private fun verified(jwtToken: String) = try {
        JWT.require(HMAC256(tokenService.getSecretValue())).build().verify(jwtToken)
        true
    } catch (e: Exception) {
        false
    }

    private fun isExpired(jwtToken: String): Boolean {
        val expiryDate = getClaimFromToken(jwtToken, EXPIRY)
        return expiryDate.isNull || Date().after(expiryDate.asDate())
    }

    private fun verifyApplicationLink(jwtToken: String): Boolean {
        val appLink = getClaimFromToken(jwtToken, APP_LINK)
        return !appLink.isNull && appLink.asString().trim().isNotEmpty() && validAppLink(appLink.asString().trim())
    }

    private fun validAppLink(appLink: String): Boolean {
        return UrlValidator(arrayOf("http", "https"), ALLOW_LOCAL_URLS).isValid(appLink)
    }

    private fun isFieldExist(jwtToken: String, fieldName: String): Boolean {
        val claim = getClaimFromToken(jwtToken, fieldName)
        return !claim.isNull && claim.asString().trim().isNotEmpty()
    }

    private fun bringUser(username: String) = userRepository.findByUsername(username)

    private fun isCorrectCivilId(jwtToken: String, user: User) =
        user.extraInfo3 == getClaimStringFromToken(jwtToken, CIVIL_ID_CLAIM)

    private fun isCorrectSmartCardId(jwtToken: String, user: User) =
        user.extraInfo4 == getClaimStringFromToken(jwtToken, SMART_CARD_ID_CLAIM)
}