package com.progressoft.dmskuwait.utils

import kotlin.random.Random

class StrongPasswordGenerator {

    companion object {
        private const val UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
        private const val LOWER = "abcdefghijklmnopqrstuvwxyz"
        private const val DIGITS = "0123456789"
        private const val SPECIAL = "!@#$%^&*()-_=+[]{}|;:,.<>?"
        private val ALL = UPPER + LOWER + DIGITS + SPECIAL

        fun generatePassword(length: Int): String {
            require(length >= 8) { "Password length should be at least 8 characters." }

            val passwordChars = mutableListOf(
                UPPER.random(),
                LOWER.random(),
                DIGITS.random(),
                SPECIAL.random()
            )

            repeat(length - 4) {
                passwordChars += ALL.random()
            }

            passwordChars.shuffle(Random)
            return passwordChars.joinToString("")
        }
    }
}