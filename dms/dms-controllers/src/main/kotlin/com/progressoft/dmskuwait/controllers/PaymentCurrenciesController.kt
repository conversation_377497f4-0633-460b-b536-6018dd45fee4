package com.progressoft.dmskuwait.controllers

import com.progressoft.jfw.Currencies
import com.progressoft.jfw.model.bussinessobject.core.JFWCurrency
import com.progressoft.psdms.application.models.Currency
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RestController

@RestController
class PaymentCurrenciesController {

    @Autowired
    private val currencies: Currencies? = null

    @GetMapping("/api/misc/filter/currencies")
    fun getAllCurrencies(): ResponseEntity<List<Currency>>? {
        return ResponseEntity.ok(getCurrencies())
    }

    private fun getCurrencies(): List<Currency> {
        return currencies!!.get().stream()
            .map { item: JFWCurrency -> mapCurrency(item) }
            .toList()
    }

    private fun mapCurrency(item: JFWCurrency): Currency {
        return Currency(item.id, item.name, item.code, item.codeNamePair)
    }
}