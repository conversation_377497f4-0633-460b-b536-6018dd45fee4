package com.progressoft.dmskuwait.services

import com.progressoft.dmskuwait.dto.UIOTPPageResponse
import com.progressoft.jfw.labels.repository.LabelsRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
open class DmsOtpPageService @Autowired constructor(private val labelsRepository: LabelsRepository?) {

    open fun getUIOTPPageResponse(languageCode: String?): UIOTPPageResponse {
        val response = UIOTPPageResponse()
        response.otpFormTitle = labelsRepository!!.getByKeyAndLanguage("otp.form.title", languageCode)
        response.resendTimerMessage = labelsRepository.getByKeyAndLanguage("otp.resend.timer.message", languageCode)
        response.resendTimerMessageSeconds =
            labelsRepository.getByKeyAndLanguage(
                "otp.resend.timer.message.seconds",
                languageCode
            )

        response.resendButton = labelsRepository.getByKeyAndLanguage("otp.resend.button", languageCode)
        response.submitButton = labelsRepository.getByKeyAndLanguage("otp.submit.button", languageCode)
        response.otpRequiredMessage = labelsRepository.getByKeyAndLanguage("otp.required.message", languageCode)
        response.redirectToLoginPageMessage =
            labelsRepository.getByKeyAndLanguage(
                "otp.redirect.to.login.message",
                languageCode
            )

        return response
    }
}