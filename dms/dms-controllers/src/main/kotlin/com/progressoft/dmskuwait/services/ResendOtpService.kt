//package com.progressoft.dmskuwait.services
//
//import com.progressoft.dms.entities.DMS_OTP
//import com.progressoft.dms.entities.DMS_OTP.Companion.SESSION_ID
//import com.progressoft.dms.entities.DMS_OTP.Companion.USERNAME
//import com.progressoft.dmskuwait.dto.ResendOtpRequest
//import com.progressoft.dmskuwait.dto.ResendOtpResponse
//import com.progressoft.dmskuwait.exceptions.OTPValidationException
//import com.progressoft.dmskuwait.utils.RSADecryptor
//import com.progressoft.dmskuwait.utils.otp.generator.OtpGenerator
//import com.progressoft.dmskuwait.utils.otp.generator.emailsender.OtpEmailSender
//import com.progressoft.jfw.Query
//import com.progressoft.jfw.labels.Labels
//import com.progressoft.jfw.model.dao.item.ItemDao
//import com.progressoft.jupiter.authentication.strategy.api.AuthenticationStatus
//import lombok.extern.slf4j.Slf4j
//import org.slf4j.LoggerFactory
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.beans.factory.annotation.Value
//import org.springframework.http.HttpStatus
//import org.springframework.http.ResponseEntity
//import org.springframework.stereotype.Component
//import java.util.*
//
//@Component
//@Slf4j
//open class ResendOtpService @Autowired constructor(
//    private val itemDao: ItemDao?,
//    private val otpGenerator: OtpGenerator?,
//    @Value("#{\${dms.otp.max.validate.attempts}}") private val otpMaxResendAttempts: Int?,
//    private val decryptor: RSADecryptor?,
//    private val emailUtil: OtpEmailSender
//) {
//    open fun resendOtp(request: ResendOtpRequest): ResponseEntity<ResendOtpResponse> {
//        try {
//            val decryptedSessionId: String = decryptor!!.decrypt(request.sessionId!!)
//            val existingOtp: DMS_OTP = findExistingOtp(decryptedSessionId, request.userName!!)
//            checkResendRequest(existingOtp)
//            return handleResendingOtp(existingOtp)
//        } catch (ex: OTPValidationException) {
//            LOG.warn(
//                "OTP Validation Exception Occurs | Username {} |  Exception Message {}",
//                request.userName,
//                ex.exceptionMessage
//            )
//            return buildResponse(AuthenticationStatus.FAILD.name, ex.responseMessage, ex.httpStatus)
//        } catch (exception: Exception) {
//            LOG.error(
//                "Exception Occurs In OTP-Resend | Username {} | ErrorMessage {}",
//                request.userName,
//                exception.message,
//                exception
//            )
//            return buildResponse(
//                AuthenticationStatus.FAILD.name,
//                Labels.description("otp.internal.system.error"),
//                HttpStatus.INTERNAL_SERVER_ERROR
//            )
//        }
//    }
//
//    private fun checkResendRequest(existingOtp: DMS_OTP) {
//        if (Objects.isNull(existingOtp)) {
//            throw OTPValidationException(HttpStatus.CONFLICT, Labels.description("otp.not.found"), "OTP Not Found")
//        } else if (isReachedMaxResendAttempts(existingOtp)) {
//            itemDao!!.removeItem(existingOtp)
//            throw OTPValidationException(
//                HttpStatus.TOO_MANY_REQUESTS,
//                Labels.description("otp.max.resend.attempts.reached"),
//                "OTP Max Resend Attempts Reached"
//            )
//        }
//    }
//
//    private fun handleResendingOtp(existingOtp: DMS_OTP): ResponseEntity<ResendOtpResponse> {
//        LOG.debug("About To Generate New OTP For User: {}", existingOtp.username)
//        val updateOtp: DMS_OTP = otpGenerator!!.generateAndUpdateOtp(existingOtp)
//        LOG.debug("New OTP Was Generated For User: {}", updateOtp.username)
//        emailUtil.send(updateOtp.username, updateOtp.otp)
//        return buildResponse(AuthenticationStatus.SUCCESS.name, Labels.description("login.success"), HttpStatus.OK)
//    }
//
//    private fun findExistingOtp(sessionId: String, userName: String): DMS_OTP {
//        return Query(itemDao)
//            .equals(SESSION_ID, sessionId)
//            .first(DMS_OTP::class.java, USERNAME, userName)
//    }
//
//    private fun isReachedMaxResendAttempts(existingOtp: DMS_OTP): Boolean {
//        return existingOtp.reSendAttempts >= otpMaxResendAttempts!!
//    }
//
//    private fun buildResponse(
//        status: String,
//        message: String,
//        conflict: HttpStatus
//    ): ResponseEntity<ResendOtpResponse> {
//        val response = ResendOtpResponse()
//        response.status = status
//        response.message = message
//        return ResponseEntity.status(conflict).body(response)
//    }
//
//    companion object {
//        private val LOG = LoggerFactory.getLogger(ResendOtpService::class.java)
//    }
//}
