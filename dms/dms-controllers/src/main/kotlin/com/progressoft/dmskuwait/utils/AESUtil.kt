package com.progressoft.dmskuwait.utils

import java.nio.charset.StandardCharsets.UTF_8
import java.security.SecureRandom
import java.util.*
import java.util.Base64.getDecoder
import java.util.Base64.getEncoder
import javax.crypto.Cipher
import javax.crypto.SecretKey
import javax.crypto.SecretKeyFactory
import javax.crypto.spec.GCMParameterSpec
import javax.crypto.spec.PBEKeySpec
import javax.crypto.spec.SecretKeySpec

class AESUtil {

    companion object {
        private const val AES_ALGORITHM = "AES/GCM/NoPadding"
        private const val IV_LENGTH = 12
        private const val TAG_LENGTH = 128
        private const val SALT_LENGTH = 16
        private const val ITERATION_COUNT = 65536
        private const val KEY_LENGTH = 256
        private const val AES = "AES"
        private const val ALGORITHM = "PBKDF2WithHmacSHA256"

        @Throws(Exception::class)
        fun encrypt(plainText: String, key: ByteArray): String {
            val salt = generateRandomBytes(SALT_LENGTH)
            val iv = generateRandomBytes(IV_LENGTH)
            val secretKey = deriveKey(String(key, UTF_8), salt)

            val cipher = Cipher.getInstance(AES_ALGORITHM)
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, GCMParameterSpec(TAG_LENGTH, iv))
            val encryptedBytes = cipher.doFinal(plainText.toByteArray(UTF_8))

            val combined = salt + iv + encryptedBytes
            return getEncoder().encodeToString(combined)
        }

        @Throws(Exception::class)
        fun decrypt(encryptedText: String, key: ByteArray): String {
            val decodedBytes = getDecoder().decode(encryptedText)

            val salt = decodedBytes.copyOfRange(0, SALT_LENGTH)
            val iv = decodedBytes.copyOfRange(SALT_LENGTH, SALT_LENGTH + IV_LENGTH)
            val cipherText = decodedBytes.copyOfRange(SALT_LENGTH + IV_LENGTH, decodedBytes.size)

            val secretKey = deriveKey(String(key, UTF_8), salt)
            val cipher = Cipher.getInstance(AES_ALGORITHM)
            cipher.init(Cipher.DECRYPT_MODE, secretKey, GCMParameterSpec(TAG_LENGTH, iv))

            val decryptedBytes = cipher.doFinal(cipherText)
            return String(decryptedBytes, UTF_8)
        }

        private fun generateRandomBytes(size: Int): ByteArray {
            val randomBytes = ByteArray(size)
            SecureRandom().nextBytes(randomBytes)
            return randomBytes
        }

        private fun deriveKey(key: String, salt: ByteArray): SecretKey {
            val factory = SecretKeyFactory.getInstance(ALGORITHM)
            val spec = PBEKeySpec(key.toCharArray(), salt, ITERATION_COUNT, KEY_LENGTH)
            val secretKey = factory.generateSecret(spec)
            return SecretKeySpec(secretKey.encoded, AES)
        }
    }
}