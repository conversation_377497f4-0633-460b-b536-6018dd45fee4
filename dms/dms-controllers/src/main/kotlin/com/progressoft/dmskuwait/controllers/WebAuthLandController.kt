package com.progressoft.dmskuwait.controllers

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm.HMAC256
import com.progressoft.dmskuwait.dto.TokenAuthenticationResponse
import com.progressoft.dmskuwait.dto.TokenUserInfoResponse
import com.progressoft.dmskuwait.repository.UserRepository
import com.progressoft.dmskuwait.services.DmsLoginService
import com.progressoft.dmskuwait.services.JwtTokenService
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.EXPIRY
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.JWT_TOKEN
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.JWT_TOKEN_EXP
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.LANGUAGE
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.LOCALE
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.REFRESH_TOKEN
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.REFRESH_TOKEN_EXP
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.TENANT
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.USERNAME
import com.progressoft.dmskuwait.utils.JwtTokenUtil.Companion.getClaimStringFromToken
import com.progressoft.dmskuwait.validation.TokenValidation
import com.progressoft.jupiter.authentication.strategy.api.AuthenticationStatus.SUCCESS
import com.progressoft.jupiter.rest.security.authenticate.request.AuthenticationRequest
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.apache.commons.lang3.StringUtils.EMPTY
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.view.RedirectView
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.*
import javax.ws.rs.core.Response
import javax.ws.rs.core.Response.serverError


@RestController
class WebAuthLandController @Autowired constructor(
    private val tokenValidation: TokenValidation,
    private val userRepository: UserRepository,
    private val loginService: DmsLoginService,
    private val tokenService: JwtTokenService
) {

    @PostMapping("/auth/token/authenticate")
    fun jwtTokenLogin(request: HttpServletRequest): Response {
        try {
            val jwtToken = request.getHeader("Authorization")
            LOGGER.info("---------------Starting validating JWT Token---------------")
            val isValid = tokenValidation.validate(jwtToken)
            LOGGER.info("---------------JWT token verified successfully---------------")
            if (!isValid)
                return serverError().entity("Invalid Token").build()

            val authenticate =
                loginService.authenticate(getAuthenticationRequest(jwtToken), request.remoteAddr, EMPTY, true)
            if (authenticate.status != SUCCESS.name)
                return serverError().entity(authenticate.message).build()

            val loginInfo = loginService.loginUsingToken(jwtToken, request.remoteAddr).body!!
            if (loginInfo.status != SUCCESS.name)
                return serverError().entity(loginInfo.message).build()

            return Response.ok(
                htmlForm(
                    TokenAuthenticationResponse(
                        generateJwtTokenForUserInfo(loginInfo),
                        url(request)
                    )
                )
            ).build()
        } catch (e: Exception) {
            LOGGER.error("Invalid authenticate request : {}", e.message)
            return serverError().entity("Invalid authenticate request").build()
        }
    }


    @GetMapping("/auth/token/login")
    fun validateAndSaveToken(request: HttpServletRequest, response: HttpServletResponse): RedirectView {
        try {
            LOGGER.info("---------------Starting validating login Token---------------")
            val token = request.getParameter("authenticationToken")
            val isValid = tokenValidation.validateUserInfoToken(token)
            LOGGER.info("---------------Login token verified successfully---------------")
            if (isValid) {
                addNewCookie(response, JWT_TOKEN, getClaimStringFromToken(token, JWT_TOKEN))
                addNewCookie(response, JWT_TOKEN_EXP, getClaimStringFromToken(token, JWT_TOKEN_EXP))
                addNewCookie(response, REFRESH_TOKEN, getClaimStringFromToken(token, REFRESH_TOKEN))
                addNewCookie(response, REFRESH_TOKEN_EXP, getClaimStringFromToken(token, REFRESH_TOKEN_EXP))
                addNewCookie(response, TENANT, getClaimStringFromToken(token, TENANT))
                addNewCookie(response, USERNAME, getClaimStringFromToken(token, USERNAME))
                addNewCookie(response, LANGUAGE, getClaimStringFromToken(token, LANGUAGE))
                return RedirectView("/")
            }
            return RedirectView("/")
        } catch (e: Exception) {
            LOGGER.error("Invalid save token request : {}", e.message)
            return RedirectView("/")
        }
    }

    private fun addNewCookie(response: HttpServletResponse, key: String, value: String) {
        response.addHeader("Set-Cookie", "${key}=${value}; Path=/; Max-Age=120;")
    }

    private fun getAuthenticationRequest(jwtToken: String): AuthenticationRequest {
        val username = getClaimStringFromToken(jwtToken, USERNAME)
        val userInfo = userRepository.findByUsername(username).get()
        val request = AuthenticationRequest()
        request.username = username
        request.password = tokenService.getUsersPassword()
        request.tenant = userInfo.tenantId
        request.language = getClaimStringFromToken(jwtToken, LOCALE)
        return request
    }

    private fun generateJwtTokenForUserInfo(loginInfo: TokenUserInfoResponse) = JWT.create()
        .withClaim(USERNAME, loginInfo.username)
        .withClaim(TENANT, loginInfo.tenant)
        .withClaim(LANGUAGE, loginInfo.language)
        .withClaim(JWT_TOKEN, loginInfo.jwt)
        .withClaim(JWT_TOKEN_EXP, loginInfo.jwtExp.toString())
        .withClaim(REFRESH_TOKEN, loginInfo.refreshToken)
        .withClaim(REFRESH_TOKEN_EXP, loginInfo.refreshTokenExp.toString())
        .withClaim(EXPIRY, getExpiryDate())
        .sign(HMAC256(tokenService.getSecretValue()))

    private fun getExpiryDate() =
        Date.from(LocalDateTime.now().plusMinutes(1).atZone(ZoneId.systemDefault()).toInstant())

    companion object {
        private val LOGGER: Logger = LoggerFactory.getLogger(WebAuthLandController::class.java)

        private fun url(req: HttpServletRequest): String {
            return String.format("%s://%s:%s%s", req.scheme, req.serverName, req.serverPort, "/auth/token/login")
        }

        private fun htmlForm(response: TokenAuthenticationResponse): String {
            return "<form name='jfwLoginForm' action= '" + response.url + "' method='get'>" +
                    "<input type='hidden' name='authenticationToken' value='" + response.generatedJwtToken + "'>" +
                    "<input type='submit' value='Submit' style='display:none;'>" +
                    "</form>"
        }
    }
}