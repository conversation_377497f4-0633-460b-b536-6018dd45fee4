package com.progressoft.dmskuwait.utils

import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jfw.notifications.entity.JFWGenericConfiguration
import com.progressoft.jupiter.security.rsa.RSA
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import java.util.*

@Component
open class RSADecryptor @Autowired constructor(val rsa: RSA?, val itemDao: ItemDao?) {
    open fun decrypt(encryptedString: String): String {
        return rsa!!.decrypt(
            Base64.getDecoder().decode(encryptedString.toByteArray()),
            rsa.readPrivateKey(
                itemDao!!.getItem(
                    JFWGenericConfiguration::class.java,
                    "configKey",
                    "private.key"
                ).configValue
            )
        )
    }
}
