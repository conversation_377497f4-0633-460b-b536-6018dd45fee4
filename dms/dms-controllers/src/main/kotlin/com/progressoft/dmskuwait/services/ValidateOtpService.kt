package com.progressoft.dmskuwait.services

import com.progressoft.dms.entities.DMS_OTP
import com.progressoft.dms.entities.DMS_OTP.Companion.SESSION_ID
import com.progressoft.dms.entities.DMS_OTP.Companion.USERNAME
import com.progressoft.dmskuwait.dto.ValidateOtpRequest
import com.progressoft.dmskuwait.dto.ValidateOtpResponse
import com.progressoft.dmskuwait.exceptions.OTPValidationException
import com.progressoft.dmskuwait.utils.RSADecryptor
import com.progressoft.jfw.Query
import com.progressoft.jfw.labels.Labels
import com.progressoft.jfw.model.bussinessobject.security.DefaultExtendedUserDetails
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.jfw.model.service.security.JfwAuthentication
import com.progressoft.jupiter.authentication.strategy.api.AuthenticationStatus
import com.progressoft.jupiter.authentication.strategy.api.JwtHelper
import com.progressoft.jupiter.authentication.strategy.api.complete.CompleteAuthentication
import com.progressoft.jupiter.authentication.strategy.api.complete.Constants
import com.progressoft.jupiter.authentication.strategy.api.request.AuthenticationRequestImpl
import com.progressoft.jupiter.authentication.strategy.api.response.AuthenticationResponse
import com.progressoft.jupiter.secrurity.secrets.SecretsLoader
import com.progressoft.jupiter.secrurity.secrets.impl.SecretName
import lombok.extern.slf4j.Slf4j
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatus.*
import org.springframework.http.ResponseEntity
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.authentication.dao.DaoAuthenticationProvider
import org.springframework.stereotype.Component
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*


@Component
@Slf4j
open class ValidateOtpService(
    private val itemDao: ItemDao?,
    private val authenticationCompleter: CompleteAuthentication?,
    private val daoAuthenticationProvider: DaoAuthenticationProvider?,
    private val decryptor: RSADecryptor?,
    private val secretsLoader: SecretsLoader?,
    @Value("#{\${dms.otp.max.validate.attempts}}") private val otpMaxValidateAttempts: Int
) {
    open fun validateOtp(request: ValidateOtpRequest, remoteAddr: String): ResponseEntity<ValidateOtpResponse> {
        try {
            val decryptedSessionId: String = decryptor!!.decrypt(request.sessionId!!)
            val decryptedOtp: String = decryptor.decrypt(request.otp!!)
            val existingOtp: DMS_OTP = findOTP(decryptedSessionId, request.userName!!)
            validateOTPRequest(existingOtp)
            return processOTPMatching(request, existingOtp, remoteAddr, decryptedOtp)
        } catch (ex: OTPValidationException) {
            LOG.warn(
                "OTP Validation Exception Occurs | Username {} |  Exception Message {}",
                request.userName,
                ex.exceptionMessage
            )
            return buildResponse(AuthenticationStatus.FAILD.name, ex.responseMessage, ex.httpStatus)
        } catch (exception: Exception) {
            LOG.error(
                "Exception Occurs In OTP-Validation | Username {} | ErrorMessage {}",
                request.userName,
                exception.message,
                exception
            )
            return buildResponse(
                AuthenticationStatus.FAILD.name,
                Labels.description("otp.internal.system.error"),
                INTERNAL_SERVER_ERROR
            )
        }
    }

    private fun validateOTPRequest(existingOtp: DMS_OTP) {
        if (Objects.isNull(existingOtp)) {
            throw OTPValidationException(CONFLICT, Labels.description("otp.not.found"), "OTP Not Found")
        }
        if (isReachedMaxAttempts(existingOtp)) {
            itemDao!!.removeItem(existingOtp)
            throw OTPValidationException(
                TOO_MANY_REQUESTS,
                Labels.description("otp.max.validation.attempts.reached"),
                "OTP Max Validation Attempts Reached"
            )
        }
        if (isExpiredExistingOtp(existingOtp)) {
            existingOtp.validateAttempts += 1
            itemDao!!.persist(existingOtp)
            throw OTPValidationException(GONE, Labels.description("otp.expired"), "OTP Expired")
        }
    }

    private fun processOTPMatching(
        request: ValidateOtpRequest,
        existingOtp: DMS_OTP,
        remoteAddr: String,
        decryptedOtp: String
    ): ResponseEntity<ValidateOtpResponse> {
        if (existingOtp.otp == decryptedOtp) {
            LOG.debug("OTP Validated Successfully For User: " + request.userName)
            itemDao!!.removeItem(existingOtp)
            return completeAuthentication(request, remoteAddr)
        } else {
            LOG.debug("OTP Not Matched For User: " + request.userName)
            existingOtp.validateAttempts += 1
            itemDao!!.persist(existingOtp)
            return buildResponse(
                AuthenticationStatus.FAILD.name,
                Labels.description("otp.not.matched"),
                UNAUTHORIZED
            )
        }
    }

    private fun completeAuthentication(
        request: ValidateOtpRequest,
        remoteAddr: String
    ): ResponseEntity<ValidateOtpResponse> {
        val usernameTenant: String = request.userName + "/" + request.tenant
        val decryptedPassword: String = decryptor!!.decrypt(request.password!!)
        val authentication = daoAuthenticationProvider!!.authenticate(
            UsernamePasswordAuthenticationToken(
                usernameTenant,
                decryptedPassword
            )
        )
        val jfwAuthentication = JfwAuthentication(authentication, request.tenant)
        val user =
            Query(itemDao).equals("username", request.userName).equals("tenantId", request.tenant).first(
                User::class.java
            )
        user.failedLoginAttempts = 0
        itemDao!!.merge(user)
        val authenticationRequest = AuthenticationRequestImpl(
            request.userName,
            decryptedPassword,
            request.tenant,
            request.language,
            remoteAddr
        )
        val completeResponse = authenticationCompleter!!.complete(
            jfwAuthentication,
            authentication.principal as DefaultExtendedUserDetails,
            authenticationRequest
        )
        return buildResponse(completeResponse, request.userName!!)
    }

    private fun buildResponse(
        status: String,
        message: String,
        httpStatus: HttpStatus
    ): ResponseEntity<ValidateOtpResponse> {
        val response = ValidateOtpResponse()
        response.status = status
        response.message = message
        return ResponseEntity.status(httpStatus).body(response)
    }

    private fun buildResponse(
        completeResponse: AuthenticationResponse,
        userName: String
    ): ResponseEntity<ValidateOtpResponse> {
        if (completeResponse.status() == AuthenticationStatus.FAILD) {
            return buildResponse(completeResponse.status().name, completeResponse.message(), UNAUTHORIZED)
        } else {
            val response = ValidateOtpResponse()
            response.status = completeResponse.status().name
            response.message = completeResponse.message()
            response.jwt = completeResponse.token()
            response.refreshToken = completeResponse.refreshToken()
            response.jwtExp = getJwtExp(completeResponse)
            response.refreshTokenExp = getRefreshExp(completeResponse)
            response.username = userName
            return ResponseEntity.status(OK).body(response)
        }
    }

    private fun getRefreshExp(completeResponse: AuthenticationResponse): Long {
        return JwtHelper.getJwtClaims(
            completeResponse.refreshToken(),
            secretsLoader!!.loadSecret(SecretName.JWT_SECRET),
            Constants.REFRESH
        )
            .expiration.toInstant().toEpochMilli()
    }

    private fun getJwtExp(completeResponse: AuthenticationResponse): Long {
        return JwtHelper.getJwtClaims(
            completeResponse.token(),
            secretsLoader!!.loadSecret(SecretName.JWT_SECRET),
            Constants.BEARER
        )
            .expiration.toInstant().toEpochMilli()
    }

    private fun isReachedMaxAttempts(existingOtp: DMS_OTP): Boolean {
        return existingOtp.validateAttempts >= otpMaxValidateAttempts
    }

    private fun isExpiredExistingOtp(existingOtp: DMS_OTP): Boolean {
        return Timestamp.valueOf(LocalDateTime.now()).after(existingOtp.expiryTime)
    }


    private fun findOTP(sessionId: String, userName: String): DMS_OTP {
        return Query(itemDao)
            .equals(SESSION_ID, sessionId)
            .first(DMS_OTP::class.java, USERNAME, userName)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ValidateOtpService::class.java)
    }
}
