package com.progressoft.dmskuwait.controllers

import com.progressoft.dms.entities.DMS_Correspondence
import com.progressoft.dms.entities.DMS_DisputeCase
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.CASE_REF_NO
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.CURRENCY
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.DEFENDANT_BANK
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.DISPUTE_AMOUNT
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.DISPUTE_OVER_DISPUTE
import com.progressoft.dms.entities.DMS_DisputeCase.Companion.PAYMENT_SYSTEM
import com.progressoft.dmskuwait.dto.DisputeCaseDto
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.dao.item.ItemDao
import com.progressoft.psdms.application.exceptions.FetchDisputesException
import com.progressoft.psdms.application.utils.Constants.Companion.APPROVED_CLOSED_DISPUTE_STATUS_CODE
import com.progressoft.psdms.application.utils.Constants.Companion.CLOSED_DISPUTE_STATUS_CODE
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.support.PagedListHolder
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
class DisputeCaseReferenceController(
    @Autowired private val itemDao: ItemDao,
) {
    @GetMapping("/api/views/fetch-disputes/lookup-grid-data")
    fun listActiveDisputes(
        @PageableDefault pageable: Pageable,
        @RequestParam parameters: MutableMap<String, String?>
    ): ResponseEntity<Page<out DisputeCaseDto>> {
        try {
            val itemId = parameters["itemId"]?.toLong()
            val disputeCase = itemDao.getItem(DMS_DisputeCase::class.java, "id", itemId)
            val disputeList: List<DisputeCaseDto> = pullDisputeCases(disputeCase, parameters)
            return ResponseEntity.ok().body(preparePages(disputeList, pageable))
        } catch (ex: Exception) {
            throw FetchDisputesException(ex)
        }
    }

    private fun pullDisputeCases(
        disputeCase: DMS_DisputeCase?,
        parameters: MutableMap<String, String?>
    ): List<DisputeCaseDto> {
        val approvedDisputesIds = pullApprovedDisputes()
        val closedByOperatorIds = pullOperatorApprovedDisputes()

        val activeApprovedDisputes = getActiveApprovedDisputes(disputeCase, approvedDisputesIds, parameters)
        val closedApprovedDisputes = getApprovedClosedDisputes(disputeCase, approvedDisputesIds + closedByOperatorIds, parameters)
        val closedDtoList = mapToDtoList(closedApprovedDisputes)
        val activeDtoList = mapToDtoList(activeApprovedDisputes)
        return if (closedDtoList.isNotEmpty() && activeDtoList.isNotEmpty())
            closedDtoList + activeDtoList
        else if (closedDtoList.isNotEmpty())
            closedDtoList
        else activeDtoList
    }

    private fun pullApprovedDisputes() = Query(itemDao)
        .equals("action", "Approve Dispute")
        .`in`("actionBy", "Assignee Bank", "Operator", "Defendant NCB")
        .items(DMS_Correspondence::class.java)
        .map { it.dispute.id }

    private fun pullOperatorApprovedDisputes() = Query(itemDao)
        .equals("action", "Dispute closed")
        .equals("actionBy", "Operator")
        .items(DMS_Correspondence::class.java)
        .map { it.dispute.id }

    private fun getApprovedClosedDisputes(
        disputeCase: DMS_DisputeCase?,
        approvedDisputesIds: List<Long>,
        parameters: MutableMap<String, String?>
    ): List<DMS_DisputeCase> = Query(itemDao)
        .idNotEquals(disputeCase!!.id)
        .apply {
            if (approvedDisputesIds.isNotEmpty()) {
                idIn(*approvedDisputesIds.toLongArray())
            }
        }
        .isFalse(DISPUTE_OVER_DISPUTE)
        .statusCodeIn(APPROVED_CLOSED_DISPUTE_STATUS_CODE, CLOSED_DISPUTE_STATUS_CODE)
        .equals("$DEFENDANT_BANK.code", disputeCase.claimantBank?.code)
        .equals("$PAYMENT_SYSTEM.code", disputeCase.paymentSystem?.code)
        .equalsIf(parameters["caseReferenceNumber"]!!.isNotBlank(), CASE_REF_NO, parameters["caseReferenceNumber"])
        .equalsIf(parameters["currency"]!!.isNotBlank(), "$CURRENCY.code", parameters["currency"])
        .greaterThanOrEqualsIf(
            parameters["fromAmount"]!!.isNotBlank() && parameters["fromAmount"] != "null",
            DISPUTE_AMOUNT,
            parameters["fromAmount"]
        )
        .lessThanOrEqualsIf(
            parameters["toAmount"]!!.isNotBlank() && parameters["toAmount"] != "null",
            DISPUTE_AMOUNT,
            parameters["toAmount"]
        )
        .items(DMS_DisputeCase::class.java)

    private fun getActiveApprovedDisputes(
        disputeCase: DMS_DisputeCase?,
        approvedDisputesIds: List<Long>,
        parameters: MutableMap<String, String?>
    ): List<DMS_DisputeCase> {
        val activeApprovedDisputes = Query(itemDao)
            .idNotEquals(disputeCase!!.id)
            .apply {
                if (approvedDisputesIds.isNotEmpty()) {
                    idIn(*approvedDisputesIds.toLongArray())
                }
            }
            .isFalse(DISPUTE_OVER_DISPUTE)
            .equals("lastAction", "Approve Dispute")
            .equals("$DEFENDANT_BANK.code", disputeCase.claimantBank?.code)
            .equals("$PAYMENT_SYSTEM.code", disputeCase.paymentSystem?.code)
            .equalsIf(parameters["caseReferenceNumber"]!!.isNotBlank(), CASE_REF_NO, parameters["caseReferenceNumber"])
            .equalsIf(parameters["currency"]!!.isNotBlank(), "$CURRENCY.code", parameters["currency"])
            .greaterThanOrEqualsIf(
                parameters["fromAmount"]!!.isNotBlank() && parameters["fromAmount"] != "null",
                DISPUTE_AMOUNT,
                parameters["fromAmount"]
            )
            .lessThanOrEqualsIf(
                parameters["toAmount"]!!.isNotBlank() && parameters["toAmount"] != "null",
                DISPUTE_AMOUNT,
                parameters["toAmount"]
            )
            .items(DMS_DisputeCase::class.java)
        return activeApprovedDisputes
    }

    private fun mapToDtoList(entities: List<DMS_DisputeCase>): List<DisputeCaseDto> {
        return entities.map { entity ->
            val dto = DisputeCaseDto()
            dto.caseReferenceNumber = entity.caseReferenceNumber.toString()
            dto.paymentSystem = entity.paymentSystem!!.code
            dto.claimantBank = entity.claimantBank!!.code
            dto.defendantBank = entity.defendantBank!!.code
            dto.transactionCurrency = entity.transactionCurrency!!.codeNamePair
            dto.disputedAmount = entity.disputedAmount!!
            dto.transactionAmount = entity.transactionAmount!!
            dto.transactionDate = entity.transactionDate.toString()
            dto
        }
    }

    private fun preparePages(
        disputeList: List<DisputeCaseDto>,
        pageable: Pageable
    ): PageImpl<DisputeCaseDto> {
        val pages: PagedListHolder<out DisputeCaseDto> = PagedListHolder(disputeList)
        pages.page = pageable.pageNumber
        pages.pageSize = pageable.pageSize
        return PageImpl(pages.pageList, pageable, disputeList.size.toLong())
    }
}
