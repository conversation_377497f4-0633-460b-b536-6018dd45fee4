package com.progressoft.dmskuwait.functions

import com.progressoft.dms.entities.DMS_TermsAndConditions
import com.progressoft.jfw.Query
import com.progressoft.jfw.model.bussinessobject.security.User
import com.progressoft.jfw.workflow.WfContext
import com.progressoft.jfw.workflow.WfFunction
import org.springframework.stereotype.Component

@Component
class ResetAcceptTermsFunction : WfFunction<DMS_TermsAndConditions?>() {

    override fun execute(context: WfContext<DMS_TermsAndConditions?>) {
        Query(itemDao).items(User::class.java).forEach {
            it.termsAccepted = false
            itemDao.merge(it)
        }
    }
}