package com.progressoft.dms.dsl.workflows

import com.progressoft.dms.dsl.entities.DMS_ClaimantNCBCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_DefendantBankCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_OperatorCaseManagement_Entity
import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_DefendantNCBCaseManagement_Workflow = Workflow {
    name = "WF_DMS_DefendantNCBCaseManagement"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "4001"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Neutral"
        result {
            displayName = "Initialization-Initialize-To-Create"
            nextStepKey = 6010
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "create_8118129619"
        doRefresh = true
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "4002"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Neutral"
        result {
            displayName = "Initialization-Create-To-New-Dispute"
            nextStepKey = 6010
        }
    }
    step {
        key = 6010
        name = "To-New-Dispute"
        editable = false
        statusCode = "6010"
        action {
            key = 60101
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60101"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-New-Dispute-Accept-To-ApprveAcceptance"
                nextStepKey = 6011
            }
        }
        action {
            key = 60102
            name = "Reject"
            label = "reject_8118130040"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60102"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-New-Dispute-Reject-To-ApproveRejection"
                nextStepKey = 6012
            }
        }
        action {
            key = 60103
            name = "Ask Defendant Bank"
            label = "ask.defendant.bank"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60103"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-New-Dispute-Request-Additional-Info-To-Clmt-NCB-More-Info-From-Bank-Edit"
                nextStepKey = 7011
            }
        }
        action {
            key = 60104
            name = "Ask Claimant NCB"
            label = "ask.claimant.ncb"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60104"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-New-Dispute-Request-Additional-Info-To-Clmt-NCB-More-Info-From-NCB-Edit"
                nextStepKey = 7016
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approved_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-New-Dispute-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 6014
            }
        }
        action {
            key = 1148821254
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "1148821254"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Declined_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTNCB"
                }
            }
            result {
                displayName = "To-New-Dispute-Dispute-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 6015
            }
        }

        action {
            key = 1148821255
            name = "SVC_SLA_Arbitrate"
            label = "svc_sla_arbitrate"
            actionCode = "1148821255"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "arbitrateDisputeCase"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "arbitrateDisputeCase"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_OperatorCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Arbitrate Dispute To Operator"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            result {
                displayName = "To-New-Dispute-Dispute-SVC_SLA_Arbitrate-To-ApproveAcceptance"
                nextStepKey = 6014
            }
        }
    }
    // ************************* Additional Info Required *************************
    step {
        key = 7011
        name = "To-Clmt-NCB-More-Info-From-Bank-Edit"
        editable = true
        statusCode = "7011"
        action {
            key = 70111
            name = "Send Request"
            label = "claimant.ncb.sendrequest"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-More-Info-From-Bank-Edit-Send Request-To-NCB-RequestAdditionalInfo-Approval"
                nextStepKey = 7012
            }
        }
        action {
            key = 70112
            name = "Cancel"
            label = "claimant.ncb.cancel"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Claimant-NCB-More-Info-Claimant-Bank-Edit-Cancel-To-New-Dispute"
                nextStepKey = 6010
            }
        }
    }
    step {
        key = 7016
        name = "To-Clmt-NCB-More-Info-From-NCB-Edit"
        editable = true
        statusCode = "7016"
        action {
            key = 70161
            name = "Send Request"
            label = "claimant.ncb.sendrequest"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70161"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-More-Info-From-Bank-Edit-Send Request-To-NCB-RequestAdditionalInfo-Approval"
                nextStepKey = 7017
            }
        }
        action {
            key = 70162
            name = "Cancel"
            label = "claimant.ncb.cancel"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Claimant-NCB-More-Info-Claimant-Bank-Edit-Cancel-To-New-Dispute"
                nextStepKey = 6010
            }
        }
    }
    step {
        key = 7012
        name = "To-NCB-RequestAdditionalInfo-Approval"
        editable = false
        statusCode = "7012"
        action {
            key = 70121
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70121"
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfo"
                }
                argument {
                    name = "lastAction"
                    value = "Request More Info From Assignee Bank"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            result {
                displayName =
                    "To-NCB-RequestAdditionalInfo-Approval-Approve Request-To-NCB-To-Bank-ApprovedAdditionalInfoRequest"
                nextStepKey = 7013
            }
        }
        action {
            key = 70122
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "70122"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-RequestAdditionalInfo-DeclineRequest-To-NCB-More-Info-From-Bank-Repair"
                nextStepKey = 7014
            }
        }
    }
    step {
        key = 7017
        name = "To-NCB-RequestAdditionalInfo-Approval"
        editable = false
        statusCode = "7017"
        action {
            key = 70171
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70171"
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfo"
                }
                argument {
                    name = "lastAction"
                    value = "Request More Info From Claimant NCB"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            result {
                displayName =
                    "To-NCB-RequestAdditionalInfo-Approval-Approve Request-To-NCB-To-Bank-ApprovedAdditionalInfoRequest"
                nextStepKey = 7013
            }
        }
        action {
            key = 70122
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "70122"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-RequestAdditionalInfo-DeclineRequest-To-NCB-More-Info-From-Bank-Repair"
                nextStepKey = 7014
            }
        }
    }
    step {
        key = 7013
        name = "To-NCB-To-Bank-ApprovedAdditionalInfoRequest"
        editable = false
        statusCode = "7013"
        action {
            key = 100131
            name = "SVC_MoreInfoProvided"
            label = "JFW_WF_ACTION.*********"
            actionCode = "100131"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName =
                    "To-NCB-To-Bank-ApprovedAdditionalInfoRequest-SVC_NCB_MoreInfoProvided-To-MoreInfoProvided"
                nextStepKey = 7015
            }
        }
        action {
            key = **********
            name = "SVC_Approved_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_Approved_Dispute-To-Approved_Dispute"
                nextStepKey = 6014
            }
        }
    }

    step {
        key = 7014
        name = "To-NCB-More-Info-From-Bank-Repair"
        editable = false
        statusCode = "7014"
        action {
            key = 70141
            name = "Repair"
            label = "claimant.ncb.repair"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70141"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-NCB-More-Info-From-Bank-Repair-To-New-Dispute"
                nextStepKey = 6010
            }
        }
    }

    step {
        key = 7015
        name = "To-MoreInfoProvided"
        editable = false
        statusCode = "7015"
        action {
            key = 70152
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70152"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-MoreInfoProvided-Accept-To-ApproveAcceptance"
                nextStepKey = 6011
            }
        }
        action {
            key = 70153
            name = "Reject"
            label = "reject_8118130040"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70153"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-MoreInfoProvided-Reject-To-ApproveRejection"
                nextStepKey = 6012
            }
        }
        action {
            key = 70151
            name = "Ask Defendant Bank"
            label = "ask.defendant.bank"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "70151"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-MoreInfoProvided-Request-Additional-Info-To-Clmt-NCB-More-Info-From-Bank-Edit"
                nextStepKey = 7011
            }
        }
        action {
            key = 70154
            name = "Ask Claimant NCB"
            label = "ask.claimant.ncb"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60104"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-New-Dispute-Request-Additional-Info-To-Clmt-NCB-More-Info-From-NCB-Edit"
                nextStepKey = 7016
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approved_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-New-Dispute-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 6014
            }
        }
        action {
            key = 1148821254
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "1148821254"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Declined_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTNCB"
                }
            }
            result {
                displayName = "To-New-Dispute-Dispute-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 6015
            }
        }
    }

    step {
        key = 6011
        name = "To-Approve-Acceptance"
        editable = false
        statusCode = "6011"
        action {
            key = 60111
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approved_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-Approve-Acceptance-ApproveRequest-To-Approved-Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 60112
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "60112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-Approve-Acceptance-Decline-Request-DeclineRequest-To-New-Dispute"
                nextStepKey = 6010
            }
        }
    }
    step {
        key = 6012
        name = "To-Approve-Rejection"
        editable = false
        statusCode = "6012"
        action {
            key = 60121
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60121"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Declined_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTNCB"
                }
            }
            result {
                displayName = "To-Approve-Rejection-ApproveRequest-To-Declined-Dispute"
                nextStepKey = 6015
            }
        }
        action {
            key = 60122
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "60122"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-Approve-Rejection-DeclineRequest-To-Repair"
                nextStepKey = 6013
            }
        }
    }
    step {
        key = 6013
        name = "To-Repair"
        editable = false
        statusCode = "6013"
        action {
            key = 60131
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60131"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Repair-Accept-To-ApproveAcceptance"
                nextStepKey = 6011
            }
        }
        action {
            key = 60132
            name = "Reject"
            label = "reject_8118130040"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60132"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Repair-Reject-To-ApproveRejection"
                nextStepKey = 6012
            }
        }
        action {
            key = 60133
            name = "Ask Defendant Bank"
            label = "ask.defendant.bank"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60133"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Repair-Request-Additional-Info-To-Clmt-NCB-More-Info-From-Bank-Edit"
                nextStepKey = 7011
            }
        }
        action {
            key = 60134
            name = "Ask Claimant NCB"
            label = "ask.claimant.ncb"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60104"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-New-Dispute-Request-Additional-Info-To-Clmt-NCB-More-Info-From-NCB-Edit"
                nextStepKey = 7016
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approved_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-New-Dispute-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 6014
            }
        }
        action {
            key = 1148821254
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "1148821254"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Declined_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTNCB"
                }
            }
            result {
                displayName = "To-New-Dispute-Dispute-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 6015
            }
        }
    }

    step {
        key = 6015
        name = "To-Declined-Dispute"
        editable = false
        statusCode = "6015"
        action {
            key = 60151
            name = "SVC_Re_Present"
            label = "JFW_WF_ACTION.1148826015"
            actionCode = "60151"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Declined-Dispute-SVC_Approved_RePresent-To-Represent"
                nextStepKey = 6616
            }
        }
        action {
            key = 60152
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.185360253"
            actionCode = "60152"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Declined-Dispute-SVC_Operator_MoreInfo-To-Operator-Requires-More-Info"
                nextStepKey = 6600
            }
        }
        action {
            key = 60153
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "60153"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 60154
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.185399903"
            actionCode = "60154"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Decline-To-RejectedDispute"
                nextStepKey = 6015
            }
        }
        action {
            key = 60155
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.185381307"
            actionCode = "60155"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Accept-To-DeclinedDispute"
                nextStepKey = 6015
            }
        }
        action {
            key = 7066
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "7066"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    step {
        key = 6616
        name = "To-Represent"
        editable = false
        statusCode = "6616"
        action {
            key = 66161
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "66161"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Represent-Accept-To-ApproveAcceptance"
                nextStepKey = 6011
            }
        }
        action {
            key = 66162
            name = "Reject"
            label = "reject_8118130040"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "66162"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Represent-Reject-To-ApproveRejection"
                nextStepKey = 6012
            }
        }
        action {
            key = 66163
            name = "Ask Defendant Bank"
            label = "ask.defendant.bank"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "66163"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Represent-Request-Additional-Info-To-Clmt-NCB-More-Info-From-Bank-Edit"
                nextStepKey = 7011
            }
        }
        action {
            key = 66164
            name = "Ask Claimant NCB"
            label = "ask.claimant.ncb"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60104"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-New-Dispute-Request-Additional-Info-To-Clmt-NCB-More-Info-From-NCB-Edit"
                nextStepKey = 7016
            }
        }
        action {
            key = 60151
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "60151"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 60152
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.185399903"
            actionCode = "60152"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Decline-To-RejectedDispute"
                nextStepKey = 6015
            }
        }
        action {
            key = 60153
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.185360253"
            actionCode = "60153"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Represent-SVC_Operator_MoreInfo-To-Operator-Requires-More-Info"
                nextStepKey = 6600
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approved_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-New-Dispute-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 6014
            }
        }
        action {
            key = 1148821254
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "1148821254"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Declined_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTNCB"
                }
            }
            result {
                displayName = "To-New-Dispute-Dispute-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 6015
            }
        }
    }

    // ************ Provide More Info Requested from Defendant NCB ************
    step {
        key = 6600
        name = "To-Operator-Requires-More-Info"
        editable = false
        statusCode = "6600"
        action {
            key = 66001
            name = "Additional-Info"
            label = "defendant.provide-additional-info"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "66001"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            serviceAction = false
            actionLevel = "Maker Level"
            result {
                displayName = "To-Additional-Info-To-Provide-Additional-Info"
                nextStepKey = 7700
            }
        }
    }

    step {
        key = 7700
        name = "To-Provide-Additional-Info"
        editable = true
        statusCode = "7700"
        action {
            key = 77001
            name = "Save"
            label = "defendant.saveandsubmit"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "77001"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-To-ModifiedAdditionalInfo"
                nextStepKey = 8800
            }
        }
        action {
            key = 77002
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "77002"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-To-Additional-Info"
                nextStepKey = 6600
            }
        }
    }

    step {
        key = 8800
        name = "To-ModifiedAdditionalInfo"
        editable = false
        statusCode = "8800"
        action {
            key = 88001
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "88001"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_OperatorCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfoProvided"
                }
                argument {
                    name = "lastAction"
                    value = "Provide Additional Info To Operator"
                }
                argument {
                    name = "lastActionBy"
                    value = "Defendant NCB"
                }
            }
            result {
                displayName = "To-ModifiedAdditionalInfo-Approve-To-SubmitMoreInfoRequest"
                nextStepKey = 9990
            }
        }
        action {
            key = 88002
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "88002"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-ModifiedRequestApprove-Reject-To-MoreInfoRejected"
                nextStepKey = 10100
            }
        }
    }

    step {
        key = 9990
        name = "To-SubmitMoreInfoRequest"
        editable = false
        statusCode = "9990"
        action {
            key = 60152
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.185360253"
            actionCode = "60152"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Declined-Dispute-SVC_Operator_MoreInfo-To-Operator-Requires-More-Info"
                nextStepKey = 6600
            }
        }
        action {
            key = 99901
            name = "SVC_Re_Present"
            label = "JFW_WF_ACTION.1148826015"
            actionCode = "99901"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_Approved_Dispute-To-Approved_Dispute"
                nextStepKey = 6013
            }
        }
        action {
            key = 99902
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "99902"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 99903
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.185399903"
            actionCode = "99903"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Decline-To-RejectedDispute"
                nextStepKey = 6015
            }
        }
        action {
            key = 99904
            name = "SVC_Operator_MoreInfo"
            label = "JFW_WF_ACTION.185399904"
            actionCode = "99904"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Declined-Dispute-SVC_Operator_MoreInfo-To-ApproveRejection"
                nextStepKey = 6600
            }
        }
        action {
            key = 99905
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.185381307"
            actionCode = "99905"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_FinalDecline-To-RejectedDispute"
                nextStepKey = 6015
            }
        }
    }

    step {
        key = 10100
        name = "To-MoreInfoRejected"
        editable = false
        statusCode = "10100"
        action {
            key = 101001
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "101001"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Rejected-Repair-To-Provide-Additional-Info"
                nextStepKey = 7700
            }
        }
    }

    step {
        key = 6014
        name = "To-Approved-Dispute"
        editable = false
        statusCode = "6014"
        action {
            key = 6115
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "6115"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    step {
        key = 714
        name = "To-CloseDispute"
        editable = false
        statusCode = "714"
    }

}