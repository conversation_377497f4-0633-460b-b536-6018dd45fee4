package com.progressoft.dms.dsl.workflows

import com.progressoft.dms.dsl.entities.DMS_ClaimantBankCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_DefendantNCBCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_OperatorCaseManagement_Entity
import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_DefendantBankCaseManagement_Workflow = Workflow {
    name = "WF_DMS_DefendantBankCaseManagement"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "4001"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Neutral"
        result {
            displayName = "Initialization-Initialize-To-Reply"
            nextStepKey = 402
        }
    }
    initialAction {
        key = 1
        name = "Reply"
        label = "reply_8118127697"
        doRefresh = true
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "4002"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Neutral"
        result {
            displayName = "Initialization-Create-To-Create"
            nextStepKey = 402
        }
    }

    step {
        key = 402
        name = "To-Reply"
        editable = false
        statusCode = "402"
        action {
            key = 4021
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4021"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }
            result {
                displayName = "To-Reply-Request Accept-To-RequestAccept"
                nextStepKey = 701
            }
        }
        action {
            key = 4022
            name = "Reject"
            label = "reject_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4022"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "DefendantMakerReject"
                }
            }
            result {
                displayName = "To-Reply-Request Reject-To-RequestReject"
                nextStepKey = 702
            }
        }
        action {
            key = 4023
            name = "Ask Claimant Bank"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4023"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 404
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Accept"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-Reply-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Reject"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTBANK"
                }
            }
            result {
                displayName = "To-Reply-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 706
            }
        }
    }

    step {
        key = 404
        name = "To-Edit"
        editable = false
        statusCode = "404"
        action {
            key = 4041
            name = "Send Request"
            label = "sendrequest_8118129684"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4041"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerDefendantAdditionalInfo"
                }
            }
            result {
                displayName = "To-Send-Additional-Info-Request"
                nextStepKey = 704
            }
        }
        action {
            key = 302
            name = "Cancel"
            label = "cancel_8118129689"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4009"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Cancel-To-Create"
                nextStepKey = 402
            }
        }
    }

    step {
        key = 701
        name = "To-RequestAccept"
        editable = false
        statusCode = "701"
        action {
            key = 201
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4006"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Accept"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "Approved"
                }
            }
            result {
                displayName = "To-RequestApprove-ApproveAcceptance-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = 203
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "4008"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-RequestApprove-DeclineAcceptance-To-Re-Decision"
                nextStepKey = 707
            }
        }
    }

    step {
        key = 702
        name = "To-RequestReject"
        editable = false
        statusCode = "702"
        action {
            key = 301
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "5006"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Reject"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTBANK"
                }
            }
            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "DefendantCheckerReject"
                }
            }
            result {
                displayName = "To-RequestApprove-ApproveRejection-To-ApproveRejection"
                nextStepKey = 706
            }
        }
        action {
            key = 303
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "5008"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-RequestApprove-DeclineRejection-To-Re-Decision"
                nextStepKey = 707
            }
        }
    }

    step {
        key = 704
        name = "To-RequestAdditionalInfo"
        editable = false
        statusCode = "704"
        action {
            key = 710
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "710"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Request Additional Info For Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfo"
                }
            }
            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerDefendantAdditionalInfo"
                }
            }
            result {
                displayName = "To-RequestAdditionalInfo-ApproveRequest-To-ApproveAdditionalInfoRequest"
                nextStepKey = 708
            }
        }
        action {
            key = 304
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "304"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-RequestAdditionalInfo-DeclineRequest-To-Re-Decision"
                nextStepKey = 707
            }
        }
    }
    step {
        key = 707
        name = "To-Re-Decision"
        editable = false
        statusCode = "707"
        action {
            key = 7071
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7071"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }
            result {
                displayName = "To-Re-Decision-Request Accept-To-RequestAccept"
                nextStepKey = 701
            }
        }
        action {
            key = 7072
            name = "Reject"
            label = "reject_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7072"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }

            result {
                displayName = "To-Re-Decision-Request Reject-To-RequestReject"
                nextStepKey = 702
            }
        }
        action {
            key = 7073
            name = "Ask Claimant Bank"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7073"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 404
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Accept"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-Re-Decision-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Reject"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTBANK"
                }
            }
            result {
                displayName = "To-Re-Decision-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 706
            }
        }
    }

    step {
        key = 706
        name = "To-ApproveRejection"
        editable = false
        statusCode = "706"
        action {
            key = 7061
            name = "SVC_Re_Present"
            label = "JFW_WF_ACTION.*********"
            actionCode = "7061"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "TrxlistingProcessing-SVC_MoreInfoProvided-To-MoreInfoProvided-TrxReview"
                nextStepKey = 913
            }
        }
        action {
            key = **********
            name = "SVC_Approved_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Approved_Dispute-To-ApproveAcceptance"
                nextStepKey = 703
            }

        }
        action {
            key = **********
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "12"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_MoreInfo-To-ApproveAdditionalInfoRequest"
                nextStepKey = 10010
            }
        }
        action {
            key = 7063
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "7063"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 703
            }
        }
        action {
            key = 7064
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "7064"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Decline-To-RejectedDispute"
                nextStepKey = 706
            }
        }
        action {
            key = 7065
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.185381307"
            actionCode = "7065"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Accept-To-DeclinedDispute"
                nextStepKey = 706
            }
        }
        action {
            key = 7066
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "7066"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    step {
        key = 8010
        name = "To-MoreInfoRequired"
        editable = false
        statusCode = "8010"

    }

    step {
        key = 703
        name = "To-ApproveAcceptance"
        editable = false
        statusCode = "703"
        action {
            key = 1148821257
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "13"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    step {
        key = 708
        name = "To-ApproveAdditionalInfoRequest"
        editable = false
        statusCode = "708"
        action {
            key = 1148821249
            name = "SVC_MoreInfoProvided"
            label = "JFW_WF_ACTION.*********"
            actionCode = "708"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "TrxlistingProcessing-SVC_MoreInfoProvided-To-MoreInfoProvided-TrxReview"
                nextStepKey = 709
            }
        }
    }
    step {
        key = 709
        name = "To-MoreInfoProvided"
        editable = false
        statusCode = "709"
        action {
            key = 7091
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7091"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }
            result {
                displayName = "To-Reply-Request Accept-To-RequestAccept"
                nextStepKey = 701
            }
        }
        action {
            key = 7092
            name = "Reject"
            label = "reject_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7092"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request Reject-To-RequestReject"
                nextStepKey = 702
            }
        }
        action {
            key = 7093
            name = "Ask Claimant Bank"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7093"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 404
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Accept"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-MoreInfoProvided-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Reject"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTBANK"
                }
            }
            result {
                displayName = "To-MoreInfoProvided-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 706
            }
        }
    }
    // ************ Provide More Info Requested from Defendant NCB ************
    step {
        key = 10010
        name = "To-Additional-Info"
        editable = false
        statusCode = "10010"
        action {
            key = 100101
            name = "Additional-Info"
            label = "claimant.provide-additional-info"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "100101"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            serviceAction = false
            actionLevel = "Maker Level"
            result {
                displayName = "To-Additional-Info-To-Provide-Additional-Info"
                nextStepKey = 10011
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantNCBCaseManagement,DMS_ClaimantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approved_Dispute"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-Re-Presented-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
    }
    step {
        key = 10011
        name = "To-Provide-Additional-Info"
        editable = true
        statusCode = "10011"
        action {
            key = 100111
            name = "Save"
            label = "claimant.saveandsubmit"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "8111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }
            result {
                displayName = "To-Provide-Additional-Info-To-ModifiedAdditionalInfo"
                nextStepKey = 10012
            }
        }
        action {
            key = 100112
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "100112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-To-Additional-Info"
                nextStepKey = 10010
            }
        }
    }
    step {
        key = 10012
        name = "To-ModifiedAdditionalInfo"
        editable = false
        statusCode = "10012"
        action {
            key = 100121
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "100121"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_OperatorCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Additional Info Provided"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfoProvided"
                }
            }
            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerDefendantAdditionalInfo"
                }
            }
            result {
                displayName = "To-ModifiedAdditionalInfo-Approve-To-SubmitMoreInfoRequest"
                nextStepKey = 10013
            }
        }
        action {
            key = 100122
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "100122"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-ModifiedRequestApprove-Reject-To-MoreInfoRejected"
                nextStepKey = 10014
            }
        }
    }
    step {
        key = 10013
        name = "To-SubmitMoreInfoRequest"
        editable = false
        statusCode = "10013"
        action {
            key = 100131
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "100131"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_MoreInfo-To-ApproveAdditionalInfoRequest"
                nextStepKey = 10010
            }
        }
        action {
            key = 100132
            name = "SVC_Accept"
            label = "JFW_WF_ACTION.*********"
            actionCode = "100132"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = 100133
            name = "SVC_Reject"
            label = "JFW_WF_ACTION.*********"
            actionCode = "100133"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_Reject-To-ApproveRejection"
                nextStepKey = 706
            }
        }
        action {
            key = 100134
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "100134"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 703
            }
        }
        action {
            key = 100135
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "100135"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveRejection-SVC_Decline-To-RejectedDispute"
                nextStepKey = 706
            }
        }
        action {
            key = 100136
            name = "SVC_Approved_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "100136"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Approved_Dispute-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = 100137
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.185381307"
            actionCode = "100137"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Accept-To-DeclinedDispute"
                nextStepKey = 706
            }
        }
        action {
            key = 7066
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "7066"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }
    step {
        key = 10014
        name = "To-MoreInfoRejected"
        editable = false
        statusCode = "100141"
        action {
            key = 8141
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "100141"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Rejected-Repair-To-Provide-Additional-Info"
                nextStepKey = 10011
            }
        }
    }

    step {
        key = 913
        name = "To-Re-Presented"
        editable = false
        statusCode = "913"
        action {
            key = 9131
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "9131"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerActionRepresent"
                }
            }
            result {
                displayName = "To-Reply-Request Accept-To-RequestAccept"
                nextStepKey = 701
            }
        }
        action {
            key = 9132
            name = "Reject"
            label = "reject_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "9132"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "defendantBankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "defendantBankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }
            result {
                displayName = "To-Reply-Request Reject-To-RequestReject"
                nextStepKey = 702
            }
        }
        action {
            key = 9133
            name = "Ask Claimant Bank"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "9133"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 404
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Accept"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "To-Re-Presented-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Reject"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assignee Bank"
                }
            }
            function {
                functionKey = null
                name = "incrementRejectionCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRejectionCount"
                }
                argument {
                    name = "type"
                    value = "DEFENDANTBANK"
                }
            }
            result {
                displayName = "To-Re-Presented-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 706
            }
        }
    }

    step {
        key = 714
        name = "To-ApproveCloseDispute"
        editable = false
        statusCode = "714"
    }
}