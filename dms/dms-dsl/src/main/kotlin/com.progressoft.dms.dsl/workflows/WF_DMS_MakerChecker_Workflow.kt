package com.progressoft.dms.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_MakerChecker_Workflow = Workflow {
    name = "WF_DMS_MakerChecker"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "4001"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"
        result {
            displayName = "Initialization-Initialize-To-Create"
            nextStepKey = 402
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "create_8118129619"
        doRefresh = true
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "4002"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Maker Level"
        function {
            functionKey = null
            name = "aclConfigValidator"
            type = "spring"
            functionType = "validator"
            argument {
                name = "bean.name"
                value = "aclConfigValidator"
            }
        }
        result {
            displayName = "Initialization-Create-To-Create"
            nextStepKey = 402
        }
    }
    step {
        key = 402
        name = "To-Create"
        editable = false
        statusCode = "402"
        action {
            key = 101
            name = "Request Approve"
            label = "requestapprove_8118129631"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4003"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Create-Request Approve-To-RequestApprove"
                nextStepKey = 403
            }
        }
        action {
            key = 102
            name = "Edit"
            label = "edit_8118129637"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4004"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Create-Edit-To-Edit"
                nextStepKey = 404
            }
        }
        action {
            key = 103
            name = "Delete"
            label = "delete_8118129643"
            showAsBulk = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4005"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            confirmMessage {
                title = "deleteconfirmmsg_8118129648"
                description = "deleteconfirmdesc_8118129651"
            }
            result {
                displayName = "To-Create-Delete-To-Deleted"
                nextStepKey = 405
            }
        }
    }
    step {
        key = 403
        name = "To-RequestApprove"
        editable = false
        statusCode = "403"
        action {
            key = 201
            name = "Approve"
            label = "approve_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4006"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            result {
                displayName = "To-RequestApprove-Approve-To-Approved"
                nextStepKey = 406
            }
        }
        action {
            key = 202
            name = "Reject"
            label = "reject_8118129671"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionPortalId = "8118121163"
            actionCode = "4007"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-RequestApprove-Reject-To-NewEntryRejected"
                nextStepKey = 412
            }
        }
    }
    step {
        key = 404
        name = "To-Edit"
        editable = true
        statusCode = "404"
        action {
            key = 301
            name = "Save"
            label = "save_8118129683"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4008"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Save-To-Create"
                nextStepKey = 402
            }
        }
        action {
            key = 302
            name = "Cancel"
            label = "cancel_8118129689"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4009"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Cancel-To-Create"
                nextStepKey = 402
            }
        }
    }
    step {
        key = 405
        name = "To-Deleted"
        editable = false
        statusCode = "405"
    }
    step {
        key = 406
        name = "To-Approved"
        editable = false
        statusCode = "406"
        action {
            key = 501
            name = "Modify"
            label = "modify_8118129707"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4011"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Approved-Modify-To-Modified"
                nextStepKey = 408
            }
        }
        action {
            key = 502
            name = "Delete"
            label = "delete_8118129713"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4012"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Approved-Delete-To-DeletedApproval"
                nextStepKey = 409
            }
        }
    }
    step {
        key = 407
        name = "To-Rejected"
        editable = false
        statusCode = "407"
        action {
            key = 601
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4013"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Rejected-Repair-To-EditRepair"
                nextStepKey = 411
            }
        }
    }
    step {
        key = 408
        name = "To-Modified"
        editable = true
        statusCode = "408"
        action {
            key = 701
            name = "Save"
            label = "save_8118129737"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4014"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Modified-Save-To-ModifiedRequestApprove"
                nextStepKey = 413
            }
        }
        action {
            key = 702
            name = "Cancel"
            label = "cancel_8118129743"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4015"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Modified-Cancel-To-Approved"
                nextStepKey = 406
            }
        }
    }
    step {
        key = 409
        name = "To-DeletedApproval"
        editable = false
        statusCode = "409"
        action {
            key = 801
            name = "Approve"
            label = "approve_8118129755"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4016"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-DeletedApproval-Approve-To-Deleted"
                nextStepKey = 405
            }
        }
        action {
            key = 802
            name = "Reject"
            label = "reject_8118129761"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4017"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-DeletedApproval-Reject-To-Approved"
                nextStepKey = 406
            }
        }
    }
    step {
        key = 411
        name = "To-EditRepair"
        editable = true
        statusCode = "411"
        action {
            key = 901
            name = "Save"
            label = "save_8118129773"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4020"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRepair-Save-To-ModifiedRequestApprove"
                nextStepKey = 413
            }
        }
        action {
            key = 902
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4015"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRepair-Cancel-To-Rejected"
                nextStepKey = 407
            }
        }
    }
    step {
        key = 412
        name = "To-NewEntryRejected"
        editable = false
        statusCode = "412"
        action {
            key = 1001
            name = "Repair"
            label = "repair_8118129791"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4021"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-NewEntryRejected-Repair-To-RepairNewEntry"
                nextStepKey = 414
            }
        }
    }
    step {
        key = 413
        name = "To-ModifiedRequestApprove"
        editable = false
        statusCode = "413"
        action {
            key = 1101
            name = "Approve"
            label = "approve_8118129803"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4022"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            result {
                displayName = "To-ModifiedRequestApprove-Approve-To-Approved"
                nextStepKey = 406
            }
        }
        action {
            key = 1102
            name = "Reject"
            label = "reject_8118129809"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionPortalId = "8118121163"
            actionCode = "4023"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-ModifiedRequestApprove-Reject-To-Rejected"
                nextStepKey = 407
            }
        }
    }
    step {
        key = 414
        name = "To-RepairNewEntry"
        editable = false
        statusCode = "414"
        action {
            key = 1201
            name = "Request Approve"
            label = "requestapprove_8118129821"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4024"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RepairNewEntry-Request Approve-To-RequestApprove"
                nextStepKey = 403
            }
        }
        action {
            key = 1202
            name = "Edit"
            label = "edit_8118129827"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4025"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RepairNewEntry-Edit-To-EditRejectedNewEntry"
                nextStepKey = 415
            }
        }
        action {
            key = 1203
            name = "Delete"
            label = "delete_8118129833"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4026"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            confirmMessage {
                title = "deleteconfirmmsg_8118129838"
                description = "deleteconfirmdesc_8118129841"
            }
            result {
                displayName = "To-RepairNewEntry-Delete-To-Deleted"
                nextStepKey = 405
            }
        }
    }
    step {
        key = 415
        name = "To-EditRejectedNewEntry"
        editable = true
        statusCode = "415"
        action {
            key = 1301
            name = "Save"
            label = "save_8118129852"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4027"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRejectedNewEntry-Save-To-RepairNewEntry"
                nextStepKey = 414
            }
        }
        action {
            key = 1302
            name = "Cancel"
            label = "cancel_8118129858"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "4028"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRejectedNewEntry-Cancel-To-RepairNewEntry"
                nextStepKey = 414
            }
        }
    }
}