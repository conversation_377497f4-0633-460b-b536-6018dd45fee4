package com.progressoft.dms.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_UserReport_Workflow = Workflow {
	name = "WF_UserReport"
	status = true
	initialAction {
		key = 10
		name = "Initialize"
		label = "initialize_8118129613"
		actionCode = "1"
		notificationTimeToLive = 0
		deleteItem = "NORMAL"
		actionMode = "NORMAL"
		actionLevel = "Maker Level"
		result {
			displayName = "Initialization-Initialize-Submission"
			nextStepKey = 70000
		}
	}
	initialAction {
		key = 1
		name = "Create"
		label = "create_8118129619"
		actionCode = "2"
		notificationTimeToLive = 0
		deleteItem = "NORMAL"
		actionMode = "COMMIT"
		actionLevel = "Maker Level"
		function {
			functionKey = null
			name = "validateDisputeSummaryReportDates"
			type = "spring"
			functionType = "validator"
			argument {
				name = "bean.name"
				value = "validateDisputeSummaryReportDates"
			}
		}
		result {
			displayName = "Initialization-Create-Submission"
			nextStepKey = 70000
		}
	}
	step {
		key = 70000
		name = "Submission"
		editable = true
		statusCode ="700001"
		action {
			key = 700002
			name = "Submit"
			label = "generate.action"
			actionCode = "3"
			primaryAction = true
			iconName = "angle_double_right"
			iconColor = "#FFFFFF"
			notificationTimeToLive = 0
			deleteItem = "NORMAL"
			actionMode = "COMMIT"
			actionLevel = "Maker Level"
			function {
				functionKey = null
				name = "generateDisputesSummaryReport"
				type = "spring"
				functionType = "post-function"
				argument {
					name = "bean.name"
					value = "generateDisputesSummaryReport"
				}
			}
			result {
				displayName = "Submission-Generate-Generated"
				nextStepKey = 70001
			}
		}
	}

	step {
		key = 70001
		name = "Generated"
		editable = false
		statusCode = "700002"
	}
}
