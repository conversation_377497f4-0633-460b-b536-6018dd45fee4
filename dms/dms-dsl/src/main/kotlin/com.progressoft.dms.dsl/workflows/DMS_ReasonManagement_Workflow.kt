package com.progressoft.dms.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val DMS_ReasonManagement_Workflow = Workflow {
    name = "DMS_ReasonManagement_Workflow"
    status = true
    initialAction {
        key = 10
        name = "Initialize"
        label = "label.workflow.action.initialize"
        actionCode = "1"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"
        result {
            displayName = "Initialization-Initialize-Submission"
            nextStepKey = 3000
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "label.workflow.action.create"
        actionCode = "2"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Maker Level"
        function {
            functionKey = null
            name = "reasonMngmntValidator"
            type = "spring"
            functionType = "pre-function"
            argument {
                name = "bean.name"
                value = "reasonMngmntValidator"
            }
        }
        result {
            displayName = "Initialization-Create-Submission"
            nextStepKey = 3000
        }
    }
    step {
        key = 3000
        name = "Submission"
        editable = true
        statusCode = "3000"
        action {
            key = 30001
            name = "Cancel"
            label = "cancel.default.label"
            doRefresh = true
            showAsBulk = true
            actionCode = "5"
            iconName = "times_circle_o"
            iconColor = "#000000"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "Submission-Cancel-Submission"
                remainOnStep = true
                nextStepKey = 3000
            }
        }
        action {
            key = 30002
            name = "Submit"
            label = "submit_8118128616"
            actionCode = "3"
            primaryAction = true
            iconName = "angle_double_right"
            iconColor = "#FFFFFF"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "reasonMngmntValidator"
                type = "spring"
                functionType = "pre-function"
                argument {
                    name = "bean.name"
                    value = "reasonMngmntValidator"
                }
            }
            result {
                displayName = "Submission-Submit-CreationApproval"
                nextStepKey = 3001
            }
        }
    }
    step {
        key = 3001
        name = "CreationApproval"
        editable = false
        statusCode = "180"
        action {
            key = 30011
            name = "Reject"
            label = "reject_8118129671"
            doRefresh = true
            actionCode = "7"
            iconName = "times"
            iconColor = "#000000"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            result {
                displayName = "CreationApproval-Reject-RepairNew"
                nextStepKey = 3002
            }
        }
        action {
            key = 30012
            name = "Approve"
            label = "approve_8118129662"
            doRefresh = true
            actionCode = "6"
            primaryAction = true
            iconName = "check"
            iconColor = "#FFFFFF"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "CreationApproval-Approve-Approved"
                nextStepKey = 3003
            }
        }
    }
    step {
        key = 3002
        name = "RepairNew"
        editable = false
        statusCode = "3020"
        action {
            key = 30021
            name = "Edit"
            label = "modify_8118129707"
            showAsBulk = true
            actionCode = "8"
            primaryAction = true
            iconName = "pencil"
            iconColor = "#FFFFFF"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "RepairNew-Edit-EditNew"
                nextStepKey = 3004
            }
        }
    }
    step {
        key = 3004
        name = "EditNew"
        editable = true
        statusCode = "3004"
        action {
            key = 30041
            name = "Cancel"
            label = "cancel.default.label"
            doRefresh = true
            showAsBulk = true
            actionCode = "10"
            iconName = "times_circle_o"
            iconColor = "#000000"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "EditNew-Cancel-RepairNew"
                nextStepKey = 3002
            }
        }
        action {
            key = 30042
            name = "Submit"
            label = "submit_8118128616"
            showAsBulk = true
            actionCode = "9"
            primaryAction = true
            iconName = "angle_double_right"
            iconColor = "#FFFFFF"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "reasonMngmntValidator"
                type = "spring"
                functionType = "pre-function"
                argument {
                    name = "bean.name"
                    value = "reasonMngmntValidator"
                }
            }
            result {
                displayName = "EditNew-Submit-CreationApproval"
                nextStepKey = 3001
            }
        }
    }
    step {
        key = 3003
        name = "Approved"
        editable = false
        statusCode = "3003"
        action {
            key = 30031
            name = "Repair"
            label = "repair_8118129816"
            showAsBulk = true
            actionCode = "11"
            primaryAction = true
            iconName = "pencil"
            iconColor = "#FFFFFF"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "Approved-Repair-RepairApproved"
                nextStepKey = 3005
            }
        }
    }
    step {
        key = 3005
        name = "RepairApproved"
        editable = false
        statusCode = "3005"
        action {
            key = 30051
            name = "Edit"
            label = "modify_8118129707"
            showAsBulk = true
            actionCode = "8"
            primaryAction = true
            iconName = "pencil"
            iconColor = "#FFFFFF"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "RepairApproved-Edit-EditApproved"
                nextStepKey = 3006
            }
        }
    }
    step {
        key = 3006
        name = "EditApproved"
        editable = true
        statusCode = "3004"
        action {
            key = 30061
            name = "Cancel"
            label = "cancel.default.label"
            doRefresh = true
            showAsBulk = true
            actionCode = "10"
            iconName = "times_circle_o"
            iconColor = "#000000"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "EditApproved-Cancel-RepairApproved"
                nextStepKey = 3005
            }
        }
        action {
            key = 30062
            name = "Submit"
            label = "submit_8118128616"
            showAsBulk = true
            actionCode = "9"
            primaryAction = true
            iconName = "angle_double_right"
            iconColor = "#FFFFFF"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "reasonMngmntValidator"
                type = "spring"
                functionType = "pre-function"
                argument {
                    name = "bean.name"
                    value = "reasonMngmntValidator"
                }
            }
            result {
                displayName = "EditApproved-Submit-RepairApprovedApproval"
                nextStepKey = 3007
            }
        }
    }
    step {
        key = 3007
        name = "RepairApprovedApproval"
        editable = false
        statusCode = "3007"
        action {
            key = 30071
            name = "Reject"
            label = "reject_8118129671"
            doRefresh = true
            actionCode = "7"
            iconName = "times"
            iconColor = "#000000"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            result {
                displayName = "RepairApprovedApproval-Reject-RepairApproved"
                nextStepKey = 3005
            }
        }
        action {
            key = 30072
            name = "Approve"
            label = "approve_8118129662"
            doRefresh = true
            actionCode = "6"
            primaryAction = true
            iconName = "check"
            iconColor = "#FFFFFF"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "RepairApprovedApproval-Approve-Approved"
                nextStepKey = 3003
            }
        }
    }
}
