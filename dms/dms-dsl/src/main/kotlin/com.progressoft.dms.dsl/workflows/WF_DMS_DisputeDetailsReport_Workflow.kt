package com.progressoft.dms.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_DisputeDetailsReport_Workflow = Workflow {
	name = "WF_DMS_DisputeDetailsReport"
	status = true
	initialAction {
		key = 10
		name = "Initialize"
		label = "initialize_8118129613"
		actionCode = "1"
		notificationTimeToLive = 0
		deleteItem = "NORMAL"
		actionMode = "NORMAL"
		actionLevel = "Maker Level"
		result {
			displayName = "Initialization-Initialize-Submission"
			nextStepKey = 70000
		}
	}
	initialAction {
		key = 1
		name = "Create"
		label = "create_8118129619"
		actionCode = "2"
		notificationTimeToLive = 0
		deleteItem = "NORMAL"
		actionMode = "COMMIT"
		actionLevel = "Maker Level"
		function {
			functionKey = null
			name = "validateDisputeDetailsReportDates"
			type = "spring"
			functionType = "validator"
			argument {
				name = "bean.name"
				value = "validateDisputeDetailsReportDates"
			}
		}
		function {
			functionKey = null
			name = "validateDisputeDetailsReportAmounts"
			type = "spring"
			functionType = "validator"
			argument {
				name = "bean.name"
				value = "validateDisputeDetailsReportAmounts"
			}
		}
		result {
			displayName = "Initialization-Create-Submission"
			nextStepKey = 70000
		}
	}
	step {
		key = 70000
		name = "Submission"
		editable = true
		statusCode ="700001"
		action {
			key = 700002
			name = "Submit"
			label = "generate.action"
			actionCode = "3"
			primaryAction = true
			iconName = "angle_double_right"
			iconColor = "#FFFFFF"
			notificationTimeToLive = 0
			deleteItem = "NORMAL"
			actionMode = "COMMIT"
			actionLevel = "Maker Level"
			function {
				functionKey = null
				name = "generateDisputeDetailsReport"
				type = "spring"
				functionType = "post-function"
				argument {
					name = "bean.name"
					value = "generateDisputeDetailsReport"
				}
			}
			result {
				displayName = "Submission-Generate-Generated"
				nextStepKey = 70001
			}
		}
	}

	step {
		key = 70001
		name = "Generated"
		editable = false
		statusCode = "700002"
	}
}
