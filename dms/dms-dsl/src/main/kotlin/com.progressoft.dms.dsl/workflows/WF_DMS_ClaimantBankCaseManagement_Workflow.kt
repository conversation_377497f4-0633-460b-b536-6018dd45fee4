package com.progressoft.dms.dsl.workflows

import com.progressoft.dms.dsl.entities.DMS_ClaimantNCBCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_DefendantBankCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_OperatorCaseManagement_Entity
import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_ClaimantBankCaseManagement_Workflow = Workflow {
    name = "WF_DMS_ClaimantBankCaseManagement"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "2001"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"
        result {
            displayName = "Initialization-Initialize-To-Create"
            nextStepKey = 306
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "create_8118129619"
        doRefresh = true
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "2002"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Maker Level"
        result {
            displayName = "Initialization-Create-To-Create"
            nextStepKey = 306
        }
    }

    step {
        key = 306
        name = "To-Submitted"
        editable = false
        statusCode = "306"
        action {
            key = **********
            name = "SVC_Accept"
            label = "JFW_WF_ACTION.*********"
            actionCode = "10"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = **********
            name = "SVC_Reject"
            label = "JFW_WF_ACTION.*********"
            actionCode = "11"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_Reject-To-ApproveRejection"
                nextStepKey = 706
            }
        }
        action {
            key = **********
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "12"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_MoreInfo-To-ApproveAdditionalInfoRequest"
                nextStepKey = 810
            }
        }
    }

    step {
        key = 304
        name = "To-Edit"
        editable = true
        statusCode = "304"
        action {
            key = 301
            name = "Save"
            label = "save_8118129683"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2008"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }
            result {
                displayName = "To-Edit-Save-To-Create"
                nextStepKey = 302
            }
        }
        action {
            key = 302
            name = "Cancel"
            label = "cancel_8118129689"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2009"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Cancel-To-Create"
                nextStepKey = 302
            }
        }
    }
    step {
        key = 305
        name = "To-Deleted"
        editable = false
        statusCode = "305"
    }

    step {
        key = 307
        name = "To-Rejected"
        editable = false
        statusCode = "307"
        action {
            key = 601
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2013"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Rejected-Repair-To-EditRepair"
                nextStepKey = 311
            }
        }
    }
    step {
        key = 309
        name = "To-DeletedApproval"
        editable = false
        statusCode = "309"
        action {
            key = 801
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2016"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "DefendantMakerReject"
                }
            }
            result {
                displayName = "To-DeletedApproval-Approve-To-Deleted"
                nextStepKey = 305
            }
        }
        action {
            key = 802
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2017"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-DeletedApproval-Reject-To-Approved"
                nextStepKey = 306
            }
        }
    }
    step {
        key = 311
        name = "To-EditRepair"
        editable = true
        statusCode = "311"
        action {
            key = 901
            name = "Save"
            label = "save_8118129773"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2020"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerActionRepresent"
                }
            }
            result {
                displayName = "To-EditRepair-Save-To-ModifiedRequestApprove"
                nextStepKey = 313
            }
        }
        action {
            key = 902
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2015"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRepair-Cancel-To-Rejected"
                nextStepKey = 307
            }
        }
    }
    step {
        key = 312
        name = "To-NewEntryRejected"
        editable = false
        statusCode = "312"
        action {
            key = 1001
            name = "Repair"
            label = "repair_8118129791"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2021"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-NewEntryRejected-Repair-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
    }
    step {
        key = 313
        name = "To-ModifiedRequestApprove"
        editable = false
        statusCode = "313"
        action {
            key = 1101
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2022"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "Approved"
                }
            }
            result {
                displayName = "To-ModifiedRequestApprove-Approve-To-Approved"
                nextStepKey = 306
            }
        }
        action {
            key = 1102
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "2023"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-ModifiedRequestApprove-Reject-To-Rejected"
                nextStepKey = 307
            }
        }
    }
    step {
        key = 314
        name = "To-RepairNewEntry"
        editable = false
        statusCode = "314"
        action {
            key = 1201
            name = "Request Approve"
            label = "requestapprove_8118129821"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2024"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }
            result {
                displayName = "To-RepairNewEntry-Request Approve-To-RequestApprove"
                nextStepKey = 303
            }
        }
        action {
            key = 1202
            name = "Edit"
            label = "edit_8118129827"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2025"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RepairNewEntry-Edit-To-EditRejectedNewEntry"
                nextStepKey = 315
            }
        }
        action {
            key = 1203
            name = "Delete"
            label = "delete_8118129833"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2026"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            confirmMessage {
                title = "deleteconfirmmsg_8118129838"
                description = "deleteconfirmdesc_8118129841"
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "Deleted"
                }
            }
            result {
                displayName = "To-RepairNewEntry-Delete-To-Deleted"
                nextStepKey = 305
            }
        }
    }
    step {
        key = 315
        name = "To-EditRejectedNewEntry"
        editable = true
        statusCode = "315"
        action {
            key = 1301
            name = "Save"
            label = "save_8118129852"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2027"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRejectedNewEntry-Save-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
        action {
            key = 1302
            name = "Cancel"
            label = "cancel_8118129858"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2028"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRejectedNewEntry-Cancel-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
    }

    step {
        key = 706
        name = "To-ApproveRejection"
        editable = false
        statusCode = "706"
        action {
            key = 7061
            name = "Re-Present"
            label = "claimant.re_present"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7061"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            conditionGroup {
                name = "representDisputeCondition"
                isOr = false
                condition {
                    condKey = 91002
                    name = "representDisputeCondition"
                    negate = false
                    type = "spring"
                    argument {
                        name = "bean.name"
                        value = "representDisputeCondition"
                    }
                    argument {
                        name = "type"
                        value = "CLAIMANTBANK"
                    }
                }
            }
            result {
                displayName = "To-Rejected-Repair-To-Re-Present"
                nextStepKey = 910
            }

            function {
                functionKey = null
                name = "disputeCaseAttCounter"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "disputeCaseAttCounter"
                }
            }
        }
        action {
            key = 1000
            name = "Arbitrate Dispute Case"
            label = "arbitratetodisputecase_8118121000"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "1000"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            conditionGroup {
                name = "arbitrateDisputeCondition"
                isOr = false
                condition {
                    condKey = 910022
                    name = "arbitrateDisputeCondition"
                    negate = false
                    type = "spring"
                    argument {
                        name = "bean.name"
                        value = "arbitrateDisputeCondition"
                    }
                    argument {
                        name = "type"
                        value = "CLAIMANTBANK"
                    }
                }
            }
            result {
                displayName = "To-ApproveRejection Approve-To-RequestArbitrate"
                nextStepKey = 1001
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "ArbitrateRequest"
                }
            }
        }
        action {
            key = 7064
            name = "Close Dispute"
            label = "closedispute_8118129731"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7064"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CloseDispute"
                }
            }
            result {
                displayName = "To-ApproveRejection-To-CloseDispute"
                nextStepKey = 713
            }
        }
    }

    step {
        key = 1001
        name = "To-RequestArbitrate"
        editable = false
        statusCode = "1001"
        action {
            key = 1002
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "1002"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "arbitrateDisputeCase"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "arbitrateDisputeCase"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_OperatorCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Arbitrate Dispute Case"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assigner Bank"
                }
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerArbitrate"
                }
            }
            result {
                displayName = "To-RequestArbitrate-Approve-To-ApproveRejection"
                nextStepKey = 1004
            }
        }
        action {
            key = 1003
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "1003"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-RequestArbitrate-Reject-To-NewEntryRejected"
                nextStepKey = 706
            }
        }

    }

    step {
        key = 1004
        name = "To-ApproveArbitrate"
        editable = false
        statusCode = "1004"
        action {
            key = 10041
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "10041"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_NCB_MoreInfo-To-NCB-Requires-More-Info"
                nextStepKey = 1005
            }
        }
        action {
            key = 10043
            name = "SVC_Approved_Dispute"
            label = "JFW_WF_ACTION.185313001"
            actionCode = "10043"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Approved_Dispute-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = 10043
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "10043"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 703
            }
        }
        action {
            key = 10044
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "10044"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Decline-To-RejectedDispute"
                nextStepKey = 706
            }
        }
        action {
            key = 81307
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "81307"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Accept-To-DeclinedDispute"
                nextStepKey = 8151
            }
        }
    }

    step {
        key = 1005
        name = "To-NCB-Requires-More-Info"
        editable = false
        statusCode = "1005"
        action {
            key = 10051
            name = "Additional-Info"
            label = "claimant.provide-additional-info"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "10051"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            serviceAction = false
            actionLevel = "Maker Level"
            result {
                displayName = "To-Additional-Info-To-Provide-Additional-Info"
                nextStepKey = 8110
            }
        }
    }

    // ***************************** MORE INFO *****************************
    step {
        key = 810
        name = "To-ApproveAdditionalInfoRequest"
        editable = false
        statusCode = "810"
        action {
            key = 8101
            name = "Additional-Info"
            label = "claimant.provide-additional-info"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "8101"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            serviceAction = false
            actionLevel = "Maker Level"
            result {
                displayName = "To-Additional-Info-To-Provide-Additional-Info"
                nextStepKey = 811
            }
        }
    }
    step {
        key = 811
        name = "To-Provide-Additional-Info"
        editable = true
        statusCode = "811"
        action {
            key = 8111
            name = "Save"
            label = "claimant.saveandsubmit"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "8111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerActionRepresent"
                }
            }
            result {
                displayName = "To-Provide-Additional-Info-To-ModifiedAdditionalInfo"
                nextStepKey = 812
            }
        }
        action {
            key = 8112
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "8112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-To-Additional-Info"
                nextStepKey = 810
            }
        }
    }
    step {
        key = 812
        name = "To-ModifiedAdditionalInfo"
        editable = false
        statusCode = "812"
        action {
            key = 8121
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "8121"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_DefendantBankCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Additional Info Provided"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assigner Bank"
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfoProvided"
                }
            }

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerDefendantAdditionalInfo"
                }
            }
            result {
                displayName = "To-ModifiedAdditionalInfo-Approve-To-SubmitMoreInfoRequest"
                nextStepKey = 813
            }
        }
        action {
            key = 8122
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "8122"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-ModifiedRequestApprove-Reject-To-MoreInfoRejected"
                nextStepKey = 814
            }
        }
    }
    step {
        key = 813
        name = "To-SubmitMoreInfoRequest"
        editable = false
        statusCode = "813"
        action {
            key = 8131
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "8131"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_MoreInfo-To-ApproveAdditionalInfoRequest"
                nextStepKey = 810
            }
        }
        action {
            key = 8132
            name = "SVC_Accept"
            label = "JFW_WF_ACTION.*********"
            actionCode = "8132"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = 8133
            name = "SVC_Reject"
            label = "JFW_WF_ACTION.*********"
            actionCode = "8133"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_Reject-To-ApproveRejection"
                nextStepKey = 706
            }
        }
        action {
            key = 81307
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "81307"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Accept-To-DeclinedDispute"
                nextStepKey = 8151
            }
        }
    }
    step {
        key = 814
        name = "To-MoreInfoRejected"
        editable = false
        statusCode = "814"
        action {
            key = 8141
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "8141"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerAction"
                }
            }
            result {
                displayName = "To-Rejected-Repair-To-Provide-Additional-Info"
                nextStepKey = 811
            }
        }
    }
    step {
        key = 910
        name = "To-Re-Present-Edit"
        editable = true
        statusCode = "910"
        action {
            key = 9101
            name = "Save"
            label = "claimant.savetorepresent"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "9101"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Rejected-Repair-To-Provide-Additional-Info"
                nextStepKey = 911
            }
            function {
                functionKey = null
                name = "representAttachmentValidator"
                type = "spring"
                functionType = "validator"
                argument {
                    name = "bean.name"
                    value = "representAttachmentValidator"
                }
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerActionRepresent"
                }
            }
        }
        action {
            key = 9102
            name = "Cancel"
            label = "claimant.canceltorepresent"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "9102"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Rejected-Repair-To-Provide-Additional-Info"
                nextStepKey = 706
            }
        }
    }
    step {
        key = 911
        name = "To-Re-Present-Review"
        editable = false
        statusCode = "911"
        action {
            key = 9111
            name = "Proceed"
            label = "proceed_8118129662"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "9111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantBankCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_DefendantBankCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Re-Present Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assigner Bank"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Re_Present"
                }
            }
            function {
                functionKey = null
                name = "incrementRepresentCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRepresentCount"
                }
                argument {
                    name = "type"
                    value = "CLAIMANTBANK"
                }
            }

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "Approved"
                }
            }

            function {
                functionKey = null
                name = "disputeCaseAttCounter"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "disputeCaseAttCounter"
                }
            }
            result {
                displayName = "To-Rejected-Repair-To-Provide-Additional-Info"
                nextStepKey = 913
            }
        }
        action {
            key = 9112
            name = "Repair"
            label = "repair_8118129662"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "9112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-Rejected-Repair-To-Provide-Additional-Info"
                nextStepKey = 912
            }
        }
    }
    step {
        key = 912
        name = "To-Repair-Represented"
        editable = false
        statusCode = "912"
        action {
            key = 9121
            name = "Represent-Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "9121"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Repair-Represented-To-Re-Present-Edit"
                nextStepKey = 910
            }
        }
    }
    step {
        key = 913
        name = "To-Represented"
        editable = false
        statusCode = "913"
        action {
            key = 9131
            name = "SVC_Reject"
            label = "JFW_WF_ACTION.*********"
            actionCode = "9131"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Represented-SVC_Reject-To-Re-Present-Edit"
                nextStepKey = 706
            }
        }
        action {
            key = 9133
            name = "SVC_Accept"
            label = "JFW_WF_ACTION.*********"
            actionCode = "9133"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Represented-SVC_Accepted-To-Approved-Dispute"
                nextStepKey = 703
            }
        }
        action {
            key = 9132
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "9132"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Represented-SVC_MoreInfo-To-ApproveAdditionalInfoRequest"
                nextStepKey = 810
            }
        }
    }


    step {
        key = 703
        name = "To-ApproveAcceptance"
        editable = false
        statusCode = "703"
        action {
            key = 7062
            name = "Close Dispute"
            label = "closedispute_8118129731"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7062"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CloseDispute"
                }
            }
            result {
                displayName = "To-ApproveAcceptance-To-CloseDispute"
                nextStepKey = 720
            }
        }
    }

    step {
        key = 8151
        name = "To-DeclinedDispute"
        editable = false
        statusCode = "8151"
        action {
            key = 7062
            name = "Close Dispute"
            label = "closedispute_8118129731"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "7062"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-ApproveRejection-To-CloseDispute"
                nextStepKey = 713
            }
        }
    }

    step {
        key = 713
        name = "To-CloseDispute"
        editable = false
        statusCode = "713"
        action {
            key = 715
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "715"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement,DMS_OperatorCaseManagement,DMS_DisputeCase"
                }
                argument {
                    name = "stepName"
                    value = "SVC_CloseDispute"
                }
                argument {
                    name = "lastAction"
                    value = "Close Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assigner Bank"
                }
            }
            result {
                displayName = "To-CloseDispute-Approve-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
        action {
            key = 716
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "716"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-CloseDispute-Reject-To-NewEntryRejected"
                nextStepKey = 706
            }
        }
    }
    step {
        key = 7150
        name = "To-CloseDispute"
        editable = false
        statusCode = "713"
        action {
            key = 71501
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "71501"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement,DMS_OperatorCaseManagement,DMS_DisputeCase"
                }
                argument {
                    name = "stepName"
                    value = "SVC_CloseDispute"
                }
                argument {
                    name = "lastAction"
                    value = "Close Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assigner Bank"
                }
            }

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "ApproveCloseDispute"
                }
            }
            result {
                displayName = "To-CloseDispute-Approve-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
        action {
            key = 71502
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "71502"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-CloseDispute-Reject-To-NewEntryClosed"
                nextStepKey = 706
            }
        }
    }
    step {
        key = 720
        name = "To-CloseDispute"
        editable = false
        statusCode = "713"
        action {
            key = 7201
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "715"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement,DMS_OperatorCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_CloseDispute"
                }
                argument {
                    name = "lastAction"
                    value = "Close Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assigner Bank"
                }
            }
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_DisputeCase"
                }
                argument {
                    name = "stepName"
                    value = "SVC_CloseApprovedDispute"
                }
                argument {
                    name = "lastAction"
                    value = "Close Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assigner Bank"
                }
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "ApproveCloseDispute"
                }
            }
            result {
                displayName = "To-CloseDispute-Approve-To-ApproveCloseApprovedDispute"
                nextStepKey = 721
            }
        }
        action {
            key = 7202
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "716"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-CloseDispute-Reject-To-NewEntryRejected"
                nextStepKey = 703
            }
        }
    }

    step {
        key = 714
        name = "To-ApproveCloseDispute"
        editable = false
        statusCode = "714"
    }
    step {
        key = 721
        name = "To-ApproveCloseApprovedDispute"
        editable = false
        statusCode = "721"
    }



// Additional Information For Claimant NCB
    step {
        key = 8110
        name = "To-Provide-Additional-Info-For-NCB"
        editable = true
        statusCode = "8110"
        action {
            key = 81101
            name = "Save"
            label = "claimant.saveandsubmit"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "81101"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "MakerDefendantAdditionalInfo"
                }
            }
            result {
                displayName = "To-Provide-Additional-Info-For-NCB-To-ModifiedAdditionalInfo"
                nextStepKey = 8120
            }
        }
        action {
            key = 81102
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "81102"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-For-NCB-To-Additional-Info"
                nextStepKey = 1005
            }
        }
    }
    step {
        key = 8120
        name = "To-ModifiedAdditionalInfo-For-NCB"
        editable = false
        statusCode = "8120"
        action {
            key = 81201
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "81201"
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_OperatorCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Additional Info Provided"
                }
                argument {
                    name = "lastActionBy"
                    value = "Assigner Bank"
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfoProvided"
                }
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "operatorAdditionalInfoProvided"
                }
            }
            result {
                displayName = "To-ModifiedAdditionalInfo-Approve-For-NCB-To-SubmitMoreInfoRequest-For-NCB"
                nextStepKey = 8130
            }
        }
        action {
            key = 81202
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "81202"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-ModifiedRequestApprove-Reject-For-NCB-To-MoreInfoRejected-For-NCB"
                nextStepKey = 8140
            }
        }
    }
    step {
        key = 8130
        name = "To-SubmitMoreInfoRequest-For-NCB"
        editable = false
        statusCode = "8130"
        action {
            key = 81301
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "81301"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_NCB_MoreInfo-To-NCB-Requires-More-Info"
                nextStepKey = 1005
            }
        }
        action {
            key = 81302
            name = "SVC_Accept"
            label = "JFW_WF_ACTION.185313306"
            actionCode = "81302"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Accept-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = 81303
            name = "SVC_Reject"
            label = "JFW_WF_ACTION.*********"
            actionCode = "81303"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Reject-To-ApproveRejection"
                nextStepKey = 706
            }
        }
        action {
            key = 81304
            name = "SVC_Approved_Dispute"
            label = "JFW_WF_ACTION.185313001"
            actionCode = "81304"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Approved_Dispute-To-ApproveAcceptance"
                nextStepKey = 703
            }
        }
        action {
            key = 81305
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "81305"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 703
            }
        }
        action {
            key = 81306
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "81306"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Decline-To-RejectedDispute"
                nextStepKey = 706
            }
        }
        action {
            key = 81307
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "81307"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Accept-To-DeclinedDispute"
                nextStepKey = 8151
            }
        }
    }
    step {
        key = 8140
        name = "To-MoreInfoRejected-For-NCB"
        editable = false
        statusCode = "8140"
        action {
            key = 81401
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "81401"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }
            result {
                displayName = "To-MoreInfoRejected-For-NCB-Repair-To-Provide-Additional-Info-For-NCB"
                nextStepKey = 8110
            }
        }
    }

}