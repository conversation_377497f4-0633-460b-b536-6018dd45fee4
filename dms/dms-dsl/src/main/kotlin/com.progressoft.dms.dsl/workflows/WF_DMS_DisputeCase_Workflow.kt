package com.progressoft.dms.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_DisputeCase_Workflow = Workflow {
    name = "WF_DMS_DisputeCase"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "2001"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"
        result {
            displayName = "Initialization-Initialize-To-Create"
            nextStepKey = 1110
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "create_8118129619"
        doRefresh = true
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "2002"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Maker Level"
        result {
            displayName = "Initialization-Create-To-Create"
            nextStepKey = 1110
        }
        function {
            functionKey = null
            name = "setDisputeProp"
            type = "spring"
            functionType = "pre-function"
            argument {
                name = "bean.name"
                value = "setDisputeProp"
            }
        }
    }

    step{
        key = 1110
        name = "Create"
        editable = true
        statusCode = "1110"
        action {
            key = 11101
            name = "Create Dispute"
            label = "create_dispute_81181211101"
            doRefresh = true
            doParentRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "11101"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "disputeValidator"
                type = "spring"
                functionType = "validator"
                argument {
                    name = "bean.name"
                    value = "disputeValidator"
                }
            }
            result {
                displayName = "Create-Create Dispute-To-Create"
                nextStepKey = 302
            }
        }
    }

    step {
        key = 302
        name = "To-Create"
        editable = false
        statusCode = "302"
        action {
            key = 101
            name = "Request Approve"
            label = "requestapprove_8118129631"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2003"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "RequestApprove"
                }
            }
            result {
                displayName = "To-Create-Request Approve-To-RequestApprove"
                nextStepKey = 303
            }
        }
        action {
            key = 102
            name = "Edit"
            label = "edit_8118129637"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2004"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Create-Edit-To-Edit"
                nextStepKey = 304
            }
        }
        action {
            key = 103
            name = "Delete"
            label = "delete_8118129643"
            showAsBulk = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2005"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            confirmMessage {
                title = "deleteconfirmmsg_8118129648"
                description = "deleteconfirmdesc_8118129651"
            }
            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "Deleted"
                }
            }
            result {
                displayName = "To-Create-Delete-To-Deleted"
                nextStepKey = 305
            }
        }
    }

    step {
        key = 303
        name = "To-RequestApprove"
        editable = false
        statusCode = "303"
        action {
            key = 201
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2006"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "submitDisputeCase"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "submitDisputeCase"
                }
            }
            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "Approved"
                }
            }
            function {
                functionKey = null
                name = "actionNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "actionNotification"
                }
                argument {
                    name = "Action"
                    value = "Created"
                }
            }
            result {
                displayName = "To-RequestApprove-Approve-To-Submitted"
                nextStepKey = 406
            }
        }
        action {
            key = 202
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "2007"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CheckerRepair"
                }
            }

            result {
                displayName = "To-RequestApprove-Reject-To-NewEntryRejected"
                nextStepKey = 312
            }
        }
    }
    step {
        key = 304
        name = "To-Edit"
        editable = true
        statusCode = "304"
        action {
            key = 301
            name = "Save"
            label = "save_8118129683"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2008"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "disputeValidator"
                type = "spring"
                functionType = "validator"
                argument {
                    name = "bean.name"
                    value = "disputeValidator"
                }
            }

            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "Modified"
                }
            }
            result {
                displayName = "To-Edit-Save-To-Create"
                nextStepKey = 302
            }
        }
        action {
            key = 302
            name = "Cancel"
            label = "cancel_8118129689"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2009"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Cancel-To-Create"
                nextStepKey = 302
            }
        }
    }

    step {
        key = 406
        name = "To-Active"
        editable = false
        statusCode = "406"
        action {
            key = 4061
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "4061"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "CloseDispute"
                }
            }
            result {
                displayName = "To-Submitted-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
        action {
            key = 4063
            name = "SVC_CloseApprovedDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "4063"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "ApproveCloseDispute"
                }
            }
            result {
                displayName = "To-Submitted-SVC_CloseApprovedDispute-To-ApproveCloseDispute"
                nextStepKey = 721
            }
        }
        action {
            key = 4062
            name = "SVC_OperatorCloseDispute"
            label = "SVC_OperatorCloseDispute"
            actionCode = "4062"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Submitted-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    step {
        key = 312
        name = "To-NewEntryRejected"
        editable = false
        statusCode = "312"
        action {
            key = 1001
            name = "Repair"
            label = "repair_8118129791"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2021"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-NewEntryRejected-Repair-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
    }

    step {
        key = 314
        name = "To-RepairNewEntry"
        editable = false
        statusCode = "314"
        action {
            key = 1201
            name = "Request Approve"
            label = "requestapprove_8118129821"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2024"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "RequestApprove"
                }
            }
            result {
                displayName = "To-RepairNewEntry-Request Approve-To-RequestApprove"
                nextStepKey = 303
            }
        }
        action {
            key = 1202
            name = "Edit"
            label = "edit_8118129827"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2025"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RepairNewEntry-Edit-To-EditRejectedNewEntry"
                nextStepKey = 315
            }
        }
        action {
            key = 1203
            name = "Delete"
            label = "delete_8118129833"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2026"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            confirmMessage {
                title = "deleteconfirmmsg_8118129838"
                description = "deleteconfirmdesc_8118129841"
            }
            function {
                functionKey = null
                name = "bellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "Deleted"
                }
            }
            result {
                displayName = "To-RepairNewEntry-Delete-To-Deleted"
                nextStepKey = 305
            }
        }
    }

    step {
        key = 315
        name = "To-EditRejectedNewEntry"
        editable = true
        statusCode = "315"
        action {
            key = 1301
            name = "Save"
            label = "save_8118129852"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2027"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "disputeValidator"
                type = "spring"
                functionType = "validator"
                argument {
                    name = "bean.name"
                    value = "disputeValidator"
                }
            }
            result {
                displayName = "To-EditRejectedNewEntry-Save-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
        action {
            key = 1302
            name = "Cancel"
            label = "cancel_8118129858"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2028"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRejectedNewEntry-Cancel-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
    }

    step {
        key = 714
        name = "To-ApproveCloseDispute"
        editable = false
        statusCode = "714"
    }

    step {
        key = 305
        name = "To-Deleted"
        editable = false
        statusCode = "305"
    }
    step {
        key = 721
        name = "To-ApproveCloseApprovedDispute"
        editable = false
        statusCode = "721"
    }
}