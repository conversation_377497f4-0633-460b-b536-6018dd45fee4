package com.progressoft.dms.dsl.workflows

import com.progressoft.dms.dsl.entities.DMS_ClaimantBankCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_ClaimantNCBCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_DefendantBankCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_DefendantNCBCaseManagement_Entity
import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_OperatorCaseManagement_Workflow = Workflow {
    name = "WF_DMS_OperatorCaseManagement"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "3001"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Neutral"
        result {
            displayName = "Initialization-Initialize-To-Reply"
            nextStepKey = 9900
        }
    }
    initialAction {
        key = 1
        name = "NCB-Created"
        label = "claimant.ncb.new"
        doRefresh = true
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "3002"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Neutral"
        result {
            displayName = "Initialization-Create-To-Create"
            nextStepKey = 9900
        }
    }

    step {
        key = 9900
        name = "Arbitrated-Dispute"
        editable = false
        statusCode = "9900"
        action {
            key = 99001
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99001"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-Accept-To-AcceptanceApproval"
                nextStepKey = 9901
            }
        }
        action {
            key = 99002
            name = "Reject"
            label = "reject_8118130040"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99002"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-Reject-To-RejectionApproval"
                nextStepKey = 9902
            }
        }
        action {
            key = 99003
            name = "Ask Claimant"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99003"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 11011
            }
        }
        action {
            key = 99004
            name = "Ask Defendant"
            label = "ask.defendant.bank"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99004"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "Arbitrated-Dispute-Request-Additional-Info-DefendantNCB-To-Request-Additional-Info"
                nextStepKey = 1302
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approve"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-SVC_SLA_Accept-To-AcceptanceApproval"
                nextStepKey = 9903
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-SVC_SLA_Reject-To-RejectionApproval"
                nextStepKey = 9904
            }
        }
    }
    step {
        key = 9901
        name = "To-AcceptanceApproval"
        editable = false
        statusCode = "9901"
        action {
            key = 99011
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99011"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approve"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorCheckerApprove"
                }
            }
            result {
                displayName = "To-AcceptanceApproval-Approve-To-Approved-Dispute"
                nextStepKey = 9903
            }
        }
        action {
            key = 99012
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "99012"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorCheckerRepair"
                }
            }
            result {
                displayName = "To-AcceptanceApproval-Decline-To-NewEntryRejected"
                nextStepKey = 9905
            }
        }
    }
    step {
        key = 9902
        name = "To-RejectionApproval"
        editable = false
        statusCode = "9902"
        action {
            key = 99021
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99021"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorCheckerReject"
                }
            }
            result {
                displayName = "To-RejectionApproval-Approve-To-Declined-Dispute"
                nextStepKey = 9904
            }
        }
        action {
            key = 99022
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "99022"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorCheckerRepair"
                }
            }
            result {
                displayName = "To-RejectionApproval-Decline-To-NewEntryRejected"
                nextStepKey = 9905
            }
        }
    }
    step {
        key = 9905
        name = "To-Repair"
        editable = false
        statusCode = "9905"
        action {
            key = 99051
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99051"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "To-Repair-Accept-To-AcceptanceApproval"
                nextStepKey = 9901
            }
        }
        action {
            key = 99052
            name = "Reject"
            label = "reject_8118130040"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99052"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "To-Repair-Reject-To-RejectionApproval"
                nextStepKey = 9902
            }
        }
        action {
            key = 99053
            name = "Ask Claimant"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99053"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 11011
            }
        }

        action {
            key = 99054
            name = "Ask Defendant"
            label = "ask.defendant.bank"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "99054"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "Arbitrated-Dispute-Request-Additional-Info-DefendantNCB-To-RequestApproval"
                nextStepKey = 1322
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approve"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-SVC_SLA_Accept-To-AcceptanceApproval"
                nextStepKey = 9903
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-SVC_SLA_Reject-To-RejectionApproval"
                nextStepKey = 9904
            }
        }
    }

    step {
        key = 9903
        name = "To-Approved-Dispute"
        editable = false
        statusCode = "9903"
        action {
            key = 99030
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "99030"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    step {
        key = 714
        name = "To-CloseDispute"
        editable = false
        statusCode = "714"
    }

    step {
        key = 9904
        name = "To-Declined-Dispute"
        editable = false
        statusCode = "9904"
        action {
            key = 99030
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "99030"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorCheckerReject"
                }
            }
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    // ************************* Additional Info Required *************************
    step {
        key = 11011
        name = "To-Operator-More-Info-From-Bank-Edit"
        editable = false
        statusCode = "11011"
        action {
            key = 110111
            name = "Send Request"
            label = "claimant.ncb.sendrequest"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"

            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName =
                    "To-Operator-More-Info-From-Bank-Edit-Send Request-To-Operator-RequestAdditionalInfo-Approval"
                nextStepKey = 11012
            }
        }
        action {
            key = 110112
            name = "Cancel"
            label = "claimant.ncb.cancel"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Operator-More-Info-Claimant-Bank-Edit-Cancel-To-Operator-More-Info-From-Bank-Request"
                nextStepKey = 9900
            }
        }
    }
    ////ask claimant checker
    step {
        key = 11012
        name = "To-Operator-RequestAdditionalInfo-Approval"
        editable = false
        statusCode = "11012"
        action {
            key = 110121
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110121"
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfo"
                }
                argument {
                    name = "lastAction"
                    value = "Request More Info From Assigner"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerToClaimant"
                }
            }
            result {
                displayName =
                    "To-Operator-RequestAdditionalInfo-Approval-Approve Request-To-Operator-To-NCB-ApprovedAdditionalInfoRequest"
                nextStepKey = 11013
            }
        }
        action {
            key = 110122
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "110122"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorCheckerRepair"
                }
            }
            result {
                displayName = "To-RequestAdditionalInfo-DeclineRequest-To-Operator-More-Info-From-Bank-Repair"
                nextStepKey = 11014
            }
        }
    }

    step {
        key = 11013
        name = "To-Operator-To-NCB-ApprovedAdditionalInfoRequest-FromClaimantNCB"
        editable = false
        statusCode = "11013"
        action {
            key = 110131
            name = "SVC_MoreInfoProvided"
            label = "JFW_WF_ACTION.*********"
            actionCode = "110131"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorAdditionalInfoProvided"
                }
            }
            result {
                displayName = "To-Claimant-NCB-SVC_MoreInfo-To-ApproveAdditionalInfoRequest"
                nextStepKey = 11015
            }
        }
    }

    step {
        key = 11014
        name = "To-Operator-More-Info-From-Bank-Repair"
        editable = false
        statusCode = "11014"
        action {
            key = 110141
            name = "Repair"
            label = "claimant.ncb.repair"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110141"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Operator-More-Info-From-Bank-Repair-To-Operator-More-Info-From-Bank-Request"
                nextStepKey = 11016
            }
        }
    }

    step {
        key = 11015
        name = "To-MoreInfoProvided"
        editable = false
        statusCode = "11015"
        action {
            key = 110151
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110151"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "To-Repair-Accept-To-AcceptanceApproval"
                nextStepKey = 9901
            }
        }
        //////////////////
        action {
            key = 110152
            name = "Reject"
            label = "reject_8118130040"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110152"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "To-Repair-Reject-To-RejectionApproval"
                nextStepKey = 9902
            }
        }
        action {
            key = 110154
            name = "Ask Claimant"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110154"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 11011
            }
        }
        action {
            key = 110153
            name = "Ask Defendant"
            label = "ask.defendant.bank"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110153"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "Arbitrated-Dispute-Request-Additional-Info-DefendantNCB-To-RequestApproval"
                nextStepKey = 1302
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approve"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-SVC_SLA_Accept-To-AcceptanceApproval"
                nextStepKey = 9903
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-SVC_SLA_Reject-To-RejectionApproval"
                nextStepKey = 9904
            }
        }
    }

    step {
        key = 11016
        name = "To-Operator-More-Info-From-Bank-Edit"
        editable = false
        statusCode = "11016"
        action {
            key = 110161
            name = "Send Request"
            label = "claimant.ncb.sendrequest"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110161"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName =
                    "To-Operator-More-Info-From-Bank-Edit-Send Request-To-Operator-RequestAdditionalInfo-Approval"
                nextStepKey = 11012
            }
        }
        action {
            key = 110162
            name = "Cancel"
            label = "claimant.ncb.cancel"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "110162"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Operator-More-Info-Claimant-Bank-Edit-Cancel-To-Operator-More-Info-From-Bank-Request"
                nextStepKey = 11014
            }
        }
    }

    step {
        key = 1302
        name = "To-Request-Additional-Info-From-Defendant-NCB"
        editable = false
        statusCode = "1302"
        action {
            key = 13021
            name = "Send"
            label = "send_8118129683"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "13021"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "To-RequestAdditionalInfo-Send-To-RequestApprove"
                nextStepKey = 1300
            }
        }
        action {
            key = 13022
            name = "Cancel"
            label = "cancel_8118129689"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "13022"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RequestAdditionalInfo-Cancel-To-Arbitrated-Dispute"
                nextStepKey = 9900
            }
        }
    }
    step {
        key = 1322
        name = "To-Request-Additional-Info-From-Defendant-NCB"
        editable = true
        statusCode = "1322"
        action {
            key = 13221
            name = "Send"
            label = "send_8118129683"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "13221"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "To-RequestAdditionalInfo-Send-To-RequestApprove"
                nextStepKey = 1300
            }
        }
        action {
            key = 13222
            name = "Cancel"
            label = "cancel_8118129689"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "13222"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RequestAdditionalInfo-Cancel-To-Arbitrated-Dispute"
                nextStepKey = 9905
            }
        }
    }
    step {
        key = 1300
        name = "To-RequestApproval"
        editable = false
        statusCode = "1300"
        action {
            key = 13001
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "13001"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "nationalTargetEntity"
                    value = DMS_DefendantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfo"
                }
                argument {
                    name = "lastAction"
                    value = "Request More Info From Assignee"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorToDefendantAdditionalInfo"
                }
            }
            result {
                displayName = "To-RequestApproval-Approve-To-Approved-Request"
                nextStepKey = 1308
            }
        }
        action {
            key = 13002
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "13002"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorCheckerRepair"
                }
            }
            result {
                displayName = "To-RequestApproval-Decline-To-RequestRejected"
                nextStepKey = 9905
            }
        }
    }
    step {
        key = 1308
        name = "To-Operator-To-NCB-ApprovedAdditionalInfoRequest-FromDefendantNCB"
        editable = false
        statusCode = "1308"
        action {
            key = 13081
            name = "SVC_MoreInfoProvided"
            label = "JFW_WF_ACTION.*********"
            actionCode = "13081"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-Claimant-NCB-SVC_MoreInfo-To-ApproveAdditionalInfoRequest"
                nextStepKey = 1400
            }
        }
    }

    step {
        key = 1400
        name = "To-DefendantMoreInfoProvided"
        editable = false
        statusCode = "1400"
        action {
            key = 14001
            name = "Accept"
            label = "accept_8118129672"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "14001"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "To-Repair-Accept-To-AcceptanceApproval"
                nextStepKey = 9901
            }
        }
        action {
            key = 14002
            name = "Reject"
            label = "reject_8118130040"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "14002"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            function {
                functionKey = null
                name = "bankCaseBellNotification"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "bankCaseBellNotification"
                }
                argument {
                    name = "notificationType"
                    value = "OperatorMakerAction"
                }
            }
            result {
                displayName = "To-Repair-Reject-To-RejectionApproval"
                nextStepKey = 9902
            }
        }
        action {
            key = 14003
            name = "Ask Claimant"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "14003"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 11011
            }
        }
        action {
            key = 14004
            name = "Ask Defendant"
            label = "ask.defendant.bank"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "14004"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "Arbitrated-Dispute-Request-Additional-Info-DefendantNCB-To-RequestApproval"
                nextStepKey = 1302
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_Approve"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            function {
                functionKey = null
                name = "approvedDisputeNotificationMessage"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "approvedDisputeNotificationMessage"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-SVC_SLA_Accept-To-AcceptanceApproval"
                nextStepKey = 9903
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_ClaimantNCBCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Operator"
                }
            }
            result {
                displayName = "Arbitrated-Dispute-SVC_SLA_Reject-To-RejectionApproval"
                nextStepKey = 9904
            }
        }
    }
}