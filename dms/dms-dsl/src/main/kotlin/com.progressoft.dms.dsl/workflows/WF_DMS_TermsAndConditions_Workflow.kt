package com.progressoft.dms.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_TermsAndConditions_Workflow = Workflow {
    name = "WF_DMS_TermsAndConditions"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        actionCode = "1"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"

        result {
            displayName = "Initialization-Initialize-EditStep"
            nextStepKey = 554001
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "create_8118129619"
        doRefresh = true
        actionCode = "2"
        serviceAction = true
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "DRAFT"
        actionLevel = "Maker Level"

        result {
            displayName = "Initialization-Create-EditStep"
            nextStepKey = 554001
        }
    }
    step {
        key = 554001
        name = "To-Create"
        editable = true
        statusCode = "554001"
        action {
            key = 12916492
            name = "Request Approve"
            label = "requestapprove_8118129631"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "21"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            result {
                displayName = "To-Create-Request Approve-To-RequestApprove"
                nextStepKey = 554002
            }
        }
    }

    step {
        key = 554002
        name = "To-RequestApprove"
        editable = false
        statusCode = "554002"
        action {
            key = 270088122
            name = "Approve"
            label = "approve_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "3"
            notify = true
            notificationTimeToLive = 0
            sendIdOnly = true
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            tenantAware = true
            actionLevel = "Checker Level"

            result {
                displayName = "CreationApproval-Approve-ViewApproved"
                nextStepKey = 554003
            }
        }
    }
    step {
        key = 554003
        name = "ViewApproved"
        editable = false
        statusCode = "554003"
        action {
            key = 2009206734
            name = "Edit"
            label = "edit_8118129827"
            actionCode = "9"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"

            result {
                displayName = "ViewApproved-Edit-EditApproved"
                nextStepKey = 554004
            }
        }
    }
    step {
        key = 554004
        name = "EditApproved"
        editable = true
        statusCode = "554004"
        action {
            key = 38003820
            name = "Save"
            label = "save_8118129852"
            doRefresh = true
            actionCode = "11"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"

            result {
                displayName = "EditApproved-Save-ModificationApproval"
                nextStepKey = 554006
            }
        }
        action {
            key = 860404382
            name = "Cancel"
            label = "cancel_8118129858"
            actionCode = "12"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            result {
                displayName = "EditApproved-Cancel-ViewApproved"
                nextStepKey = 554003
            }
        }
    }

    step {
        key = 554006
        name = "ModificationApproval"
        editable = false
        statusCode = "554006"
        action {
            key = 765069488
            name = "Reject"
            label = "reject_8118130040"
            actionCode = "14"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"

            result {
                displayName = "ModificationApproval-Reject-RepairRejected"
                nextStepKey = 554007
            }
        }
        action {
            key = 1708299783
            name = "Approve"
            label = "approve_8118129662"
            doRefresh = true
            actionCode = "13"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT_DRAFT"
            actionLevel = "Checker Level"

            function {
                functionKey = null
                name = "resetAcceptTermsFunction"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "resetAcceptTermsFunction"
                }
            }

            result {
                displayName = "ModificationApproval-Approve-ViewApproved"
                nextStepKey = 554003
            }
        }
    }

    step {
        key = 554007
        name = "RepairRejected"
        editable = false
        statusCode = "554007"
        action {
            key = 1003864638
            name = "Reset"
            label = "reset"
            doRefresh = true
            actionCode = "16"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "REMOVE_DRAFT"
            actionLevel = "Maker Level"

            result {
                displayName = "RepairRejected-Reset-ViewApproved"
                nextStepKey = 554003
            }
        }
        action {
            key = 1088063286
            name = "Edit"
            label = "edit_8118129827"
            actionCode = "15"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            result {
                displayName = "RepairRejected-Edit-EditRejected"
                nextStepKey = 554008
            }
        }
    }
    step {
        key = 554008
        name = "EditRejected"
        editable = true
        statusCode = "554008"
        action {
            key = 2002595504
            name = "Save"
            label = "save_8118129852"
            doRefresh = true
            actionCode = "17"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "DRAFT"
            actionLevel = "Maker Level"

            result {
                displayName = "EditRejected-Save-ModificationApproval"
                nextStepKey = 554006
            }
        }
        action {
            key = 2106464816
            name = "Cancel"
            label = "cancel_8118129858"
            doRefresh = true
            actionCode = "18"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"

            result {
                displayName = "EditRejected-Cancel-RepairRejected"
                nextStepKey = 554007
            }
        }
    }

}