package com.progressoft.dms.dsl.workflows

import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_Bank_Workflow = Workflow {
    name = "WF_DMS_Bank"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "2001"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Maker Level"
        result {
            displayName = "Initialization-Initialize-To-Create"
            nextStepKey = 302
        }
    }
    initialAction {
        key = 1
        name = "Create"
        label = "create_8118129619"
        doRefresh = true
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "2002"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Maker Level"
        result {
            displayName = "Initialization-Create-To-Create"
            nextStepKey = 302
        }
    }
    step {
        key = 302
        name = "To-Create"
        editable = false
        statusCode = "302"
        action {
            key = 101
            name = "Request Approve"
            label = "requestapprove_8118129631"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2003"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Create-Request Approve-To-RequestApprove"
                nextStepKey = 303
            }
        }
        action {
            key = 102
            name = "Edit"
            label = "edit_8118129637"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2004"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Create-Edit-To-Edit"
                nextStepKey = 304
            }
        }
    }
    step {
        key = 303
        name = "To-RequestApprove"
        editable = false
        statusCode = "303"
        action {
            key = 201
            name = "Approve"
            label = "approve_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2006"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "createBank"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "createBank"
                }
            }
            result {
                displayName = "To-RequestApprove-Approve-To-Approved"
                nextStepKey = 306
            }
        }
        action {
            key = 202
            name = "Reject"
            label = "reject_8118129671"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionPortalId = "**********"
            actionCode = "2007"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-RequestApprove-Reject-To-NewEntryRejected"
                nextStepKey = 312
            }
        }
    }
    step {
        key = 304
        name = "To-Edit"
        editable = true
        statusCode = "304"
        action {
            key = 301
            name = "Save"
            label = "save_8118129683"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2008"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Save-To-Create"
                nextStepKey = 302
            }
        }
        action {
            key = 302
            name = "Cancel"
            label = "cancel_8118129689"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2009"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Cancel-To-Create"
                nextStepKey = 302
            }
        }
    }
    step {
        key = 306
        name = "To-Approved"
        editable = false
        statusCode = "306"
        action {
            key = 501
            name = "Modify"
            label = "modify_8118129707"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2011"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Approved-Modify-To-Modified"
                nextStepKey = 308
            }
        }
    }
    step {
        key = 307
        name = "To-Rejected"
        editable = false
        statusCode = "307"
        action {
            key = 601
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2013"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Rejected-Repair-To-EditRepair"
                nextStepKey = 311
            }
        }
    }
    step {
        key = 308
        name = "To-Modified"
        editable = true
        statusCode = "308"
        action {
            key = 701
            name = "Save"
            label = "save_8118129737"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2014"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Modified-Save-To-ModifiedRequestApprove"
                nextStepKey = 313
            }
        }
        action {
            key = 702
            name = "Cancel"
            label = "cancel_8118129743"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2015"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Modified-Cancel-To-Approved"
                nextStepKey = 306
            }
        }
    }
    step {
        key = 311
        name = "To-EditRepair"
        editable = true
        statusCode = "311"
        action {
            key = 901
            name = "Save"
            label = "save_8118129773"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2020"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRepair-Save-To-ModifiedRequestApprove"
                nextStepKey = 313
            }
        }
        action {
            key = 902
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2015"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRepair-Cancel-To-Rejected"
                nextStepKey = 307
            }
        }
    }
    step {
        key = 312
        name = "To-NewEntryRejected"
        editable = false
        statusCode = "312"
        action {
            key = 1001
            name = "Repair"
            label = "repair_8118129791"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2021"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-NewEntryRejected-Repair-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
    }
    step {
        key = 313
        name = "To-ModifiedRequestApprove"
        editable = false
        statusCode = "313"
        action {
            key = 1101
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2022"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            result {
                displayName = "To-ModifiedRequestApprove-Approve-To-Approved"
                nextStepKey = 306
            }
        }
        action {
            key = 1102
            name = "Reject"
            label = "reject_8118129809"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionPortalId = "**********"
            actionCode = "2023"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-ModifiedRequestApprove-Reject-To-Rejected"
                nextStepKey = 307
            }
        }
    }
    step {
        key = 314
        name = "To-RepairNewEntry"
        editable = false
        statusCode = "314"
        action {
            key = 1201
            name = "Request Approve"
            label = "requestapprove_8118129821"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2024"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RepairNewEntry-Request Approve-To-RequestApprove"
                nextStepKey = 303
            }
        }
        action {
            key = 1202
            name = "Edit"
            label = "edit_8118129827"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2025"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RepairNewEntry-Edit-To-EditRejectedNewEntry"
                nextStepKey = 315
            }
        }
    }
    step {
        key = 315
        name = "To-EditRejectedNewEntry"
        editable = true
        statusCode = "315"
        action {
            key = 1301
            name = "Save"
            label = "save_8118129852"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2027"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRejectedNewEntry-Save-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
        action {
            key = 1302
            name = "Cancel"
            label = "cancel_8118129858"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "2028"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-EditRejectedNewEntry-Cancel-To-RepairNewEntry"
                nextStepKey = 314
            }
        }
    }
}