package com.progressoft.dms.dsl.workflows

import com.progressoft.dms.dsl.entities.DMS_ClaimantBankCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_ClaimantNCBCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_DefendantNCBCaseManagement_Entity
import com.progressoft.dms.dsl.entities.DMS_OperatorCaseManagement_Entity
import com.progressoft.jupiter.kotlin.dsl.Workflow

val WF_DMS_ClaimantNCBCaseManagement_Workflow = Workflow {
    name = "WF_DMS_ClaimantNCBCaseManagement"
    status = false
    initialAction {
        key = 10
        name = "Initialize"
        label = "initialize_8118129613"
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "3001"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "NORMAL"
        actionLevel = "Neutral"
        result {
            displayName = "Initialization-Initialize-To-Reply"
            nextStepKey = 3010
        }
    }
    initialAction {
        key = 1
        name = "NCB-Created"
        label = "claimant.ncb.new"
        doRefresh = true
        nextItemMode = "DONT_GET_NEXT"
        actionCode = "3002"
        notificationTimeToLive = 0
        deleteItem = "NORMAL"
        actionMode = "COMMIT"
        actionLevel = "Neutral"
        result {
            displayName = "Initialization-Create-To-Create"
            nextStepKey = 3010
        }
    }

    step {
        key = 3010
        name = "To-Clmt-NCB-New-Dispute"
        editable = false
        statusCode = "3010"
        action {
            key = 30102
            name = "NCB-Accept"
            label = "claimant.ncb.accept"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30102"
            actionPortalId = "8118621167"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-New-Dispute-To-NCB-Accept-Request-Approval"
                nextStepKey = 4010
            }
        }
        action {
            key = 30103
            name = "NCB-Reject"
            label = "claimant.ncb.reject"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30103"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-New-Dispute-To-NCB-Reject-Request-Approval"
                nextStepKey = 5010
            }
        }
        action {
            key = 30101
            name = "Ask Claimant Bank"
            label = "ask.claimant.bank"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30101"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-New-Dispute-To-Clmt-NCB-More-Info-From-Bank-Edit"
                nextStepKey = 3011
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "arbitrateDisputeCase"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "arbitrateDisputeCase"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-Clmt-NCB-New-Dispute-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 4011
            }
        }
        action {
            key = 1148821254
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "1148821254"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-Clmt-NCB-New-Dispute-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 5011
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Arbitrate"
            label = "svc_sla_arbitrate"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "arbitrateDisputeCase"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "arbitrateDisputeCase"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_OperatorCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Arbitrate Dispute To Operator"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-Clmt-NCB-New-Dispute-SVC_SLA_Arbitrate-To-ApproveAcceptance"
                nextStepKey = 6014
            }
        }
    }
    step {
        key = 3011
        name = "To-Clmt-NCB-More-Info-From-Bank-Edit"
        editable = true
        statusCode = "3011"
        action {
            key = 30111
            name = "Send Request"
            label = "claimant.ncb.sendrequest"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-More-Info-From-Bank-Edit-Send Request-To-NCB-RequestAdditionalInfo-Approval"
                nextStepKey = 3012
            }
        }
        action {
            key = 30112
            name = "Cancel"
            label = "claimant.ncb.cancel"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName =
                    "To-Claimant-NCB-More-Info-Claimant-Bank-Edit-Cancel-To-Clmt-NCB-More-Info-From-Bank-Request"
                nextStepKey = 3010
            }
        }
    }
    step {
        key = 3012
        name = "To-NCB-RequestAdditionalInfo-Approval"
        editable = false
        statusCode = "3012"
        action {
            key = 30121
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30121"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_ClaimantBankCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfo"
                }
                argument {
                    name = "lastAction"
                    value = "Request More Info From Assigner Bank"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName =
                    "To-NCB-RequestAdditionalInfo-Approval-Approve Request-To-NCB-To-Bank-ApprovedAdditionalInfoRequest"
                nextStepKey = 3013
            }
        }
        action {
            key = 30122
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "30122"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-RequestAdditionalInfo-DeclineRequest-To-NCB-More-Info-From-Bank-Repair"
                nextStepKey = 3014
            }
        }
    }

    step {
        key = 4010
        name = "To-NCB-Accept-Request-Approval"
        editable = false
        statusCode = "4010"
        action {
            key = 40101
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "40101"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "arbitrateDisputeCase"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "arbitrateDisputeCase"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-NCB-Request-Approval-ApproveRequest-To-NCB-Approved-Dispute"
                nextStepKey = 4011
            }
        }
        action {
            key = 40102
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "40102"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-NCB-Request-Approval-DeclineRequest-To-Clmt-NCB-More-Info-From-Bank-Request"
                nextStepKey = 3010
            }
        }
    }

    step {
        key = 5010
        name = "To-NCB-Reject-Request-Approval"
        editable = false
        statusCode = "5010"
        action {
            key = 50101
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "50101"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-NCB-Request-Approval-ApproveRequest-To-RejectedDispute"
                nextStepKey = 5011
            }
        }
        action {
            key = 50102
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "50102"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-NCB-Request-Approval-DeclineRequest-To-Clmt-NCB-New-Dispute"
                nextStepKey = 3010
            }
        }
    }

    step {
        key = 5020
        name = "To-NCB-Reject-Request-Approval"
        editable = false
        statusCode = "5020"
        action {
            key = 50201
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "50101"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-NCB-Request-Approval-ApproveRequest-To-RejectedDispute"
                nextStepKey = 5011
            }
        }
        action {
            key = 50202
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "50102"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-NCB-Request-Approval-DeclineRequest-To-Clmt-NCB-New-Dispute"
                nextStepKey = 6015
            }
        }
    }

    step {
        key = 3013
        name = "To-NCB-To-Bank-ApprovedAdditionalInfoRequest"
        editable = false
        statusCode = "3013"
        action {
            key = 30131
            name = "SVC_MoreInfoProvided"
            label = "JFW_WF_ACTION.*********"
            actionCode = "30131"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "TrxlistingProcessing-SVC_MoreInfoProvided-To-MoreInfoProvided-TrxReview"
                nextStepKey = 3015
            }
        }
    }

    step {
        key = 4011
        name = "To-Defendant-NCB"
        editable = false
        statusCode = "4011"
        action {
            key = **********
            name = "SVC_Declined_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_Declined_Dispute-To-Declined_Dispute"
                nextStepKey = 6015
            }
        }
        action {
            key = 1148821201
            name = "SVC_Approved_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "1148821201"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_Approved_Dispute-To-Approved_Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 1148821202
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.185313002"
            actionCode = "1148821202"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_More_Info"
                nextStepKey = 1105
            }
        }
    }
    step {
        key = 9010
        name = "To-Edit"
        editable = true
        statusCode = "9010"
        action {
            key = 90100
            name = "Send Request"
            label = "sendrequest_8118129684"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "90100"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Send-Re-Present-Request"
                nextStepKey = 9011
            }
            function {
                functionKey = null
                name = "representAttachmentValidator"
                type = "spring"
                functionType = "validator"
                argument {
                    name = "bean.name"
                    value = "representAttachmentValidator"
                }
            }
        }
        action {
            key = 90101
            name = "Cancel"
            label = "cancel_8118190101"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "90101"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Cancel-To-RejectedDispute"
                nextStepKey = 6015
            }
        }
    }

    step {
        key = 9011
        name = "To-RequestRePresent"
        editable = false
        statusCode = "9011"
        action {
            key = 90110
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "90110"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_Re_Present"
                }
                argument {
                    name = "lastAction"
                    value = "Re-Present Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            function {
                functionKey = null
                name = "incrementRepresentCount"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "incrementRepresentCount"
                }
                argument {
                    name = "type"
                    value = "CLAIMANTNCB"
                }
            }
            result {
                displayName = "To-RequestRePresent-ApproveRequest-To-ApprovedRePresentRequest"
                nextStepKey = 9013
            }
        }
        action {
            key = 90111
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "90111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-RequestRePresent-DeclineRequest-To-DeclinedRepairedDispute"
                nextStepKey = 9012
            }
        }
    }

    step {
        key = 9012
        name = "To-DeclinedRepairedDispute"
        editable = false
        statusCode = "9012"
        action {
            key = 90120
            name = "Re Present"
            label = "re_present_8118150110"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "90120"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-RepairRePresentDispute-Re-Present-To-Edit"
                nextStepKey = 9014
            }
        }
    }

    step {
        key = 9014
        name = "To-Edit"
        editable = true
        statusCode = "9014"
        action {
            key = 90140
            name = "Send Request"
            label = "sendrequest_8118129684"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "90140"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Send-Re-Present-Request"
                nextStepKey = 9011
            }
            function {
                functionKey = null
                name = "representAttachmentValidator"
                type = "spring"
                functionType = "validator"
                argument {
                    name = "bean.name"
                    value = "representAttachmentValidator"
                }
            }
        }
        action {
            key = 90141
            name = "Cancel"
            label = "cancel_8118190101"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "90141"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Edit-Cancel-To-RejectedDispute"
                nextStepKey = 9012
            }
        }
    }

    step {
        key = 3014
        name = "To-NCB-More-Info-From-Bank-Repair"
        editable = false
        statusCode = "3014"
        action {
            key = 30141
            name = "Repair"
            label = "claimant.ncb.repair"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30141"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-NCB-More-Info-From-Bank-Repair-To-Clmt-NCB-More-Info-From-Bank-Request"
                nextStepKey = 3010
            }
        }
    }

    step {
        key = 3015
        name = "To-MoreInfoProvided"
        editable = false
        statusCode = "3015"
        action {
            key = 30152
            name = "NCB-Accept"
            label = "claimant.ncb.accept"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30152"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-New-Dispute-To-NCB-Request-Approval"
                nextStepKey = 4010
            }
        }
        action {
            key = 30153
            name = "NCB-Reject"
            label = "claimant.ncb.reject"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30153"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-New-Dispute-To-NCB-Reject-Request-Approval"
                nextStepKey = 5010
            }
        }
        action {
            key = 30151
            name = "Ask Claimant Bank"
            label = "ask_claimant_bank_8118129671"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "30151"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Reply-Request AdditionalInfo-To-RequestAdditionalInfo"
                nextStepKey = 3011
            }
        }
        action {
            key = **********
            name = "SVC_SLA_Accept"
            label = "svc_sla_accept"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "arbitrateDisputeCase"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "arbitrateDisputeCase"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Approve Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-Clmt-NCB-New-Dispute-SVC_SLA_Accept-To-ApproveAcceptance"
                nextStepKey = 4011
            }
        }
        action {
            key = 1148821254
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "1148821254"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-Clmt-NCB-New-Dispute-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 5011
            }
        }
    }

    step {
        key = 9013
        name = "To-ApprovedRePresentRequest"
        editable = false
        statusCode = "9013"
        action {
            key = **********
            name = "SVC_Declined_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_Declined_Dispute-To-Declined_Dispute"
                nextStepKey = 6015
            }
        }
        action {
            key = 1148829013
            name = "SVC_Approved_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "1148829013"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_Approved_Dispute-To-Approved_Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 1148821213
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.185313002"
            actionCode = "1148821213"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_More_Info"
                nextStepKey = 1105
            }
        }
    }

    step {
        key = 5011
        name = "To-RejectedDispute"
        editable = false
        statusCode = "5011"
        action {
            key = 7066
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "7066"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    step {
        key = 6014
        name = "To-Approved-Dispute"
        editable = false
        statusCode = "6014"
        action {
            key = 61140
            name = "SVC_CloseDispute"
            label = "JFW_WF_ACTION.185313307"
            actionCode = "61140"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveAcceptance-SVC_CloseDispute-To-ApproveCloseDispute"
                nextStepKey = 714
            }
        }
    }

    step {
        key = 714
        name = "To-CloseDispute"
        editable = false
        statusCode = "714"
    }

    step {
        key = 6015
        name = "To-Declined-Dispute"
        editable = false
        statusCode = "6015"
        action {
            key = 60150
            name = "Re Present"
            label = "re_present_8118150110"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60150"
            actionPortalId = "**********"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            conditionGroup {
                name = "representDisputeCondition"
                isOr = false
                condition {
                    condKey = 91003
                    name = "representDisputeCondition"
                    negate = false
                    type = "spring"
                    argument {
                        name = "bean.name"
                        value = "representDisputeCondition"
                    }
                    argument {
                        name = "type"
                        value = "CLAIMANTNCB"
                    }
                }
            }
            result {
                displayName = "To-RejectedDispute-Re_Present-To-Edit"
                nextStepKey = 9010
            }
            function {
                functionKey = null
                name = "disputeCaseAttCounter"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "disputeCaseAttCounter"
                }
            }
        }
        action {
            key = 60151
            name = "Arbitrate To Operator"
            label = "clmt.ncb.arbitrate"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60151"
            actionPortalId = "8118621167"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            conditionGroup {
                name = "arbitrateDisputeCondition"
                isOr = false
                condition {
                    condKey = 910023
                    name = "arbitrateDisputeCondition"
                    negate = false
                    type = "spring"
                    argument {
                        name = "bean.name"
                        value = "arbitrateDisputeCondition"
                    }
                    argument {
                        name = "type"
                        value = "CLAIMANTNCB"
                    }
                }
            }
            result {
                displayName = "To-ApproveRejection Approve-To-RequestArbitrate"
                nextStepKey = 6016
            }
        }
        action {
            key = 60152
            name = "NCB-Reject"
            label = "claimant.ncb.reject"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60152"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionPortalId = "**********"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Clmt-NCB-New-Dispute-To-NCB-Reject-Request-Approval"
                nextStepKey = 5020
            }
        }
        action {
            key = 1148821254
            name = "SVC_SLA_Reject"
            label = "svc_sla_reject"
            actionCode = "1148821254"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            function {
                functionKey = null
                name = "updateAllDisputes"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "updateAllDisputes"
                }
                argument {
                    name = "disputeNames"
                    value =
                        "DMS_ClaimantBankCaseManagement,DMS_DefendantBankCaseManagement,DMS_DefendantNCBCaseManagement"
                }
                argument {
                    name = "stepName"
                    value = "SVC_FinalDecline"
                }
                argument {
                    name = "lastAction"
                    value = "Reject Dispute"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-Clmt-NCB-New-Dispute-SVC_SLA_Reject-To-ApproveRejection"
                nextStepKey = 5011
            }
        }
    }

    step {
        key = 6016
        name = "To-RequestArbitration"
        editable = false
        statusCode = "6016"
        action {
            key = 60161
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60161"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "arbitrateDisputeCase"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "arbitrateDisputeCase"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_OperatorCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Arbitrate Dispute To Operator"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-RequestArbitrate-Approve-To-ApproveRejection"
                nextStepKey = 6017
            }
        }
        action {
            key = 60162
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "60162"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-RequestArbitrate-Reject-To-NewEntryRejected"
                nextStepKey = 6015
            }
        }
    }
    step {
        key = 6017
        name = "To-ApproveArbitrate"
        editable = false
        statusCode = "6017"
        action {
            key = 60171
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "60171"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 60172
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "60172"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Decline-To-RejectedDispute"
                nextStepKey = 5011
            }
        }
        action {
            key = 60173
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "60173"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantBank-SVC_Reject-To-ApproveRejection"
                nextStepKey = 12010
            }
        }
        action {
            key = 60174
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "60174"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_FinalDecline-To-RejectedDispute"
                nextStepKey = 5011
            }
        }
    }

    // ************************** Provide More Info For Defendant NCB **************************
    step {
        key = 1105
        name = "To-NCB-Requires-More-Info"
        editable = false
        statusCode = "1105"
        action {
            key = 10051
            name = "Additional-Info"
            label = "claimant.provide-additional-info"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "11051"
            actionPortalId = "8118621167"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            serviceAction = false
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-For-Defendant-NCB"
                nextStepKey = 8210
            }
        }
    }
    step {
        key = 8210
        name = "To-Provide-Additional-Info-For-Defendant-NCB"
        editable = true
        statusCode = "8210"
        action {
            key = 82101
            name = "Save"
            label = "claimant.saveandsubmit"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "82101"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-For-NCB-To-ModifiedAdditionalInfo"
                nextStepKey = 8220
            }
        }
        action {
            key = 82102
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "82102"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-For-NCB-To-Additional-Info"
                nextStepKey = 1105
            }
        }
    }
    step {
        key = 8220
        name = "To-ModifiedAdditionalInfo-For-NCB"
        editable = false
        statusCode = "8220"
        action {
            key = 82201
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "82201"
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_DefendantNCBCaseManagement_Entity.name
                }
                argument {
                    name = "lastAction"
                    value = "Additional Info Provided"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfoProvided"
                }
            }
            result {
                displayName = "To-ModifiedAdditionalInfo-Approve-For-NCB-To-SubmitMoreInfoRequest-For-NCB"
                nextStepKey = 7113
            }
        }
        action {
            key = 82202
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "82202"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-ModifiedRequestApprove-Reject-For-NCB-To-MoreInfoRejected-For-NCB"
                nextStepKey = 8240
            }
        }
    }
    step {
        key = 8240
        name = "To-MoreInfoRejected-For-NCB"
        editable = false
        statusCode = "8240"
        action {
            key = 82401
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "82401"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-MoreInfoRejected-For-NCB-Repair-To-Provide-Additional-Info-For-NCB"
                nextStepKey = 8210
            }
        }
    }
    step {
        key = 7113
        name = "To-NCB-To-Bank-ApprovedAdditionalInfoRequest"
        editable = false
        statusCode = "7113"
        action {
            key = **********
            name = "SVC_Declined_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_Declined_Dispute-To-Declined_Dispute"
                nextStepKey = 6015
            }
        }
        action {
            key = **********
            name = "SVC_Approved_Dispute"
            label = "JFW_WF_ACTION.*********"
            actionCode = "**********"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_Approved_Dispute-To-Approved_Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 1148821213
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.185313002"
            actionCode = "1148821213"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-DefendantNCB-SVC_More_Info"
                nextStepKey = 1105
            }
        }
    }

    // ************************** Provide More Info For Operator **************************
    step {
        key = 12010
        name = "To-Operator-Requires-More-Info"
        editable = false
        statusCode = "12010"
        action {
            key = 120101
            name = "Additional-Info"
            label = "claimant.provide-additional-info"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "120101"
            actionPortalId = "8118621167"
            portalHeight = 450
            portalWidth = 450
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            serviceAction = false
            actionLevel = "Maker Level"
            result {
                displayName = "To-Additional-Info-To-Provide-Additional-Info"
                nextStepKey = 12011
            }
        }
    }

    step {
        key = 12011
        name = "To-Provide-Additional-Info-For-NCB"
        editable = true
        statusCode = "12011"
        action {
            key = 120111
            name = "Save"
            label = "claimant.saveandsubmit"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "120111"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-For-NCB-To-ModifiedAdditionalInfo"
                nextStepKey = 12012
            }
        }
        action {
            key = 120112
            name = "Cancel"
            label = "cancel_8118129779"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "120112"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-Provide-Additional-Info-For-NCB-To-Additional-Info"
                nextStepKey = 12010
            }
        }
    }
    step {
        key = 12012
        name = "To-ModifiedAdditionalInfo-For-NCB"
        editable = false
        statusCode = "12012"
        action {
            key = 120121
            name = "Proceed"
            label = "proceed_8118129662"
            doRefresh = true
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "120121"
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Checker Level"
            function {
                functionKey = null
                name = "moveDisputeToStep"
                type = "spring"
                functionType = "post-function"
                argument {
                    name = "bean.name"
                    value = "moveDisputeToStep"
                }
                argument {
                    name = "regionalTargetEntity"
                    value = DMS_OperatorCaseManagement_Entity.name
                }
                argument {
                    name = "stepName"
                    value = "SVC_MoreInfoProvided"
                }
                argument {
                    name = "lastAction"
                    value = "Provide Additional Info To Operator"
                }
                argument {
                    name = "lastActionBy"
                    value = "Claimant NCB"
                }
            }
            result {
                displayName = "To-ModifiedAdditionalInfo-Approve-For-NCB-To-SubmitMoreInfoRequest-For-NCB"
                nextStepKey = 12013
            }
        }
        action {
            key = 120122
            name = "Repair"
            label = "repair_8118129662"
            doRefresh = true
            actionEditable = true
            nextItemMode = "DONT_GET_NEXT"
            portalWidth = 800
            portalHeight = 800
            actionCode = "120122"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "COMMIT"
            actionLevel = "Checker Level"
            result {
                displayName = "To-ModifiedRequestApprove-Reject-For-NCB-To-MoreInfoRejected-For-NCB"
                nextStepKey = 12014
            }
        }
    }
    step {
        key = 12013
        name = "To-SubmitMoreInfoRequest-For-NCB"
        editable = false
        statusCode = "12013"
        action {
            key = 120131
            name = "SVC_MoreInfo"
            label = "JFW_WF_ACTION.*********"
            actionCode = "120131"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_NCB_MoreInfo-To-NCB-Requires-More-Info"
                nextStepKey = 12010
            }
        }
        action {
            key = 120132
            name = "SVC_Accept"
            label = "JFW_WF_ACTION.185313306"
            actionCode = "120132"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Accept-To-ApproveAcceptance"
                nextStepKey = 6014
            }
        }
        action {
            key = 120133
            name = "SVC_Reject"
            label = "JFW_WF_ACTION.185313408"
            actionCode = "120133"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_Reject-To-ApproveRejection"
                nextStepKey = 5011
            }
        }
        action {
            key = 120134
            name = "SVC_Approve"
            label = "JFW_WF_ACTION.*********"
            actionCode = "120134"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Approve-To-Approved-Dispute"
                nextStepKey = 6014
            }
        }
        action {
            key = 120135
            name = "SVC_Decline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "120135"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-ApproveArbitrate-SVC_Decline-To-RejectedDispute"
                nextStepKey = 5011
            }
        }
        action {
            key = 120136
            name = "SVC_FinalDecline"
            label = "JFW_WF_ACTION.*********"
            actionCode = "120136"
            serviceAction = true
            notificationTimeToLive = 0
            actionLevel = "Neutral"
            result {
                displayName = "To-SubmitMoreInfoRequest-For-NCB-SVC_FinalDecline-To-RejectedDispute"
                nextStepKey = 5011
            }
        }
    }
    step {
        key = 12014
        name = "To-MoreInfoRejected-For-NCB"
        editable = false
        statusCode = "12014"
        action {
            key = 120141
            name = "Repair"
            label = "repair_8118129725"
            nextItemMode = "DONT_GET_NEXT"
            actionCode = "81401"
            notificationTimeToLive = 0
            deleteItem = "NORMAL"
            actionMode = "NORMAL"
            actionLevel = "Maker Level"
            result {
                displayName = "To-MoreInfoRejected-For-NCB-Repair-To-Provide-Additional-Info-For-NCB"
                nextStepKey = 12010
            }
        }
    }

}
