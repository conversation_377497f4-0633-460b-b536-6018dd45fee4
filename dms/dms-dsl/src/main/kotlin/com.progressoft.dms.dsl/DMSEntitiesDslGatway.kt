package com.progressoft.dms.dsl

import com.progressoft.dms.dsl.entities.*
import com.progressoft.repository.entity.EntityDslGateway
import com.progressoft.repository.entity.dto.EntityDto
import org.springframework.stereotype.Component

@Component
class DMSEntitiesDslGatway : EntityDslGateway {

    override fun allEntities(): MutableList<EntityDto> {

        return mutableListOf(
            DMS_Participant_Entity,
            DMS_NationalCentralBank_Entity,
            DMS_ClaimantBankCaseManagement_Entity,
            DMS_SystemConfiguration_Entity,
            DMS_PaymentSystems_Entity,
            DMS_DefendantBankCaseManagement_Entity,
            DMS_DefendantNCBCaseManagement_Entity,
            DMS_ClaimantNCBCaseManagement_Entity,
            DMS_OperatorCaseManagement_Entity,
            DMS_TransactionAdditionalInfo_Entity,
            DMS_DisputeCase_Entity,
            DMS_DisputeDetailsReport_Entity,
            DMS_Correspondence_Entity,
            DMS_SLAConfiguration_Entity,
            DMS_ReasonManagement_Entity,
            DMS_DisputeSummaryReport_Entity,
            DMS_TermsAndConditions_Entity,
            DMS_AclConfigs_Entity
        )
    }
}