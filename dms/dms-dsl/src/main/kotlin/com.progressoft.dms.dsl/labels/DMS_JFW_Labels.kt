package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

var DMS_JFW_Labels = Labels {
    label("label.workflow.status.ready.for.submission") {
        en("Ready for Submission")
        ar("جاهز للتقديم")
        other("fr", "Prêt pour la soumission")
    }
    label("label.workflow.status.under.modification") {
        en("Under Modification")
        ar("تحت التعديل")
        other("fr", "En modification")
    }
    label("label.workflow.status.repair.new") {
        en("Repair - New")
        ar("إصلاح - جديد")
        other("fr", "Correction - Nouveau")
    }
    label("label.workflow.status.repair.approved") {
        en("Repair - Approved")
        ar("إصلاح - مقبول")
        other("fr", "Correction - approuvé")
    }
    label("label.workflow.status.approved") {
        en("Approved")
        ar("مقبول")
        other("fr", "Approuvé")
    }
    label("label.workflow.status.pending.modification.approval") {
        en("Pending for Modification Approval")
        ar("بانتظار الموافقة على التعديلات")
        other("fr", "En attente d'approbation de la modification")
    }
    label("create.default.label") {
        other("fr", "Créer")
    }
    label("cancel.default.label") {
        other("fr", "Annuler")
    }
    label("com.progressoft.jfw.model.bussinessobject.workflow.workflowstatus.pendingforapproval") {
        other("fr", "En attente de l'approbation")
    }
    label("label.workflow.status.ready.for.submission") {
        other("fr", "Prêt pour la soumission")
    }
    label("username") {
        other("fr", "Nom De l'Utilisateur")
    }
    label("field.tenant") {
        other("fr", "Locataire")
    }
    label("password") {
        other("fr", "Mot De Passe")
    }
    label("login") {
        other("fr", "Connectez-Vous")
    }
    label("bulk.action") {
        other("fr", "Actions Groupées")
    }
    label("no.selected.bulk.action") {
        other("fr", "Veuillez Selectionner Un Ou Plusieurs Eléments")
    }
    label("generate.action") {
        en("Generate")
        ar("إنشاء")
        other("fr", "Générer")
    }
    label("ready.for.generation") {
        en("Ready For Generation")
        ar("Ready For Generation")
        other("fr", "Prêt pour la génération")
    }
    label("report.generated") {
        en("Generated")
        ar("Generated")
        other("fr", "Généré")
    }
    label("button.home") {
        other("fr", "page d'accueil")
    }
    label("export.report.to") {
        other("fr", "Exporter vers")
    }
    label("table.pagination.size") {
        other("fr", "Taille du tableau ")
    }
    label("table.pagination.of") {
        other("fr", " de")
    }
    label("table.pagination.page") {
        other("fr", "Page")
    }
    label("sidebar.search.placeholder") {
        other("fr", "Recherche...")
    }
    label("header.change.password") {
        other("fr", "Changer le mot de passe")
    }
    label("header.logout") {
        other("fr", "Déconnexion")
    }
    label("create.breadcrumb.label") {
        other("fr", " - Créer")
    }
    label("item.lookup.select") {
        other("fr", "Sélectionner")
    }
    label("change.password.tittle") {
        other("fr", "Changer le mot de passe")
    }
    label("change.password.current") {
        other("fr", "mot de passe actuel")
    }
    label("change.password.new") {
        other("fr", "Nouveau mot de passe")
    }
    label("change.password.new") {
        other("fr", "Nouveau mot de passe")
    }
    label("change.password.verify") {
        other("fr", "Vérifier")
    }
    label("change.password.confirm.msg") {
        other("fr", "Pour confirmer, saisissez à nouveau le nouveau mot de passe.")
    }
    label("change.password.cancel") {
        other("fr", "Annuler")
    }
    label("change.password.save") {
        other("fr", "Enregistrer")
    }
    label("messages.system.tittle") {
        other("fr", "Messages système")
    }
    label("comments") {
        other("fr", "Commentaires")
    }
    label("comment.actions") {
        other("fr", "Actes")
    }
    label("first.page") {
        other("fr", "D'abord")
    }
    label("previous.page") {
        other("fr", "Précédente")
    }
    label("last.page") {
        other("fr", "Dernière")
    }
    label("subview.pagination.total") {
        other("fr", "Totale")
    }
    label("submit.default.label") {
        other("fr", "Soumettre")
    }
    label("create.and.new") {
        other("fr", "créer et nouveau")
    }
    label("create.and.list") {
        other("fr", "créer et lister")
    }
}

