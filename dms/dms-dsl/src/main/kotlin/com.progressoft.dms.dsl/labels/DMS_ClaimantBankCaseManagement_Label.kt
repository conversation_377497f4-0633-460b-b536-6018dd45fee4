package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

var DMS_ClaimantBankCaseManagement_Label = Labels {
    label("dms_claimantBankCaseManagement.view"){
        en("Assigner Bank Cases")
        ar("Assigner Bank Cases")
        other("fr", "Litiges du participant requérent")
    }
    label("dms_claimantBankCaseManagement.note") {
        en("Note")
        ar("Note")
        other("fr", "Note")
    }
    label("dms_claimantBankCaseManagement.id") {
        en("Id")
        ar("Id")
        other("fr", "ID")
    }
    label("dms_claimantBankCaseManagement.disputeCase") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("dms_claimantBankCaseManagement.caseInfo") {
        en("Case Info")
        ar("Case Info")
        other("fr", "Informations sur le litige")
    }
    label("dms_claimantBankCaseManagement.submitted") {
        en("Submitted")
        ar("Submitted")
        other("fr", "Soumis")
    }
    label("claimant.saveandsubmit") {
        en("Save And Submit")
        ar("حفظ وتقديم")
        other("fr", "Enregistrer et envoyer")
    }
    label("JFW_WF_ACTION.*********") {
        en("SVC_NCB_DisputeDeclined")
        ar("SVC_NCB_DisputeDeclined")
        other("fr", "SVC_NCB_DISPUTEDECLIND")
    }
    label("claimant.re_present") {
        en("Re-Present")
        ar("اعادة تمثيل القضية")
        other("fr", "Représenter")
    }
    label("claimant.savetorepresent") {
        en("Save And Submit")
        ar("حفظ وتقديم")
        other("fr", "Enregistrer et envoyer")
    }
    label("claimant.canceltorepresent") {
        en("Cancel")
        ar("إلغاء")
        other("fr", "Annuler")
    }
    label("claimant.approvetorepresent") {
        en("Approve Re-Presentment")
        ar("الموافقة علي اعادة التمثيل")
        other("fr", "Approuver le re-présentation")
    }
    label("claimant.rejecttorepresent") {
        en("Reject Re-Presentment")
        ar("رفض موافقة التمثيل")
        other("fr", "Rejeter le re-présentation")
    }
    label("defendant.represented") {
        en("Represented")
        ar("اعادة تمثيل - تم")
        other("fr", "Représenté")
    }
    label("claimant.representrejected") {
        en("Represent - Rejected")
        ar("اعادة تمثيل - مقبول")
        other("fr", "Représentation - rejeté")
    }
    label("claimant.representwaitingapproval") {
        en("Represent - Waiting Approval")
        ar("اعادة تمثيل - بانتظار الموافقة")
        other("fr", "Représentation - Approbation en attente")
    }
    label("claimant.representedit") {
        en("Represent - Edit")
        ar("اعادة تمثيل - تعديل")
        other("fr", "Représenter - modifier")
    }
    label("JFW_WF_ACTION.*********") {
        en("SVC_NCB_DisputeDeclined")
        ar("SVC_NCB_DisputeDeclined")
        other("fr", "SVC_NCB_DISPUTEDECLIND")
    }
    label("clmt_bank_provide_more_info") {
        en("Provide Additional Information")
        ar("Provide Additional Information")
        other("fr", "Fournir des informations supplémentaires")
    }
    label("clmt_bank_more_info_submitted_to_ncb") {
        en("More Info Provided - Submitted")
        ar("More Info Provided - Submitted")
        other("fr", "Plus d'informations - soumis")
    }
    label("clmt_bank_more_info_waiting_approval") {
        en("More Info - Waiting Approval")
        ar("More Info - Waiting Approval")
        other("fr", "Plus d'informations - Approbation en attente")
    }
    label("clmt_bank_more_info_rejected") {
        en("More Info Provided - Rejected")
        ar("More Info Provided - Rejected")
        other("fr", "Plus d'informations - rejeté")
    }
    label("claimant_declined_dispute") {
        en("Declined Dispute")
        ar("Declined Dispute")
        other("fr", "Litige Rejeté")
    }
}
