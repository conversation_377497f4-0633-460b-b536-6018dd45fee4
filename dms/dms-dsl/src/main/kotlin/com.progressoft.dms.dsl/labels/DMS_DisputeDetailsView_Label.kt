package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

var DMS_DisputeDetailsView_Label = Labels {
    label("dispute.details.view") {
        en("Dispute Details Reports")
        ar("تقارير تفاصيل النزاعات")
        other("fr", "Rapports détaillés des litiges")
    }
    label("dispute.details.report.filters") {
        en("Dispute Details Report Filters")
        ar("مرشحات تقرير تفاصيل النزاعات")
        other("fr", "Filtres du rapport détaillé des litiges")
    }
    label("dispute.details.report.amount_from") {
        en("Amount From")
        ar("المبلغ من")
        other("fr", "Du Montant")
    }
    label("dispute.details.report.amount_to") {
        en("Amount To")
        ar("المبلغ ل")
        other("fr", "Au Montant")
    }
    label("dispute.details.report.att") {
        en("Generated Reports")
        ar("التقارير")
        other("fr", "Rapports générés")
    }
    label("dispute.details.report.failed.generation") {
        en( "Failed To Generate Dispute Details Report")
        ar("فشل في إنشاء تقرير تفاصيل النزاع")
        other("fr", "Échec de génération du rapport détaillé du litige")
    }
    label("dispute.details.report.type") {
        en("Report Type")
        ar("نوع التقرير")
        other("fr", "Type de rapport")
    }
    label("amount.range.validation.msg") {
        en("Amount From should be less than Amount To")
        ar("يجب أن يكون المبلغ من أقل من المبلغ إلى")
        other("fr", "La valeur du champ: Du montant, doit être inférieur à la valeur du champ: Au montant")
    }
}
