package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

val DMS_DefendantBankCaseManagement_Label = Labels{
    label("dms_defendantBankCaseManagement.view"){
        en("Assignee Bank Cases")
        ar("Assignee Bank Cases")
        other("fr", "Litiges du Participant Défendeur")
    }
    label("dms_defendantBankCaseManagement.note") {
        en("Note")
        ar("Note")
        other("fr", "Note")
    }
    label("dms_defendantBankCaseManagement.id") {
        en("Id")
        ar("Id")
        other("fr", "ID")
    }
    label("dms_defendantBankCaseManagement.disputeCase") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("dms_defendantBankCaseManagement.caseInfo") {
        en("Case Info")
        ar("Case Info")
        other("fr", "Informations sur le Litige")
    }
    label("defendant.more_information_provided") {
        en("More Information Provided")
        ar("معلومات اضافية")
        other("fr", "Plus d'informations fournies")
    }
    label("defendant.more_information_provided") {
        en("More Information Provided")
        ar("معلومات اضافية")
        other("fr", "Plus d'informations fournies")
    }
}
