package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

var DMS_DisputeCase_Label = Labels {
    label("dms_disputeCase.view") {
        en("Dispute Cases")
        ar("النزاعات")
        other("fr", "Litiges")
    }
    label("dms_disputeCase.caseReferenceNumber") {
        en("Case Reference Number")
        ar("الرقم المرجعي للقضية")
        other("fr", "Numéro de Référence du Litige")
    }
    label("dms_disputeCase.creationDateTime") {
        en("Creation Date Time")
        ar("تاريخ / وقت الإنشاء")
        other("fr", "Date et heure de création")
    }
    label("dms_disputeCase.transactionReference") {
        en("Transaction Reference")
        ar("الرقم المرجعي للحركة النقدية")
        other("fr", "Référence de transaction")
    }
    label("dms_disputeCase.transactionDate") {
        en("Transaction Date")
        ar("تاريخ الحركة النقدية")
        other("fr", "Date de la transaction")
    }
    label("dms_disputeCase.transactionCurrency") {
        en("Transaction Currency")
        ar("عملة الحركة النقدية")
        other("fr", "Devise de la transaction")
    }
    label("dms_disputeCase.transactionAmount") {
        en("Transaction Amount")
        ar("مبلغ الحركة النقدية")
        other("fr", "Montant de la transaction")
    }
    label("dms_disputeCase.transactionInformation") {
        en("Transaction Information")
        ar("Transaction Information")
        other("fr", "Informations sur les transactions")
    }
    label("dms_disputeCase.disputedAmount") {
        en("Disputed Amount")
        ar("المبلغ المتنازع عليه")
        other("fr", "Montant du Litige")
    }
    label("dms_disputeCase.senderParticipant") {
        en("Sender Participant")
        ar("البنك المرسل")
        other("fr", "Expéditeur")
    }
    label("dms_disputeCase.tranSenderParticipant") {
        en("Transaction Sender Participant")
        ar("مشارك مرسل المعاملة")
        other("fr", "Participant émetteur de la transaction litigieuse")
    }
    label("dms_disputeCase.receiverParticipant") {
        en("Receiver Participant")
        ar("البنك المستقبل")
        other("fr", "Participant Destinataire")
    }
    label("dms_disputeCase.tranReceiverParticipant") {
        en("Transaction Receiver Participant")
        ar("البنك المستقبل")
        other("fr", "Participant Destinataire de la transaction")
    }
    label("dms_disputeCase.notes") {
        en("Notes")
        ar("الملاحظات")
        other("fr", "Remarques")
    }
    label("dms_disputeCase.reason") {
        en("Reason")
        ar("السبب")
        other("fr", "Motif")
    }
    label("dms_disputeCase.paymentSystem") {
        en("Payment System")
        ar("نظام الدفع المستخدم")
        other("fr", "Système de Paiement")
    }
    label("dms_disputeCase.latestActivity") {
        en("Latest Activity")
        ar("Latest Activity")
        other("fr", "Dernère Activité")
    }
    label("dms_disputeCase.caseInfo") {
        en("Case Information")
        ar("معلومات القضية")
        other("fr", "Informations sur les Litiges")
    }
    label("dms_disputeCase.transactionInfo") {
        en("Transaction Key Information")
        ar("معلومات الحركة النقدية")
        other("fr",  "Informations clés de la transaction")
    }
    label("dms_disputeCase.lastNote") {
        en("Last Note")
        ar("Last Note")
        other("fr", "Dernière note")
    }
    label("dms_disputeCase.lastAction") {
        en("Last Action")
        ar("Last Action")
        other("fr", "Dernière action")
    }
    label("dms_disputeCase.lastActionBy") {
        en("Last Action By")
        ar("Last Action By")
        other("fr", "Dernière action de")
    }
    label("dms_disputeCase.claimantBank") {
        en("Assigner Bank")
        ar("البنك المدعي")
        other("fr", "Banque du Requérant")
    }
    label("dms_disputeCase.defendantBank") {
        en("Assignee Bank")
        ar("البنك المدعى عليه")
        other("fr", "Banque du Défendeur")
    }
    label("claimant.provide-additional-info") {
        en("Provide More Information")
        ar("تقديم معلومات اضافية")
        other("fr", "Fournir plus d'informations")
    }
    label("dms_disputeCase.id") {
        en("Id")
        ar("الرقم")
        other("fr", "Identifiant")
    }
    label("claimant_moreinfo_approve") {
        en("Approve more Information")
        ar("الموافقة على المعلومات الاضافية")
        other("fr", "Approuver la Demande d'Informations")
    }
    label("claimant_moreinfo_reject") {
        en("Reject More Information")
        ar("رفض المعلومات الاضافية")
        other("fr", "Rejeter la Demande d'Informations")
    }
    label("claimant.additional_info_required") {
        en("Additional Info Required")
        ar("مطلوب معلومات اضافية")
        other("fr", "Informations supplémentaires requises")
    }
    label("claimant.provide_additional_info") {
        en("Provide Additional Info")
        ar("تقديم معلومات اضافية")
        other("fr", "Fournir des informations supplémentaires")
    }
    label("claimant.more_info_waiting_approval") {
        en("More Info - Waiting Approval")
        ar("معلومات اضافية - بانتظار الموافقة")
        other("fr", "Plus d'informations - Approbation en Attente")
    }
    label("claimant.more_info_submitted") {
        en("More Info - Submitted")
        ar("معلومات اضافية - قدمت")
        other("fr", "Demande d'informations - soumis")
    }
    label("claimant.more_info_rejected") {
        en("More Info - Rejected")
        ar("معلومات اضافية - مرفوض")
        other("fr", "Demande d'informations - rejeté")
    }
    label("claimant.saveandsubmit") {
        en("Save And Submit")
        ar("حفظ وتقديم")
        other("fr", "Enregistrer et envoyer")
    }
    label("JFW_WF_ACTION.*********") {
        en("SVC_NCB_DisputeDeclined")
        ar("SVC_NCB_DisputeDeclined")
        other("fr", "SVC_NCB_DISPUTEDECLIND")
    }
    label("claimant.re_present") {
        en("Re-Present")
        ar("اعادة تمثيل القضية")
        other("fr", "Représenter")
    }
    label("claimant.savetorepresent") {
        en("Save And Submit")
        ar("حفظ وتقديم")
        other("fr", "Enregistrer et envoyer")
    }
    label("claimant.canceltorepresent") {
        en("Cancel")
        ar("إلغاء")
        other("fr", "Annuler")
    }
    label("claimant.approvetorepresent") {
        en("Approve Re-Presentment")
        ar("الموافقة علي اعادة التمثيل")
        other("fr", "Approuver la re-présentation")
    }
    label("claimant.rejecttorepresent") {
        en("Reject Re-Presentment")
        ar("رفض موافقة التمثيل")
        other("fr", "Rejeter la re-présentation")
    }
    label("defendant.represented") {
        en("Represented")
        ar("اعادة تمثيل - تم")
        other("fr", "Représenté")
    }
    label("claimant.representrejected") {
        en("Represent - Rejected")
        ar("اعادة تمثيل - مقبول")
        other("fr", "Représentation - rejeté")
    }
    label("claimant.representwaitingapproval") {
        en("Represent - Waiting Approval")
        ar("اعادة تمثيل - بانتظار الموافقة")
        other("fr", "Représentation - Approbation en attente")
    }
    label("claimant.representedit") {
        en("Represent - Edit")
        ar("اعادة تمثيل - تعديل")
        other("fr", "Représentation - modifier")
    }
    label("JFW_WF_ACTION.*********") {
        en("SVC_NCB_DisputeDeclined")
        ar("SVC_NCB_DisputeDeclined")
        other("fr", "SVC_NCB_DISPUTEDECLIND")
    }
    label("clmt_bank_provide_more_info") {
        en("Provide Additional Information")
        ar("Provide Additional Information")
        other("fr", "Fournir des informations supplémentaires")
    }
    label("clmt_bank_more_info_submitted_to_ncb") {
        en("More Info Provided - Submitted")
        ar("More Info Provided - Submitted")
        other("fr", "Plus d'informations fournies - soumis")
    }
    label("clmt_bank_more_info_waiting_approval") {
        en("More Info - Waiting Approval")
        ar("More Info - Waiting Approval")
        other("fr", "Plus d'informations - Approbation en attente")
    }
    label("clmt_bank_more_info_rejected") {
        en("More Info Provided - Rejected")
        ar("More Info Provided - Rejected")
        other("fr", "Plus d'informations fournies - rejeté")
    }
    label("claimant.fetch.transactions") {
        en("Fetch Transaction")
        ar("Fetch Transaction")
        other("fr", "Récupérer la transaction")
    }
    label("dispute_case_fetch_info") {
        en("Fetch Information")
        ar("Fetch Information")
        other("fr", "Récupérer des informations")
    }
    label("create_dispute_81181211101") {
        en("Create Dispute")
        ar("Create Dispute")
        other("fr", "Créer un Litige")
    }
    label("dms_disputeCase.TransactionAdditionalInfo") {
        en("Transaction Additional Information")
        ar("Transaction Additional Information")
        other("fr", "Informations supplémentaires de la Transaction")
    }
    label("claimant_bank_attachment") {
        en("Attachments")
        ar("Attachments")
        other("fr", "Pieces Jointes")
    }
    label("dms_disputeCase.claimantBankCaseManagement") {
        en("Assigner Bank Case Management")
        ar("Assigner Bank Case Management")
        other("fr", "Gestion des Litiges des Participants requérents")
    }
    label("dms_disputeCase.defendantBankCaseManagement") {
        en("Assignee Bank Case Management")
        ar("Assignee Bank Case Management")
        other("fr", "Gestion des Litiges des Participants Défendeurs")
    }
    label("dms_disputeCase.claimantNCBCaseManagement") {
        en("Assigner NCB Case Management")
        ar("Assigner NCB Case Management")
        other("fr", "Gestion des Litiges des DN des Requérents")
    }
    label("dms_disputeCase.defendantNCBCaseManagement") {
        en("Assignee NCB Case Management")
        ar("Assignee NCB Case Management")
        other("fr", "Gestion des Litiges des DN des Défendeurs")
    }
    label("dms_disputeCase.correspondence") {
        en("Correspondence")
        ar("Correspondence")
        other("fr", "Correspondance")
    }
    label("dms_disputeCase.urgency") {
        en("Urgency")
        ar("مطالب ملحة")
        other("fr", "Niveau d'Urgence")
    }
    label("dms_disputeCase.urgency.normal") {
        en("Normal")
        ar("Normal")
        other("fr", "Normale")
    }
    label("dms_disputeCase.urgency.urgent") {
        en("Urgent")
        ar("Urgent")
        other("fr", "Urgent")
    }
    label("dms_disputeCase.request.type"){
        en("Request Type")
        ar("نوع الطلب")
        other("fr", "type de demande")
    }
    label("dms_disputeCase.dispute.ref"){
        en("Dispute Reference")
        ar("مرجع النزاع")
        other("fr", "Référence du litige")

    }
    label("dms_disputeCase.dispute.info.portlet"){
        en("Dispute Reference Information")
        ar("معلومات النزاع المرجعي")
        other("fr", "Information sur la référence du litige")
    }
    label("dms_disputeCase.fetch.disputes.portlet"){
        en("Fetch Disputes")
        ar("عرض النزاعات")
        other("fr", "Récupérer les litiges")
    }
    label("dms_disputeCase.dispute.over.dispute"){
        en("Dispute over dispute")
        ar("نزاع على نزاع")
        other("fr", "Litiges sur un Litige cloturé")
    }
    label("dms_disputeCase.disputeKeyInfo"){
        en("Dispute Key Information")
        ar("معلومات النزاع")
        other("fr", "Informations clés sur un litige")
    }
    label("dms_disputeCase.null.amount.validation.message"){
        en("Disputed amount should not be null and be greater than zero")
        ar("المبلغ المتنازع عليه يجب أن يكون أكبر من الصفر..")
        other("fr", "Le montant contesté doit être supérieur à zéro.")
    }
    label("dms_disputeCase.amount.validation.message"){
        en("Disputed amount should be less than or equals the transaction amount")
        ar("يجب أن يكون المبلغ المتنازع عليه أقل من أو يساوي مبلغ المعاملة")
        other("fr", "Le montant contesté doit être inférieur ou égal au montant de la transaction.")
    }
    label("dms_disputeCase.attachment.validation.message"){
        en("Attachment is required")
        ar("المرفق مطلوب")
        other("fr", "Une pièce jointe est requise")
    }
    label("dms_disputeCase.existing.dispute.validation.message"){
        en("Dispute case exist with same transaction reference")
        ar("توجد حالة نزاع مع مرجع المعاملة ")
        other("fr", "Un cas litigieux existe avec la référence de transaction ")
    }
    label("dms_disputeCase.dispute.over.dispute.validation.message"){
        en("Dispute case already disputed")
        ar("حالة النزاع متنازع عليها بالفعل")
        other("fr", "Cas litigieux déjà contesté")
    }
}
