package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

val DMS_Correspondence_Label = Labels {
    label("correspondence_view") {
        en("Correspondence")
        ar("Correspondence")
        other("fr", "Correspondance")
    }
    label("correspondence_info") {
        en("Correspondence Info")
        ar("Correspondence Info")
        other("fr", "Informations sur la correspondance")
    }
    label("correspondence_action") {
        en("Action")
        ar("Action")
        other("fr", "Action")
    }
    label("correspondence_action_by") {
        en("Action By")
        ar("Action By")
        other("fr", "Action")
    }
    label("correspondence_note") {
        en("Note")
        ar("Note")
        other("fr", "Note")
    }
    label("correspondence_reason") {
        en("Reason")
        ar("السبب")
        other("fr", "Motif")
    }
    label("correspondence_id") {
        en("Id")
        ar("Id")
        other("fr", "ID")
    }
    label("correspondence_dispute_case") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
}
