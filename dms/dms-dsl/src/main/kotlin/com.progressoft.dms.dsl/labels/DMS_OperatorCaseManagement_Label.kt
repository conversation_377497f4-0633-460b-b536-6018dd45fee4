package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

val DMS_OperatorCaseManagement_Label = Labels {
    label("dms_operatorCaseManagement.view"){
        en("Operator Cases")
        ar("حالات المشغل")
        other("fr", "Litiges des Opérateurs")
    }
    label("dms_operatorCaseManagement.note") {
        en("Note")
        ar("Note")
        other("fr", "Note")
    }
    label("dms_operatorCaseManagement.id") {
        en("Id")
        ar("Id")
        other("fr", "ID")
    }
    label("dms_operatorCaseManagement.disputeCase") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("dms_operatorCaseManagement.caseInfo") {
        en("Case Info")
        ar("Case Info")
        other("fr", "Informations sur les Litiges")
    }
    label("operator.toapprovearbitrate") {
        en("Dispute Arbitrated")
        ar("نزاع معروض على المشغل")
        other("fr", "Litige Arbitré")
    }
    label("operator.arbitrated_dispute") {
        en("Arbitrated Dispute")
        ar("القضية المعروضة")
        other("fr", "Litige Arbitré")
    }
    label("operator.arbitratewaitingapproval") {
        en("Arbitrate To Operator - Waiting Approval")
        ar("عرض النزاع على المشغل - بانتظار الموافقة")
        other("fr", "Demande d'Arbitrage - Approbation en attente")
    }
    label("operator_more_info_required") {
        en("More Info Required")
        ar("More Info Required")
        other("fr", "Plus d'informations requises")
    }
    label("operator_waiting_approval") {
        en("More Info - Waiting Approval")
        ar("More Info - Waiting Approval")
        other("fr", "Plus d'informations - Approbation en attente")
    }
    label("operator_more_info_submitted_feom_claimant") {
        en("Operator - More Info Submitted From Assigner")
        ar("Operator - More Info Submitted From Assigner")
        other("fr", "Opérateur - Informations soumises par le Requérent")
    }
    label("operator_more_info_rejected") {
        en("More Info - Rejected")
        ar("More Info - Rejected")
        other("fr", "Plus d'informations - rejetées")
    }
    label("operator_more_info_provided") {
        en("Operator - More Info Provided")
        ar("Operator - More Info Provided")
        other("fr", "Opérateur - Plus d'informations fournies")
    }
    label("ask.claimant") {
        en("Ask Assigner")
        ar("Ask Assigner")
        other("fr", "Demander au Requérent")
    }
    label("operator_more_info_repair") {
        en("More Info - Repair")
        ar("معلومات اضافية - تعديل")
        other("fr", "Plus d'informations - Correction")
    }
    label("operator_more_info_submitted_from_defendant_NCB") {
        en("Operator - More Info Submitted From Assignee")
        ar("Operator - More Info Submitted From Assignee")
        other("fr", "Opérateur - Plus d'informations soumises par le défendeur")
    }
    label("JFW_WF_ACTION.185313081") {
        en("SVC_NCB_MoreInfo")
        ar("SVC_NCB_MoreInfo")
        other("fr", "Svc_ncb_moreinfo")
    }
    label("dfndnt_operator_more_info_provided") {
        en("More Info From Assignee Provided")
        ar("More Info From Assignee Provided")
        other("fr", "Informations fournies au défendeur ")
    }
    label("claimant.ncb.approveadditionalinforequest") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("claimant.ncb.approveadditionalinforequest") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("ask.defendant.ncb") {
        en("Ask Assignee NCB")
        ar("Ask Assignee NCB")
        other("fr", "Demander à la DN du défendeur")
    }
    label("ask.ncb.defendant") {
        en("More Info Required")
        ar("More Info Required")
        other("fr", "Plus d'informations requises")
    }
}
