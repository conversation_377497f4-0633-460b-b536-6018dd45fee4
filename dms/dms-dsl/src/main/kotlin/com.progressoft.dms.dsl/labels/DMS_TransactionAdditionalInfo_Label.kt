package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

val DMS_TransactionAdditionalInfo_Label = Labels {
    label("additional_info_id") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("additional_info_key") {
        en("Key")
        ar("Key")
        other("fr", "Clé")
    }
    label("additional_info_value") {
        en("Value")
        ar("Value")
        other("fr", "Valeur")
    }
    label("additional_info_claimant_bank_dispute") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
}
