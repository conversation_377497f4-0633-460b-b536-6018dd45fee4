package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

var LABELS_ONE = Labels {
    label("dispute.view_8118100000") {
        en("Disputes")
        ar("النزاعات")
        other("fr", "Litiges")
    }
    label("id_8118100009") {
        en("ID")
        ar("ID")
        other("fr", "ID")
    }
    label("refnumber_8118100014") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéro de référence des participants")
    }
    label("paymentsystem.id_8118100026") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118100031") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118100036") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("paymentsystem.codenamepair_8118100041") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118100019") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputeStatus_8118100020") {
        en("Dispute Status")
        ar("Dispute Status")
        other("fr", "Statut du Litige")
    }
    label("disputerefrenceid_8118100046") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118100051") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118100056") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "Date de validation du SLA")
    }
    label("iipsid_8118100061") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("achid_8118100067") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("senderbank.code_8118100083") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118100093") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118100073") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant émetteur")
    }
    label("receiverbank.code_8118100109") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118100119") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118100099") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118100125") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Participant du Débiteur")
    }
    label("debtorbankname_8118100130") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118100135") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant du Créancier")
    }
    label("creditorbankname_8118100140") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118100145") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de session du Paiement")
    }
    label("paymentsessiondate_8118100150") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118100155") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118100160") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date de Règlement du Paiement")
    }
    label("paymentstatus.id_8118100170") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118100175") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118100180") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118100185") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118100165") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118100191") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du Paiement")
    }
    label("paymencategory_8118100196") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118100206") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Urgence du Paiement.ID")
    }
    label("paymenturgency.code_8118100211") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118100216") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Urgence du Paiement.nom")
    }
    label("paymenturgency.codenamepair_8118100221") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de Paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118100201") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118100242") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118100227") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118100247") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du Paiement")
    }
    label("debtorname_8118100252") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("debtoriban_8118100257") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur ")
    }
    label("debtornationalid_8118100262") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118100267") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du Débiteur")
    }
    label("beneficiaryname_8118100272") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du Bénéficiaire")
    }
    label("beneficiaryiban_8118100277") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire ")
    }
    label("beneficiarynationalid_8118100282") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "ID National du Bénéficiaire")
    }
    label("beneficiaryaddress_8118100287") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du Bénéficiaire")
    }
    label("mpcode_8118100292") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de référence du Paiement")
    }
    label("mpname_8118100297") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de Message")
    }
    label("paycurrency_8118100307") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118100312") {
        en("Amount")
        ar("المبلغ")
        other("fr", "Montant")
    }
    label("setlment_8118100317") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("mpsenderbnkcd_8118100327") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code Banque de l'Initiateur")
    }
    label("sendername_8118100332") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Nom de l'Initiateur)")
    }
    label("mpsenderbrnch_8118100337") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence de l'Initiateur)")
    }
    label("mpreceiverbnkcd_8118100342") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code Banque du Destinataire)")
    }
    label("mpreceivername_8118100347") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Destinataire)")
    }
    label("mpreceiverbrnch_8118100352") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118100357") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (Statut)")
    }
    label("mpsessionno_8118100362") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118100367") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la Transaction")
    }
    label("achname_8118100372") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du Paiement (ID du Lot)")
    }
    label("achendtoend_8118100387") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de bout en bout")
    }
    label("achinstructionid_8118100392") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'Instruction")
    }
    label("achmandateid_8118100397") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118100402") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118100407") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118100412") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("achcreditoraccount_8118100417") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118100422") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118100427") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118100432") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118100437") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118100442") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("achtransactionpurpose_8118100447") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118100452") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118100457") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118100462") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118100467") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118100472") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Info additionnelle")
    }
    label("paymentrefid_8118100477") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("mainreason.id_8118100487") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118100492") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118100497") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118100502") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118100482") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118100508") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("dispute.cr_8118100513") {
        en("CR")
        other("fr", "Cr")
    }
    label("dispute.db_8118100517") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118100521") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur de Paiement ")
    }
    label("mobilenoserviceid_8118100526") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service de numéro de mobile")
    }
    label("merchantbillerref_8118100531") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Ref du Marchand")
    }
    label("merchantidbillerid_8118100536") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118100541") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118100546") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du Litige")
    }
    label("urgencysla.id_8118100556") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118100561") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118100566") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118100571") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118100551") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118100577") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118100587") {
        en("fees.id")
        ar("fees.id")
        other("fr", "Frais.")
    }
    label("fees.code_8118100592") {
        en("fees.code")
        ar("fees.code")
        other("fr", "Code lié aux Frais")
    }
    label("fees.name_8118100597") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118100602") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "Frais.")
    }
    label("fees_8118100582") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais des litiges")
    }
    label("checkercomments_8118100608") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118100613") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118100618") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118100623") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118100628") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118100633") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("question_8118100638") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118100643") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("answer_8118100648") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118100653") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("serial_8118100658") {
        en("Serial")
        ar("Serial")
        other("fr", "Série")
    }
    label("represnted_8118100663") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118100668") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre des demandes d'information")
    }
    label("isrepresented_8118100673") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Représentation")
    }
    label("rejectionreason.id_8118100683") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118100688") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118100693") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118100698") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118100678") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118100704") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118100709") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118100719") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118100724") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118100729") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118100734") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118100714") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("escalationreason.description_8118100740") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("isescalated_8118100745") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Arbitrage")
    }
    label("escalationcharges.id_8118100755") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118100760") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118100765") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118100770") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118100750") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'Arbitrage")
    }
    label("escalationcharges.amount_8118100776") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des Frais")
    }
    label("penalizedparty_8118100781") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie Pénalisée")
    }
    label("dispute.notspecified_8118100786") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("dispute.initiatorbank_8118100790") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("dispute.recieverbank_8118100794") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118100798") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires liés à l'Arbitrage")
    }
    label("isgoodfaith_8118100803") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118100808") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("dispute.notsettled_8118100813") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("dispute.settled_8118100817") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("dispute.manuallysettled_8118100821") {
        en("Manually Settled")
        other("fr", "Réglé Manuellement")
    }
    label("finalstatustime_8118100825") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Heure du Statut final")
    }
    label("direction_8118100830") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118100835") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118100840") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118100845") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118100850") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118100855") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118100860") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118100870") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118100875") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118100880") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118100885") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118100890") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118100895") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118100900") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118100905") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("disputecase_8118100913") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("dispute.disputecase.tab_8118100918") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("dispute.disputecase.form_8118100923") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118100931") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("dispute.paymentinformation.tab_8118100936") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("dispute.paymentinformation.form_8118100941") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118100969") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("dispute.iipsinformation.tab_8118100974") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("dispute.iipsinformation.form_8118100979") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118100997") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("dispute.achinformation.tab_8118101002") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("dispute.achinformation.form_8118101007") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118101034") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("dispute.disputeinformation.tab_8118101039") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("dispute.disputeinformation.form_8118101044") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118101054") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("dispute.rejectedresons.tab_8118101059") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("dispute.rejectedresons.form_8118101064") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118101070") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("dispute.disputecycle.tab_8118101075") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("dispute.disputecycle.form_8118101080") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118101089") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("dispute.finalrejectionreason.tab_8118101094") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("dispute.finalrejectionreason.form_8118101099") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118101106") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("dispute.arbitrationinformation.tab_8118101111") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("dispute.arbitrationinformation.form_8118101116") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118101126") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("dispute.workflowstatus.tab_8118101131") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("dispute.workflowstatus.form_8118101136") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedetails_8118101146") {
        en("Dispute Details")
        ar("Dispute Details")
        other("fr", "Détails du litige")
    }
    label("dispute.attachments.tab_8118101151") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("dispute.attachments.form_8118101156") {
        en("Attachments")
        ar("Attachments")
        other("fr", "Pièces jointes")
    }
    label("activity_8118101162") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("dispute.attachments.tab_8118101167") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("dispute.comments.tab_8118101172") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("dispute.changehistory.tab_8118101177") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("details_8118101440") {
        en("Details")
        ar("التفاصيل")
        other("fr", "Détails")
    }
    label("dispute.disputehistory.tab_8118101688") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("dispute.disputeinquiries.tab_8118101937") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requêtes sur le litige")
    }
    label("disputecase_8118101182") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("dispute.disputecase.tab_8118101187") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("dispute.disputecase.form_8118101192") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118101200") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("dispute.paymentinformation.tab_8118101205") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("dispute.paymentinformation.form_8118101210") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118101238") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("dispute.iipsinformation.tab_8118101243") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("dispute.iipsinformation.form_8118101248") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118101266") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("dispute.achinformation.tab_8118101271") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("dispute.achinformation.form_8118101276") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118101303") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("dispute.disputeinformation.tab_8118101308") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("dispute.disputeinformation.form_8118101313") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118101323") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("dispute.rejectedresons.tab_8118101328") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("dispute.rejectedresons.form_8118101333") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118101339") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("dispute.disputecycle.tab_8118101344") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("dispute.disputecycle.form_8118101349") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118101358") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("dispute.finalrejectionreason.tab_8118101363") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("dispute.finalrejectionreason.form_8118101368") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118101375") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("dispute.arbitrationinformation.tab_8118101380") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("dispute.arbitrationinformation.form_8118101385") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118101395") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("dispute.workflowstatus.tab_8118101400") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("dispute.workflowstatus.form_8118101405") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedetails_8118101415") {
        en("Dispute Details")
        ar("Dispute Details")
        other("fr", "Détails du litige")
    }
    label("activity_8118101420") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("dispute.attachments.tab_8118101425") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("dispute.comments.tab_8118101430") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("dispute.changehistory.tab_8118101435") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputehistory.view_8118101445") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("id_8118101451") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118101461") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118101466") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118101456") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("currency.stringisocode_8118101487") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118101472") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("cycle_8118101492") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("mainreason.id_8118101502") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118101507") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118101512") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118101517") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118101497") {
        en("Main reason")
        ar("Main reason")
        other("fr", "Motif Principal")
    }
    label("actualamount_8118101523") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118101528") {
        en("Case description")
        ar("Case description")
        other("fr", "Description du Litige")
    }
    label("approved_8118101533") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("rejectionreason.id_8118101543") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118101548") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118101553") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118101558") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118101538") {
        en("Dispute Rejection Reason")
        ar("Dispute Rejection Reason")
        other("fr", "Motif de rejet du litige")
    }
    label("rejectioncomment_8118101564") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("currency.id_8118101569") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118101574") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118101579") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118101584") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118101589") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118101594") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118101604") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118101609") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118101614") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118101619") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118101624") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118101629") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118101634") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118101639") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("cycleinformation_8118101646") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehistory.cycleinformation.tab_8118101651") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehistory.cycleinformation.form_8118101656") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("cycleinformation_8118101667") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehistory.cycleinformation.tab_8118101672") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehistory.cycleinformation.form_8118101677") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputedetails.view_8118101693") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("id_8118101701") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118101711") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118101716") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118101706") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118101722") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118101727") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118101732") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("responsedate_8118101737") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de Réponse")
    }
    label("answer_8118101742") {
        en("Answer")
        ar("Answer")
        other("fr", "Réponse")
    }
    label("answeratt_8118101747") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("answerapproved_8118101752") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118101757") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118101762") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118101767") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118101772") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118101777") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118101787") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118101792") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118101797") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118101802") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118101807") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118101812") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118101817") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118101822") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118101829") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedetails.requestedinformation.tab_8118101834") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedetails.requestedinformation.form_8118101839") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118101846") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedetails.response.tab_8118101851") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedetails.response.form_8118101856") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("activity_8118101863") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedetails.attachments.tab_8118101868") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedetails.comments.tab_8118101873") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedetails.changehistory.tab_8118101878") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118101883") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedetails.requestedinformation.tab_8118101888") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedetails.requestedinformation.form_8118101893") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118101900") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedetails.response.tab_8118101905") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedetails.response.form_8118101910") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("activity_8118101917") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedetails.attachments.tab_8118101922") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedetails.comments.tab_8118101927") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedetails.changehistory.tab_8118101932") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputehoutreplied.view_8118101942") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("id_8118101948") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118101958") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118101963") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118101953") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("currency.stringisocode_8118101984") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118101969") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("cycle_8118101989") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("mainreason.id_8118101999") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118102004") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118102009") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118102014") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118101994") {
        en("Main reason")
        ar("Main reason")
        other("fr", "Motif Principal")
    }
    label("actualamount_8118102020") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118102025") {
        en("Case description")
        ar("Case description")
        other("fr", "Description du Litige")
    }
    label("approved_8118102030") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("rejectionreason.id_8118102040") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118102045") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118102050") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118102055") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118102035") {
        en("Dispute Rejection Reason")
        ar("Dispute Rejection Reason")
        other("fr", "Motif de rejet du litige")
    }
    label("rejectioncomment_8118102061") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("currency.id_8118102066") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118102071") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118102076") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118102081") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118102086") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118102091") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118102101") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118102106") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118102111") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118102116") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118102121") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118102126") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118102131") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118102136") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("cycleinformation_8118102143") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehoutreplied.cycleinformation.tab_8118102148") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehoutreplied.cycleinformation.form_8118102153") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("cycleinformation_8118102164") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehoutreplied.cycleinformation.tab_8118102169") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehoutreplied.cycleinformation.form_8118102174") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinreplied.view_8118102185") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("id_8118102191") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118102201") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118102206") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118102196") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("currency.stringisocode_8118102227") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118102212") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("cycle_8118102232") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("mainreason.id_8118102242") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118102247") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118102252") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118102257") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118102237") {
        en("Main reason")
        ar("Main reason")
        other("fr", "Motif Principal")
    }
    label("actualamount_8118102263") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118102268") {
        en("Case description")
        ar("Case description")
        other("fr", "Description du Litige")
    }
    label("approved_8118102273") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("rejectionreason.id_8118102283") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118102288") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118102293") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118102298") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118102278") {
        en("Dispute Rejection Reason")
        ar("Dispute Rejection Reason")
        other("fr", "Motif de rejet du litige")
    }
    label("rejectioncomment_8118102304") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("currency.id_8118102309") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118102314") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118102319") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118102324") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118102329") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118102334") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118102344") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118102349") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118102354") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118102359") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118102364") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118102369") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118102374") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118102379") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("cycleinformation_8118102386") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinreplied.cycleinformation.tab_8118102391") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinreplied.cycleinformation.form_8118102396") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("cycleinformation_8118102407") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinreplied.cycleinformation.tab_8118102412") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinreplied.cycleinformation.form_8118102417") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinward.view_8118102428") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("id_8118102434") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118102444") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118102449") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118102439") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("currency.stringisocode_8118102470") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118102455") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("cycle_8118102475") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("mainreason.id_8118102485") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118102490") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118102495") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118102500") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118102480") {
        en("Main reason")
        ar("Main reason")
        other("fr", "Motif Principal")
    }
    label("actualamount_8118102506") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118102511") {
        en("Case description")
        ar("Case description")
        other("fr", "Description du Litige")
    }
    label("approved_8118102516") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("rejectionreason.id_8118102526") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118102531") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118102536") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118102541") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118102521") {
        en("Dispute Rejection Reason")
        ar("Dispute Rejection Reason")
        other("fr", "Motif de rejet du litige")
    }
    label("rejectioncomment_8118102547") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("currency.id_8118102552") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118102557") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118102562") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118102567") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118102572") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118102577") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118102587") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118102592") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118102597") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118102602") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118102607") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118102612") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118102617") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118102622") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("cycleinformation_8118102629") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinward.cycleinformation.tab_8118102634") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinward.cycleinformation.form_8118102639") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("cycleinformation_8118102650") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinward.cycleinformation.tab_8118102655") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinward.cycleinformation.form_8118102660") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinapproval.view_8118102671") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("id_8118102677") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118102687") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118102692") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118102682") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("currency.stringisocode_8118102713") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118102698") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("cycle_8118102718") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("mainreason.id_8118102728") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118102733") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118102738") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118102743") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118102723") {
        en("Main reason")
        ar("Main reason")
        other("fr", "Motif Principal")
    }
    label("actualamount_8118102749") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118102754") {
        en("Case description")
        ar("Case description")
        other("fr", "Description du Litige")
    }
    label("approved_8118102759") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("rejectionreason.id_8118102769") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118102774") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118102779") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118102784") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118102764") {
        en("Dispute Rejection Reason")
        ar("Dispute Rejection Reason")
        other("fr", "Motif de rejet du litige")
    }
    label("rejectioncomment_8118102790") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("currency.id_8118102795") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118102800") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118102805") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118102810") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118102815") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118102820") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118102830") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118102835") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118102840") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118102845") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118102850") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118102855") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118102860") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118102865") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("cycleinformation_8118102872") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinapproval.cycleinformation.tab_8118102877") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinapproval.cycleinformation.form_8118102882") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("cycleinformation_8118102893") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinapproval.cycleinformation.tab_8118102898") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehinapproval.cycleinformation.form_8118102903") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehwaitreply.view_8118102914") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("id_8118102920") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118102930") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118102935") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118102925") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("currency.stringisocode_8118102956") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118102941") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("cycle_8118102961") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("mainreason.id_8118102971") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118102976") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118102981") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118102986") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118102966") {
        en("Main reason")
        ar("Main reason")
        other("fr", "Motif Principal")
    }
    label("actualamount_8118102992") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118102997") {
        en("Case description")
        ar("Case description")
        other("fr", "Description du Litige")
    }
    label("approved_8118103002") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("rejectionreason.id_8118103012") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118103017") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118103022") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118103027") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118103007") {
        en("Dispute Rejection Reason")
        ar("Dispute Rejection Reason")
        other("fr", "Motif de rejet du litige")
    }
    label("rejectioncomment_8118103033") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("currency.id_8118103038") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118103043") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118103048") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118103053") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118103058") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118103063") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118103073") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118103078") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118103083") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118103088") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118103093") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118103098") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118103103") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118103108") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("cycleinformation_8118103115") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehwaitreply.cycleinformation.tab_8118103120") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehwaitreply.cycleinformation.form_8118103125") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("cycleinformation_8118103136") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehwaitreply.cycleinformation.tab_8118103141") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehwaitreply.cycleinformation.form_8118103146") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehapproval.view_8118103157") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("id_8118103163") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118103173") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118103178") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118103168") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("currency.stringisocode_8118103199") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118103184") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("cycle_8118103204") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("mainreason.id_8118103214") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118103219") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118103224") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118103229") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118103209") {
        en("Main reason")
        ar("Main reason")
        other("fr", "Motif Principal")
    }
    label("actualamount_8118103235") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118103240") {
        en("Case description")
        ar("Case description")
        other("fr", "Description du Litige")
    }
    label("approved_8118103245") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("rejectionreason.id_8118103255") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118103260") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118103265") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118103270") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118103250") {
        en("Dispute Rejection Reason")
        ar("Dispute Rejection Reason")
        other("fr", "Motif de rejet du litige")
    }
    label("rejectioncomment_8118103276") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("currency.id_8118103281") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118103286") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118103291") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118103296") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118103301") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118103306") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118103316") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118103321") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118103326") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118103331") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118103336") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118103341") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118103346") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118103351") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("cycleinformation_8118103358") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehapproval.cycleinformation.tab_8118103363") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehapproval.cycleinformation.form_8118103368") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("cycleinformation_8118103379") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehapproval.cycleinformation.tab_8118103384") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputehapproval.cycleinformation.form_8118103389") {
        en("Cycle Information")
        ar("Cycle Information")
        other("fr", "Informations sur le cycle")
    }
    label("disputepaymentmp.view_8118103400") {
        en("Mpclear Payment Refrence")
        ar("Mpclear Payment Refrence")
        other("fr", "MPClear Payment Refension")
    }
    label("selectpaymentrefrence_8118103408") {
        en("Select Payment Refrence")
        ar("Select Payment Refrence")
        other("fr", "Sélectionner la référence du Paiement")
    }
    label("disputepaymentmp.selectpaymentrefrence.tab_8118103413") {
        en("Select Payment Refrence")
        ar("Select Payment Refrence")
        other("fr", "Sélectionner la référence du Paiement")
    }
    label("selectpaymentrefrence_8118103418") {
        en("Select Payment Refrence")
        ar("Select Payment Refrence")
        other("fr", "Sélectionner la référence du Paiement")
    }
    label("disputepaymentach.view_8118103423") {
        en("ACH Payment Refrence")
        ar("ACH Payment Refrence")
        other("fr", "Réfression de Paiement ACH")
    }
    label("selectpaymentrefrence_8118103431") {
        en("Select Payment Refrence")
        ar("Select Payment Refrence")
        other("fr", "Sélectionner la référence du Paiement")
    }
    label("disputepaymentach.selectpaymentrefrence.tab_8118103436") {
        en("Select Payment Refrence")
        ar("Select Payment Refrence")
        other("fr", "Sélectionner la référence du Paiement")
    }
    label("selectpaymentrefrence_8118103441") {
        en("Select Payment Refrence")
        ar("Select Payment Refrence")
        other("fr", "Sélectionner la référence du Paiement")
    }
    label("disputeapproval.view_8118103446") {
        en("Dispute Approval")
        ar("Dispute Approval")
        other("fr", "Approbation du Litige")
    }
    label("id_8118103467") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refnumber_8118103472") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéros de Référence des Participants")
    }
    label("paymentsystem.id_8118103483") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118103488") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118103493") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118103498") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118103477") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputerefrenceid_8118103503") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118103508") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118103513") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118103518") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("achid_8118103523") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("senderbank.code_8118103538") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118103548") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118103528") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118103564") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118103574") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118103554") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118103580") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Participant du Débiteur")
    }
    label("debtorbankname_8118103585") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118103590") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant du Créancier")
    }
    label("creditorbankname_8118103595") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118103600") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118103605") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118103610") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118103615") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date de Règlement du Paiement")
    }
    label("paymentstatus.id_8118103625") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118103630") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118103635") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118103640") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118103620") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118103646") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du Paiement")
    }
    label("paymencategory_8118103651") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118103661") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de Paiement.ID")
    }
    label("paymenturgency.code_8118103666") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118103671") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de Paiement.nom")
    }
    label("paymenturgency.codenamepair_8118103676") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de Paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118103656") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118103697") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118103682") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118103702") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du Paiement")
    }
    label("debtorname_8118103707") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("debtoriban_8118103712") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118103717") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118103722") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du Débiteur")
    }
    label("beneficiaryname_8118103727") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du Bénéficiaire")
    }
    label("beneficiaryiban_8118103732") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118103737") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro d'ID National du Bénéficiaire")
    }
    label("beneficiaryaddress_8118103742") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du Bénéficiaire")
    }
    label("mpcode_8118103747") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du Paiement")
    }
    label("mpname_8118103752") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de Message")
    }
    label("paycurrency_8118103762") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118103767") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118103772") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("mpsenderbnkcd_8118103782") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code Banque de l'Initiateur)")
    }
    label("sendername_8118103787") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Nom de l'Initiateur)")
    }
    label("mpsenderbrnch_8118103792") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence de l'Initiateur)")
    }
    label("mpreceiverbnkcd_8118103797") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code Banque du Destinataire)")
    }
    label("mpreceivername_8118103802") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Destinataire)")
    }
    label("mpreceiverbrnch_8118103807") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118103812") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (Statut)")
    }
    label("mpsessionno_8118103817") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118103822") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la Transaction")
    }
    label("achname_8118103827") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du Paiement (ID du Lot)")
    }
    label("achendtoend_8118103842") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de bout en bout")
    }
    label("achinstructionid_8118103847") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'Instruction")
    }
    label("achmandateid_8118103852") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118103857") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118103862") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118103867") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("achcreditoraccount_8118103872") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118103877") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118103882") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118103887") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118103892") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118103897") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("achtransactionpurpose_8118103902") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118103907") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118103912") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118103917") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118103922") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118103927") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Info additionnelle")
    }
    label("paymentrefid_8118103932") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("mainreason.id_8118103942") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118103947") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118103952") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118103957") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118103937") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118103963") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputeapproval.cr_8118103968") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputeapproval.db_8118103972") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118103976") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur de Paiement ")
    }
    label("mobilenoserviceid_8118103981") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service de numéro de mobile")
    }
    label("merchantbillerref_8118103986") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Ref du Marchand ")
    }
    label("merchantidbillerid_8118103991") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du Marchand ID du Facturier")
    }
    label("actualamount_8118103996") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118104001") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du Litige")
    }
    label("urgencysla.id_8118104011") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118104016") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118104021") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118104026") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118104006") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118104032") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite Maximale")
    }
    label("fees.id_8118104042") {
        en("fees.id")
        ar("fees.id")
        other("fr", "Frais.")
    }
    label("fees.code_8118104047") {
        en("fees.code")
        ar("fees.code")
        other("fr", "Code lié aux Frais")
    }
    label("fees.name_8118104052") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118104057") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "Frais.")
    }
    label("fees_8118104037") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais des litiges")
    }
    label("checkercomments_8118104063") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118104068") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118104073") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118104078") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118104083") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118104088") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("question_8118104093") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118104098") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("answer_8118104103") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118104108") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("serial_8118104113") {
        en("Serial")
        ar("Serial")
        other("fr", "Série")
    }
    label("represnted_8118104118") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118104123") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre des demandes d'information")
    }
    label("isrepresented_8118104128") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Représentation")
    }
    label("rejectionreason.id_8118104138") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118104143") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118104148") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118104153") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118104133") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118104159") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118104164") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118104174") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118104179") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118104184") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118104189") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118104169") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("escalationreason.description_8118104195") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("isescalated_8118104200") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Arbitrage")
    }
    label("escalationcharges.id_8118104210") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118104215") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118104220") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118104225") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118104205") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'Arbitrage")
    }
    label("escalationcharges.amount_8118104231") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des Frais")
    }
    label("penalizedparty_8118104236") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie Pénalisée")
    }
    label("disputeapproval.notspecified_8118104241") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputeapproval.initiatorbank_8118104245") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputeapproval.recieverbank_8118104249") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118104253") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires liés à l'Arbitrage")
    }
    label("isgoodfaith_8118104258") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118104263") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputeapproval.notsettled_8118104268") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputeapproval.settled_8118104272") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputeapproval.manuallysettled_8118104276") {
        en("Manually Settled")
        other("fr", "Réglé Manuellement")
    }
    label("finalstatustime_8118104280") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Heure du Statut final")
    }
    label("direction_8118104285") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118104290") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118104295") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118104300") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118104305") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118104310") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118104315") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118104325") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118104330") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118104335") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118104340") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118104345") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118104350") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118104355") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118104360") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("disputecase_8118104367") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeapproval.disputecase.tab_8118104372") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeapproval.disputecase.form_8118104377") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118104385") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeapproval.paymentinformation.tab_8118104390") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeapproval.paymentinformation.form_8118104395") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118104423") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeapproval.iipsinformation.tab_8118104428") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeapproval.iipsinformation.form_8118104433") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118104451") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeapproval.achinformation.tab_8118104456") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeapproval.achinformation.form_8118104461") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118104488") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeapproval.disputeinformation.tab_8118104493") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeapproval.disputeinformation.form_8118104498") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118104508") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeapproval.rejectedresons.tab_8118104513") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeapproval.rejectedresons.form_8118104518") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118104524") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeapproval.disputecycle.tab_8118104529") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeapproval.disputecycle.form_8118104534") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118104543") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeapproval.finalrejectionreason.tab_8118104548") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeapproval.finalrejectionreason.form_8118104553") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118104560") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeapproval.arbitrationinformation.tab_8118104565") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeapproval.arbitrationinformation.form_8118104570") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118104580") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeapproval.workflowstatus.tab_8118104585") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeapproval.workflowstatus.form_8118104590") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118104600") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeapproval.attachments.tab_8118104605") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputeapproval.comments.tab_8118104610") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputeapproval.changehistory.tab_8118104615") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputehistory_8118104873") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("disputeapproval.disputehistory.tab_8118104878") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("disputecase_8118104620") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeapproval.disputecase.tab_8118104625") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeapproval.disputecase.form_8118104630") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118104638") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeapproval.paymentinformation.tab_8118104643") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeapproval.paymentinformation.form_8118104648") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118104676") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeapproval.iipsinformation.tab_8118104681") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeapproval.iipsinformation.form_8118104686") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118104704") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeapproval.achinformation.tab_8118104709") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeapproval.achinformation.form_8118104714") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118104741") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeapproval.disputeinformation.tab_8118104746") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeapproval.disputeinformation.form_8118104751") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118104761") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeapproval.rejectedresons.tab_8118104766") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeapproval.rejectedresons.form_8118104771") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118104777") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeapproval.disputecycle.tab_8118104782") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeapproval.disputecycle.form_8118104787") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118104796") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeapproval.finalrejectionreason.tab_8118104801") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeapproval.finalrejectionreason.form_8118104806") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118104813") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeapproval.arbitrationinformation.tab_8118104818") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeapproval.arbitrationinformation.form_8118104823") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118104833") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeapproval.workflowstatus.tab_8118104838") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeapproval.workflowstatus.form_8118104843") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118104853") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeapproval.attachments.tab_8118104858") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputeapproval.comments.tab_8118104863") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputeapproval.changehistory.tab_8118104868") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputedtlswaitreply.view_8118104883") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("id_8118104891") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118104901") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118104906") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118104896") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118104912") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118104917") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118104922") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("responsedate_8118104927") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de Réponse")
    }
    label("answer_8118104932") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118104937") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("answerapproved_8118104942") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118104947") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118104952") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118104957") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118104962") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118104967") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118104977") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118104982") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118104987") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118104992") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118104997") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118105002") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118105007") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118105012") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118105019") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlswaitreply.requestedinformation.tab_8118105024") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlswaitreply.requestedinformation.form_8118105029") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118105036") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlswaitreply.response.tab_8118105041") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlswaitreply.response.form_8118105046") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118105053") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlswaitreply.workflowstatus.tab_8118105058") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlswaitreply.workflowstatus.form_8118105063") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118105073") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlswaitreply.attachments.tab_8118105078") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedtlswaitreply.comments.tab_8118105083") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedtlswaitreply.changehistory.tab_8118105088") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118105093") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlswaitreply.requestedinformation.tab_8118105098") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlswaitreply.requestedinformation.form_8118105103") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118105110") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlswaitreply.response.tab_8118105115") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlswaitreply.response.form_8118105120") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118105127") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlswaitreply.workflowstatus.tab_8118105132") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlswaitreply.workflowstatus.form_8118105137") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118105147") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlswaitreply.attachments.tab_8118105152") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedtlswaitreply.comments.tab_8118105157") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedtlswaitreply.changehistory.tab_8118105162") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputedtlsmoreinfo.view_8118105167") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("id_8118105175") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118105185") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118105190") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118105180") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118105196") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118105201") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118105206") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("responsedate_8118105211") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de Réponse")
    }
    label("answer_8118105216") {
        en("Answer")
        ar("Answer")
        other("fr", "Réponse")
    }
    label("answeratt_8118105221") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("answerapproved_8118105226") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118105231") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118105236") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118105241") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118105246") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118105251") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118105261") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118105266") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118105271") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118105276") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118105281") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118105286") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118105291") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118105296") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118105303") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsmoreinfo.requestedinformation.tab_8118105308") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsmoreinfo.requestedinformation.form_8118105313") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118105320") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsmoreinfo.response.tab_8118105325") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsmoreinfo.response.form_8118105330") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118105337") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsmoreinfo.workflowstatus.tab_8118105342") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsmoreinfo.workflowstatus.form_8118105347") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118105357") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsmoreinfo.attachments.tab_8118105362") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedtlsmoreinfo.comments.tab_8118105367") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedtlsmoreinfo.changehistory.tab_8118105372") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118105377") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsmoreinfo.requestedinformation.tab_8118105382") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsmoreinfo.requestedinformation.form_8118105387") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118105394") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsmoreinfo.response.tab_8118105399") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsmoreinfo.response.form_8118105404") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118105411") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsmoreinfo.workflowstatus.tab_8118105416") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsmoreinfo.workflowstatus.form_8118105421") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118105431") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsmoreinfo.attachments.tab_8118105436") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedtlsmoreinfo.comments.tab_8118105441") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedtlsmoreinfo.changehistory.tab_8118105446") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputedtlsqapp.view_8118105451") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("id_8118105459") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118105469") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118105474") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118105464") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118105480") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118105485") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118105490") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("responsedate_8118105495") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de Réponse")
    }
    label("answer_8118105500") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118105505") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("answerapproved_8118105510") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118105515") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118105520") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118105525") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118105530") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118105535") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118105545") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118105550") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118105555") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118105560") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118105565") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118105570") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118105575") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118105580") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118105587") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsqapp.requestedinformation.tab_8118105592") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsqapp.requestedinformation.form_8118105597") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118105604") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsqapp.response.tab_8118105609") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsqapp.response.form_8118105614") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118105621") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsqapp.workflowstatus.tab_8118105626") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsqapp.workflowstatus.form_8118105631") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118105641") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsqapp.attachments.tab_8118105646") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedtlsqapp.comments.tab_8118105651") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedtlsqapp.changehistory.tab_8118105656") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118105661") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsqapp.requestedinformation.tab_8118105666") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsqapp.requestedinformation.form_8118105671") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118105678") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsqapp.response.tab_8118105683") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsqapp.response.form_8118105688") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118105695") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsqapp.workflowstatus.tab_8118105700") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsqapp.workflowstatus.form_8118105705") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118105715") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsqapp.attachments.tab_8118105720") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedtlsqapp.comments.tab_8118105725") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedtlsqapp.changehistory.tab_8118105730") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputedtlsansrapp.view_8118105735") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("id_8118105743") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118105753") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118105758") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118105748") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118105764") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118105769") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118105774") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("responsedate_8118105779") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de Réponse")
    }
    label("answer_8118105784") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118105789") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("answerapproved_8118105794") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118105799") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118105804") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118105809") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118105814") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118105819") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118105829") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118105834") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118105839") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118105844") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118105849") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118105854") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118105859") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118105864") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118105871") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsansrapp.requestedinformation.tab_8118105876") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsansrapp.requestedinformation.form_8118105881") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118105888") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsansrapp.response.tab_8118105893") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsansrapp.response.form_8118105898") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118105905") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsansrapp.workflowstatus.tab_8118105910") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsansrapp.workflowstatus.form_8118105915") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118105925") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsansrapp.attachments.tab_8118105930") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedtlsansrapp.comments.tab_8118105935") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedtlsansrapp.changehistory.tab_8118105940") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118105945") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsansrapp.requestedinformation.tab_8118105950") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsansrapp.requestedinformation.form_8118105955") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118105962") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsansrapp.response.tab_8118105967") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsansrapp.response.form_8118105972") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118105979") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsansrapp.workflowstatus.tab_8118105984") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsansrapp.workflowstatus.form_8118105989") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118105999") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsansrapp.attachments.tab_8118106004") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputedtlsansrapp.comments.tab_8118106009") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputedtlsansrapp.changehistory.tab_8118106014") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputerejection.view_8118106019") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreasons_8118106027") {
        en("Rejection Reasons")
        ar("Rejection Reasons")
        other("fr", "Motifs de rejet")
    }
    label("disputerejection.rejectionreasons.tab_8118106032") {
        en("Rejection Reasons")
        ar("Rejection Reasons")
        other("fr", "Motifs de rejet")
    }
    label("disputerejection.rejectionreasons.form_8118106037") {
        en("Rejection Reasons")
        ar("Rejection Reasons")
        other("fr", "Motifs de rejet")
    }
    label("rejectionreasons_8118106043") {
        en("Rejection Reasons")
        ar("Rejection Reasons")
        other("fr", "Motifs de rejet")
    }
    label("disputerejection.rejectionreasons.tab_8118106048") {
        en("Rejection Reasons")
        ar("Rejection Reasons")
        other("fr", "Motifs de rejet")
    }
    label("disputerejection.rejectionreasons.form_8118106053") {
        en("Rejection Reasons")
        ar("Rejection Reasons")
        other("fr", "Motifs de rejet")
    }
    label("disputerequest.view_8118106059") {
        en("Dispute Request")
        ar("Dispute Request")
        other("fr", "Demande de litige")
    }
    label("id_8118106080") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refnumber_8118106085") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéros de Référence des Participants")
    }
    label("paymentsystem.id_8118106096") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118106101") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118106106") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118106111") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118106090") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputerefrenceid_8118106116") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118106121") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118106126") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118106131") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("achid_8118106136") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("senderbank.code_8118106151") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118106161") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118106141") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118106177") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118106187") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118106167") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118106193") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Participant du Débiteur")
    }
    label("debtorbankname_8118106198") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118106203") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant du Créancier")
    }
    label("creditorbankname_8118106208") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118106213") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118106218") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118106223") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118106228") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date de Règlement du Paiement")
    }
    label("paymentstatus.id_8118106238") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118106243") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118106248") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118106253") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118106233") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118106259") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du Paiement")
    }
    label("paymencategory_8118106264") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118106274") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de Paiement.ID")
    }
    label("paymenturgency.code_8118106279") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118106284") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de Paiement.nom")
    }
    label("paymenturgency.codenamepair_8118106289") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de Paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118106269") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118106310") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118106295") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118106315") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du Paiement")
    }
    label("debtorname_8118106320") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("debtoriban_8118106325") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118106330") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118106335") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du Débiteur")
    }
    label("beneficiaryname_8118106340") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du Bénéficiaire")
    }
    label("beneficiaryiban_8118106345") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118106350") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro d'ID National du Bénéficiaire")
    }
    label("beneficiaryaddress_8118106355") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du Bénéficiaire")
    }
    label("mpcode_8118106360") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du Paiement")
    }
    label("mpname_8118106365") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de Message")
    }
    label("paycurrency_8118106375") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118106380") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118106385") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("mpsenderbnkcd_8118106395") {
        en("Instructing Participant (Initiator Bank Cod")
        ar("Instructing Participant (Initiator Bank Cod")
        other("fr", "Participant Emetteur (Code Banque de l'Initiateur")
    }
    label("sendername_8118106400") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Nom de l'Initiateur)")
    }
    label("mpsenderbrnch_8118106405") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence de l'Initiateur)")
    }
    label("mpreceiverbnkcd_8118106410") {
        en("Instructed Participant (Receiving Bank Cod")
        ar("Instructed Participant (Receiving Bank Cod")
        other("fr", "Participant Instruit (Code Banque du Destinataire")
    }
    label("mpreceivername_8118106415") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Destinataire)")
    }
    label("mpreceiverbrnch_8118106420") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118106425") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (Statut")
    }
    label("mpsessionno_8118106430") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118106435") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la Transaction")
    }
    label("achname_8118106440") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du Paiement (ID du Lot)")
    }
    label("achdescription_8118106445") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("achendtoend_8118106455") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de bout en bout")
    }
    label("achinstructionid_8118106460") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'Instruction")
    }
    label("achmandateid_8118106465") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118106470") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118106475") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118106480") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("achcreditoraccount_8118106485") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118106490") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118106495") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118106500") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118106505") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118106510") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("achtransactionpurpose_8118106515") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118106520") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118106525") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118106530") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118106535") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118106540") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Info additionnelle")
    }
    label("paymentrefid_8118106545") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("mainreason.id_8118106555") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118106560") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118106565") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118106570") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118106550") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118106576") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputerequest.cr_8118106581") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputerequest.db_8118106585") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118106589") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur de Paiement ")
    }
    label("mobilenoserviceid_8118106594") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service de numéro de mobile")
    }
    label("merchantbillerref_8118106599") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Ref du Marchand")
    }
    label("merchantidbillerid_8118106604") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118106609") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118106614") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du Litige")
    }
    label("urgencysla.id_8118106624") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118106629") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118106634") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118106639") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118106619") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118106645") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date Limite Maximale")
    }
    label("fees.id_8118106655") {
        en("fees.id")
        ar("fees.id")
        other("fr", "Frais.")
    }
    label("fees.code_8118106660") {
        en("fees.code")
        ar("fees.code")
        other("fr", "Code Lié aux Frais")
    }
    label("fees.name_8118106665") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118106670") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "Frais.")
    }
    label("fees_8118106650") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais des Litiges")
    }
    label("checkercomments_8118106676") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118106681") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118106686") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118106691") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118106696") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118106701") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("question_8118106706") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118106711") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la Question")
    }
    label("answer_8118106716") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118106721") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("serial_8118106726") {
        en("Serial")
        ar("Serial")
        other("fr", "En série")
    }
    label("represnted_8118106731") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118106736") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre des demandes d'information")
    }
    label("isrepresented_8118106741") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Représentation")
    }
    label("rejectionreason.id_8118106751") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118106756") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118106761") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118106766") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118106746") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118106772") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118106777") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118106787") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118106792") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118106797") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118106802") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118106782") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("escalationreason.description_8118106808") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("isescalated_8118106813") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Arbitrage")
    }
    label("escalationcharges.id_8118106823") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118106828") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118106833") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118106838") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118106818") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'Arbitrage")
    }
    label("escalationcharges.amount_8118106844") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des Frais")
    }
    label("penalizedparty_8118106849") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie Pénalisée")
    }
    label("disputerequest.notspecified_8118106854") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputerequest.initiatorbank_8118106858") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputerequest.recieverbank_8118106862") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118106866") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires liés à l'Arbitrage")
    }
    label("isgoodfaith_8118106871") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118106876") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputerequest.notsettled_8118106881") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputerequest.settled_8118106885") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputerequest.manuallysettled_8118106889") {
        en("Manually Settled")
        other("fr", "Réglé Manuellement")
    }
    label("finalstatustime_8118106893") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Heure du Statut final")
    }
    label("direction_8118106898") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118106903") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118106908") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118106913") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118106918") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118106923") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118106928") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118106938") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118106943") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118106948") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118106953") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118106958") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118106963") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118106968") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118106973") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("disputecase_8118106980") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputerequest.disputecase.tab_8118106985") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputerequest.disputecase.form_8118106990") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118106998") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputerequest.paymentinformation.tab_8118107003") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputerequest.paymentinformation.form_8118107008") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118107036") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputerequest.iipsinformation.tab_8118107041") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputerequest.iipsinformation.form_8118107046") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118107064") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputerequest.achinformation.tab_8118107069") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputerequest.achinformation.form_8118107074") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118107101") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputerequest.disputeinformation.tab_8118107106") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputerequest.disputeinformation.form_8118107111") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118107121") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputerequest.rejectedresons.tab_8118107126") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputerequest.rejectedresons.form_8118107131") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118107137") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputerequest.disputecycle.tab_8118107142") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputerequest.disputecycle.form_8118107147") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118107156") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputerequest.finalrejectionreason.tab_8118107161") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputerequest.finalrejectionreason.form_8118107166") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118107173") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputerequest.arbitrationinformation.tab_8118107178") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputerequest.arbitrationinformation.form_8118107183") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118107193") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputerequest.workflowstatus.tab_8118107198") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputerequest.workflowstatus.form_8118107203") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118107213") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputerequest.attachments.tab_8118107218") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputerequest.comments.tab_8118107223") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputerequest.changehistory.tab_8118107228") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputecase_8118107233") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputerequest.disputecase.tab_8118107238") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputerequest.disputecase.form_8118107243") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118107251") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputerequest.paymentinformation.tab_8118107256") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputerequest.paymentinformation.form_8118107261") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118107289") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputerequest.iipsinformation.tab_8118107294") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputerequest.iipsinformation.form_8118107299") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118107317") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputerequest.achinformation.tab_8118107322") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputerequest.achinformation.form_8118107327") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118107354") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputerequest.disputeinformation.tab_8118107359") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputerequest.disputeinformation.form_8118107364") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118107374") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputerequest.rejectedresons.tab_8118107379") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputerequest.rejectedresons.form_8118107384") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118107390") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputerequest.disputecycle.tab_8118107395") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputerequest.disputecycle.form_8118107400") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118107409") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputerequest.finalrejectionreason.tab_8118107414") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputerequest.finalrejectionreason.form_8118107419") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118107426") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputerequest.arbitrationinformation.tab_8118107431") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputerequest.arbitrationinformation.form_8118107436") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118107446") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputerequest.workflowstatus.tab_8118107451") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputerequest.workflowstatus.form_8118107456") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118107466") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputerequest.attachments.tab_8118107471") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputerequest.comments.tab_8118107476") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputerequest.changehistory.tab_8118107481") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputemoreinfo.view_8118107486") {
        en("Disputes Need More Informations")
        ar("Disputes Need More Informations")
        other("fr", "Les litiges ont besoin de plus d'informations")
    }
    label("id_8118107507") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("question_8118107512") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118107517") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("answer_8118107522") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118107527") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("refnumber_8118107532") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéros de Référence des Participants")
    }
    label("paymentsystem.id_8118107543") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118107548") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118107553") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118107558") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118107537") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputerefrenceid_8118107563") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118107568") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118107573") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118107578") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("achid_8118107583") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("senderbank.code_8118107598") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118107608") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118107588") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118107624") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118107634") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118107614") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118107640") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Participant du Débiteur")
    }
    label("debtorbankname_8118107645") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118107650") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant du Créancier")
    }
    label("creditorbankname_8118107655") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118107660") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118107665") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118107670") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118107675") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date de Règlement du Paiement")
    }
    label("paymentstatus.id_8118107685") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118107690") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118107695") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118107700") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118107680") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118107706") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du Paiement")
    }
    label("paymencategory_8118107711") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118107721") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de Paiement.ID")
    }
    label("paymenturgency.code_8118107726") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118107731") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de Paiement.nom")
    }
    label("paymenturgency.codenamepair_8118107736") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de Paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118107716") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118107757") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118107742") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118107762") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du Paiement")
    }
    label("debtorname_8118107767") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("debtoriban_8118107772") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118107777") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118107782") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du Débiteur")
    }
    label("beneficiaryname_8118107787") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du Bénéficiaire")
    }
    label("beneficiaryiban_8118107792") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118107797") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro d'ID National du Bénéficiaire")
    }
    label("beneficiaryaddress_8118107802") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du Bénéficiaire")
    }
    label("mpcode_8118107807") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du Paiement")
    }
    label("mpname_8118107812") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de Message")
    }
    label("paycurrency_8118107822") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118107827") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118107832") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("mpsenderbnkcd_8118107842") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code Banque de l'Initiateur)")
    }
    label("sendername_8118107847") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Nom de l'Initiateur)")
    }
    label("mpsenderbrnch_8118107852") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence de l'Initiateur)")
    }
    label("mpreceiverbnkcd_8118107857") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code Banque du Destinataire")
    }
    label("mpreceivername_8118107862") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Destinataire)")
    }
    label("mpreceiverbrnch_8118107867") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118107872") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (Statut)")
    }
    label("mpsessionno_8118107877") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118107882") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la Transaction")
    }
    label("achname_8118107887") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du Paiement (ID du Lot)")
    }
    label("achendtoend_8118107902") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de bout en bout")
    }
    label("achinstructionid_8118107907") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'Instruction")
    }
    label("achmandateid_8118107912") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118107917") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118107922") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118107927") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("achcreditoraccount_8118107932") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118107937") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118107942") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118107947") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118107952") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118107957") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("achtransactionpurpose_8118107962") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118107967") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118107972") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118107977") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118107982") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118107987") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Info additionnelle")
    }
    label("paymentrefid_8118107992") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("mainreason.id_8118108002") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118108007") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118108012") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118108017") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118107997") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118108023") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputemoreinfo.cr_8118108028") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputemoreinfo.db_8118108032") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118108036") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur de Paiement ")
    }
    label("mobilenoserviceid_8118108041") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service de numéro de mobile")
    }
    label("merchantbillerref_8118108046") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Ref du Marchand")
    }
    label("merchantidbillerid_8118108051") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118108056") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118108061") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du Litige")
    }
    label("urgencysla.id_8118108071") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118108076") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118108081") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118108086") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118108066") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118108092") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118108102") {
        en("fees.id")
        ar("fees.id")
        other("fr", "Frais.")
    }
    label("fees.code_8118108107") {
        en("fees.code")
        ar("fees.code")
        other("fr", "Code lié aux Frais")
    }
    label("fees.name_8118108112") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118108117") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "Frais.")
    }
    label("fees_8118108097") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais des litiges")
    }
    label("checkercomments_8118108123") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118108128") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118108133") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118108138") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118108143") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118108148") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("serial_8118108153") {
        en("Serial")
        ar("Serial")
        other("fr", "Série")
    }
    label("represnted_8118108158") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118108163") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre des demandes d'information")
    }
    label("isrepresented_8118108168") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Représentation")
    }
    label("rejectionreason.id_8118108178") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118108183") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118108188") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118108193") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118108173") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118108199") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118108204") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118108214") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118108219") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118108224") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118108229") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118108209") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("escalationreason.description_8118108235") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("isescalated_8118108240") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Arbitrage")
    }
    label("escalationcharges.id_8118108250") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118108255") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118108260") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118108265") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118108245") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'Arbitrage")
    }
    label("escalationcharges.amount_8118108271") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des Frais")
    }
    label("penalizedparty_8118108276") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie Pénalisée")
    }
    label("disputemoreinfo.notspecified_8118108281") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputemoreinfo.initiatorbank_8118108285") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputemoreinfo.recieverbank_8118108289") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118108293") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires liés à l'Arbitrage")
    }
    label("isgoodfaith_8118108298") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118108303") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputemoreinfo.notsettled_8118108308") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputemoreinfo.settled_8118108312") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputemoreinfo.manuallysettled_8118108316") {
        en("Manually Settled")
        other("fr", "Réglé Manuellement")
    }
    label("finalstatustime_8118108320") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Heure du Statut final")
    }
    label("direction_8118108325") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118108330") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118108335") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118108340") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118108345") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118108350") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118108355") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118108365") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118108370") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118108375") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118108380") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118108385") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118108390") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118108395") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118108400") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118108407") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputemoreinfo.requestedinformation.tab_8118108412") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputemoreinfo.requestedinformation.form_8118108417") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("moreinformations_8118108424") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputemoreinfo.moreinformations.tab_8118108429") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputemoreinfo.moreinformations.form_8118108434") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputecase_8118108441") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputemoreinfo.disputecase.tab_8118108446") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputemoreinfo.disputecase.form_8118108451") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118108459") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("paymentInformation") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("achInformation") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("mpClearInformation") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputemoreinfo.paymentinformation.tab_8118108464") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputemoreinfo.paymentinformation.form_8118108469") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118108497") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputemoreinfo.iipsinformation.tab_8118108502") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputemoreinfo.iipsinformation.form_8118108507") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118108525") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputemoreinfo.achinformation.tab_8118108530") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputemoreinfo.achinformation.form_8118108535") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118108562") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputemoreinfo.disputeinformation.tab_8118108567") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputemoreinfo.disputeinformation.form_8118108572") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118108582") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputemoreinfo.rejectedresons.tab_8118108587") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputemoreinfo.rejectedresons.form_8118108592") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118108598") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputemoreinfo.disputecycle.tab_8118108603") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputemoreinfo.disputecycle.form_8118108608") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118108617") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputemoreinfo.finalrejectionreason.tab_8118108622") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputemoreinfo.finalrejectionreason.form_8118108627") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118108634") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputemoreinfo.arbitrationinformation.tab_8118108639") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputemoreinfo.arbitrationinformation.form_8118108644") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118108654") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemoreinfo.workflowstatus.tab_8118108659") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemoreinfo.workflowstatus.form_8118108664") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118108674") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputemoreinfo.attachments.tab_8118108679") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputemoreinfo.comments.tab_8118108684") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputemoreinfo.changehistory.tab_8118108689") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeinquiries_8118108981") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("disputemoreinfo.disputeinquiries.tab_8118108986") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("requestedinformation_8118108694") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputemoreinfo.requestedinformation.tab_8118108699") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputemoreinfo.requestedinformation.form_8118108704") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("moreinformations_8118108711") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputemoreinfo.moreinformations.tab_8118108716") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputemoreinfo.moreinformations.form_8118108721") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputecase_8118108728") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputemoreinfo.disputecase.tab_8118108733") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputemoreinfo.disputecase.form_8118108738") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118108746") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputemoreinfo.paymentinformation.tab_8118108751") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputemoreinfo.paymentinformation.form_8118108756") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118108784") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputemoreinfo.iipsinformation.tab_8118108789") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputemoreinfo.iipsinformation.form_8118108794") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118108812") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputemoreinfo.achinformation.tab_8118108817") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputemoreinfo.achinformation.form_8118108822") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118108849") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputemoreinfo.disputeinformation.tab_8118108854") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputemoreinfo.disputeinformation.form_8118108859") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118108869") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputemoreinfo.rejectedresons.tab_8118108874") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputemoreinfo.rejectedresons.form_8118108879") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118108885") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputemoreinfo.disputecycle.tab_8118108890") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputemoreinfo.disputecycle.form_8118108895") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118108904") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputemoreinfo.finalrejectionreason.tab_8118108909") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputemoreinfo.finalrejectionreason.form_8118108914") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118108921") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputemoreinfo.arbitrationinformation.tab_8118108926") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputemoreinfo.arbitrationinformation.form_8118108931") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118108941") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemoreinfo.workflowstatus.tab_8118108946") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemoreinfo.workflowstatus.form_8118108951") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118108961") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputemoreinfo.attachments.tab_8118108966") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputemoreinfo.comments.tab_8118108971") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputemoreinfo.changehistory.tab_8118108976") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeansweraprroval.view_8118108991") {
        en("Response To more Information Approval")
        ar("Response To more Information Approval")
        other("fr", "Approbation de la réponse à une demande d'informations")
    }
    label("id_8118109012") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("question_8118109017") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118109022") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("answer_8118109027") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118109032") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("refnumber_8118109037") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéros de Référence des Participants")
    }
    label("paymentsystem.id_8118109048") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118109053") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118109058") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118109063") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118109042") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputerefrenceid_8118109068") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118109073") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118109078") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118109083") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("achid_8118109088") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("senderbank.code_8118109103") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118109113") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118109093") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118109129") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118109139") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118109119") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118109145") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Participant du Débiteur")
    }
    label("debtorbankname_8118109150") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118109155") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant du Créancier")
    }
    label("creditorbankname_8118109160") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118109165") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118109170") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118109175") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118109180") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date de Règlement du Paiement")
    }
    label("paymentstatus.id_8118109190") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118109195") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118109200") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118109205") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118109185") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118109211") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du Paiement")
    }
    label("paymencategory_8118109216") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118109226") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de Paiement.ID")
    }
    label("paymenturgency.code_8118109231") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118109236") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de Paiement.nom")
    }
    label("paymenturgency.codenamepair_8118109241") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de Paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118109221") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118109262") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118109247") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118109267") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du Paiement")
    }
    label("debtorname_8118109272") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("debtoriban_8118109277") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118109282") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118109287") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du Débiteur")
    }
    label("beneficiaryname_8118109292") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du Bénéficiaire")
    }
    label("beneficiaryiban_8118109297") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118109302") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro d'ID National du Bénéficiaire")
    }
    label("beneficiaryaddress_8118109307") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du Bénéficiaire")
    }
    label("mpcode_8118109312") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du Paiement")
    }
    label("mpname_8118109317") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de Message")
    }
    label("paycurrency_8118109327") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118109332") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118109337") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("mpsenderbnkcd_8118109347") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code Banque de l'Initiateur)")
    }
    label("sendername_8118109352") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Nom de l'Initiateur)")
    }
    label("mpsenderbrnch_8118109357") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence de l'Initiateur)")
    }
    label("mpreceiverbnkcd_8118109362") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code Banque du Destinataire")
    }
    label("mpreceivername_8118109367") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Destinataire)")
    }
    label("mpreceiverbrnch_8118109372") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118109377") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (Statut")
    }
    label("mpsessionno_8118109382") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118109387") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la Transaction")
    }
    label("achname_8118109392") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du Paiement (ID du Lot)")
    }
    label("achendtoend_8118109407") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de bout en bout")
    }
    label("achinstructionid_8118109412") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'Instruction")
    }
    label("achmandateid_8118109417") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118109422") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118109427") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118109432") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("achcreditoraccount_8118109437") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118109442") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118109447") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118109452") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118109457") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118109462") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("achtransactionpurpose_8118109467") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118109472") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118109477") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118109482") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118109487") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118109492") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Info additionnelle")
    }
    label("paymentrefid_8118109497") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("mainreason.id_8118109507") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118109512") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118109517") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118109522") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118109502") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118109528") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputeansweraprroval.cr_8118109533") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputeansweraprroval.db_8118109537") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118109541") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur de Paiement ")
    }
    label("mobilenoserviceid_8118109546") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service de numéro de mobile")
    }
    label("merchantbillerref_8118109551") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Ref du Facturier")
    }
    label("merchantidbillerid_8118109556") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118109561") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118109566") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du Litige")
    }
    label("urgencysla.id_8118109576") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118109581") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118109586") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118109591") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118109571") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118109597") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118109607") {
        en("fees.id")
        ar("fees.id")
        other("fr", "Frais.")
    }
    label("fees.code_8118109612") {
        en("fees.code")
        ar("fees.code")
        other("fr", "Code lié aux Frais")
    }
    label("fees.name_8118109617") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118109622") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "Frais.")
    }
    label("fees_8118109602") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais des litiges")
    }
    label("checkercomments_8118109628") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118109633") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118109638") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118109643") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118109648") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118109653") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("serial_8118109658") {
        en("Serial")
        ar("Serial")
        other("fr", "Série")
    }
    label("represnted_8118109663") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118109668") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre des demandes d'information")
    }
    label("isrepresented_8118109673") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Représentation")
    }
    label("rejectionreason.id_8118109683") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118109688") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118109693") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118109698") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118109678") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118109704") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118109709") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118109719") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118109724") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118109729") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118109734") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118109714") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("escalationreason.description_8118109740") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("isescalated_8118109745") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Arbitrage")
    }
    label("escalationcharges.id_8118109755") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118109760") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118109765") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118109770") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118109750") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'Arbitrage")
    }
    label("escalationcharges.amount_8118109776") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des Frais")
    }
    label("penalizedparty_8118109781") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie Pénalisée")
    }
    label("disputeansweraprroval.notspecified_8118109786") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputeansweraprroval.initiatorbank_8118109790") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputeansweraprroval.recieverbank_8118109794") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118109798") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires liés à l'Arbitrage")
    }
    label("isgoodfaith_8118109803") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118109808") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputeansweraprroval.notsettled_8118109813") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputeansweraprroval.settled_8118109817") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputeansweraprroval.manuallysettled_8118109821") {
        en("Manually Settled")
        other("fr", "Réglé Manuellement")
    }
    label("finalstatustime_8118109825") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Heure du Statut final")
    }
    label("direction_8118109830") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118109835") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118109840") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118109845") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118109850") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118109855") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118109860") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118109870") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118109875") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118109880") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118109885") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118109890") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118109895") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118109900") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118109905") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118109912") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeansweraprroval.requestedinformation.tab_8118109917") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeansweraprroval.requestedinformation.form_8118109922") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("moreinformations_8118109929") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputeansweraprroval.moreinformations.tab_8118109934") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputeansweraprroval.moreinformations.form_8118109939") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputecase_8118109946") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeansweraprroval.disputecase.tab_8118109951") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeansweraprroval.disputecase.form_8118109956") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118109964") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeansweraprroval.paymentinformation.tab_8118109969") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeansweraprroval.paymentinformation.form_8118109974") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118110002") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeansweraprroval.iipsinformation.tab_8118110007") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeansweraprroval.iipsinformation.form_8118110012") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118110030") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeansweraprroval.achinformation.tab_8118110035") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeansweraprroval.achinformation.form_8118110040") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118110067") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeansweraprroval.disputeinformation.tab_8118110072") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeansweraprroval.disputeinformation.form_8118110077") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118110087") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeansweraprroval.rejectedresons.tab_8118110092") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeansweraprroval.rejectedresons.form_8118110097") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118110103") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeansweraprroval.disputecycle.tab_8118110108") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeansweraprroval.disputecycle.form_8118110113") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118110122") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeansweraprroval.finalrejectionreason.tab_8118110127") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeansweraprroval.finalrejectionreason.form_8118110132") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118110139") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeansweraprroval.arbitrationinformation.tab_8118110144") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeansweraprroval.arbitrationinformation.form_8118110149") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118110159") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeansweraprroval.workflowstatus.tab_8118110164") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeansweraprroval.workflowstatus.form_8118110169") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118110179") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeansweraprroval.attachments.tab_8118110184") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputeansweraprroval.comments.tab_8118110189") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputeansweraprroval.changehistory.tab_8118110194") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeinquiries_8118110486") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("disputeansweraprroval.disputeinquiries.tab_8118110491") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("requestedinformation_8118110199") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeansweraprroval.requestedinformation.tab_8118110204") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeansweraprroval.requestedinformation.form_8118110209") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("moreinformations_8118110216") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputeansweraprroval.moreinformations.tab_8118110221") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputeansweraprroval.moreinformations.form_8118110226") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputecase_8118110233") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeansweraprroval.disputecase.tab_8118110238") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeansweraprroval.disputecase.form_8118110243") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118110251") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeansweraprroval.paymentinformation.tab_8118110256") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeansweraprroval.paymentinformation.form_8118110261") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118110289") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeansweraprroval.iipsinformation.tab_8118110294") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeansweraprroval.iipsinformation.form_8118110299") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118110317") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeansweraprroval.achinformation.tab_8118110322") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeansweraprroval.achinformation.form_8118110327") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118110354") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeansweraprroval.disputeinformation.tab_8118110359") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeansweraprroval.disputeinformation.form_8118110364") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118110374") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeansweraprroval.rejectedresons.tab_8118110379") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeansweraprroval.rejectedresons.form_8118110384") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118110390") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeansweraprroval.disputecycle.tab_8118110395") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeansweraprroval.disputecycle.form_8118110400") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118110409") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeansweraprroval.finalrejectionreason.tab_8118110414") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeansweraprroval.finalrejectionreason.form_8118110419") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118110426") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeansweraprroval.arbitrationinformation.tab_8118110431") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeansweraprroval.arbitrationinformation.form_8118110436") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118110446") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeansweraprroval.workflowstatus.tab_8118110451") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeansweraprroval.workflowstatus.form_8118110456") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118110466") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeansweraprroval.attachments.tab_8118110471") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputeansweraprroval.comments.tab_8118110476") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputeansweraprroval.changehistory.tab_8118110481") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputewaitreply.view_8118110496") {
        en("Dispute Waiting Reply")
        ar("Dispute Waiting Reply")
        other("fr", "Réponse en attente de litige")
    }
    label("id_8118110517") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("question_8118110522") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118110527") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("refnumber_8118110532") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéros de Référence des Participants")
    }
    label("paymentsystem.id_8118110543") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118110548") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118110553") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118110558") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118110537") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputerefrenceid_8118110563") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118110568") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118110573") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118110578") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("achid_8118110583") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("senderbank.code_8118110598") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118110608") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118110588") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118110624") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118110634") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118110614") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118110640") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Participant du Débiteur")
    }
    label("debtorbankname_8118110645") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118110650") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant du Créancier")
    }
    label("creditorbankname_8118110655") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118110660") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118110665") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118110670") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118110675") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date de Règlement du Paiement")
    }
    label("paymentstatus.id_8118110685") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118110690") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118110695") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118110700") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118110680") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118110706") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du Paiement")
    }
    label("paymencategory_8118110711") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118110721") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de Paiement.ID")
    }
    label("paymenturgency.code_8118110726") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118110731") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de Paiement.nom")
    }
    label("paymenturgency.codenamepair_8118110736") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de Paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118110716") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118110757") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118110742") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118110762") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du Paiement")
    }
    label("debtorname_8118110767") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("debtoriban_8118110772") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118110777") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118110782") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du Débiteur")
    }
    label("beneficiaryname_8118110787") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du Bénéficiaire")
    }
    label("beneficiaryiban_8118110792") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118110797") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro d'ID National du Bénéficiaire")
    }
    label("beneficiaryaddress_8118110802") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du Bénéficiaire")
    }
    label("mpcode_8118110807") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du Paiement")
    }
    label("mpname_8118110812") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de Message")
    }
    label("paycurrency_8118110822") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118110827") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118110832") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("mpsenderbnkcd_8118110842") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code Banque de l'Initiateur)")
    }
    label("sendername_8118110847") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Initiator Bank Name)")
    }
    label("mpsenderbrnch_8118110852") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence de l'Initiateur)")
    }
    label("mpreceiverbnkcd_8118110857") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code Banque du Destinataire")
    }
    label("mpreceivername_8118110862") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Destinataire)")
    }
    label("mpreceiverbrnch_8118110867") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118110872") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (Statut")
    }
    label("mpsessionno_8118110877") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118110882") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la Transaction")
    }
    label("achname_8118110887") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du Paiement (ID du Lot)")
    }
    label("achendtoend_8118110902") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de bout en bout")
    }
    label("achinstructionid_8118110907") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'Instruction")
    }
    label("achmandateid_8118110912") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118110917") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118110922") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118110927") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("achcreditoraccount_8118110932") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118110937") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118110942") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118110947") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118110952") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118110957") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("achtransactionpurpose_8118110962") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118110967") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118110972") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118110977") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118110982") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118110987") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Info additionnelle")
    }
    label("paymentrefid_8118110992") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("mainreason.id_8118111002") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118111007") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118111012") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118111017") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118110997") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118111023") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputewaitreply.cr_8118111028") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputewaitreply.db_8118111032") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118111036") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur de Paiement ")
    }
    label("mobilenoserviceid_8118111041") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service de numéro de mobile")
    }
    label("merchantbillerref_8118111046") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Merchant Biller Ref")
    }
    label("merchantidbillerid_8118111051") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118111056") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118111061") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du Litige")
    }
    label("urgencysla.id_8118111071") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118111076") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118111081") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118111086") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118111066") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118111092") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118111102") {
        en("fees.id")
        ar("fees.id")
        other("fr", "Frais.")
    }
    label("fees.code_8118111107") {
        en("fees.code")
        ar("fees.code")
        other("fr", "Code lié aux Frais")
    }
    label("fees.name_8118111112") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118111117") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "Frais.")
    }
    label("fees_8118111097") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais des litiges")
    }
    label("checkercomments_8118111123") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118111128") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118111133") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118111138") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118111143") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118111148") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("answer_8118111153") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118111158") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("serial_8118111163") {
        en("Serial")
        ar("Serial")
        other("fr", "En série")
    }
    label("represnted_8118111168") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118111173") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre des demandes d'information")
    }
    label("isrepresented_8118111178") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Représentation")
    }
    label("rejectionreason.id_8118111188") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118111193") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118111198") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118111203") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118111183") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118111209") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118111214") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118111224") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118111229") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118111234") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118111239") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118111219") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("escalationreason.description_8118111245") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("isescalated_8118111250") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Arbitrage")
    }
    label("escalationcharges.id_8118111260") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118111265") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118111270") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118111275") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118111255") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'Arbitrage")
    }
    label("escalationcharges.amount_8118111281") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des Frais")
    }
    label("penalizedparty_8118111286") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie Pénalisée")
    }
    label("disputewaitreply.notspecified_8118111291") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputewaitreply.initiatorbank_8118111295") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputewaitreply.recieverbank_8118111299") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118111303") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires liés à l'Arbitrage")
    }
    label("isgoodfaith_8118111308") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118111313") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputewaitreply.notsettled_8118111318") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputewaitreply.settled_8118111322") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputewaitreply.manuallysettled_8118111326") {
        en("Manually Settled")
        other("fr", "Réglé Manuellement")
    }
    label("finalstatustime_8118111330") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Heure du Statut final")
    }
    label("direction_8118111335") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118111340") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118111345") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118111350") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118111355") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118111360") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118111365") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118111375") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118111380") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118111385") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118111390") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118111395") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118111400") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118111405") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118111410") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("moreinformations_8118111417") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputewaitreply.moreinformations.tab_8118111422") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputewaitreply.moreinformations.form_8118111427") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputecase_8118111434") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputewaitreply.disputecase.tab_8118111439") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputewaitreply.disputecase.form_8118111444") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118111452") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputewaitreply.paymentinformation.tab_8118111457") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputewaitreply.paymentinformation.form_8118111462") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118111490") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputewaitreply.iipsinformation.tab_8118111495") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputewaitreply.iipsinformation.form_8118111500") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118111518") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputewaitreply.achinformation.tab_8118111523") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputewaitreply.achinformation.form_8118111528") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118111555") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputewaitreply.disputeinformation.tab_8118111560") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputewaitreply.disputeinformation.form_8118111565") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118111575") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputewaitreply.rejectedresons.tab_8118111580") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputewaitreply.rejectedresons.form_8118111585") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118111591") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputewaitreply.disputecycle.tab_8118111596") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputewaitreply.disputecycle.form_8118111601") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118111610") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputewaitreply.finalrejectionreason.tab_8118111615") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputewaitreply.finalrejectionreason.form_8118111620") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118111627") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputewaitreply.arbitrationinformation.tab_8118111632") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputewaitreply.arbitrationinformation.form_8118111637") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118111647") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputewaitreply.workflowstatus.tab_8118111652") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputewaitreply.workflowstatus.form_8118111657") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118111667") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputewaitreply.attachments.tab_8118111672") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputewaitreply.comments.tab_8118111677") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputewaitreply.changehistory.tab_8118111682") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("details_8118111957") {
        en("Details")
        ar("التفاصيل")
        other("fr", "Détails")
    }
    label("disputewaitreply.disputehistory.tab_8118111962") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("disputewaitreply.disputeinquiries.tab_8118111967") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("moreinformations_8118111687") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputewaitreply.moreinformations.tab_8118111692") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputewaitreply.moreinformations.form_8118111697") {
        en("More Informations")
        ar("More Informations")
        other("fr", "Plus d'informations")
    }
    label("disputecase_8118111704") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputewaitreply.disputecase.tab_8118111709") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputewaitreply.disputecase.form_8118111714") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118111722") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputewaitreply.paymentinformation.tab_8118111727") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputewaitreply.paymentinformation.form_8118111732") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118111760") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputewaitreply.iipsinformation.tab_8118111765") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputewaitreply.iipsinformation.form_8118111770") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118111788") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputewaitreply.achinformation.tab_8118111793") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputewaitreply.achinformation.form_8118111798") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118111825") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputewaitreply.disputeinformation.tab_8118111830") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputewaitreply.disputeinformation.form_8118111835") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118111845") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputewaitreply.rejectedresons.tab_8118111850") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputewaitreply.rejectedresons.form_8118111855") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118111861") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputewaitreply.disputecycle.tab_8118111866") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputewaitreply.disputecycle.form_8118111871") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118111880") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputewaitreply.finalrejectionreason.tab_8118111885") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputewaitreply.finalrejectionreason.form_8118111890") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118111897") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputewaitreply.arbitrationinformation.tab_8118111902") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputewaitreply.arbitrationinformation.form_8118111907") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118111917") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputewaitreply.workflowstatus.tab_8118111922") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputewaitreply.workflowstatus.form_8118111927") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118111937") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputewaitreply.attachments.tab_8118111942") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputewaitreply.comments.tab_8118111947") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputewaitreply.changehistory.tab_8118111952") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeinapproval.view_8118111972") {
        en("Dispute Approval")
        ar("Dispute Approval")
        other("fr", "Approbation du Litige")
    }
    label("id_8118111993") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refnumber_8118111998") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéros de Référence des Participants")
    }
    label("paymentsystem.id_8118112009") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118112014") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118112019") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118112024") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118112003") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputerefrenceid_8118112029") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118112034") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118112039") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118112044") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("achid_8118112049") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("senderbank.code_8118112064") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118112074") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118112054") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118112090") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118112100") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118112080") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118112106") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Participant du Débiteur")
    }
    label("debtorbankname_8118112111") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118112116") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant du Créancier")
    }
    label("creditorbankname_8118112121") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118112126") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118112131") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118112136") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118112141") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date de Règlement du Paiement")
    }
    label("paymentstatus.id_8118112151") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118112156") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118112161") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118112166") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118112146") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118112172") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du Paiement")
    }
    label("paymencategory_8118112177") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118112187") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de Paiement.ID")
    }
    label("paymenturgency.code_8118112192") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118112197") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de Paiement.nom")
    }
    label("paymenturgency.codenamepair_8118112202") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de Paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118112182") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118112223") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118112208") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118112228") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du Paiement")
    }
    label("debtorname_8118112233") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("debtoriban_8118112238") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118112243") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118112248") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du Débiteur")
    }
    label("beneficiaryname_8118112253") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du Bénéficiaire")
    }
    label("beneficiaryiban_8118112258") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118112263") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro d'ID National du Bénéficiaire")
    }
    label("beneficiaryaddress_8118112268") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du Bénéficiaire")
    }
    label("mpcode_8118112273") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du Paiement")
    }
    label("mpname_8118112278") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de Message")
    }
    label("paycurrency_8118112288") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118112293") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118112298") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("mpsenderbnkcd_8118112308") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code Banque de l'Initiateur)")
    }
    label("sendername_8118112313") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Initiator Bank Name)")
    }
    label("mpsenderbrnch_8118112318") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence de l'Initiateur)")
    }
    label("mpreceiverbnkcd_8118112323") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code Banque du Destinataire")
    }
    label("mpreceivername_8118112328") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Destinataire)")
    }
    label("mpreceiverbrnch_8118112333") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118112338") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (Statut")
    }
    label("mpsessionno_8118112343") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118112348") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la Transaction")
    }
    label("achname_8118112353") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du Paiement (ID du Lot)")
    }
    label("achendtoend_8118112368") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de bout en bout")
    }
    label("achinstructionid_8118112373") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'Instruction")
    }
    label("achmandateid_8118112378") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118112383") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118112388") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118112393") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("achcreditoraccount_8118112398") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118112403") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118112408") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118112413") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118112418") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118112423") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("achtransactionpurpose_8118112428") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118112433") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118112438") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118112443") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118112448") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118112453") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Info additionnelle")
    }
    label("paymentrefid_8118112458") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("mainreason.id_8118112468") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118112473") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118112478") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118112483") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118112463") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118112489") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputeinapproval.cr_8118112494") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputeinapproval.db_8118112498") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118112502") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur de Paiement ")
    }
    label("mobilenoserviceid_8118112507") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service de numéro de mobile")
    }
    label("merchantbillerref_8118112512") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Merchant Biller Ref")
    }
    label("merchantidbillerid_8118112517") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118112522") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118112527") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du Litige")
    }
    label("urgencysla.id_8118112537") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118112542") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118112547") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118112552") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118112532") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118112558") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118112568") {
        en("fees.id")
        ar("fees.id")
        other("fr", "Frais.")
    }
    label("fees.code_8118112573") {
        en("fees.code")
        ar("fees.code")
        other("fr", "Code lié aux Frais")
    }
    label("fees.name_8118112578") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118112583") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "Frais.")
    }
    label("fees_8118112563") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais des litiges")
    }
    label("checkercomments_8118112589") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118112594") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118112599") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118112604") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118112609") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118112614") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("question_8118112619") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118112624") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("answer_8118112629") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118112634") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("serial_8118112639") {
        en("Serial")
        ar("Serial")
        other("fr", "Série")
    }
    label("represnted_8118112644") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118112649") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre des demandes d'information")
    }
    label("isrepresented_8118112654") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Représentation")
    }
    label("rejectionreason.id_8118112664") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118112669") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118112674") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118112679") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118112659") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118112685") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118112690") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118112700") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118112705") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118112710") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118112715") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118112695") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("escalationreason.description_8118112721") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("isescalated_8118112726") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Arbitrage")
    }
    label("escalationcharges.id_8118112736") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118112741") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118112746") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118112751") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118112731") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'Arbitrage")
    }
    label("escalationcharges.amount_8118112757") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des Frais")
    }
    label("penalizedparty_8118112762") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie Pénalisée")
    }
    label("disputeinapproval.notspecified_8118112767") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputeinapproval.initiatorbank_8118112771") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputeinapproval.recieverbank_8118112775") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118112779") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires liés à l'Arbitrage")
    }
    label("isgoodfaith_8118112784") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118112789") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputeinapproval.notsettled_8118112794") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputeinapproval.settled_8118112798") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputeinapproval.manuallysettled_8118112802") {
        en("Manually Settled")
        other("fr", "Réglé Manuellement")
    }
    label("finalstatustime_8118112806") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Heure du Statut final")
    }
    label("direction_8118112811") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118112816") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118112821") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118112826") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118112831") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118112836") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118112841") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118112851") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118112856") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118112861") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118112866") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118112871") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118112876") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118112881") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118112886") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("disputecase_8118112893") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinapproval.disputecase.tab_8118112898") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinapproval.disputecase.form_8118112903") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118112911") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeinapproval.paymentinformation.tab_8118112916") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeinapproval.paymentinformation.form_8118112921") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118112949") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinapproval.iipsinformation.tab_8118112954") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinapproval.iipsinformation.form_8118112959") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118112977") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinapproval.achinformation.tab_8118112982") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinapproval.achinformation.form_8118112987") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118113014") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinapproval.disputeinformation.tab_8118113019") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinapproval.disputeinformation.form_8118113024") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118113034") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinapproval.rejectedresons.tab_8118113039") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinapproval.rejectedresons.form_8118113044") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118113050") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinapproval.disputecycle.tab_8118113055") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinapproval.disputecycle.form_8118113060") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118113069") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeinapproval.finalrejectionreason.tab_8118113074") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeinapproval.finalrejectionreason.form_8118113079") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118113086") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeinapproval.arbitrationinformation.tab_8118113091") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeinapproval.arbitrationinformation.form_8118113096") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118113106") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinapproval.workflowstatus.tab_8118113111") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinapproval.workflowstatus.form_8118113116") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118113126") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinapproval.attachments.tab_8118113131") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputeinapproval.comments.tab_8118113136") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputeinapproval.changehistory.tab_8118113141") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("details_8118113399") {
        en("Details")
        ar("التفاصيل")
        other("fr", "Détails")
    }
    label("disputeinapproval.disputehistory.tab_8118113404") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("disputeinapproval.disputeinwardinquires.tab_8118113693") {
        en("Dispute Inward Inquires")
        ar("Dispute Inward Inquires")
        other("fr", "Demandes sur les Litiges Entrants")
    }
    label("disputecase_8118113146") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinapproval.disputecase.tab_8118113151") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinapproval.disputecase.form_8118113156") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118113164") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeinapproval.paymentinformation.tab_8118113169") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputeinapproval.paymentinformation.form_8118113174") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118113202") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinapproval.iipsinformation.tab_8118113207") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinapproval.iipsinformation.form_8118113212") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118113230") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinapproval.achinformation.tab_8118113235") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinapproval.achinformation.form_8118113240") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118113267") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinapproval.disputeinformation.tab_8118113272") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinapproval.disputeinformation.form_8118113277") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118113287") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinapproval.rejectedresons.tab_8118113292") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinapproval.rejectedresons.form_8118113297") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118113303") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinapproval.disputecycle.tab_8118113308") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinapproval.disputecycle.form_8118113313") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118113322") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeinapproval.finalrejectionreason.tab_8118113327") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputeinapproval.finalrejectionreason.form_8118113332") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118113339") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeinapproval.arbitrationinformation.tab_8118113344") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputeinapproval.arbitrationinformation.form_8118113349") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118113359") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinapproval.workflowstatus.tab_8118113364") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinapproval.workflowstatus.form_8118113369") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118113379") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinapproval.attachments.tab_8118113384") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputeinapproval.comments.tab_8118113389") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputeinapproval.changehistory.tab_8118113394") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeinappdtls.view_8118113409") {
        en("Dispute Inward Inquires")
        ar("Dispute Inward Inquires")
        other("fr", "Demandes sur les Litiges Entrants")
    }
    label("id_8118113417") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118113427") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118113432") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118113422") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118113438") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118113443") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118113448") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("responsedate_8118113453") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de Réponse")
    }
    label("answer_8118113458") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118113463") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("answerapproved_8118113468") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118113473") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118113478") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118113483") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118113488") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118113493") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118113503") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118113508") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118113513") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118113518") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118113523") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118113528") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118113533") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118113538") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118113545") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeinappdtls.requestedinformation.tab_8118113550") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeinappdtls.requestedinformation.form_8118113555") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118113562") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputeinappdtls.response.tab_8118113567") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputeinappdtls.response.form_8118113572") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118113579") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinappdtls.workflowstatus.tab_8118113584") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinappdtls.workflowstatus.form_8118113589") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118113599") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinappdtls.attachments.tab_8118113604") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputeinappdtls.comments.tab_8118113609") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputeinappdtls.changehistory.tab_8118113614") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118113619") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeinappdtls.requestedinformation.tab_8118113624") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeinappdtls.requestedinformation.form_8118113629") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118113636") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputeinappdtls.response.tab_8118113641") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputeinappdtls.response.form_8118113646") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118113653") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinappdtls.workflowstatus.tab_8118113658") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinappdtls.workflowstatus.form_8118113663") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118113673") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinappdtls.attachments.tab_8118113678") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputeinappdtls.comments.tab_8118113683") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputeinappdtls.changehistory.tab_8118113688") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputequestionapproval.view_8118113698") {
        en("Request More Information Approval")
        ar("Request More Information Approval")
        other("fr", "Demander une approbation plus d'informations")
    }
    label("id_8118113719") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("question_8118113724") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118113729") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes pour la question")
    }
    label("refnumber_8118113734") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéros de Référence des Participants")
    }
    label("paymentsystem.id_8118113745") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118113750") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118113755") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118113760") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118113739") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputerefrenceid_8118113765") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118113770") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118113775") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118113780") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("achid_8118113785") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("senderbank.code_8118113800") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118113810") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118113790") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118113826") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118113836") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118113816") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118113842") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Participant du Débiteur")
    }
    label("debtorbankname_8118113847") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118113852") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant du Créancier")
    }
    label("creditorbankname_8118113857") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118113862") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118113867") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118113872") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118113877") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date de Règlement du Paiement")
    }
    label("paymentstatus.id_8118113887") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118113892") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118113897") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118113902") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118113882") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118113908") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du Paiement")
    }
    label("paymencategory_8118113913") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118113923") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de Paiement.ID")
    }
    label("paymenturgency.code_8118113928") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118113933") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de Paiement.nom")
    }
    label("paymenturgency.codenamepair_8118113938") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de Paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118113918") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118113959") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118113944") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118113964") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du Paiement")
    }
    label("debtorname_8118113969") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("debtoriban_8118113974") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118113979") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118113984") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du Débiteur")
    }
    label("beneficiaryname_8118113989") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du Bénéficiaire")
    }
    label("beneficiaryiban_8118113994") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118113999") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro d'ID National du Bénéficiaire")
    }
    label("beneficiaryaddress_8118114004") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du Bénéficiaire")
    }
    label("mpcode_8118114009") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du Paiement")
    }
    label("mpname_8118114014") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de Message")
    }
    label("paycurrency_8118114024") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118114029") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118114034") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("mpsenderbnkcd_8118114044") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code Banque de l'Initiateur)")
    }
    label("sendername_8118114049") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Initiator Bank Name)")
    }
    label("mpsenderbrnch_8118114054") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence de l'Initiateur)")
    }
    label("mpreceiverbnkcd_8118114059") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code Banque du Destinataire)")
    }
    label("mpreceivername_8118114064") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Destinataire)")
    }
    label("mpreceiverbrnch_8118114069") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118114074") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (Statut")
    }
    label("mpsessionno_8118114079") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118114084") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la Transaction")
    }
    label("achname_8118114089") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du Paiement (ID du Lot)")
    }
    label("achendtoend_8118114104") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de bout en bout")
    }
    label("achinstructionid_8118114109") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'Instruction")
    }
    label("achmandateid_8118114114") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118114119") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118114124") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118114129") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du Débiteur")
    }
    label("achcreditoraccount_8118114134") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118114139") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118114144") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118114149") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118114154") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118114159") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date de Règlement")
    }
    label("achtransactionpurpose_8118114164") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118114169") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118114174") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118114179") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118114184") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118114189") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Info additionnelle")
    }
    label("paymentrefid_8118114194") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du Paiement")
    }
    label("mainreason.id_8118114204") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118114209") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118114214") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118114219") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118114199") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118114225") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputequestionapproval.cr_8118114230") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputequestionapproval.db_8118114234") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118114238") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur de Paiement ")
    }
    label("mobilenoserviceid_8118114243") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service de numéro de mobile")
    }
    label("merchantbillerref_8118114248") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Merchant Biller Ref")
    }
    label("merchantidbillerid_8118114253") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118114258") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118114263") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du Litige")
    }
    label("urgencysla.id_8118114273") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118114278") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118114283") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118114288") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118114268") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118114294") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118114304") {
        en("fees.id")
        ar("fees.id")
        other("fr", "Frais.")
    }
    label("fees.code_8118114309") {
        en("fees.code")
        ar("fees.code")
        other("fr", "Code lié aux Frais")
    }
    label("fees.name_8118114314") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118114319") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "Frais.")
    }
    label("fees_8118114299") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais des litiges")
    }
    label("checkercomments_8118114325") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118114330") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118114335") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118114340") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118114345") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118114350") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("answer_8118114355") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118114360") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes pour la Réponse")
    }
    label("serial_8118114365") {
        en("Serial")
        ar("Serial")
        other("fr", "Série")
    }
    label("represnted_8118114370") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118114375") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre des demandes d'information")
    }
    label("isrepresented_8118114380") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Représentation")
    }
    label("rejectionreason.id_8118114390") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118114395") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118114400") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118114405") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118114385") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118114411") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118114416") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118114426") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118114431") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118114436") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118114441") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118114421") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("escalationreason.description_8118114447") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'Arbitrage")
    }
    label("isescalated_8118114452") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Arbitrage")
    }
    label("escalationcharges.id_8118114462") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118114467") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118114472") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118114477") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118114457") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'Arbitrage")
    }
    label("escalationcharges.amount_8118114483") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des Frais")
    }
    label("penalizedparty_8118114488") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie Pénalisée")
    }
    label("disputequestionapproval.notspecified_8118114493") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputequestionapproval.initiatorbank_8118114497") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputequestionapproval.recieverbank_8118114501") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118114505") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires liés à l'Arbitrage")
    }
    label("isgoodfaith_8118114510") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi)")
    }
    label("settled_8118114515") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputequestionapproval.notsettled_8118114520") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputequestionapproval.settled_8118114524") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputequestionapproval.manuallysettled_8118114528") {
        en("Manually Settled")
        other("fr", "Réglé Manuellement")
    }
    label("finalstatustime_8118114532") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Heure du Statut final")
    }
    label("direction_8118114537") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118114542") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118114547") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118114552") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118114557") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118114562") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du Statut")
    }
    label("statusid.description_8118114567") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118114577") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118114582") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118114587") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118114592") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118114597") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118114602") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118114607") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118114612") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("question_8118114619") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputequestionapproval.question.tab_8118114624") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputequestionapproval.question.form_8118114629") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputecase_8118114636") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputequestionapproval.disputecase.tab_8118114641") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputequestionapproval.disputecase.form_8118114646") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118114654") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputequestionapproval.paymentinformation.tab_8118114659") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputequestionapproval.paymentinformation.form_8118114664") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118114692") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputequestionapproval.iipsinformation.tab_8118114697") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputequestionapproval.iipsinformation.form_8118114702") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118114720") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputequestionapproval.achinformation.tab_8118114725") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputequestionapproval.achinformation.form_8118114730") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118114757") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputequestionapproval.disputeinformation.tab_8118114762") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputequestionapproval.disputeinformation.form_8118114767") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118114777") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputequestionapproval.rejectedresons.tab_8118114782") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputequestionapproval.rejectedresons.form_8118114787") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118114793") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputequestionapproval.disputecycle.tab_8118114798") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputequestionapproval.disputecycle.form_8118114803") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118114812") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputequestionapproval.finalrejectionreason.tab_8118114817") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputequestionapproval.finalrejectionreason.form_8118114822") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118114829") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputequestionapproval.arbitrationinformation.tab_8118114834") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputequestionapproval.arbitrationinformation.form_8118114839") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118114849") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputequestionapproval.workflowstatus.tab_8118114854") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputequestionapproval.workflowstatus.form_8118114859") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118114869") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputequestionapproval.attachments.tab_8118114874") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputequestionapproval.comments.tab_8118114879") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputequestionapproval.changehistory.tab_8118114884") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeinquiries_8118115159") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("disputequestionapproval.disputeinquiries.tab_8118115164") {
        en("Dispute Inquiries")
        ar("Dispute Inquiries")
        other("fr", "Requête liée au litige")
    }
    label("question_8118114889") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputequestionapproval.question.tab_8118114894") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputequestionapproval.question.form_8118114899") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputecase_8118114906") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputequestionapproval.disputecase.tab_8118114911") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputequestionapproval.disputecase.form_8118114916") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118114924") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputequestionapproval.paymentinformation.tab_8118114929") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("disputequestionapproval.paymentinformation.form_8118114934") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations sur le Paiement")
    }
    label("iipsinformation_8118114962") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputequestionapproval.iipsinformation.tab_8118114967") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputequestionapproval.iipsinformation.form_8118114972") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118114990") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputequestionapproval.achinformation.tab_8118114995") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputequestionapproval.achinformation.form_8118115000") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118115027") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputequestionapproval.disputeinformation.tab_8118115032") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputequestionapproval.disputeinformation.form_8118115037") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118115047") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputequestionapproval.rejectedresons.tab_8118115052") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputequestionapproval.rejectedresons.form_8118115057") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118115063") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputequestionapproval.disputecycle.tab_8118115068") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputequestionapproval.disputecycle.form_8118115073") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118115082") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputequestionapproval.finalrejectionreason.tab_8118115087") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("disputequestionapproval.finalrejectionreason.form_8118115092") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif de rejet final")
    }
    label("arbitrationinformation_8118115099") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputequestionapproval.arbitrationinformation.tab_8118115104") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("disputequestionapproval.arbitrationinformation.form_8118115109") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'Arbitrage")
    }
    label("workflowstatus_8118115119") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputequestionapproval.workflowstatus.tab_8118115124") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputequestionapproval.workflowstatus.form_8118115129") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118115139") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputequestionapproval.attachments.tab_8118115144") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("disputequestionapproval.comments.tab_8118115149") {
        en("Comments")
        ar("تعليقات")
        other("fr", "Commentaires")
    }
    label("disputequestionapproval.changehistory.tab_8118115154") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputequestions.view_8118115169") {
        en("Dispute Request More Information")
        ar("Dispute Request More Information")
        other("fr", "Demande de litige plus d'informations")
    }
    label("question_8118115176") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputequestions.question.tab_8118115181") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputequestions.question.form_8118115186") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("attachments_8118115192") {
        en("Attachments")
        ar("Attachments")
        other("fr", "Pièces jointes")
    }
    label("disputequestions.attachments.tab_8118115197") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("questionattachment_8118115202") {
        en("Question Attachment")
        ar("Question Attachment")
        other("fr", "Pièce jointe")
    }
    label("disputequestions.questionattachment.tab_8118115207") {
        en("Question Attachment")
        ar("Question Attachment")
        other("fr", "Pièce jointe")
    }
    label("disputequestions.questionattachment.form_8118115212") {
        en("Question Attachment")
        ar("Question Attachment")
        other("fr", "Pièce jointe")
    }
    label("confemailtemplate.324834069") {
        en("Email Subject")
        ar("Email Subject")
        other("fr", "Sujet du courriel")
    }
    label("confemailtemplate.216086513") {
        en("Email Body")
        ar("Email Body")
        other("fr", "Corps de l'e-mail")
    }
    label("confemailtemplate.216086513.supportedValues") {
        en("Supported Values for email body template, please copy and paste as needed:")
        other("fr", "Valeurs prises en charge pour le modèle de corps de l'email, veuillez copier et coller au besoin:")
    }
    label("achPaymentTransaction_8118115212") {
        en("ACH Payment Transaction Reference")
        ar("ACH Payment Transaction Reference")
        other("fr", "Référence de la transaction de Paiement ACH")
    }
    label("mpcPaymentTransaction_8118115212") {
        en("MP Clear Payment Transaction Reference")
        ar("MP Clear Transaction Reference")
        other("fr", "Référence de transaction de Paiement MPclear")
    }
    label("JFW_WF_ACTION.185381307") {
        en("SVC_FinalDecline")
        ar("SVC_FinalDecline")
        other("fr", "SVC_FINALDECLINE")
    }
    label("SVC_OperatorCloseDispute") {
        en("SVC_OperatorCloseDispute")
        ar("SVC_OperatorCloseDispute")
        other("fr", "SVC_OperatorClosedispute")
    }
    label("confemailtemplate.216086513.disputeProperties") {
        en("")
    }
    label("terms.and.conditions.80579032") {
        en("Terms And Conditions")
    }
    label("reset") {
        en("Reset")
        ar("إعادة ضبط")
    }
}
