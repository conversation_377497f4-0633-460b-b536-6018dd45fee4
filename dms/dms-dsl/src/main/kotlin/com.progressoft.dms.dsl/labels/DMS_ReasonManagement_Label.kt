package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

val DMS_ReasonManagement_Label = Labels {
    label("reason.management.view") {
        en("Reasons Management")
        ar("إدارة الأسباب")
        other("fr", "Gestion des Motifs")
    }
    label("reason.management.details.portlet") {
        en("Details")
        ar("التفاصيل")
        other("fr", "Détails")
    }
    label("reason.management.code") {
        en("Code")
        ar("الرمز")
        other("fr", "Code")
    }
    label("reason.management.name") {
        en("Name")
        ar("الاسم")
        other("fr", "Nom")
    }
    label("reason.management.desc") {
        en("Description")
        ar("الوصف")
        other("fr", "Description")
    }
    label("reason.management.isdispute") {
        en("Is Dispute")
        ar("الوصف")
        other("fr", "Litige")
    }
    label("reason.management.isrepresentment") {
        en("Is Representment")
        ar("الوصف")
        other("fr", "Représentation")
    }
    label("reason.management.isrejection") {
        en("Is Rejection")
        ar("الوصف")
        other("fr", "Rejet")
    }
    label("reason.management.isreqinfo") {
        en("Is Request Additional Info")
        ar("الوصف")
        other("fr", "Informations Supplémentaires")
    }
    label("reason.code.uniqueness.msg") {
        en("Reason code should be unique")
        ar("يجب أن يكون الرمز مميزاً")
        other("fr", "Le code du motif devrait être unique")
    }
    label("reason.type.select.msg") {
        en("At least one reason type should be selected")
        ar("يجب أن يكون الرمز مميزاً")
        other("fr", "Au moins un type de motif doit être sélectionné")
    }
}
