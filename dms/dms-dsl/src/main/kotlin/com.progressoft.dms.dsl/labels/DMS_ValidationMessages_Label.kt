package com.progressoft.dms.dsl.labels
import com.progressoft.jupiter.kotlin.dsl.Labels

var DMS_ValidationMessages_Label = Labels {
    label("valid.email") {
        en("Enter valid email with maximum characters of 40")
        ar("أدخل بريدًا إلكترونيًا صالحًا بحد أقصى 40 حرفًا")
        other("fr", "Entrez une adresse e-mail valide avec un maximum de 40 caractères")
    }
    label("code.validation.msg") {
        en("The maximum limit is 50 characters")
        ar("الحد الأقصى هو 50 حرفًا")
        other("fr", "La limite maximale est de 50 caractères")
    }
    label("short.name") {
        en("Short name must be between 3 and 4 uppercase letters.")
        ar("يجب أن يتكون الاسم المختصر من 3 إلى 4 أحرف كبيرة.")
        other("fr", "Le nom court doit contenir entre 3 et 4 lettres majuscules.")
    }
    label("valid.amount") {
        en("Please enter a valid number with up to two decimal places")
        ar("الرجاء إدخال رقم صالح مع ما يصل إلى منزلتين عشريتين")
        other("fr", "Veuillez saisir un nombre valide avec jusqu'à deux décimales")
    }
    label("email.validation.msg"){
        en("Email must follow the following pattern: <EMAIL> with maximum characters of 40")
        ar("يجب أن يتبع البريد الإلكتروني النمط التالي: <EMAIL> و بحد أقصى 40 حرفًا")
        other("fr", "L'e-mail doit suivre le modèle suivant : <EMAIL> avec un maximum de 40 caractères.")
    }
    label("desc.validation.msg"){
        en("The maximum limit is 225 characters")
        ar("الحد الأقصى هو 225 حرفًا")
        other("fr", "La limite maximale est de 225 caractères")
    }
    label("desc.tool.tip"){
        en("The maximum limit is 100 characters")
        ar("الحد الأقصى هو 100 حرفًا")
        other("fr", "La limite maximale est de 100 caractères")
    }
    label("disputed.amount.validation.msg"){
        en("Disputed amount should be less than or equals the transaction amount")
        ar("يجب أن يكون المبلغ المتنازع عليه أقل من أو يساوي مبلغ المعاملة")
        other("fr", "Le montant contesté doit être inférieur ou égal au montant de la transaction")
    }
}