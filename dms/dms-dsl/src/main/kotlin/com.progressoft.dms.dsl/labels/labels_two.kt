package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

var LABELS_TWO = Labels {
    label("question_8118115217") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputequestions.question.tab_8118115222") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("disputequestions.question.form_8118115227") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("attachments_8118115233") {
        en("Attachments")
        ar("Attachments")
        other("fr", "Pièces jointes")
    }
    label("questionattachment_8118115238") {
        en("Question Attachment")
        ar("Question Attachment")
        other("fr", "Pièce jointe")
    }
    label("disputeanswer.view_8118115243") {
        en("Response to Requsted Information")
        ar("Response to Requsted Information")
        other("fr", "Réponse aux informations requises")
    }
    label("responsetomoreinformation_8118115250") {
        en("Response to More Information")
        ar("Response to More Information")
        other("fr", "Réponse à une demande d'informations")
    }
    label("disputeanswer.responsetomoreinformation.tab_8118115255") {
        en("Response to More Information")
        ar("Response to More Information")
        other("fr", "Réponse à une demande d'informations")
    }
    label("disputeanswer.responsetomoreinformation.form_8118115260") {
        en("Response to More Information")
        ar("Response to More Information")
        other("fr", "Réponse à une demande d'informations")
    }
    label("attachments_8118115266") {
        en("Attachments")
        ar("Attachments")
        other("fr", "Pièces jointes")
    }
    label("disputeanswer.attachments.tab_8118115271") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "Pièces jointes")
    }
    label("answerattachment_8118115276") {
        en("Answer Attachment")
        ar("Answer Attachment")
        other("fr", "Pièce jointe pour la réponse")
    }
    label("disputeanswer.answerattachment.tab_8118115281") {
        en("Answer Attachment")
        ar("Answer Attachment")
        other("fr", "Pièce jointe pour la de réponse")
    }
    label("disputeanswer.answerattachment.form_8118115286") {
        en("Answer Attachment")
        ar("Answer Attachment")
        other("fr", "Pièce jointe pour la de réponse")
    }
    label("responsetomoreinformation_8118115291") {
        en("Response to More Information")
        ar("Response to More Information")
        other("fr", "Réponse à une demande d'informations")
    }
    label("disputeanswer.responsetomoreinformation.tab_8118115296") {
        en("Response to More Information")
        ar("Response to More Information")
        other("fr", "Réponse à une demande d'informations")
    }
    label("disputeanswer.responsetomoreinformation.form_8118115301") {
        en("Response to More Information")
        ar("Response to More Information")
        other("fr", "Réponse à une demande d'informations")
    }
    label("attachments_8118115307") {
        en("Attachments")
        ar("Attachments")
        other("fr", "Pièces jointes")
    }
    label("answerattachment_8118115312") {
        en("Answer Attachment")
        ar("Answer Attachment")
        other("fr", "Pièce jointe pour la réponse")
    }
    label("disputeinward.view_8118115317") {
        en("Dispute Master Query")
        ar("Dispute Master Query")
        other("fr", "Requête liée au litige")
    }
    label("id_8118115334") {
        en("ID")
        ar("ID")
        other("fr", "ID")
    }
    label("direction_8118115339") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("refnumber_8118115344") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéro de Référence des Participants")
    }
    label("paymentsystem.id_8118115355") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118115360") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118115365") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118115370") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118115349") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de Paiement")
    }
    label("disputerefrenceid_8118115375") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118115380") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118115385") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "Date de validation du SLA")
    }
    label("iipsid_8118115390") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("achid_8118115395") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("senderbank.code_8118115410") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118115420") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118115400") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118115436") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118115446") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118115426") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118115452") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Débiteur")
    }
    label("debtorbankname_8118115457") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118115462") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant Créancier")
    }
    label("creditorbankname_8118115467") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118115472") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118115477") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118115482") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118115487") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date du Règlement du Paiement")
    }
    label("paymentstatus.id_8118115497") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118115502") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118115507") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118115512") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118115492") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118115518") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du paiement")
    }
    label("paymencategory_8118115523") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118115533") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de paiement.ID")
    }
    label("paymenturgency.code_8118115538") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118115543") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de paiement.nom")
    }
    label("paymenturgency.codenamepair_8118115548") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118115528") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118115569") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118115554") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118115574") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du paiement")
    }
    label("debtorname_8118115579") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du débiteur")
    }
    label("debtoriban_8118115584") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118115589") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118115594") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du débiteur")
    }
    label("beneficiaryname_8118115599") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du bénéficiaire")
    }
    label("beneficiaryiban_8118115604") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118115609") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro National d'ID du Bénéficiaire")
    }
    label("beneficiaryaddress_8118115614") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du bénéficiaire")
    }
    label("mpcode_8118115619") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du paiement")
    }
    label("mpname_8118115624") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de message")
    }
    label("paycurrency_8118115634") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118115639") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118115644") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date du Règlement")
    }
    label("mpsenderbnkcd_8118115654") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code banque de l'Initiateur)")
    }
    label("sendername_8118115659") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Nom de l'Initiateur)")
    }
    label("mpsenderbrnch_8118115664") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence Initiatrice)")
    }
    label("mpreceiverbnkcd_8118115669") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code banque du Destinataire")
    }
    label("mpreceivername_8118115674") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Participant Destinataire)")
    }
    label("mpreceiverbrnch_8118115679") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118115684") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (statut")
    }
    label("mpsessionno_8118115689") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118115694") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la transaction")
    }
    label("achname_8118115699") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du paiement (ID du Lot)")
    }
    label("achdescription_8118115704") {
        en("Reason")
        ar("السبب")
        other("fr", "Motif")
    }
    label("achendtoend_8118115714") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de Bout en Bout")
    }
    label("achinstructionid_8118115719") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'instruction")
    }
    label("achmandateid_8118115724") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118115729") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118115734") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118115739") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du débiteur")
    }
    label("achcreditoraccount_8118115744") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118115749") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118115754") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118115759") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118115764") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118115769") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date du Règlement")
    }
    label("achtransactionpurpose_8118115774") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118115779") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118115784") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118115789") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118115794") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118115799") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Information additionnelle")
    }
    label("paymentrefid_8118115804") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("mainreason.id_8118115814") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118115819") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118115824") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118115829") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118115809") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118115835") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputeinward.cr_8118115840") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputeinward.db_8118115844") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118115848") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur du paiement")
    }
    label("mobilenoserviceid_8118115853") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service du numéro de mobile")
    }
    label("merchantbillerref_8118115858") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Ref du Marchand")
    }
    label("merchantidbillerid_8118115863") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118115868") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118115873") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du cas")
    }
    label("urgencysla.id_8118115883") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118115888") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118115893") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118115898") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118115878") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118115904") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118115914") {
        en("fees.id")
        ar("fees.id")
        other("fr", "frais.")
    }
    label("fees.code_8118115919") {
        en("fees.code")
        ar("fees.code")
        other("fr", "frais de code")
    }
    label("fees.name_8118115924") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118115929") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "frais.")
    }
    label("fees_8118115909") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais associés au litige")
    }
    label("checkercomments_8118115935") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118115940") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118115945") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118115950") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118115955") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118115960") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("question_8118115965") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118115970") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "Pièces jointes de la question")
    }
    label("answer_8118115975") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118115980") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "Pièces jointes de la réponse")
    }
    label("serial_8118115985") {
        en("Serial")
        ar("Serial")
        other("fr", "En série")
    }
    label("represnted_8118115990") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118115995") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre de demandes d'information")
    }
    label("isrepresented_8118116000") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Est représenté")
    }
    label("rejectionreason.id_8118116010") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118116015") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118116020") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118116025") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118116005") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118116031") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118116036") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118116046") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118116051") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118116056") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118116061") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118116041") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("escalationreason.description_8118116067") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("isescalated_8118116072") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Est arbitré")
    }
    label("escalationcharges.id_8118116082") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118116087") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118116092") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118116097") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118116077") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("escalationcharges.amount_8118116103") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des frais")
    }
    label("penalizedparty_8118116108") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie pénalisée")
    }
    label("disputeinward.notspecified_8118116113") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputeinward.initiatorbank_8118116117") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputeinward.recieverbank_8118116121") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118116125") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires d'arbitrage")
    }
    label("isgoodfaith_8118116130") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118116135") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputeinward.notsettled_8118116140") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputeinward.settled_8118116144") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputeinward.manuallysettled_8118116148") {
        en("Manually Settled")
        other("fr", "Réglé manuellement")
    }
    label("finalstatustime_8118116152") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Temps du statut final")
    }
    label("currency.id_8118116157") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118116162") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118116167") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118116172") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118116177") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118116182") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118116192") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118116197") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118116202") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118116207") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118116212") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118116217") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118116222") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118116227") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("disputecase_8118116234") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinward.disputecase.tab_8118116239") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinward.disputecase.form_8118116244") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118116253") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeinward.paymentinformation.tab_8118116258") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeinward.paymentinformation.form_8118116263") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("iipsinformation_8118116291") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinward.iipsinformation.tab_8118116296") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinward.iipsinformation.form_8118116301") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118116319") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinward.achinformation.tab_8118116324") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinward.achinformation.form_8118116329") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118116356") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinward.disputeinformation.tab_8118116361") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinward.disputeinformation.form_8118116366") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118116376") {
        en("Rejected Reasons")
        ar("Rejected Reasons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinward.rejectedresons.tab_8118116381") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinward.rejectedresons.form_8118116386") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118116392") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinward.disputecycle.tab_8118116397") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinward.disputecycle.form_8118116402") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118116411") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeinward.finalrejectionreason.tab_8118116416") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeinward.finalrejectionreason.form_8118116421") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("arbitrationinformation_8118116428") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeinward.arbitrationinformation.tab_8118116433") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeinward.arbitrationinformation.form_8118116438") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("workflowstatus_8118116448") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinward.workflowstatus.tab_8118116453") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinward.workflowstatus.form_8118116458") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118116468") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinward.attachments.tab_8118116473") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputeinward.comments.tab_8118116478") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputeinward.changehistory.tab_8118116483") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("details_8118116742") {
        en("Details")
        ar("التفاصيل")
        other("fr", "Détails")
    }
    label("disputeinward.disputehistory.tab_8118116747") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("disputeinward.disputeinwardinquires.tab_8118117036") {
        en("Dispute Inward Inquires")
        ar("Dispute Inward Inquires")
        other("fr", "Demandes sur les Litiges Entrants")
    }
    label("disputecase_8118116488") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinward.disputecase.tab_8118116493") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinward.disputecase.form_8118116498") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118116507") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeinward.paymentinformation.tab_8118116512") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeinward.paymentinformation.form_8118116517") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("iipsinformation_8118116545") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinward.iipsinformation.tab_8118116550") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinward.iipsinformation.form_8118116555") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118116573") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinward.achinformation.tab_8118116578") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinward.achinformation.form_8118116583") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118116610") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinward.disputeinformation.tab_8118116615") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinward.disputeinformation.form_8118116620") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118116630") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinward.rejectedresons.tab_8118116635") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinward.rejectedresons.form_8118116640") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118116646") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinward.disputecycle.tab_8118116651") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinward.disputecycle.form_8118116656") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118116665") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeinward.finalrejectionreason.tab_8118116670") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeinward.finalrejectionreason.form_8118116675") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("arbitrationinformation_8118116682") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeinward.arbitrationinformation.tab_8118116687") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeinward.arbitrationinformation.form_8118116692") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("workflowstatus_8118116702") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinward.workflowstatus.tab_8118116707") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinward.workflowstatus.form_8118116712") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118116722") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinward.attachments.tab_8118116727") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputeinward.comments.tab_8118116732") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputeinward.changehistory.tab_8118116737") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeinwarddetail.view_8118116752") {
        en("Dispute Inward Inquires")
        ar("Dispute Inward Inquires")
        other("fr", "Demandes sur les Litiges Entrants")
    }
    label("id_8118116760") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118116770") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118116775") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118116765") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118116781") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118116786") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118116791") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "pièces jointes pour la question")
    }
    label("responsedate_8118116796") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de réponse")
    }
    label("answer_8118116801") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118116806") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "pièces jointes pour la réponse")
    }
    label("answerapproved_8118116811") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118116816") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118116821") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118116826") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118116831") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118116836") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118116846") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118116851") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118116856") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118116861") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118116866") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118116871") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118116876") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118116881") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118116888") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeinwarddetail.requestedinformation.tab_8118116893") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeinwarddetail.requestedinformation.form_8118116898") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118116905") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputeinwarddetail.response.tab_8118116910") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputeinwarddetail.response.form_8118116915") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118116922") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinwarddetail.workflowstatus.tab_8118116927") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinwarddetail.workflowstatus.form_8118116932") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118116942") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinwarddetail.attachments.tab_8118116947") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputeinwarddetail.comments.tab_8118116952") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputeinwarddetail.changehistory.tab_8118116957") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118116962") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeinwarddetail.requestedinformation.tab_8118116967") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputeinwarddetail.requestedinformation.form_8118116972") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118116979") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputeinwarddetail.response.tab_8118116984") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputeinwarddetail.response.form_8118116989") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118116996") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinwarddetail.workflowstatus.tab_8118117001") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinwarddetail.workflowstatus.form_8118117006") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118117016") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinwarddetail.attachments.tab_8118117021") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputeinwarddetail.comments.tab_8118117026") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputeinwarddetail.changehistory.tab_8118117031") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeinreplied.view_8118117041") {
        en("Replied Disputes")
        ar("Replied Disputes")
        other("fr", "Litiges Répondus")
    }
    label("id_8118117062") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refnumber_8118117067") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéro de Référence des Participants")
    }
    label("paymentsystem.id_8118117078") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118117083") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118117088") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118117093") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118117072") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("disputerefrenceid_8118117098") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118117103") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118117108") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118117113") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("achid_8118117118") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("senderbank.code_**********") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118117143") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118117123") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118117159") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118117169") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118117149") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118117175") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Débiteur")
    }
    label("debtorbankname_8118117180") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118117185") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant Créancier")
    }
    label("creditorbankname_8118117190") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118117195") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118117200") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118117205") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118117210") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date du Règlement du Paiement")
    }
    label("paymentstatus.id_8118117220") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118117225") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118117230") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118117235") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118117215") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118117241") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du paiement")
    }
    label("paymencategory_8118117246") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118117256") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de paiement.ID")
    }
    label("paymenturgency.code_8118117261") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118117266") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de paiement.nom")
    }
    label("paymenturgency.codenamepair_8118117271") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118117251") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118117292") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118117277") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118117297") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du paiement")
    }
    label("debtorname_8118117302") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du débiteur")
    }
    label("debtoriban_8118117307") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118117312") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118117317") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du débiteur")
    }
    label("beneficiaryname_8118117322") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du bénéficiaire")
    }
    label("beneficiaryiban_8118117327") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118117332") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro National d'ID du Bénéficiaire")
    }
    label("beneficiaryaddress_8118117337") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du bénéficiaire")
    }
    label("mpcode_8118117342") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du paiement")
    }
    label("mpname_8118117347") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de message")
    }
    label("paycurrency_8118117357") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118117362") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118117367") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date du Règlement")
    }
    label("mpsenderbnkcd_8118117377") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code banque de l'Initiateur)")
    }
    label("sendername_8118117382") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Nom de l'Initiateur)")
    }
    label("mpsenderbrnch_8118117387") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence Initiatrice)")
    }
    label("mpreceiverbnkcd_8118117392") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code banque du Destinataire")
    }
    label("mpreceivername_8118117397") {
        en("Instructed Participant (Receiving Bank Name")
        ar("Instructed Participant (Receiving Bank Name")
        other("fr", "Participant Instruit (Nom du Participant Destinataire")
    }
    label("mpreceiverbrnch_8118117402") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118117407") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (statut")
    }
    label("mpsessionno_8118117412") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118117417") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la transaction")
    }
    label("achname_8118117422") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du paiement (ID du Lot)")
    }
    label("achendtoend_8118117437") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de Bout en Bout")
    }
    label("achinstructionid_8118117442") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'instruction")
    }
    label("achmandateid_8118117447") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118117452") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118117457") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118117462") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du débiteur")
    }
    label("achcreditoraccount_8118117467") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118117472") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118117477") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118117482") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118117487") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118117492") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date du Règlement")
    }
    label("achtransactionpurpose_8118117497") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118117502") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118117507") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118117512") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118117517") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118117522") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Information additionnelle")
    }
    label("paymentrefid_8118117527") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("mainreason.id_8118117537") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118117542") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118117547") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118117552") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118117532") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118117558") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputeinreplied.cr_8118117563") {
        en("CR")
        other("fr", "Cr")
    }
    label("disputeinreplied.db_8118117567") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118117571") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur du paiement")
    }
    label("mobilenoserviceid_8118117576") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service du numéro de mobile")
    }
    label("merchantbillerref_8118117581") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Ref du Marchand")
    }
    label("merchantidbillerid_8118117586") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118117591") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118117596") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du cas")
    }
    label("urgencysla.id_8118117606") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118117611") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118117616") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118117621") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118117601") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118117627") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118117637") {
        en("fees.id")
        ar("fees.id")
        other("fr", "frais.")
    }
    label("fees.code_8118117642") {
        en("fees.code")
        ar("fees.code")
        other("fr", "frais de code")
    }
    label("fees.name_8118117647") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118117652") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "frais.")
    }
    label("fees_8118117632") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais associés au litige")
    }
    label("checkercomments_8118117658") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118117663") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118117668") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118117673") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118117678") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118117683") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("question_8118117688") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118117693") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "pièces jointes pour la question")
    }
    label("answer_8118117698") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118117703") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "pièces jointes pour la réponse")
    }
    label("serial_8118117708") {
        en("Serial")
        ar("Serial")
        other("fr", "En série")
    }
    label("represnted_8118117713") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118117718") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre de demandes d'information")
    }
    label("isrepresented_8118117723") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Est représenté")
    }
    label("rejectionreason.id_8118117733") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118117738") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118117743") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118117748") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118117728") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118117754") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118117759") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118117769") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118117774") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118117779") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118117784") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118117764") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("escalationreason.description_8118117790") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("isescalated_8118117795") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Est arbitré")
    }
    label("escalationcharges.id_8118117805") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118117810") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118117815") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118117820") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118117800") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("escalationcharges.amount_8118117826") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des frais")
    }
    label("penalizedparty_8*********") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie pénalisée")
    }
    label("disputeinreplied.notspecified_8118117836") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputeinreplied.initiatorbank_8118117840") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputeinreplied.recieverbank_8118117844") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118117848") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires sur l'arbitrage")
    }
    label("isgoodfaith_8118117853") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118117858") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputeinreplied.notsettled_8118117863") {
        en("Not Settled")
        other("fr", "Non-Réglé")
    }
    label("disputeinreplied.settled_8118117867") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputeinreplied.manuallysettled_8118117871") {
        en("Manually Settled")
        other("fr", "Réglé manuellement")
    }
    label("finalstatustime_8118117875") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Temps du statut final")
    }
    label("direction_8118117880") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118117885") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118117890") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118117895") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118117900") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118117905") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118117910") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118117920") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118117925") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118117930") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118117935") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118117940") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118117945") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118117950") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118117955") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("disputecase_8118117962") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinreplied.disputecase.tab_8118117967") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinreplied.disputecase.form_8118117972") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118117980") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeinreplied.paymentinformation.tab_8118117985") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeinreplied.paymentinformation.form_8118117990") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("iipsinformation_8118118018") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinreplied.iipsinformation.tab_8118118023") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinreplied.iipsinformation.form_8118118028") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118118046") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinreplied.achinformation.tab_8118118051") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinreplied.achinformation.form_8118118056") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118118083") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinreplied.disputeinformation.tab_8118118088") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinreplied.disputeinformation.form_8118118093") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118118103") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinreplied.rejectedresons.tab_8118118108") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinreplied.rejectedresons.form_8118118113") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118118119") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinreplied.disputecycle.tab_8118118124") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinreplied.disputecycle.form_8118118129") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118118138") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeinreplied.finalrejectionreason.tab_8118118143") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeinreplied.finalrejectionreason.form_8118118148") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("arbitrationinformation_8118118155") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeinreplied.arbitrationinformation.tab_8118118160") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeinreplied.arbitrationinformation.form_8118118165") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("workflowstatus_8118118175") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinreplied.workflowstatus.tab_8118118180") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinreplied.workflowstatus.form_8118118185") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118118195") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinreplied.attachments.tab_8118118200") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputeinreplied.comments.tab_8118118205") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputeinreplied.changehistory.tab_8118118210") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("details_8118118468") {
        en("Details")
        ar("التفاصيل")
        other("fr", "Détails")
    }
    label("disputeinreplied.disputehistory.tab_8118118473") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("disputeinreplied.replieddisputeinquires.tab_8118118762") {
        en("Replied Dispute Inquires")
        ar("Replied Dispute Inquires")
        other("fr", "Demandes sur les Litiges Répondus")
    }
    label("disputecase_8118118215") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinreplied.disputecase.tab_8118118220") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeinreplied.disputecase.form_8118118225") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118118233") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeinreplied.paymentinformation.tab_8118118238") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeinreplied.paymentinformation.form_8118118243") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("iipsinformation_8118118271") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinreplied.iipsinformation.tab_8118118276") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeinreplied.iipsinformation.form_8118118281") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118118299") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinreplied.achinformation.tab_8118118304") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinreplied.achinformation.form_8118118309") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118118336") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinreplied.disputeinformation.tab_8118118341") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeinreplied.disputeinformation.form_8118118346") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118118356") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinreplied.rejectedresons.tab_8118118361") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeinreplied.rejectedresons.form_8118118366") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118118372") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinreplied.disputecycle.tab_8118118377") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeinreplied.disputecycle.form_8118118382") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118118391") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeinreplied.finalrejectionreason.tab_8118118396") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeinreplied.finalrejectionreason.form_8118118401") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("arbitrationinformation_8118118408") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeinreplied.arbitrationinformation.tab_8118118413") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeinreplied.arbitrationinformation.form_8118118418") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("workflowstatus_8118118428") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinreplied.workflowstatus.tab_8118118433") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeinreplied.workflowstatus.form_8118118438") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118118448") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeinreplied.attachments.tab_8118118453") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputeinreplied.comments.tab_8118118458") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputeinreplied.changehistory.tab_8118118463") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputedtlsinrpl.view_8118118478") {
        en("Replied Dispute Inquires")
        ar("Replied Dispute Inquires")
        other("fr", "Demandes sur les Litiges Répondus")
    }
    label("id_8118118486") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118118496") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118118501") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118118491") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118118507") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118118512") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118118517") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "pièces jointes pour la question")
    }
    label("responsedate_8118118522") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de réponse")
    }
    label("answer_8118118527") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118118532") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "pièces jointes pour la réponse")
    }
    label("answerapproved_8118118537") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118118542") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118118547") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118118552") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118118557") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118118562") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118118572") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118118577") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118118582") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118118587") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118118592") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118118597") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118118602") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118118607") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118118614") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsinrpl.requestedinformation.tab_8118118619") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsinrpl.requestedinformation.form_8118118624") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118118631") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsinrpl.response.tab_8118118636") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsinrpl.response.form_8118118641") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118118648") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsinrpl.workflowstatus.tab_8118118653") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsinrpl.workflowstatus.form_8118118658") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118118668") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsinrpl.attachments.tab_8118118673") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputedtlsinrpl.comments.tab_8118118678") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputedtlsinrpl.changehistory.tab_8118118683") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118118688") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsinrpl.requestedinformation.tab_8118118693") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsinrpl.requestedinformation.form_8118118698") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118118705") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsinrpl.response.tab_8118118710") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsinrpl.response.form_8118118715") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118118722") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsinrpl.workflowstatus.tab_8118118727") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsinrpl.workflowstatus.form_8118118732") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118118742") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsinrpl.attachments.tab_8118118747") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputedtlsinrpl.comments.tab_8118118752") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputedtlsinrpl.changehistory.tab_8118118757") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputeoutreplied.view_8118118767") {
        en("Replied Disputes")
        ar("Replied Disputes")
        other("fr", "Litiges Répondus")
    }
    label("id_8118118788") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refnumber_8118118793") {
        en("Banks Reference Number")
        ar("Banks Reference Number")
        other("fr", "Numéro de Référence des Participants")
    }
    label("paymentsystem.id_8118118804") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118118809") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("paymentsystem.name_8118118814") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118118819") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118118798") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("disputerefrenceid_8118118824") {
        en("Dispute Reference ID")
        ar("Dispute Reference ID")
        other("fr", "ID de référence du litige")
    }
    label("createdate_8118118829") {
        en("Create Date")
        ar("Create Date")
        other("fr", "Date de Création")
    }
    label("slavalidationdate_8118118834") {
        en("SLA Validation Date")
        ar("SLA Validation Date")
        other("fr", "SLA de Date de validation")
    }
    label("iipsid_8118118839") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("achid_8118118844") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("senderbank.code_8118118859") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.codenamepair_8118118869") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118118849") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.code_8118118885") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.codenamepair_8118118895") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118118875") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("debtorbankbic_8118118901") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Débiteur")
    }
    label("debtorbankname_8118118906") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("creditorbankbic_8118118911") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant Créancier")
    }
    label("creditorbankname_8118118916") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("paymentsessionid_8118118921") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("paymentsessiondate_8118118926") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("paymentpostingdate_8118118931") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("paymentsettlementdate_8118118936") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date du Règlement du Paiement")
    }
    label("paymentstatus.id_8118118946") {
        en("paymentStatus.id")
        ar("paymentStatus.id")
        other("fr", "PAYSATSATUS.ID")
    }
    label("paymentstatus.code_8118118951") {
        en("paymentStatus.code")
        ar("paymentStatus.code")
        other("fr", "PAYSATSATUS.CODE")
    }
    label("paymentstatus.name_8118118956") {
        en("paymentStatus.name")
        ar("paymentStatus.name")
        other("fr", "PAYSATSATUS.NAME")
    }
    label("paymentstatus.codenamepair_8118118961") {
        en("paymentStatus.codeNamePair")
        ar("paymentStatus.codeNamePair")
        other("fr", "PAYSATSATUS.CODENAMEPAIR")
    }
    label("paymentstatus_8118118941") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("paymentpurpose_8118118967") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du paiement")
    }
    label("paymencategory_8118118972") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("paymenturgency.id_8118118982") {
        en("paymentUrgency.id")
        ar("paymentUrgency.id")
        other("fr", "Payment de paiement.ID")
    }
    label("paymenturgency.code_8118118987") {
        en("paymentUrgency.code")
        ar("paymentUrgency.code")
        other("fr", "Code du Paiement")
    }
    label("paymenturgency.name_8118118992") {
        en("paymentUrgency.name")
        ar("paymentUrgency.name")
        other("fr", "Payment de paiement.nom")
    }
    label("paymenturgency.codenamepair_8118118997") {
        en("paymentUrgency.codeNamePair")
        ar("paymentUrgency.codeNamePair")
        other("fr", "Payment de paiement.CODENAMEPAIR")
    }
    label("paymenturgency_8118118977") {
        en("Payment Urgency")
        ar("Payment Urgency")
        other("fr", "Urgence du Paiement")
    }
    label("currency.stringisocode_8118119018") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118119003") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("paymentamount_8118119023") {
        en("Payment Amount")
        ar("Payment Amount")
        other("fr", "Montant du paiement")
    }
    label("debtorname_8118119028") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du débiteur")
    }
    label("debtoriban_8118119033") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("debtornationalid_8118119038") {
        en("Debtor National ID")
        ar("Debtor National ID")
        other("fr", "Numéro National d'ID du Débiteur")
    }
    label("debtoraddress_8118119043") {
        en("Debtor Address")
        ar("Debtor Address")
        other("fr", "Adresse du débiteur")
    }
    label("beneficiaryname_8118119048") {
        en("Beneficiary Name")
        ar("Beneficiary Name")
        other("fr", "Nom du bénéficiaire")
    }
    label("beneficiaryiban_8118119053") {
        en("Beneficiary IBAN")
        ar("Beneficiary IBAN")
        other("fr", "IBAN du Bénéficiaire")
    }
    label("beneficiarynationalid_8118119058") {
        en("Beneficiary National ID")
        ar("Beneficiary National ID")
        other("fr", "Numéro National d'ID du Bénéficiaire")
    }
    label("beneficiaryaddress_8118119063") {
        en("Beneficiary Address")
        ar("Beneficiary Address")
        other("fr", "Adresse du bénéficiaire")
    }
    label("mpcode_8118119068") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du paiement")
    }
    label("mpname_8118119073") {
        en("Message Type")
        ar("Message Type")
        other("fr", "Type de message")
    }
    label("paycurrency_8118119083") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("mppayamount_8118119088") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("setlment_8118119093") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date du Règlement")
    }
    label("mpsenderbnkcd_8118119103") {
        en("Instructing Participant (Initiator Bank Code)")
        ar("Instructing Participant (Initiator Bank Code)")
        other("fr", "Participant Emetteur (Code banque de l'Initiateur)")
    }
    label("sendername_8118119108") {
        en("Instructing Participant (Initiator Bank Name)")
        ar("Instructing Participant (Initiator Bank Name)")
        other("fr", "Participant Emetteur (Nom de l'Initiateur")
    }
    label("mpsenderbrnch_8118119113") {
        en("Instructing Branch (Initiator Branch)")
        ar("Instructing Branch (Initiator Branch)")
        other("fr", "Agence Instructrice (Agence Initiatrice)")
    }
    label("mpreceiverbnkcd_8118119118") {
        en("Instructed Participant (Receiving Bank Code)")
        ar("Instructed Participant (Receiving Bank Code)")
        other("fr", "Participant Instruit (Code banque du Destinataire")
    }
    label("mpreceivername_8118119123") {
        en("Instructed Participant (Receiving Bank Name)")
        ar("Instructed Participant (Receiving Bank Name)")
        other("fr", "Participant Instruit (Nom du Participant Destinataire")
    }
    label("mpreceiverbrnch_8118119128") {
        en("Instructed Branch (Receiving Branch)")
        ar("Instructed Branch (Receiving Branch)")
        other("fr", "Agence instruite (Agence Destinataire)")
    }
    label("mpstate_8118119133") {
        en("State (Status)")
        ar("State (Status)")
        other("fr", "État (statut")
    }
    label("mpsessionno_8118119138") {
        en("Session Info")
        ar("Session Info")
        other("fr", "Informations sur la session")
    }
    label("achcode_8118119143") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la transaction")
    }
    label("achname_8118119148") {
        en("Payment Reference ID (Batch ID)")
        ar("Payment Reference ID (Batch ID)")
        other("fr", "ID de Référence du paiement (ID du Lot)")
    }
    label("achendtoend_8118119163") {
        en("End To End ID")
        ar("End To End ID")
        other("fr", "ID de Bout en Bout")
    }
    label("achinstructionid_8118119168") {
        en("Instruction ID")
        ar("Instruction ID")
        other("fr", "ID de l'instruction")
    }
    label("achmandateid_8118119173") {
        en("Mandate ID")
        ar("Mandate ID")
        other("fr", "ID du Mandat")
    }
    label("achdebtoraccount_8118119178") {
        en("Debtor Account")
        ar("Debtor Account")
        other("fr", "Compte du Débiteur")
    }
    label("achachdebtoriban_8118119183") {
        en("Debtor IBAN")
        ar("Debtor IBAN")
        other("fr", "IBAN du Débiteur")
    }
    label("achdebtorname_8118119188") {
        en("Debtor Name")
        ar("Debtor Name")
        other("fr", "Nom du débiteur")
    }
    label("achcreditoraccount_8118119193") {
        en("Creditor Account")
        ar("Creditor Account")
        other("fr", "Compte du Créancier")
    }
    label("achcreditoriban_8118119198") {
        en("Creditor IBAN")
        ar("Creditor IBAN")
        other("fr", "IBAN du Créancier")
    }
    label("achcreditorname_8118119203") {
        en("Creditor Name")
        ar("Creditor Name")
        other("fr", "Nom du créancier")
    }
    label("achamount_8118119208") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("achsessionid_8118119213") {
        en("Session ID")
        ar("Session ID")
        other("fr", "ID de session")
    }
    label("achsettelmentdate_8118119218") {
        en("Settlement Date")
        ar("Settlement Date")
        other("fr", "Date du Règlement")
    }
    label("achtransactionpurpose_8118119223") {
        en("Transaction Purpose")
        ar("Transaction Purpose")
        other("fr", "Objectif de la Transaction")
    }
    label("achsenderagent_8118119228") {
        en("Instructing Agent")
        ar("Instructing Agent")
        other("fr", "Agent Instructeur")
    }
    label("achrecieveragent_8118119233") {
        en("Instructed Agent")
        ar("Instructed Agent")
        other("fr", "Agent instruit")
    }
    label("achsenderbranch_8118119238") {
        en("Instructing Branch")
        ar("Instructing Branch")
        other("fr", "Agence Instructrice")
    }
    label("achrecieverbranch_8118119243") {
        en("Instructed Branch")
        ar("Instructed Branch")
        other("fr", "Agence instruite")
    }
    label("achadditionalinfo_8118119248") {
        en("Additional Info")
        ar("Additional Info")
        other("fr", "Information additionnelle")
    }
    label("paymentrefid_8118119253") {
        en("Payment Reference")
        ar("Payment Reference")
        other("fr", "Référence du paiement")
    }
    label("mainreason.id_8118119263") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118119268") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118119273") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118119278") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118119258") {
        en("Main Reason")
        ar("Main Reason")
        other("fr", "Motif Principal")
    }
    label("disputepaymenttype_8118119284") {
        en("Dispute Payment Type")
        ar("Dispute Payment Type")
        other("fr", "Type de Paiement lié au Litige")
    }
    label("disputeoutreplied.cr_8118119289") {
        en("CR")
        other("fr", "Croisement")
    }
    label("disputeoutreplied.db_8118119293") {
        en("DB")
        other("fr", "Db")
    }
    label("paymentinitiatorbic_8118119297") {
        en("Payment Initiator BIC")
        ar("Payment Initiator BIC")
        other("fr", "Code BIC de l'Initiateur du Paiement")
    }
    label("mobilenoserviceid_8118119302") {
        en("Mobile Number Service ID")
        ar("Mobile Number Service ID")
        other("fr", "ID de service du numéro de mobile")
    }
    label("merchantbillerref_8118119307") {
        en("Merchant Biller Ref")
        ar("Merchant Biller Ref")
        other("fr", "Ref du Marchand")
    }
    label("merchantidbillerid_8118119312") {
        en("Merchant ID Biller ID")
        ar("Merchant ID Biller ID")
        other("fr", "ID du marchand ID du Facturier")
    }
    label("actualamount_8118119317") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("casedescription_8118119322") {
        en("Case Description")
        ar("Case Description")
        other("fr", "Description du cas")
    }
    label("urgencysla.id_8118119332") {
        en("urgencySLA.id")
        ar("urgencySLA.id")
        other("fr", "urgencesla.id")
    }
    label("urgencysla.code_8118119337") {
        en("urgencySLA.code")
        ar("urgencySLA.code")
        other("fr", "Urgencysla.code")
    }
    label("urgencysla.name_8118119342") {
        en("urgencySLA.name")
        ar("urgencySLA.name")
        other("fr", "Urgencysla.name")
    }
    label("urgencysla.codenamepair_8118119347") {
        en("urgencySLA.codeNamePair")
        ar("urgencySLA.codeNamePair")
        other("fr", "urgencesla.codenamepair")
    }
    label("urgencysla_8118119327") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("urgencysla.maxdeadline_8118119353") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("fees.id_8118119363") {
        en("fees.id")
        ar("fees.id")
        other("fr", "frais.")
    }
    label("fees.code_8118119368") {
        en("fees.code")
        ar("fees.code")
        other("fr", " Code de frais")
    }
    label("fees.name_8118119373") {
        en("fees.name")
        ar("fees.name")
        other("fr", "Frais.")
    }
    label("fees.codenamepair_8118119378") {
        en("fees.codeNamePair")
        ar("fees.codeNamePair")
        other("fr", "frais.")
    }
    label("fees_8118119358") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais associés au litige")
    }
    label("checkercomments_8118119384") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("senderbank.id_8118119389") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.name_8118119394") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("receiverbank.id_8118119399") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("receiverbank.name_8118119404") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("statusid.code_8118119409") {
        en("statusId.code")
        ar("statusId.code")
        other("fr", "statulid.code")
    }
    label("question_8118119414") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118119419") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "pièces jointes pour la question")
    }
    label("answer_8118119424") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118119429") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "pièces jointes pour la réponse")
    }
    label("serial_8118119434") {
        en("Serial")
        ar("Serial")
        other("fr", "En série")
    }
    label("represnted_8118119439") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("moreinfocount_8118119444") {
        en("More Info Count")
        ar("More Info Count")
        other("fr", "Nombre de demandes d'information")
    }
    label("isrepresented_8118119449") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Est représenté")
    }
    label("rejectionreason.id_8118119459") {
        en("rejectionReason.id")
        ar("rejectionReason.id")
        other("fr", "rejectionReason.id")
    }
    label("rejectionreason.code_8118119464") {
        en("rejectionReason.code")
        ar("rejectionReason.code")
        other("fr", "rejectionReason.code")
    }
    label("rejectionreason.name_8118119469") {
        en("rejectionReason.name")
        ar("rejectionReason.name")
        other("fr", "rejectionReason.name")
    }
    label("rejectionreason.codenamepair_8118119474") {
        en("rejectionReason.codeNamePair")
        ar("rejectionReason.codeNamePair")
        other("fr", "rejectionReason.codenamepair")
    }
    label("rejectionreason_8118119454") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("rejectionreason.description_8118119480") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalcomments_8118119485") {
        en("Rejection Comments")
        ar("Rejection Comments")
        other("fr", "Commentaires liés au rejet")
    }
    label("escalationreason.id_8118119495") {
        en("escalationReason.id")
        ar("escalationReason.id")
        other("fr", "EscaladeReason.id")
    }
    label("escalationreason.code_8118119500") {
        en("escalationReason.code")
        ar("escalationReason.code")
        other("fr", "EscaladeReason.code")
    }
    label("escalationreason.name_8118119505") {
        en("escalationReason.name")
        ar("escalationReason.name")
        other("fr", "EscalationReason.name")
    }
    label("escalationreason.codenamepair_8118119510") {
        en("escalationReason.codeNamePair")
        ar("escalationReason.codeNamePair")
        other("fr", "EscaladeReason.codenamepair")
    }
    label("escalationreason_8118119490") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("escalationreason.description_8118119516") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("isescalated_8118119521") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Est arbitré")
    }
    label("escalationcharges.id_8118119531") {
        en("escalationCharges.id")
        ar("escalationCharges.id")
        other("fr", "EscalationCharges.id")
    }
    label("escalationcharges.code_8118119536") {
        en("escalationCharges.code")
        ar("escalationCharges.code")
        other("fr", "EscalationCharges.code")
    }
    label("escalationcharges.name_8118119541") {
        en("escalationCharges.name")
        ar("escalationCharges.name")
        other("fr", "EscalationCharges.Name")
    }
    label("escalationcharges.codenamepair_8118119546") {
        en("escalationCharges.codeNamePair")
        ar("escalationCharges.codeNamePair")
        other("fr", "EscalationCharges.Codenamepair")
    }
    label("escalationcharges_8118119526") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("escalationcharges.amount_8118119552") {
        en("Charges Amount")
        ar("Charges Amount")
        other("fr", "Montant des frais")
    }
    label("penalizedparty_8118119557") {
        en("Penalized Party")
        ar("Penalized Party")
        other("fr", "Partie pénalisée")
    }
    label("disputeoutreplied.notspecified_8118119562") {
        en("Not Specified")
        other("fr", "Non spécifié")
    }
    label("disputeoutreplied.initiatorbank_8118119566") {
        en("Initiator Bank")
        other("fr", "Participant Initiateur")
    }
    label("disputeoutreplied.recieverbank_8118119570") {
        en("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("arbitrationcomments_8118119574") {
        en("Arbitration Comments")
        ar("Arbitration Comments")
        other("fr", "Commentaires d'arbitrage")
    }
    label("isgoodfaith_8118119579") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("settled_8118119584") {
        en("Settled")
        ar("Settled")
        other("fr", "Réglé")
    }
    label("disputeoutreplied.notsettled_8118119589") {
        en("Not Settled")
        other("fr", "Non-résolu")
    }
    label("disputeoutreplied.settled_8118119593") {
        en("Settled")
        other("fr", "Réglé")
    }
    label("disputeoutreplied.manuallysettled_8118119597") {
        en("Manually Settled")
        other("fr", "Réglé manuellement")
    }
    label("finalstatustime_8118119601") {
        en("Final Status Time")
        ar("Final Status Time")
        other("fr", "Temps du statut final")
    }
    label("direction_8118119606") {
        en("Dispute Direction")
        ar("Dispute Direction")
        other("fr", "Sens du litige")
    }
    label("currency.id_8118119611") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118119616") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118119621") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118119626") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118119631") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118119636") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118119646") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118119651") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118119656") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118119661") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118119666") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118119671") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118119676") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118119681") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("disputecase_8118119688") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeoutreplied.disputecase.tab_8118119693") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeoutreplied.disputecase.form_8118119698") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118119706") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeoutreplied.paymentinformation.tab_8118119711") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeoutreplied.paymentinformation.form_8118119716") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("iipsinformation_8118119744") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeoutreplied.iipsinformation.tab_8118119749") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeoutreplied.iipsinformation.form_8118119754") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118119772") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeoutreplied.achinformation.tab_8118119777") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeoutreplied.achinformation.form_8118119782") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118119809") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeoutreplied.disputeinformation.tab_8118119814") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeoutreplied.disputeinformation.form_8118119819") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118119829") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeoutreplied.rejectedresons.tab_8118119834") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeoutreplied.rejectedresons.form_8118119839") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118119845") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeoutreplied.disputecycle.tab_8118119850") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeoutreplied.disputecycle.form_8118119855") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118119864") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeoutreplied.finalrejectionreason.tab_8118119869") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeoutreplied.finalrejectionreason.form_8118119874") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("arbitrationinformation_8118119881") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeoutreplied.arbitrationinformation.tab_8118119886") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeoutreplied.arbitrationinformation.form_8118119891") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("workflowstatus_8118119901") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeoutreplied.workflowstatus.tab_8118119906") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeoutreplied.workflowstatus.form_8118119911") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118119921") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeoutreplied.attachments.tab_8118119926") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputeoutreplied.comments.tab_8118119931") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputeoutreplied.changehistory.tab_8118119936") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("details_8118120194") {
        en("Details")
        ar("التفاصيل")
        other("fr", "Détails")
    }
    label("disputeoutreplied.disputehistory.tab_8118120199") {
        en("Dispute History")
        ar("Dispute History")
        other("fr", "Historique des Litiges")
    }
    label("disputeoutreplied.replieddisputeinquires.tab_8118120488") {
        en("Replied Dispute Inquires")
        ar("Replied Dispute Inquires")
        other("fr", "Demandes sur les Litiges Répondus")
    }
    label("disputecase_8118119941") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeoutreplied.disputecase.tab_8118119946") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("disputeoutreplied.disputecase.form_8118119951") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("paymentinformation_8118119959") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeoutreplied.paymentinformation.tab_8118119964") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("disputeoutreplied.paymentinformation.form_8118119969") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("iipsinformation_8118119997") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeoutreplied.iipsinformation.tab_8118120002") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("disputeoutreplied.iipsinformation.form_8118120007") {
        en("IIPS Information")
        ar("IIPS Information")
        other("fr", "Informations IIPS")
    }
    label("achinformation_8118120025") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeoutreplied.achinformation.tab_8118120030") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeoutreplied.achinformation.form_8118120035") {
        en("ACH Information")
        ar("ACH Information")
        other("fr", "Informations ACH")
    }
    label("disputeinformation_8118120062") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeoutreplied.disputeinformation.tab_8118120067") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputeoutreplied.disputeinformation.form_8118120072") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("rejectedresons_8118120082") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeoutreplied.rejectedresons.tab_8118120087") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputeoutreplied.rejectedresons.form_8118120092") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("disputecycle_8118120098") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeoutreplied.disputecycle.tab_8118120103") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("disputeoutreplied.disputecycle.form_8118120108") {
        en("Dispute Cycle")
        ar("Dispute Cycle")
        other("fr", "Cycle du litige")
    }
    label("finalrejectionreason_8118120117") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeoutreplied.finalrejectionreason.tab_8118120122") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputeoutreplied.finalrejectionreason.form_8118120127") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("arbitrationinformation_8118120134") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeoutreplied.arbitrationinformation.tab_8118120139") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeoutreplied.arbitrationinformation.form_8118120144") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("workflowstatus_8118120154") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeoutreplied.workflowstatus.tab_8118120159") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputeoutreplied.workflowstatus.form_8118120164") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118120174") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputeoutreplied.attachments.tab_8118120179") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputeoutreplied.comments.tab_8118120184") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputeoutreplied.changehistory.tab_8118120189") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputedtlsoutrpl.view_8118120204") {
        en("Replied Dispute Inquires")
        ar("Replied Dispute Inquires")
        other("fr", "Demandes sur les Litiges Répondus")
    }
    label("id_8118120212") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118120222") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute.refnumber_8118120227") {
        en("refDispute.refNumber")
        ar("refDispute.refNumber")
        other("fr", "refDispute.refnumber")
    }
    label("refdispute_8118120217") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("requestdate_8118120233") {
        en("Request Date")
        ar("Request Date")
        other("fr", "Date de la demande")
    }
    label("question_8118120238") {
        en("Question")
        ar("Question")
        other("fr", "Question")
    }
    label("questionatt_8118120243") {
        en("Question Attachments")
        ar("Question Attachments")
        other("fr", "pièces jointes pour la question")
    }
    label("responsedate_8118120248") {
        en("Response Date")
        ar("Response Date")
        other("fr", "Date de réponse")
    }
    label("answer_8118120253") {
        en("Answer")
        ar("Answer")
        other("fr", "Répondre")
    }
    label("answeratt_8118120258") {
        en("Answer Attachments")
        ar("Answer Attachments")
        other("fr", "pièces jointes pour la réponse")
    }
    label("answerapproved_8118120263") {
        en("Answer Approved")
        ar("Answer Approved")
        other("fr", "Réponse approuvée")
    }
    label("questionapproved_8118120268") {
        en("Question Approved")
        ar("Question Approved")
        other("fr", "Question approuvée")
    }
    label("statusid_8118120273") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118120278") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118120283") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118120288") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118120298") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118120303") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118120308") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118120313") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118120318") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118120323") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118120328") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118120333") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("requestedinformation_8118120340") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsoutrpl.requestedinformation.tab_8118120345") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsoutrpl.requestedinformation.form_8118120350") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118120357") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsoutrpl.response.tab_8118120362") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsoutrpl.response.form_8118120367") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118120374") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsoutrpl.workflowstatus.tab_8118120379") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsoutrpl.workflowstatus.form_8118120384") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118120394") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsoutrpl.attachments.tab_8118120399") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputedtlsoutrpl.comments.tab_8118120404") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputedtlsoutrpl.changehistory.tab_8118120409") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("requestedinformation_8118120414") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsoutrpl.requestedinformation.tab_8118120419") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("disputedtlsoutrpl.requestedinformation.form_8118120424") {
        en("Requested Information")
        ar("Requested Information")
        other("fr", "Information demandée")
    }
    label("response_8118120431") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsoutrpl.response.tab_8118120436") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("disputedtlsoutrpl.response.form_8118120441") {
        en("Response")
        ar("Response")
        other("fr", "Réponse")
    }
    label("workflowstatus_8118120448") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsoutrpl.workflowstatus.tab_8118120453") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputedtlsoutrpl.workflowstatus.form_8118120458") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118120468") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputedtlsoutrpl.attachments.tab_8118120473") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputedtlsoutrpl.comments.tab_8118120478") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputedtlsoutrpl.changehistory.tab_8118120483") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputecancelation.view_8118120493") {
        en("Dispute Cancellation")
        ar("Dispute Cancellation")
        other("fr", "Annulation du Litige")
    }
    label("id_8118120513") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("refdispute.id_8118120524") {
        en("refDispute.id")
        ar("refDispute.id")
        other("fr", "RefDispute.id")
    }
    label("refdispute_8118120518") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("refdispute.disputerefrenceid_8118120534") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("refdispute.createdate_8118120539") {
        en("createDate")
        ar("createDate")
        other("fr", "Date de Création")
    }
    label("refdispute.paymentsystem.id_8118120554") {
        en("refDispute.paymentSystem.id")
        ar("refDispute.paymentSystem.id")
        other("fr", "refDispute.paymentsystem.id")
    }
    label("refdispute.paymentsystem.code_8118120559") {
        en("refDispute.paymentSystem.code")
        ar("refDispute.paymentSystem.code")
        other("fr", "refDispute.paymentsystem.code")
    }
    label("refdispute.paymentsystem.name_8118120564") {
        en("refDispute.paymentSystem.name")
        ar("refDispute.paymentSystem.name")
        other("fr", "refDispute.paymentsystem.name")
    }
    label("refdispute.paymentsystem.codenamepair_8118120569") {
        en("refDispute.paymentSystem.codeNamePair")
        ar("refDispute.paymentSystem.codeNamePair")
        other("fr", "refDispute.paymentsystem.codenamepair")
    }
    label("refdispute.paymentsystem_8118120549") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("refdispute.paymentrefid_8118120575") {
        en("Payment Reference ID")
        ar("Payment Reference ID")
        other("fr", "ID de Référence du paiement")
    }
    label("refdispute.senderbank.id_8118120585") {
        en("refDispute.senderBank.id")
        ar("refDispute.senderBank.id")
        other("fr", "RefDispute.SenderBank.id")
    }
    label("refdispute.senderbank.code_8118120590") {
        en("refDispute.senderBank.code")
        ar("refDispute.senderBank.code")
        other("fr", "RefDispute.SenderBank.code")
    }
    label("refdispute.senderbank.name_8118120595") {
        en("refDispute.senderBank.name")
        ar("refDispute.senderBank.name")
        other("fr", "RefDispute.Senderbank.name")
    }
    label("refdispute.senderbank.codenamepair_8118120600") {
        en("refDispute.senderBank.codeNamePair")
        ar("refDispute.senderBank.codeNamePair")
        other("fr", "RefDispute.Senderbank.Codenamepair")
    }
    label("refdispute.senderbank_8118120580") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("refdispute.receiverbank.id_8118120611") {
        en("refDispute.receiverBank.id")
        ar("refDispute.receiverBank.id")
        other("fr", "refDispute.receiverbank.id")
    }
    label("refdispute.receiverbank.code_8118120616") {
        en("refDispute.receiverBank.code")
        ar("refDispute.receiverBank.code")
        other("fr", "refDispute.receiverbank.code")
    }
    label("refdispute.receiverbank.name_8118120621") {
        en("refDispute.receiverBank.name")
        ar("refDispute.receiverBank.name")
        other("fr", "refDispute.receiverbank.name")
    }
    label("refdispute.receiverbank.codenamepair_8118120626") {
        en("refDispute.receiverBank.codeNamePair")
        ar("refDispute.receiverBank.codeNamePair")
        other("fr", "refDispute.receiverbank.codenamepair")
    }
    label("refdispute.receiverbank_8118120606") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("refdispute.debtorbankbic_8118120632") {
        en("Debtor Bank BIC")
        ar("Debtor Bank BIC")
        other("fr", "Code BIC du Débiteur")
    }
    label("refdispute.debtorbankname_8118120637") {
        en("Debtor Bank Name")
        ar("Debtor Bank Name")
        other("fr", "Nom du Participant du Débiteur")
    }
    label("refdispute.creditorbankbic_8118120642") {
        en("Creditor Bank BIC")
        ar("Creditor Bank BIC")
        other("fr", "Code BIC du Participant Créancier")
    }
    label("refdispute.creditorbankname_8118120647") {
        en("Creditor Bank Name")
        ar("Creditor Bank Name")
        other("fr", "Nom du Participant du Créancier")
    }
    label("refdispute.paymentsessionid_8118120652") {
        en("Payment Session Number")
        ar("Payment Session Number")
        other("fr", "Numéro de Session du Paiement")
    }
    label("refdispute.paymentsessiondate_8118120657") {
        en("Payment Session Date")
        ar("Payment Session Date")
        other("fr", "Date de Session du Paiement")
    }
    label("refdispute.paymentpostingdate_8118120662") {
        en("Payment Submission Date")
        ar("Payment Submission Date")
        other("fr", "Date de Soumission du Paiement")
    }
    label("refdispute.paymentsettlementdate_8118120667") {
        en("Payment Settlement Date")
        ar("Payment Settlement Date")
        other("fr", "Date du Règlement du Paiement")
    }
    label("refdispute.paymentstatus.id_8118120677") {
        en("refDispute.paymentStatus.id")
        ar("refDispute.paymentStatus.id")
        other("fr", "refDispute.paymentStatus.id")
    }
    label("refdispute.paymentstatus.code_8118120682") {
        en("refDispute.paymentStatus.code")
        ar("refDispute.paymentStatus.code")
        other("fr", "refDispute.paymentstatus.code")
    }
    label("refdispute.paymentstatus.name_8118120687") {
        en("refDispute.paymentStatus.name")
        ar("refDispute.paymentStatus.name")
        other("fr", "RefDispute.PaymentStatus.name")
    }
    label("refdispute.paymentstatus.codenamepair_8118120692") {
        en("refDispute.paymentStatus.codeNamePair")
        ar("refDispute.paymentStatus.codeNamePair")
        other("fr", "refDispute.paymentstatus.codenamepair")
    }
    label("refdispute.paymentstatus_8118120672") {
        en("Payment Status")
        ar("Payment Status")
        other("fr", "Statut du Paiement")
    }
    label("refdispute.paymentpurpose_8118120698") {
        en("Payment Purpose")
        ar("Payment Purpose")
        other("fr", "Motif du paiement")
    }
    label("refdispute.paymencategory_8118120703") {
        en("Payment Category")
        ar("Payment Category")
        other("fr", "Catégorie de Paiement")
    }
    label("cancelreason.id_8118120713") {
        en("cancelReason.id")
        ar("cancelReason.id")
        other("fr", "CancelReason.id")
    }
    label("cancelreason.code_8118120718") {
        en("cancelReason.code")
        ar("cancelReason.code")
        other("fr", "Coderason.code")
    }
    label("cancelreason.name_8118120723") {
        en("cancelReason.name")
        ar("cancelReason.name")
        other("fr", "CancelReason.name")
    }
    label("cancelreason.codenamepair_8118120728") {
        en("cancelReason.codeNamePair")
        ar("cancelReason.codeNamePair")
        other("fr", "CancelReason.codenamepair")
    }
    label("cancelreason_8118120708") {
        en("Cancel Reason")
        ar("Cancel Reason")
        other("fr", "Motif d'Annulation")
    }
    label("statusid_8118120734") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118120739") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118120744") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118120749") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118120759") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118120764") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118120769") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118120774") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118120779") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118120784") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118120789") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118120794") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("dispute_8118120801") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("disputecancelation.dispute.tab_8118120806") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("disputecancelation.dispute.form_8118120811") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("workflowstatus_8118120818") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputecancelation.workflowstatus.tab_8118120823") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputecancelation.workflowstatus.form_8118120828") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118120838") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputecancelation.changehistory.tab_8118120843") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("dispute_8118120848") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("disputecancelation.dispute.tab_8118120853") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("disputecancelation.dispute.form_8118120858") {
        en("Dispute")
        ar("Dispute")
        other("fr", "Litige")
    }
    label("workflowstatus_8118120865") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputecancelation.workflowstatus.tab_8118120870") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputecancelation.workflowstatus.form_8118120875") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118120885") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputecancelation.changehistory.tab_8118120890") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("disputefinalrejection.view_8118120895") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("finalrejectionreason_8118120903") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputefinalrejection.finalrejectionreason.tab_8118120908") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputefinalrejection.finalrejectionreason.form_8118120913") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("rejectattachment_8118120920") {
        en("Reject Attachment")
        ar("Reject Attachment")
        other("fr", "Rejeter la pièce jointe")
    }
    label("disputefinalrejection.rejectattachment.tab_8118120925") {
        en("Reject Attachment")
        ar("Reject Attachment")
        other("fr", "Rejeter la pièce jointe")
    }
    label("disputefinalrejection.rejectattachment.form_8118120930") {
        en("Reject Attachment")
        ar("Reject Attachment")
        other("fr", "Rejeter la pièce jointe")
    }
    label("finalrejectionreason_8118120935") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputefinalrejection.finalrejectionreason.tab_8118120940") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("disputefinalrejection.finalrejectionreason.form_8118120945") {
        en("Final Rejection Reason")
        ar("Final Rejection Reason")
        other("fr", "Motif du rejet final")
    }
    label("rejectattachment_8118120952") {
        en("Reject Attachment")
        ar("Reject Attachment")
        other("fr", "Rejeter la pièce jointe")
    }
    label("disputecharges.view_8118120957") {
        en("Dispute Arbitration Charges")
        ar("Dispute Arbitration Charges")
        other("fr", "Frais d'arbitrage des Litiges")
    }
    label("arbitrationcharges_8118120965") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("disputecharges.arbitrationcharges.tab_8118120970") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("disputecharges.arbitrationcharges.form_8118120975") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("arbitrationcharges_8118120981") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("disputecharges.arbitrationcharges.tab_8118120986") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("disputecharges.arbitrationcharges.form_8118120991") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("disputeescalation.view_8118120997") {
        en("Arbitration Reasons")
        ar("Arbitration Reasons")
        other("fr", "Raisons d'arbitrage")
    }
    label("arbitrationreason_8118121005") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("disputeescalation.arbitrationreason.tab_8118121010") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("disputeescalation.arbitrationreason.form_8118121015") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("arbitrationinformation_8118121021") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeescalation.arbitrationinformation.tab_8118121026") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeescalation.arbitrationinformation.form_8118121031") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("arbitrationreason_8118121037") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("disputeescalation.arbitrationreason.tab_8118121042") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("disputeescalation.arbitrationreason.form_8118121047") {
        en("Arbitration Reason")
        ar("Arbitration Reason")
        other("fr", "Motif d'arbitrage")
    }
    label("arbitrationinformation_8118121053") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeescalation.arbitrationinformation.tab_8118121058") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputeescalation.arbitrationinformation.form_8118121063") {
        en("Arbitration Information")
        ar("Arbitration Information")
        other("fr", "Informations sur l'arbitrage")
    }
    label("disputerepresent.view_8118121069") {
        en("Modify Dispute Details")
        ar("Modify Dispute Details")
        other("fr", "Modifier les détails d'un litige")
    }
    label("disputedetails_8118121077") {
        en("Dispute Details")
        ar("Dispute Details")
        other("fr", "Détails du litige")
    }
    label("disputerepresent.disputedetails.tab_8118121082") {
        en("Dispute Details")
        ar("Dispute Details")
        other("fr", "Détails du litige")
    }
    label("disputerepresent.disputedetails.form_8118121087") {
        en("Dispute Details")
        ar("Dispute Details")
        other("fr", "Détails du litige")
    }
    label("disputeinformation_8118121093") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputerepresent.disputeinformation.tab_8118121098") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputerepresent.disputeinformation.form_8118121103") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("activity_8118121110") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputerepresent.attachments.tab_8118121115") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("disputedetails_8118121120") {
        en("Dispute Details")
        ar("Dispute Details")
        other("fr", "Détails du litige")
    }
    label("disputerepresent.disputedetails.tab_8118121125") {
        en("Dispute Details")
        ar("Dispute Details")
        other("fr", "Détails du litige")
    }
    label("disputerepresent.disputedetails.form_8118121130") {
        en("Dispute Details")
        ar("Dispute Details")
        other("fr", "Détails du litige")
    }
    label("disputeinformation_8118121136") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputerepresent.disputeinformation.tab_8118121141") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("disputerepresent.disputeinformation.form_8118121146") {
        en("Dispute Information")
        ar("Dispute Information")
        other("fr", "Informations sur le Litige")
    }
    label("activity_8118121153") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputerepresent.attachments.tab_8118121158") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("participants.view_8118121163") {
        en("Participants")
        ar("Participants")
        other("fr", "Participants")
    }
    label("aclconfigs.725264883") {
        en("ACL Configurations")
        ar("تكوينات ACL")
    }
    label("maxamount.236867754") {
        en("Max Amount")
        ar("المبلغ الأقصى")
    }
    label("users.2869601920") {
        en("Users")
        ar("المستخدمين")
    }
    label("users.4269601935") {
        en("users.id")
    }
    label("users.7669601345") {
        en("users.email")
    }
    label("participants.id.3959601345") {
        en("participants.id")
    }
    label("participants.codenamepair.2859601345") {
        en("participants.codeNamePair")
    }
    label("participants.code.1679601345") {
        en("participants.code")
    }
    label("participants.name.0659601345") {
        en("participants.name")
    }
    label("acl-config.existing.user.error") {
        en("One or more of the selected users already have an ACL configuration.")
    }
    label("acl-config.unprovided.amount.and.participant.error") {
        en("Max Amount or Participants is required.")
    }
    label("id_8118121180") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("particpiantid_8118121185") {
        en("Participant ID")
        ar("Participant ID")
        other("fr", "ID des participants")
    }
    label("code_8118121190") {
        en("BIC Code")
        ar("BIC Code")
        other("fr", "Code BIC")
    }
    label("fullnameeng_8118121195") {
        en("Full Name")
        ar("الاسم الكامل")
        other("fr", "Nom Complet")
    }
    label("name_8118121200") {
        en("Short Name")
        ar("الاسم القصير")
        other("fr", "Nom court")
    }
    label("email_8118121200") {
        en("Email")
        ar("الاسم القصير")
        other("fr", "E-mail")
    }
    label("fullnamear_8118121205") {
        en("Full Name Arabic")
        ar("Full Name Arabic")
        other("fr", "Nom complet")
    }
    label("shortnamear_8118121210") {
        en("Short Name Arabic")
        ar("Short Name Arabic")
        other("fr", "Nom court")
    }
    label("email_8118121215") {
        en("Email")
        ar("Email")
        other("fr", "E-mail")
    }
    label("category.id_8118121225") {
        en("category.id")
        ar("category.id")
        other("fr", "catégorie.id")
    }
    label("category.code_8118121230") {
        en("category.code")
        ar("category.code")
        other("fr", "catégorie.code")
    }
    label("category.name_8118121235") {
        en("category.name")
        ar("category.name")
        other("fr", "Nom de catégorie")
    }
    label("category.codenamepair_8118121240") {
        en("category.codeNamePair")
        ar("category.codeNamePair")
        other("fr", "catégorie.codenamepair")
    }
    label("category_8118121220") {
        en("Participant Category")
        ar("Participant Category")
        other("fr", "Catégorie des Participants")
    }
    label("setlmttype.id_8118121251") {
        en("setlmtType.id")
        ar("setlmtType.id")
        other("fr", "setlmttype.id")
    }
    label("setlmttype.code_8118121256") {
        en("setlmtType.code")
        ar("setlmtType.code")
        other("fr", "setlmttype.code")
    }
    label("setlmttype.name_8118121261") {
        en("setlmtType.name")
        ar("setlmtType.name")
        other("fr", "setlmttype.name")
    }
    label("setlmttype.codenamepair_8118121266") {
        en("setlmtType.codeNamePair")
        ar("setlmtType.codeNamePair")
        other("fr", "setlmttype.codenamepair")
    }
    label("setlmttype_8118121246") {
        en("Settlement Type")
        ar("Settlement Type")
        other("fr", "Type de règlement")
    }
    label("setlmtaccount_8118121272") {
        en("Participant Settlement account on RTGS (IBAN)")
        ar("Participant Settlement account on RTGS (IBAN)")
        other("fr", "Compte de règlement des participants au niveau du RTGS (IBAN")
    }
    label("cbdckey_8118121277") {
        en("Settlement CBDC Wallet Key")
        ar("Settlement CBDC Wallet Key")
        other("fr", "Clé de portefeuille CBDC de règlement")
    }
    label("paymentsystem.id_8118121287") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118121292") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem.name_8118121297") {
        en("paymentSystem.name")
        ar("paymentSystem.name")
        other("fr", "Paymentsystem.name")
    }
    label("paymentsystem.codenamepair_8118121302") {
        en("paymentSystem.codeNamePair")
        ar("paymentSystem.codeNamePair")
        other("fr", "Paymentsystem.codenamepair")
    }
    label("paymentsystem_8118121282") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("description_8118121308") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("hasadmin_8118121313") {
        en("Has Admin")
        ar("Has Admin")
        other("fr", "A un admin")
    }
    label("makeremail_8118121318") {
        en("Maker Email")
        ar("Maker Email")
        other("fr", "E-mail de l'Initiateur")
    }
    label("checkeremail_8118121323") {
        en("Checker Email")
        ar("Checker Email")
        other("fr", "E-mail du vérificateur")
    }
    label("checkercomments_8118121328") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("statusid_8118121333") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("management_status") {
        en("Management Status")
        ar("Management Status")
        other("fr", "Statut de Traitement")
    }
    label("dispute_status") {
        en("Dispute Status")
        ar("Dispute Status")
        other("fr", "Statut de litige")
    }
    label("statusid.id_8118121338") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118121343") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118121348") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("creationdate_8118121353") {
        en("Created on")
        ar("تاريخ الادخال")
         other("fr", "Date de Création")
    }
    label("reason_8118121353") {
        en("Reason")
        ar("تاريخ الادخال")
        other("fr", "Motif")
    }
    label("updatingdate_8118121358") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118121363") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118121368") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118121373") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118121378") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118121383") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118121388") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118121393") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118121401") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("participants.info.tab_8118121406") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("participants.info.form_8118121411") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("rejectedresons_8118121431") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("participants.rejectedresons.tab_8118121436") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("participants.rejectedresons.form_8118121441") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("workflowstatus_8118121447") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("participants.workflowstatus.tab_8118121452") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("participants.workflowstatus.form_8118121457") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118121467") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("participants.attachments.tab_8118121472") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("participants.comments.tab_8118121477") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("participants.changehistory.tab_8118121482") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118121487") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("participants.info.tab_8118121492") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("participants.info.form_8118121497") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("rejectedresons_8118121517") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("participants.rejectedresons.tab_8118121522") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("participants.rejectedresons.form_8118121527") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("workflowstatus_8118121533") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("participants.workflowstatus.tab_8118121538") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("participants.workflowstatus.form_8118121543") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118121553") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("participants.attachments.tab_8118121558") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("participants.comments.tab_8118121563") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("participants.changehistory.tab_8118121568") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("bankrejection.view_8118121573") {
        en("Checker Comments")
        ar("Checker Comments")
        other("fr", "Commentaires")
    }
    label("rejectedresons_8118121581") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("bankrejection.rejectedresons.tab_8118121586") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("bankrejection.rejectedresons.form_8118121591") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("rejectedresons_8118121597") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("bankrejection.rejectedresons.tab_8118121602") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("bankrejection.rejectedresons.form_8118121607") {
        en("Rejected Resons")
        ar("Rejected Resons")
        other("fr", "Motifs de  Rejet")
    }
    label("confpaymentsystem.view_8118121613") {
        en("Payment Systems")
        ar("Payment Systems")
        other("fr", "Systèmes de paiement")
    }
    label("id_8118121630") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("code_8118121635") {
        en("Short Name (English)")
        ar("Short Name (English)")
        other("fr", "Nom court (English)")
    }
    label("currency.stringisocode_8118121655") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118121640") {
        en("Supported Currency")
        ar("Supported Currency")
        other("fr", "Devise Suportée")
    }
    label("arname_8118121660") {
        en("Short Name (Arabic)")
        ar("Short Name (Arabic)")
        other("fr", "Nom court")
    }
    label("name_8118121665") {
        en("Full Name (English)")
        ar("Full Name (English)")
        other("fr", "Nom complet")
    }
    label("arfullname_8118121670") {
        en("Full Name (Arabic)")
        ar("Full Name (Arabic)")
        other("fr", "Nom comple")
    }
    label("disputevalidatydays_8118121675") {
        en("Dispute Validaty Days")
        ar("Dispute Validaty Days")
        other("fr", "Nombre de Jours de Validité d'un Litige")
    }
    label("description_8118121680") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("paymentsystemintgtype_8118121685") {
        en("Integration Type")
        ar("Integration Type")
        other("fr", "Type d'intégration")
    }
    label("confpaymentsystem.manual_8118121690") {
        en("Manual")
        other("fr", "Manuel")
    }
    label("confpaymentsystem.online_8118121694") {
        en("Online")
        other("fr", "En ligne")
    }
    label("maxrepresentcount_8118121698") {
        en("Max Represent Count")
        ar("Max Represent Count")
        other("fr", "Nombre de représentations Maximum")
    }
    label("maxmoreinfocount_8118121703") {
        en("Max More Info Count")
        ar("Max More Info Count")
        other("fr", "Nombre de demandes d'Info Maximum")
    }
    label("allowescalation_8118121708") {
        en("Allow Arbitration")
        ar("Allow Arbitration")
        other("fr", "Autoriser l'arbitrage")
    }
    label("requiresenderatt_8118121713") {
        en("Sender Attachment")
        ar("Sender Attachment")
        other("fr", "Pièce jointe de l'expéditeur")
    }
    label("requirereceiveratt_8118121718") {
        en("Receiver Attachment")
        ar("Receiver Attachment")
        other("fr", "Pièce jointe du Participant Destinataire")
    }
    label("xmlfilepath_8118121723") {
        en("XML File Path")
        ar("XML File Path")
        other("fr", "Chemin du fichier XML")
    }
    label("psip_8118121728") {
        en("IP")
        ar("IP")
        other("fr", "IP")
    }
    label("psport_8118121733") {
        en("Port")
        ar("Port")
        other("fr", "Port")
    }
    label("psusername_8118121738") {
        en("Username")
        ar("Username")
        other("fr", "Nom d'utilisateur")
    }
    label("pspassword_8118121743") {
        en("Password")
        ar("Password")
        other("fr", "Mot de passe")
    }
    label("pswebserviceurl_8118121748") {
        en("Webservice Url")
        ar("Webservice Url")
        other("fr", "URL du service Web")
    }
    label("fromamount_8118121753") {
        en("From Amount")
        ar("From Amount")
        other("fr", "Quantité De")
    }
    label("toamount_8118121758") {
        en("To Amount")
        ar("To Amount")
        other("fr", "Au Montant")
    }
    label("reqnewdisputeperiod_8118121763") {
        en("Request New Dispute(Days)")
        ar("Request New Dispute(Days)")
        other("fr", "Demande d'un nouveau Litige (jours)")
    }
    label("replydisputeperiode_8118121768") {
        en("Dispute Reply (Days)")
        ar("Dispute Reply (Days)")
        other("fr", "Réponse a un litige (jours)")
    }
    label("disputerprsntperiode_8118121773") {
        en("Dispute Re-presentment (Days)")
        ar("Dispute Re-presentment (Days)")
        other("fr", "Représentation d'un Litige (jours)")
    }
    label("disputereplyperiode_8118121778") {
        en("Dispute Reply/ Re-presentment (Days)")
        ar("Dispute Reply/ Re-presentment (Days)")
        other("fr", "Réponse / re-présentation de litige (jour)")
    }
    label("escalateentrsperiode_8118121783") {
        en("Dispute Arbitration Entries (Days)")
        ar("Dispute Arbitration Entries (Days)")
        other("fr", "Entrées d'arbitrage de Litiges (jour)")
    }
    label("escentrsprocperiode_8118121788") {
        en("Dispute Arbitration Entries Processing (Days)")
        ar("Dispute Arbitration Entries Processing (Days)")
        other("fr", "Traitement des entrées d'arbitrage des Litiges (jour)")
    }
    label("expireddisputeaction_8118121793") {
        en("Expired Dispute Default Action")
        ar("Expired Dispute Default Action")
        other("fr", "Action par défaut de litige expiré")
    }
    label("confpaymentsystem.accepted_8118121798") {
        en("Accepted")
        other("fr", "Accepté")
    }
    label("confpaymentsystem.rejected_8118121802") {
        en("Rejected")
        other("fr", "Rejeté")
    }
    label("reqnewdisputeperiodmax_8118121806") {
        en("Request New Dispute MAX(Days)")
        ar("Request New Dispute MAX(Days)")
        other("fr", "Demander un nouveau Litige max (jours)")
    }
    label("replydisputeperiodemax_8118121811") {
        en("Dispute Reply MAX(Days)")
        ar("Dispute Reply MAX(Days)")
        other("fr", "Nombre de jours Max pour Répondre a un litige (jours)")
    }
    label("disputerprsntperiodemax_8118121816") {
        en("Dispute Re-presentment MAX(Days)")
        ar("Dispute Re-presentment MAX(Days)")
        other("fr", "Nombre de jours Max pour Représenter un litige (jour)")
    }
    label("disputereplyperiodemax_8118121821") {
        en("Dispute Reply/ Re-presentment MAX(Days)")
        ar("Dispute Reply/ Re-presentment MAX(Days)")
        other("fr", "Réponse au litige / re-présentation Max (jour)")
    }
    label("escalateentrsperiodemax_8118121826") {
        en("Dispute Arbitration Entries MAX(Days)")
        ar("Dispute Arbitration Entries MAX(Days)")
        other("fr", "Arbitrage de Litige Entrées max (jour)")
    }
    label("escentrsprocperiodemax_8118121831") {
        en("Dispute Arbitration Entries Processing MAX(Days)")
        ar("Dispute Arbitration Entries Processing MAX(Days)")
        other("fr", "Arbitrage de Litige Traitement des Entrées max  (jour)")
    }
    label("goodfaithinitexpiry_8118121836") {
        en("Good Faith Initiation Expiry")
        ar("Good Faith Initiation Expiry")
        other("fr", "Expiration d'initiation de bonne foi")
    }
    label("goodfaithreply_8118121841") {
        en("Good Faith Reply")
        ar("Good Faith Reply")
        other("fr", "Réponse de bonne foi")
    }
    label("goodfaithinitonreply_8118121846") {
        en("Good Faith Initiation on Reply")
        ar("Good Faith Initiation on Reply")
        other("fr", "Initiation de bonne foi sur la réponse")
    }
    label("currency.id_8118121851") {
        en("Supported Currency")
        ar("Supported Currency")
        other("fr", "Devise Suportée")
    }
    label("currency.codenamepair_8118121856") {
        en("Supported Currency")
        ar("Supported Currency")
        other("fr", "Devise Suportée")
    }
    label("statusid_8118121861") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118121866") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118121871") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118121876") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118121886") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118121891") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118121896") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118121901") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118121906") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118121911") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118121916") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118121921") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("information_8118121928") {
        en("Information")
        ar("Information")
        other("fr", "Information")
    }
    label("confpaymentsystem.information.tab_8118121933") {
        en("Information")
        ar("Information")
        other("fr", "Information")
    }
    label("confpaymentsystem.information.form_8118121938") {
        en("Information")
        ar("Information")
        other("fr", "Information")
    }
    label("xmldetails_8118121956") {
        en("Xml Details")
        ar("Xml Details")
        other("fr", "Détails XML")
    }
    label("confpaymentsystem.xmldetails.tab_8118121961") {
        en("Xml Details")
        ar("Xml Details")
        other("fr", "Détails XML")
    }
    label("confpaymentsystem.xmldetails.form_8118121966") {
        en("Xml Details")
        ar("Xml Details")
        other("fr", "Détails XML")
    }
    label("slapaymentparametersminimum_8118121977") {
        en("SLA Payment parameters - Minimum")
        ar("SLA Payment parameters - Minimum")
        other("fr", "Paramètres de paiement SLA - minimum")
    }
    label("confpaymentsystem.slapaymentparametersminimum.tab_8118121982") {
        en("SLA Payment parameters - Minimum")
        ar("SLA Payment parameters - Minimum")
        other("fr", "Paramètres de paiement SLA - minimum")
    }
    label("confpaymentsystem.slapaymentparametersminimum.form_8118121987") {
        en("SLA Payment parameters - Minimum")
        ar("SLA Payment parameters - Minimum")
        other("fr", "Paramètres de paiement SLA - minimum")
    }
    label("slapaymentparametersmaximum_8118122001") {
        en("SLA Payment parameters - Maximum")
        ar("SLA Payment parameters - Maximum")
        other("fr", "Paramètres de paiement SLA - maximum")
    }
    label("confpaymentsystem.slapaymentparametersmaximum.tab_8118122006") {
        en("SLA Payment parameters - Maximum")
        ar("SLA Payment parameters - Maximum")
        other("fr", "Paramètres de paiement SLA - maximum")
    }
    label("confpaymentsystem.slapaymentparametersmaximum.form_8118122011") {
        en("SLA Payment parameters - Maximum")
        ar("SLA Payment parameters - Maximum")
        other("fr", "Paramètres de paiement SLA - maximum")
    }
    label("goodfaithparameters_8118122022") {
        en("Good Faith parameters")
        ar("Good Faith parameters")
        other("fr", "Paramètres de bonne foi")
    }
    label("confpaymentsystem.goodfaithparameters.tab_8118122027") {
        en("Good Faith parameters")
        ar("Good Faith parameters")
        other("fr", "Paramètres de bonne foi")
    }
    label("confpaymentsystem.goodfaithparameters.form_8118122032") {
        en("Good Faith parameters")
        ar("Good Faith parameters")
        other("fr", "Paramètres de bonne foi")
    }
    label("workflowstatus_8118122040") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpaymentsystem.workflowstatus.tab_8118122045") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpaymentsystem.workflowstatus.form_8118122050") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118122060") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confpaymentsystem.attachments.tab_8118122065") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confpaymentsystem.comments.tab_8118122070") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confpaymentsystem.changehistory.tab_8118122075") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("information_8118122080") {
        en("Information")
        ar("Information")
        other("fr", "Information")
    }
    label("confpaymentsystem.information.tab_8118122085") {
        en("Information")
        ar("Information")
        other("fr", "Information")
    }
    label("confpaymentsystem.information.form_8118122090") {
        en("Information")
        ar("Information")
        other("fr", "Information")
    }
    label("xmldetails_8118122108") {
        en("Xml Details")
        ar("Xml Details")
        other("fr", "Détails XML")
    }
    label("confpaymentsystem.xmldetails.tab_8118122113") {
        en("Xml Details")
        ar("Xml Details")
        other("fr", "Détails XML")
    }
    label("confpaymentsystem.xmldetails.form_8118122118") {
        en("Xml Details")
        ar("Xml Details")
        other("fr", "Détails XML")
    }
    label("slapaymentparametersminimum_8118122129") {
        en("SLA Payment parameters - Minimum")
        ar("SLA Payment parameters - Minimum")
        other("fr", "Paramètres de paiement SLA - minimum")
    }
    label("confpaymentsystem.slapaymentparametersminimum.tab_8118122134") {
        en("SLA Payment parameters - Minimum")
        ar("SLA Payment parameters - Minimum")
        other("fr", "Paramètres de paiement SLA - minimum")
    }
    label("confpaymentsystem.slapaymentparametersminimum.form_8118122139") {
        en("SLA Payment parameters - Minimum")
        ar("SLA Payment parameters - Minimum")
        other("fr", "Paramètres de paiement SLA - minimum")
    }
    label("slapaymentparametersmaximum_8118122153") {
        en("SLA Payment parameters - Maximum")
        ar("SLA Payment parameters - Maximum")
        other("fr", "Paramètres de paiement SLA - maximum")
    }
    label("confpaymentsystem.slapaymentparametersmaximum.tab_8118122158") {
        en("SLA Payment parameters - Maximum")
        ar("SLA Payment parameters - Maximum")
        other("fr", "Paramètres de paiement SLA - maximum")
    }
    label("confpaymentsystem.slapaymentparametersmaximum.form_8118122163") {
        en("SLA Payment parameters - Maximum")
        ar("SLA Payment parameters - Maximum")
        other("fr", "Paramètres de paiement SLA - maximum")
    }
    label("goodfaithparameters_8118122174") {
        en("Good Faith parameters")
        ar("Good Faith parameters")
        other("fr", "Paramètres de bonne foi")
    }
    label("confpaymentsystem.goodfaithparameters.tab_8118122179") {
        en("Good Faith parameters")
        ar("Good Faith parameters")
        other("fr", "Paramètres de bonne foi")
    }
    label("confpaymentsystem.goodfaithparameters.form_8118122184") {
        en("Good Faith parameters")
        ar("Good Faith parameters")
        other("fr", "Paramètres de bonne foi")
    }
    label("workflowstatus_8118122192") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpaymentsystem.workflowstatus.tab_8118122197") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpaymentsystem.workflowstatus.form_8118122202") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118122212") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confpaymentsystem.attachments.tab_8118122217") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confpaymentsystem.comments.tab_8118122222") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confpaymentsystem.changehistory.tab_8118122227") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confpaymentstatus.view_8118122232") {
        en("Payment Statuses")
        ar("Payment Statuses")
        other("fr", "Statuts de paiement")
    }
    label("id_8118122249") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118122259") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118122264") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118122254") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("psrjctr.id_8118122275") {
        en("psRjctR.id")
        ar("psRjctR.id")
        other("fr", "psrjctr.id")
    }
    label("psrjctr.code_8118122280") {
        en("psRjctR.code")
        ar("psRjctR.code")
        other("fr", "psrjctr.code")
    }
    label("psrjctr.name_8118122285") {
        en("psRjctR.name")
        ar("psRjctR.name")
        other("fr", "psrjctr.name")
    }
    label("psrjctr.codenamepair_8118122290") {
        en("psRjctR.codeNamePair")
        ar("psRjctR.codeNamePair")
        other("fr", "psrjctr.codenamepair")
    }
    label("psrjctr_8118122270") {
        en("Payment System Rejection Reasons")
        ar("Payment System Rejection Reasons")
        other("fr", "Motifs de rejet du système de paiement")
    }
    label("code_8118122296") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118122301") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("description_8118122306") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118122311") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118122316") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118122321") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118122326") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118122336") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118122341") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118122346") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118122351") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118122356") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118122361") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118122366") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118122371") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118122378") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confpaymentstatus.info.tab_8118122383") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confpaymentstatus.info.form_8118122388") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118122398") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpaymentstatus.workflowstatus.tab_8118122403") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpaymentstatus.workflowstatus.form_8118122408") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118122418") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confpaymentstatus.attachments.tab_8118122423") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confpaymentstatus.comments.tab_8118122428") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confpaymentstatus.changehistory.tab_8118122433") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118122438") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confpaymentstatus.info.tab_8118122443") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confpaymentstatus.info.form_8118122448") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118122458") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpaymentstatus.workflowstatus.tab_8118122463") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpaymentstatus.workflowstatus.form_8118122468") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118122478") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confpaymentstatus.attachments.tab_8118122483") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confpaymentstatus.comments.tab_8118122488") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confpaymentstatus.changehistory.tab_8118122493") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confurgencysla.view_8118122498") {
        en("Urgency SLA")
        ar("Urgency SLA")
        other("fr", "SLA des Litiges Urgents")
    }
    label("id_8118122507") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("code_8118122512") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118122517") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("maxdeadline_8118122522") {
        en("Max Deadline")
        ar("Max Deadline")
        other("fr", "Date limite maximale")
    }
    label("description_8118122527") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118122532") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118122537") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118122542") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118122547") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118122557") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118122562") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118122567") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118122572") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118122577") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118122582") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118122587") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118122592") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118122599") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confurgencysla.info.tab_8118122604") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confurgencysla.info.form_8118122609") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118122618") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confurgencysla.workflowstatus.tab_8118122623") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confurgencysla.workflowstatus.form_8118122628") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118122638") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confurgencysla.attachments.tab_8118122643") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confurgencysla.comments.tab_8118122648") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confurgencysla.changehistory.tab_8118122653") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118122658") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confurgencysla.info.tab_8118122663") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confurgencysla.info.form_8118122668") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118122677") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confurgencysla.workflowstatus.tab_8118122682") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confurgencysla.workflowstatus.form_8118122687") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118122697") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confurgencysla.attachments.tab_8118122702") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confurgencysla.comments.tab_8118122707") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confurgencysla.changehistory.tab_8118122712") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confdisputefees.view_8118122717") {
        en("Dispute Fees")
        ar("Dispute Fees")
        other("fr", "Frais associés au litige")
    }
    label("id_8118122726") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118122736") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118122741") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118122731") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("code_8118122747") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118122752") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("description_8118122757") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("currency.stringisocode_8118122777") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118122762") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("feeamount_8118122782") {
        en("Fee Amount")
        ar("Fee Amount")
        other("fr", "Montant des frais")
    }
    label("currency.id_8118122787") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("currency.codenamepair_8118122792") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("statusid_8118122797") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118122802") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118122807") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118122812") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118122822") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118122827") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118122832") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118122837") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118122842") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118122847") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118122852") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118122857") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118122864") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputefees.info.tab_8118122869") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputefees.info.form_8118122874") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118122885") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputefees.workflowstatus.tab_8118122890") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputefees.workflowstatus.form_8118122895") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118122905") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confdisputefees.attachments.tab_8118122910") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confdisputefees.comments.tab_8118122915") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confdisputefees.changehistory.tab_8118122920") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118122925") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputefees.info.tab_8118122930") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputefees.info.form_8118122935") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118122946") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputefees.workflowstatus.tab_8118122951") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputefees.workflowstatus.form_8118122956") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118122966") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confdisputefees.attachments.tab_8118122971") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confdisputefees.comments.tab_8118122976") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confdisputefees.changehistory.tab_8118122981") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confsettlementtype.view_8118122986") {
        en("Settlement Types")
        ar("Settlement Types")
        other("fr", "Types de règlement")
    }
    label("id_8118123003") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118123013") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118123018") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118123008") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("code_8118123024") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118123029") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("description_8118123034") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118123039") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118123044") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118123049") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118123054") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118123064") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118123069") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118123074") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118123079") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118123084") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118123089") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118123094") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118123099") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118123106") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confsettlementtype.info.tab_8118123111") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confsettlementtype.info.form_8118123116") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118123125") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confsettlementtype.workflowstatus.tab_8118123130") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confsettlementtype.workflowstatus.form_8118123135") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118123145") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confsettlementtype.attachments.tab_8118123150") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confsettlementtype.comments.tab_8118123155") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confsettlementtype.changehistory.tab_8118123160") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118123165") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confsettlementtype.info.tab_8118123170") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confsettlementtype.info.form_8118123175") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118123184") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confsettlementtype.workflowstatus.tab_8118123189") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confsettlementtype.workflowstatus.form_8118123194") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118123204") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confsettlementtype.attachments.tab_8118123209") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confsettlementtype.comments.tab_8118123214") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confsettlementtype.changehistory.tab_8118123219") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confescalationcharges.view_8118123224") {
        en("Arbitration Charges")
        ar("Arbitration Charges")
        other("fr", "Frais d'arbitrage")
    }
    label("id_8118123241") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118123251") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118123256") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118123246") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("code_8118123262") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118123267") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("currency.stringisocode_8118123287") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118123272") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("amount_8118123292") {
        en("Amount")
        ar("Amount")
        other("fr", "Montant")
    }
    label("description_8118123297") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("currency.id_8118123302") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("currency.codenamepair_8118123307") {
        en("Currency")
        ar("Currency")
        other("fr", "Devise")
    }
    label("statusid_8118123312") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118123317") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118123322") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118123327") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118123337") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118123342") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118123347") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118123352") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118123357") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118123362") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118123367") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118123372") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118123379") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confescalationcharges.info.tab_8118123384") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confescalationcharges.info.form_8118123389") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118123400") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confescalationcharges.workflowstatus.tab_8118123405") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confescalationcharges.workflowstatus.form_8118123410") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118123420") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confescalationcharges.attachments.tab_8118123425") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confescalationcharges.comments.tab_8118123430") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confescalationcharges.changehistory.tab_8118123435") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118123440") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confescalationcharges.info.tab_8118123445") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confescalationcharges.info.form_8118123450") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118123461") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confescalationcharges.workflowstatus.tab_8118123466") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confescalationcharges.workflowstatus.form_8118123471") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118123481") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confescalationcharges.attachments.tab_8118123486") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confescalationcharges.comments.tab_8118123491") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confescalationcharges.changehistory.tab_8118123496") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confdisputereason.view_8118123501") {
        en("Dispute Reasons")
        ar("Dispute Reasons")
        other("fr", "Motifs des Litiges")
    }
    label("id_8118123518") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118123528") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118123533") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118123523") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("code_8118123539") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118123544") {
        en("Dispute Reason Name")
        ar("Dispute Reason Name")
        other("fr", "Nom d'un motif de litiges")
    }
    label("description_8118123549") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118123554") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118123559") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118123564") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118123569") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118123579") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118123584") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118123589") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118123594") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118123599") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118123604") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118123609") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118123614") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118123621") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputereason.info.tab_8118123626") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputereason.info.form_8118123631") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118123640") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputereason.workflowstatus.tab_8118123645") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputereason.workflowstatus.form_8118123650") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118123660") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confdisputereason.attachments.tab_8118123665") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confdisputereason.comments.tab_8118123670") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confdisputereason.changehistory.tab_8118123675") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118123680") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputereason.info.tab_8118123685") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputereason.info.form_8118123690") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118123699") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputereason.workflowstatus.tab_8118123704") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputereason.workflowstatus.form_8118123709") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118123719") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confdisputereason.attachments.tab_8118123724") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confdisputereason.comments.tab_8118123729") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confdisputereason.changehistory.tab_8118123734") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confrejectionreason.view_8118123739") {
        en("Rejection Reason")
        ar("Rejection Reason")
        other("fr", "Motif de rejet")
    }
    label("id_8118123756") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118123766") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118123771") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118123761") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("mainreason.id_8118123782") {
        en("mainReason.id")
        ar("mainReason.id")
        other("fr", "mainreason.id")
    }
    label("mainreason.code_8118123787") {
        en("mainReason.code")
        ar("mainReason.code")
        other("fr", "MAINR -ason.code")
    }
    label("mainreason.name_8118123792") {
        en("mainReason.name")
        ar("mainReason.name")
        other("fr", "MainReason.name")
    }
    label("mainreason.codenamepair_8118123797") {
        en("mainReason.codeNamePair")
        ar("mainReason.codeNamePair")
        other("fr", "mainreason.codenamepair")
    }
    label("mainreason_8118123777") {
        en("Dispute Reason")
        ar("Dispute Reason")
        other("fr", "Raisonnement de contesté")
    }
    label("code_8118123803") {
        en("code")
        ar("code")
        other("fr", "code")
    }
    label("name_8118123808") {
        en("Rejection Reason Name")
        ar("Rejection Reason Name")
        other("fr", "Nom du Motif de rejet")
    }
    label("description_8118123813") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118123818") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118123823") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118123828") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118123833") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118123843") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118123848") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118123853") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118123858") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118123863") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118123868") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118123873") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118123878") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118123885") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confrejectionreason.info.tab_8118123890") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confrejectionreason.info.form_8118123895") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118123905") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confrejectionreason.workflowstatus.tab_8118123910") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confrejectionreason.workflowstatus.form_8118123915") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118123925") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confrejectionreason.attachments.tab_8118123930") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confrejectionreason.comments.tab_8118123935") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confrejectionreason.changehistory.tab_8118123940") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118123945") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confrejectionreason.info.tab_8118123950") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confrejectionreason.info.form_8118123955") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118123965") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confrejectionreason.workflowstatus.tab_8118123970") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confrejectionreason.workflowstatus.form_8118123975") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118123985") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confrejectionreason.attachments.tab_8118123990") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confrejectionreason.comments.tab_8118123995") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confrejectionreason.changehistory.tab_8118124000") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confescalationreason.view_8118124005") {
        en("Dispute Arbitration Reasons")
        ar("Dispute Arbitration Reasons")
        other("fr", "Motifs d'arbitrage des Litiges")
    }
    label("id_8118124021") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118124031") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118124036") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118124026") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("code_8118124042") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118124047") {
        en("Arbitration Reason Name")
        ar("Arbitration Reason Name")
        other("fr", "Nom du motif de l'arbitrage")
    }
    label("description_8118124052") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118124057") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118124062") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118124067") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118124072") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118124082") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118124087") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118124092") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118124097") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118124102") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118124107") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118124112") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118124117") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118124124") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confescalationreason.info.tab_8118124129") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confescalationreason.info.form_8118124134") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118124143") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confescalationreason.workflowstatus.tab_8118124148") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confescalationreason.workflowstatus.form_8118124153") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118124163") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confescalationreason.changehistory.tab_8118124168") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118124173") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confescalationreason.info.tab_8118124178") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confescalationreason.info.form_8118124183") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118124192") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confescalationreason.workflowstatus.tab_8118124197") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confescalationreason.workflowstatus.form_8118124202") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118124212") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confescalationreason.changehistory.tab_8118124217") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confcancelreason.view_8118124222") {
        en("Dispute Cancelation Reasons")
        ar("Dispute Cancelation Reasons")
        other("fr", "Raisons d'annulation des Litiges")
    }
    label("id_8118124238") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118124248") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118124253") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118124243") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("code_8118124259") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118124264") {
        en("Cancellation Reason Name")
        ar("Cancellation Reason Name")
        other("fr", "Nom de la raison d'annulation")
    }
    label("description_8118124269") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118124274") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118124279") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118124284") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118124289") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118124299") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118124304") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118124309") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118124314") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118124319") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118124324") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118124329") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118124334") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118124341") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confcancelreason.info.tab_8118124346") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confcancelreason.info.form_8118124351") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118124360") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confcancelreason.workflowstatus.tab_8118124365") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confcancelreason.workflowstatus.form_8118124370") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118124380") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confcancelreason.changehistory.tab_8118124385") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118124390") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confcancelreason.info.tab_8118124395") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confcancelreason.info.form_8118124400") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118124409") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confcancelreason.workflowstatus.tab_8118124414") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confcancelreason.workflowstatus.form_8118124419") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118124429") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confcancelreason.changehistory.tab_8118124434") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confpsrejection.view_8118124439") {
        en("Payment System Rejection Reason")
        ar("Payment System Rejection Reason")
        other("fr", "Motif de rejet du système de paiement")
    }
    label("id_8118124454") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118124464") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118124469") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118124459") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("code_8118124475") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118124480") {
        en("Rejection Reason Name")
        ar("Rejection Reason Name")
        other("fr", "Nom du Motif de rejet")
    }
    label("description_8118124485") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118124490") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118124495") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118124500") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118124505") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118124515") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118124520") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118124525") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118124530") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118124535") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118124540") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118124545") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118124550") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118124557") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confpsrejection.info.tab_8118124562") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confpsrejection.info.form_8118124567") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118124576") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpsrejection.workflowstatus.tab_8118124581") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpsrejection.workflowstatus.form_8118124586") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("info_8118124601") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confpsrejection.info.tab_8118124606") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confpsrejection.info.form_8118124611") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118124620") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpsrejection.workflowstatus.tab_8118124625") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confpsrejection.workflowstatus.form_8118124630") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confparticipantcat.view_8118124645") {
        en("Participant Categories")
        ar("Participant Categories")
        other("fr", "Catégories  de Paiement des participants")
    }
    label("id_8118124662") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("code_8118124667") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118124672") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("description_8118124677") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("statusid_8118124682") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118124687") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118124692") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118124697") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118124707") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118124712") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118124717") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118124722") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118124727") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118124732") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118124737") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118124742") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118124749") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confparticipantcat.info.tab_8118124754") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confparticipantcat.info.form_8118124759") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118124767") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confparticipantcat.workflowstatus.tab_8118124772") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confparticipantcat.workflowstatus.form_8118124777") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118124787") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confparticipantcat.attachments.tab_8118124792") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confparticipantcat.comments.tab_8118124797") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confparticipantcat.changehistory.tab_8118124802") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118124807") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confparticipantcat.info.tab_8118124812") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confparticipantcat.info.form_8118124817") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118124825") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confparticipantcat.workflowstatus.tab_8118124830") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confparticipantcat.workflowstatus.form_8118124835") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118124845") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confparticipantcat.attachments.tab_8118124850") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confparticipantcat.comments.tab_8118124855") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confparticipantcat.changehistory.tab_8118124860") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("confemailtemplate.view_8118124865") {
        en("Email Template")
        ar("Email Template")
        other("fr", "Modèle de messagerie")
    }
    label("id_8118124880") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("code_8118124885") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118124890") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("description_8118124895") {
        en("Description")
        ar("Description")
        other("fr", "Description")
    }
    label("subject_8118124900") {
        en("Subject")
        ar("Subject")
        other("fr", "Sujet")
    }
    label("body_8118124905") {
        en("Body")
        ar("Body")
        other("fr", "Corps")
    }
    label("statusid_8118124910") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118124915") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118124920") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118124925") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118124935") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118124940") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118124945") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118124950") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118124955") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118124960") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118124965") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118124970") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("emailtemplate_8118124977") {
        en("Email Template")
        ar("Email Template")
        other("fr", "Modèle de messagerie")
    }
    label("confemailtemplate.emailtemplate.tab_8118124982") {
        en("Email Template")
        ar("Email Template")
        other("fr", "Modèle d'Email")
    }
    label("activity_8118124987") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confemailtemplate.comments.tab_8118124992") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("emailtemplate_8118124997") {
        en("Email Template")
        ar("Email Template")
        other("fr", "Modèle d'Email")
    }
    label("activity_8118125002") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confemailtemplate.comments.tab_8118125007") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("param.view_8118125012") {
        en("System Parameters")
        ar("System Parameters")
        other("fr", "Paramètres du système")
    }
    label("id_8118125019") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paramkey_8118125024") {
        en("Parameter Key")
        ar("Parameter Key")
        other("fr", "Clé du paramètre")
    }
    label("paramvalue_8118125029") {
        en("Parameter Value")
        ar("Parameter Value")
        other("fr", "Valeur du paramètre")
    }
    label("statusid_8118125034") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118125039") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118125044") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118125049") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118125059") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118125064") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118125069") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118125074") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118125079") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118125084") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118125089") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118125094") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("parameterinformation_8118125101") {
        en("Parameter Information")
        ar("Parameter Information")
        other("fr", "Informations sur les paramètres")
    }
    label("param.parameterinformation.tab_8118125106") {
        en("Parameter Information")
        ar("Parameter Information")
        other("fr", "Informations sur les paramètres")
    }
    label("param.parameterinformation.form_8118125111") {
        en("Parameter Information")
        ar("Parameter Information")
        other("fr", "Informations sur les paramètres")
    }
    label("workflowstatus_8118125118") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("param.workflowstatus.tab_8118125123") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("param.workflowstatus.form_8118125128") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("parameterinformation_8118125138") {
        en("Parameter Information")
        ar("Parameter Information")
        other("fr", "Informations sur les paramètres")
    }
    label("param.parameterinformation.tab_8118125143") {
        en("Parameter Information")
        ar("Parameter Information")
        other("fr", "Informations sur les paramètres")
    }
    label("param.parameterinformation.form_8118125148") {
        en("Parameter Information")
        ar("Parameter Information")
        other("fr", "Informations sur les paramètres")
    }
    label("workflowstatus_8118125155") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("param.workflowstatus.tab_8118125160") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("param.workflowstatus.form_8118125165") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputeurgency.view_8118125175") {
        en("Dispute Urgencies")
        ar("Dispute Urgencies")
        other("fr", "Urgences de litige")
    }
    label("id_8118125184") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("paymentsystem.id_8118125194") {
        en("paymentSystem.id")
        ar("paymentSystem.id")
        other("fr", "Paymentsystem.id")
    }
    label("paymentsystem.code_8118125199") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118125189") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("code_8118125205") {
        en("Code")
        ar("Code")
        other("fr", "Code")
    }
    label("name_8118125210") {
        en("Name")
        ar("Name")
        other("fr", "Nom")
    }
    label("description_8118125215") {
        en("Description(English)")
        ar("Description(English)")
        other("fr", "Description (English)")
    }
    label("descriptionar_8118125220") {
        en("Description(Arabic)")
        ar("Description(Arabic)")
        other("fr", "Description")
    }
    label("statusid_8118125225") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118125230") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118125235") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118125240") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118125250") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118125255") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118125260") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118125265") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118125270") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118125275") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118125280") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118125285") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118125292") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputeurgency.info.tab_8118125297") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputeurgency.info.form_8118125302") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118125312") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputeurgency.workflowstatus.tab_8118125317") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputeurgency.workflowstatus.form_8118125322") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118125332") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confdisputeurgency.attachments.tab_8118125337") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confdisputeurgency.comments.tab_8118125342") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confdisputeurgency.changehistory.tab_8118125347") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118125352") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputeurgency.info.tab_8118125357") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("confdisputeurgency.info.form_8118125362") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118125372") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputeurgency.workflowstatus.tab_8118125377") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("confdisputeurgency.workflowstatus.form_8118125382") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118125392") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("confdisputeurgency.attachments.tab_8118125397") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("confdisputeurgency.comments.tab_8118125402") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("confdisputeurgency.changehistory.tab_8118125407") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("rpt_ncp_management.view_8118125412") {
        en("NCP Report Management")
        ar("NCP Report Management")
        other("fr", "Gestion du rapport PCN")
    }
    label("id_8118125433") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("generationdate_8118125438") {
        en("Date")
        ar("Date")
        other("fr", "Date")
    }
    label("messageid_8118125443") {
        en("Message Id")
        ar("Message Id")
        other("fr", "ID du message")
    }
    label("retrynumber_8118125448") {
        en("Retry Number")
        ar("Retry Number")
        other("fr", "Nombre d'Essais")
    }
    label("submissiontime_8118125453") {
        en("Submission Time")
        ar("Submission Time")
        other("fr", "Heure de soumission")
    }
    label("statusid_8118125458") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118125463") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118125468") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118125473") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118125483") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118125488") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118125493") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118125498") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118125503") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118125508") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118125513") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118125518") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("ncpinfo_8118125525") {
        en("NCP Info")
        ar("NCP Info")
        other("fr", "Informations PCN")
    }
    label("rpt_ncp_management.ncpinfo.tab_8118125530") {
        en("NCP Info")
        ar("NCP Info")
        other("fr", "Informations PCN")
    }
    label("rpt_ncp_management.ncpinfo.form_8118125535") {
        en("NCP Info")
        ar("NCP Info")
        other("fr", "Informations PCN")
    }
    label("workflowstatus_8118125543") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_ncp_management.workflowstatus.tab_8118125548") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_ncp_management.workflowstatus.form_8118125553") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118125561") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("rpt_ncp_management.attachments.tab_8118125566") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("rpt_ncp_management.changehistory.tab_8118125571") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("ncpinfo_8118125576") {
        en("NCP Info")
        ar("NCP Info")
        other("fr", "Informations PCN")
    }
    label("rpt_ncp_management.ncpinfo.tab_8118125581") {
        en("NCP Info")
        ar("NCP Info")
        other("fr", "Informations PCN")
    }
    label("rpt_ncp_management.ncpinfo.form_8118125586") {
        en("NCP Info")
        ar("NCP Info")
        other("fr", "Informations PCN")
    }
    label("workflowstatus_8118125594") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_ncp_management.workflowstatus.tab_8118125599") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_ncp_management.workflowstatus.form_8118125604") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118125612") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("rpt_ncp_management.attachments.tab_8118125617") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("rpt_ncp_management.changehistory.tab_8118125622") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("settlementconfig.view_8118125627") {
        en("Settlement Configurations")
        ar("Settlement Configurations")
        other("fr", "Configurations de règlement")
    }
    label("id_8118125643") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("cutofftime_8118125648") {
        en("Cut-Off Time")
        ar("Cut-Off Time")
        other("fr", "Temps limite")
    }
    label("ncpfilepath_8118125653") {
        en("NCP File Path")
        ar("NCP File Path")
        other("fr", "Chemin du fichier PCN")
    }
    label("replyperiod_8118125658") {
        en("Reply Period (minute")
        ar("Reply Period (minute")
        other("fr", "Période de réponse (minutes")
    }
    label("statusid_8118125663") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118125668") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118125673") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118125678") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118125688") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118125693") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118125698") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118125703") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118125708") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118125713") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118125718") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118125723") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("configuration_8118125730") {
        en("Configuration")
        ar("Configuration")
        other("fr", "Configuration")
    }
    label("settlementconfig.configuration.tab_8118125735") {
        en("Configuration")
        ar("Configuration")
        other("fr", "Configuration")
    }
    label("settlementconfig.configuration.form_8118125740") {
        en("Configuration")
        ar("Configuration")
        other("fr", "Configuration")
    }
    label("workflowstatus_8118125748") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("settlementconfig.workflowstatus.tab_8118125753") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("settlementconfig.workflowstatus.form_8118125758") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118125768") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("settlementconfig.comments.tab_8118125773") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("settlementconfig.changehistory.tab_8118125778") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("configuration_8118125783") {
        en("Configuration")
        ar("Configuration")
        other("fr", "Configuration")
    }
    label("settlementconfig.configuration.tab_8118125788") {
        en("Configuration")
        ar("Configuration")
        other("fr", "Configuration")
    }
    label("settlementconfig.configuration.form_8118125793") {
        en("Configuration")
        ar("Configuration")
        other("fr", "Configuration")
    }
    label("workflowstatus_8118125801") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("settlementconfig.workflowstatus.tab_8118125806") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("settlementconfig.workflowstatus.form_8118125811") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118125821") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("settlementconfig.comments.tab_8118125826") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("settlementconfig.changehistory.tab_8118125831") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("rpt_recon_management.view_8118125836") {
        en("Reconciliation Report Management")
        ar("Reconciliation Report Management")
        other("fr", "Gestion des rapports de réconciliation")
    }
    label("id_8118125857") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("generationdate_8118125862") {
        en("Date")
        ar("Date")
        other("fr", "Date")
    }
    label("messageid_8118125867") {
        en("Message Id")
        ar("Message Id")
        other("fr", "ID du message")
    }
    label("participant.id_8118125877") {
        en("participant.id")
        ar("participant.id")
        other("fr", "participant.id")
    }
    label("participant.code_8118125882") {
        en("participant.code")
        ar("participant.code")
        other("fr", "Code de participant")
    }
    label("participant.name_8118125887") {
        en("participant.name")
        ar("participant.name")
        other("fr", "Nom du participant")
    }
    label("participant.codenamepair_8118125892") {
        en("participant.codeNamePair")
        ar("participant.codeNamePair")
        other("fr", "participant.codenamepair")
    }
    label("participant_8118125872") {
        en("Participant")
        ar("Participant")
        other("fr", "Participant")
    }
    label("ncpmessageid_8118125898") {
        en("NCP Message ID")
        ar("NCP Message ID")
        other("fr", "ID de message NCP")
    }
    label("statusid_8118125903") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118125908") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118125913") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118125918") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118125928") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118125933") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118125938") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118125943") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118125948") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118125953") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118125958") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118125963") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("reportinfo_8118125970") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("rpt_recon_management.reportinfo.tab_8118125975") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("rpt_recon_management.reportinfo.form_8118125980") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("workflowstatus_8118125988") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_recon_management.workflowstatus.tab_8118125993") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_recon_management.workflowstatus.form_8118125998") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118126006") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("rpt_recon_management.attachments.tab_8118126011") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("rpt_recon_management.changehistory.tab_8118126016") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("reportinfo_8118126021") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("rpt_recon_management.reportinfo.tab_8118126026") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("rpt_recon_management.reportinfo.form_8118126031") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("workflowstatus_8118126039") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_recon_management.workflowstatus.tab_8118126044") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_recon_management.workflowstatus.form_8118126049") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118126057") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("rpt_recon_management.attachments.tab_8118126062") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("rpt_recon_management.changehistory.tab_8118126067") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("rpt_tx_management.view_8118126072") {
        en("Transaction Report Management")
        ar("Transaction Report Management")
        other("fr", "Gestion du rapport des transactions")
    }
    label("id_8118126093") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("generationdate_8118126098") {
        en("Date")
        ar("Date")
        other("fr", "Date")
    }
    label("messageid_8118126103") {
        en("Message Id")
        ar("Message Id")
        other("fr", "ID du message")
    }
    label("participant.id_8118126113") {
        en("participant.id")
        ar("participant.id")
        other("fr", "participant.id")
    }
    label("participant.code_8118126118") {
        en("participant.code")
        ar("participant.code")
        other("fr", "Code de participant")
    }
    label("participant.name_8118126123") {
        en("participant.name")
        ar("participant.name")
        other("fr", "Nom du participant")
    }
    label("participant.codenamepair_8118126128") {
        en("participant.codeNamePair")
        ar("participant.codeNamePair")
        other("fr", "participant.codenamepair")
    }
    label("participant_8118126108") {
        en("Participant")
        ar("Participant")
        other("fr", "Participant")
    }
    label("ncpmessageid_8118126134") {
        en("NCP Message ID")
        ar("NCP Message ID")
        other("fr", "ID de message NCP")
    }
    label("statusid_8118126139") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118126144") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118126149") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118126154") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118126164") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118126169") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118126174") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118126179") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118126184") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118126189") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118126194") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118126199") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("reportinfo_8118126206") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("rpt_tx_management.reportinfo.tab_8118126211") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("rpt_tx_management.reportinfo.form_8118126216") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("workflowstatus_8118126224") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_tx_management.workflowstatus.tab_8118126229") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_tx_management.workflowstatus.form_8118126234") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118126242") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("rpt_tx_management.attachments.tab_8118126247") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("rpt_tx_management.changehistory.tab_8118126252") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("reportinfo_8118126257") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("rpt_tx_management.reportinfo.tab_8118126262") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("rpt_tx_management.reportinfo.form_8118126267") {
        en("Report Info")
        ar("Report Info")
        other("fr", "Signaler des informations")
    }
    label("workflowstatus_8118126275") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_tx_management.workflowstatus.tab_8118126280") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rpt_tx_management.workflowstatus.form_8118126285") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118126293") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("rpt_tx_management.attachments.tab_8118126298") {
        en("Attachments")
        ar("مرفقات")
        other("fr", "pièces jointes")
    }
    label("rpt_tx_management.changehistory.tab_8118126303") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("rptbankdispute.view_8118126308") {
        en("Bank Dispute Summary")
        ar("Bank Dispute Summary")
        other("fr", "Résumé des litiges bancaires")
    }
    label("id_8118126322") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("senderbankid_8118126327") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("recieverbankid_8118126332") {
        en("Reciever Bank")
        ar("Reciever Bank")
        other("fr", "Participant Destinataire")
    }
    label("status_8118126337") {
        en("status")
        ar("status")
        other("fr", "statut")
    }
    label("fromdate_8118126342") {
        en("From Date")
        ar("From Date")
        other("fr", "De la date")
    }
    label("todate_8118126347") {
        en("To Date")
        ar("To Date")
        other("fr", "À la Date")
    }
    label("bankname_8118126352") {
        en("Bank Name")
        ar("Bank Name")
        other("fr", "Nom du Participant")
    }
    label("issueropen_8118126357") {
        en("Outward Open")
        ar("Outward Open")
        other("fr", "Ouvert pour sortant")
    }
    label("issuerclose_8118126362") {
        en("Outward Close")
        ar("Outward Close")
        other("fr", "Fermé pour sortant")
    }
    label("acquireropen_8118126367") {
        en("Inward Open")
        ar("Inward Open")
        other("fr", "Ouvert pour entrant")
    }
    label("acquirerclose_8118126372") {
        en("Inward Close")
        ar("Inward Close")
        other("fr", "Fermé pour entrant")
    }
    label("total_8118126377") {
        en("Total")
        ar("Total")
        other("fr", "Total")
    }
    label("statusid_8118126382") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118126387") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118126392") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118126397") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118126407") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118126412") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118126417") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118126422") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118126427") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118126432") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118126437") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118126442") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118126449") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("rptbankdispute.info.tab_8118126454") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("rptbankdispute.info.form_8118126459") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118126472") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptbankdispute.workflowstatus.tab_8118126477") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptbankdispute.workflowstatus.form_8118126482") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("info_8118126489") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("rptbankdispute.info.tab_8118126494") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("rptbankdispute.info.form_8118126499") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118126512") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptbankdispute.workflowstatus.tab_8118126517") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptbankdispute.workflowstatus.form_8118126522") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedetailed.view_8118126529") {
        en("Dispute Detailed")
        ar("Dispute Detailed")
        other("fr", "Litige détaillé")
    }
    label("id_8118126543") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("disputeid_8118126548") {
        en("Dispute ID")
        ar("Dispute ID")
        other("fr", "ID de litige")
    }
    label("actiondate_8118126553") {
        en("Action Date")
        ar("Action Date")
        other("fr", "Date d'action")
    }
    label("actionname_8118126558") {
        en("Action Name")
        ar("Action Name")
        other("fr", "Nom de l'action")
    }
    label("disputestatus_8118126563") {
        en("Dispute Status")
        ar("Dispute Status")
        other("fr", "Statut du litige")
    }
    label("paymentsystem_8118126568") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("cyclecount_8118126573") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("isgoodfaith_8118126578") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("isrepresented_8118126583") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Est représenté")
    }
    label("isarbitrated_8118126588") {
        en("Is Arbitrated")
        ar("Is Arbitrated")
        other("fr", "Est arbitré")
    }
    label("deadlinedate_8118126593") {
        en("Deadline date")
        ar("Deadline date")
        other("fr", "Date limite")
    }
    label("currency.stringisocode_8118126613") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118126598") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("disputeamount_8118126618") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("transactionamount_8118126623") {
        en("Transaction Amount")
        ar("Transaction Amount")
        other("fr", "Montant de la transaction")
    }
    label("makeruser_8118126628") {
        en("Maker User")
        ar("Maker User")
        other("fr", "Utilisateur de fabricant")
    }
    label("checkeruser_8118126633") {
        en("Checker User")
        ar("Checker User")
        other("fr", "Vérificateur")
    }
    label("accountnumber_8118126638") {
        en("Account Number")
        ar("Account Number")
        other("fr", "Numéro de compte")
    }
    label("transactionid_8118126643") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la transaction")
    }
    label("senderbank.id_8118126653") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.code_8118126658") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.name_8118126663") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("senderbank.codenamepair_8118126668") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118126648") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.id_8118126679") {
        en("receiverBank.id")
        ar("receiverBank.id")
        other("fr", "receiverbank.id")
    }
    label("receiverbank.code_8118126684") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.name_8118126689") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("receiverbank.codenamepair_8118126694") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118126674") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("reasons_8118126700") {
        en("Reasons")
        ar("Reasons")
        other("fr", "Motifs")
    }
    label("comments_8118126705") {
        en("Comments")
        ar("Comments")
        other("fr", "commentaires")
    }
    label("currency.id_8118126710") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118126715") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118126720") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118126725") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118126730") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118126735") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118126745") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118126750") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118126755") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118126760") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118126765") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118126770") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118126775") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118126780") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("paymentinformation_8118126787") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("rptdisputedetailed.paymentinformation.tab_8118126792") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("rptdisputedetailed.paymentinformation.form_8118126797") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("workflowstatus_8118126823") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedetailed.workflowstatus.tab_8118126828") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedetailed.workflowstatus.form_8118126833") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("paymentinformation_8118126840") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("rptdisputedetailed.paymentinformation.tab_8118126845") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("rptdisputedetailed.paymentinformation.form_8118126850") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("workflowstatus_8118126876") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedetailed.workflowstatus.tab_8118126881") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedetailed.workflowstatus.form_8118126886") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedeadline.view_8118126893") {
        en("Dispute Deadline")
        ar("Dispute Deadline")
        other("fr", "Délai de Traitement du  Litige")
    }
    label("id_8118126907") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("disputeid_8118126912") {
        en("Dispute ID")
        ar("Dispute ID")
        other("fr", "ID de litige")
    }
    label("actiondate_8118126917") {
        en("Action Date")
        ar("Action Date")
        other("fr", "Date de l'action")
    }
    label("disputestatus_8118126922") {
        en("Dispute Status")
        ar("Dispute Status")
        other("fr", "Statut de litige")
    }
    label("deadlinedays_8118126927") {
        en("Deadline Days")
        ar("Deadline Days")
        other("fr", "Jours de date limite")
    }
    label("deadlinedate_8118126932") {
        en("Deadline Date")
        ar("Deadline Date")
        other("fr", "Date limite")
    }
    label("paymentsystem_8118126937") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("cyclecount_8118126942") {
        en("Cycle Count")
        ar("Cycle Count")
        other("fr", "Nombre de cycles")
    }
    label("isgoodfaith_8118126947") {
        en("Is Good Faith")
        ar("Is Good Faith")
        other("fr", "Est de bonne foi")
    }
    label("isrepresented_8118126952") {
        en("Is Represented")
        ar("Is Represented")
        other("fr", "Est représenté")
    }
    label("currency.stringisocode_8118126972") {
        en("currency.stringISOCode")
        ar("currency.stringISOCode")
        other("fr", "devise.stringisocode")
    }
    label("currency_8118126957") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("disputeamount_8118126977") {
        en("Dispute Amount")
        ar("Dispute Amount")
        other("fr", "Montant du Litige")
    }
    label("transactionamount_8118126982") {
        en("Transaction Amount")
        ar("Transaction Amount")
        other("fr", "Montant de la transaction")
    }
    label("reasons_8118126987") {
        en("Reasons")
        ar("Reasons")
        other("fr", "Motifs")
    }
    label("comments_8118126992") {
        en("Comments")
        ar("Comments")
        other("fr", "commentaires")
    }
    label("accountnumber_8118126997") {
        en("Account Number")
        ar("Account Number")
        other("fr", "Numéro de compte")
    }
    label("transactionid_8118127002") {
        en("Transaction ID")
        ar("Transaction ID")
        other("fr", "ID de la transaction")
    }
    label("senderbank.id_8118127012") {
        en("senderBank.id")
        ar("senderBank.id")
        other("fr", "SenderBank.id")
    }
    label("senderbank.code_8118127017") {
        en("senderBank.code")
        ar("senderBank.code")
        other("fr", "SenderBank.code")
    }
    label("senderbank.name_8118127022") {
        en("senderBank.name")
        ar("senderBank.name")
        other("fr", "Senderbank.name")
    }
    label("senderbank.codenamepair_8118127027") {
        en("senderBank.codeNamePair")
        ar("senderBank.codeNamePair")
        other("fr", "Senderbank.codenamepair")
    }
    label("senderbank_8118127007") {
        en("Sender Bank")
        ar("Sender Bank")
        other("fr", "Participant Emetteur")
    }
    label("receiverbank.id_8118127038") {
        en("receiverBank.id")
        ar("receiverBank.id")
        other("fr", "receiverbank.id")
    }
    label("receiverbank.code_8118127043") {
        en("receiverBank.code")
        ar("receiverBank.code")
        other("fr", "receiverbank.code")
    }
    label("receiverbank.name_8118127048") {
        en("receiverBank.name")
        ar("receiverBank.name")
        other("fr", "réceptbank.name")
    }
    label("receiverbank.codenamepair_8118127053") {
        en("receiverBank.codeNamePair")
        ar("receiverBank.codeNamePair")
        other("fr", "receiverbank.codenamepair")
    }
    label("receiverbank_8118127033") {
        en("Receiver Bank")
        ar("Receiver Bank")
        other("fr", "Participant Destinataire")
    }
    label("currency.id_8118127059") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("currency.codenamepair_8118127064") {
        en("Payment Currency")
        ar("Payment Currency")
        other("fr", "Devise du Paiement")
    }
    label("statusid_8118127069") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118127074") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118127079") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118127084") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118127094") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118127099") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118127104") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118127109") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118127114") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118127119") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118127124") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118127129") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("paymentinformation_8118127136") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("rptdisputedeadline.paymentinformation.tab_8118127141") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("rptdisputedeadline.paymentinformation.form_8118127146") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("workflowstatus_8118127169") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedeadline.workflowstatus.tab_8118127174") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedeadline.workflowstatus.form_8118127179") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("paymentinformation_8118127186") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("rptdisputedeadline.paymentinformation.tab_8118127191") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("rptdisputedeadline.paymentinformation.form_8118127196") {
        en("Payment Information")
        ar("Payment Information")
        other("fr", "Informations du paiement")
    }
    label("workflowstatus_8118127219") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedeadline.workflowstatus.tab_8118127224") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("rptdisputedeadline.workflowstatus.form_8118127229") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemanagement.view_8118127236") {
        en("Dispute Collection Management")
        ar("Dispute Collection Management")
        other("fr", "Gestion de la collecte des Litiges")
    }
    label("id_8118127243") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("collectionstart_8118127248") {
        en("Collection Start")
        ar("Collection Start")
        other("fr", "Début de la collecte")
    }
    label("collectionend_8118127253") {
        en("Collection End")
        ar("Collection End")
        other("fr", "Fin de collecte")
    }
    label("ncpmessageid_8118127258") {
        en("NCP Message ID")
        ar("NCP Message ID")
        other("fr", "ID de message NCP")
    }
    label("statusid_8118127263") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118127268") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118127273") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118127278") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118127288") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118127293") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118127298") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118127303") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118127308") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118127313") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118127318") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118127323") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("management_8118127330") {
        en("Management")
        ar("Management")
        other("fr", "Gestion")
    }
    label("disputemanagement.management.tab_8118127335") {
        en("Management")
        ar("Management")
        other("fr", "Gestion")
    }
    label("disputemanagement.management.form_8118127340") {
        en("Management")
        ar("Management")
        other("fr", "Gestion")
    }
    label("workflowstatus_8118127348") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemanagement.workflowstatus.tab_8118127353") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemanagement.workflowstatus.form_8118127358") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118127365") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputemanagement.comments.tab_8118127370") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputemanagement.changehistory.tab_8118127375") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("management_8118127380") {
        en("Management")
        ar("Management")
        other("fr", "Gestion")
    }
    label("disputemanagement.management.tab_8118127385") {
        en("Management")
        ar("Management")
        other("fr", "Gestion")
    }
    label("disputemanagement.management.form_8118127390") {
        en("Management")
        ar("Management")
        other("fr", "Gestion")
    }
    label("workflowstatus_8118127398") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemanagement.workflowstatus.tab_8118127403") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("disputemanagement.workflowstatus.form_8118127408") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118127415") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("disputemanagement.comments.tab_8118127420") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("disputemanagement.changehistory.tab_8118127425") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("userspayments.view_8118127430") {
        en("Users Payments")
        ar("Users Payments")
        other("fr", "Paiements des utilisateurs")
    }
    label("id_8118127446") {
        en("ID")
        ar("ID")
        other("fr", "IDENTIFIANT")
    }
    label("user_8118127451") {
        en("User")
        ar("User")
        other("fr", "Utilisateur")
    }
    label("user.username_8118127457") {
        en("Username")
        ar("Username")
        other("fr", "Nom d'utilisateur")
    }
    label("paymentsystem.code_8118127473") {
        en("paymentSystem.code")
        ar("paymentSystem.code")
        other("fr", "Paymentsystem.code")
    }
    label("paymentsystem_8118127462") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("paymentsystem.id_8118127478") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("paymentsystem.name_8118127483") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("statusid_8118127488") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("statusid.id_8118127493") {
        en("StatusId")
        ar("الحالة")
        other("fr", "ID du Statut")
    }
    label("statusid.codenamepair_8118127498") {
        en("StatusCode")
        ar("الحالة")
        other("fr", "Code du statut")
    }
    label("statusid.description_8118127503") {
        en("Status")
        ar("الحالة")
        other("fr", "Statut")
    }
    label("updatingdate_8118127513") {
        en("Updated on")
        ar("تاريخ التعديل")
        other("fr", "Mis à jour le")
    }
    label("lockeduntil_8118127518") {
        en("Due on")
        ar("تاريخ الانتهاء")
        other("fr", "Dû le")
    }
    label("deletedon_8118127523") {
        en("Deleted on")
        ar("تاريخ الحذف")
        other("fr", "Supprimé")
    }
    label("createdby_8118127528") {
        en("Created by")
        ar("ادخله")
        other("fr", "Créé par")
    }
    label("updatedby_8118127533") {
        en("Updated by")
        ar("عدله")
        other("fr", "Mis à jour par")
    }
    label("lockedby_8118127538") {
        en("Locked By")
        ar("محجوز ل")
        other("fr", "Verrouillé par")
    }
    label("deletedby_8118127543") {
        en("Deleted By")
        ar("حذفه")
        other("fr", "Supprimé par")
    }
    label("deletedflag_8118127548") {
        en("Deleted")
        ar("مؤشر الحذف")
        other("fr", "Supprimé")
    }
    label("info_8118127555") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("userspayments.info.tab_8118127560") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("userspayments.info.form_8118127565") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118127572") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("userspayments.workflowstatus.tab_8118127577") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("userspayments.workflowstatus.form_8118127582") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118127592") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("userspayments.comments.tab_8118127597") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("userspayments.changehistory.tab_8118127602") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("info_8118127607") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("userspayments.info.tab_8118127612") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("userspayments.info.form_8118127617") {
        en("Info")
        ar("Info")
        other("fr", "Info")
    }
    label("workflowstatus_8118127624") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("userspayments.workflowstatus.tab_8118127629") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("userspayments.workflowstatus.form_8118127634") {
        en("Workflow Status")
        ar("وضع العمل")
        other("fr", "Statut du flux de travail")
    }
    label("activity_8118127644") {
        en("Activity")
        ar("الفعاليات")
        other("fr", "Activité")
    }
    label("userspayments.comments.tab_8118127649") {
        en("Comments")
        ar("تعليقات")
        other("fr", "commentaires")
    }
    label("userspayments.changehistory.tab_8118127654") {
        en("Change History")
        ar("سجل التغييرات")
        other("fr", "Historique des Modifications")
    }
    label("dispute.menu_8118103451") {
        en("Dispute")
        other("fr", "Litige")
    }
    label("outward.menu_8118103455") {
        en("Outward")
        other("fr", "Sortant")
    }
    label("disputeapproval.menu_8118103459") {
        en("Dispute Approval")
        other("fr", "Approbation du litige")
    }
    label("disputerequest.menu_8118106072") {
        en("Dispute Request")
        other("fr", "Demande de litige")
    }
    label("disputeneedmoreinformation.menu_8118107499") {
        en("Dispute Need More Information")
        other("fr", "Le Litige a besoin de plus d'informations")
    }
    label("disputeneedmoreinformationapproval.menu_8118109004") {
        en("Dispute Need More Information Approval")
        other("fr", "approbation de la demande d'information")
    }
    label("inward.menu_8118110505") {
        en("Inward")
        other("fr", "Entrant")
    }
    label("disputewaitingreply.menu_8118110509") {
        en("Dispute Waiting Reply")
        other("fr", "Litige en attente de Réponse")
    }
    label("disputeapproval.menu_8118111985") {
        en("Dispute Approval")
        other("fr", "Approbation du litige")
    }
    label("disputemoreinformationapproval.menu_8118113711") {
        en("Dispute More Information Approval")
        other("fr", "approbation de la demande d'information")
    }
    label("disputemasterquery.menu_8118115326") {
        en("Dispute Master Query")
        other("fr", "Requête maîtresse de litige")
    }
    label("replieddisputes.menu_8118117054") {
        en("Replied Disputes")
        other("fr", "Litiges Répondus")
    }
    label("replieddisputes.menu_8118118780") {
        en("Replied Disputes")
        other("fr", "Litiges Répondus")
    }
    label("disputecancelation.menu_8118120506") {
        en("Dispute Cancelation")
        other("fr", "Annulation des litiges")
    }
    label("participants.menu_8118121168") {
        en("Participants")
        other("fr", "Participants")
    }
    label("manageparticipants.menu_8118121172") {
        en("Manage Participants")
        other("fr", "Gérer les participants")
    }
    label("configurations.menu_8118121618") {
        en("Configurations and Participants")
        other("fr", "Configurations et participants")
    }
    label("configurations.menu_8118121619") {
        en("Configurations")
        other("fr", "Configurations")
    }
    label("paymentsystems.menu_8118121622") {
        en("Payment Systems")
        other("fr", "Systèmes de paiement")
    }
    label("paymentstatuses.menu_8118122241") {
        en("Payment Statuses")
        other("fr", "Statuts de paiement")
    }
    label("settlementtypes.menu_8118122995") {
        en("Settlement Types")
        other("fr", "Types de règlement")
    }
    label("disputearbitrationcharges.menu_8118123233") {
        en("Dispute Arbitration Charges")
        other("fr", "Frais d'arbitrage des Litiges")
    }
    label("disputereasons.menu_8118123510") {
        en("Dispute Reasons")
        other("fr", "Motifs des Litiges")
    }
    label("disputerejectionreasons.menu_8118123748") {
        en("Dispute Rejection Reasons")
        other("fr", "Motifs de rejet de litige")
    }
    label("disputearbitrationreasons.menu_8118124014") {
        en("Dispute Arbitration Reasons")
        other("fr", "Motifs d'arbitrage des Litiges")
    }
    label("disputecancellationreasons.menu_8118124231") {
        en("Dispute Cancellation Reasons")
        other("fr", "Débit des raisons d'annulation")
    }
    label("paymentsystemrejectionreasons.menu_8118124448") {
        en("Payment System Rejection Reasons")
        other("fr", "Motifs de rejet du système de paiement")
    }
    label("participantcategories.menu_8118124654") {
        en("Participant Categories")
        other("fr", "Catégories de Paiment des participants")
    }
    label("emailtemplate.menu_8118124874") {
        en("Email Template")
        other("fr", "Modèle d'Email")
    }
    label("reports.menu_8118125417") {
        en("Reports")
        other("fr", "Rapports")
    }
    label("settlementreports.menu_8118125421") {
        en("Settlement Reports")
        other("fr", "Rapports de règlement")
    }
    label("ncpreportmanagement.menu_8118125425") {
        en("NCP Report Management")
        other("fr", "Gestion du rapport PCN")
    }
    label("settlementconfigurations.menu_8118125636") {
        en("Settlement Configurations")
        other("fr", "Configurations de règlement")
    }
    label("reconciliationreportmanagement.menu_8118125849") {
        en("Reconciliation Report Management")
        other("fr", "Gestion du rapport de réconciliation")
    }
    label("transactionreportmanagement.menu_8118126085") {
        en("Transaction Report Management")
        other("fr", "Gestion du rapport des transactions")
    }
    label("bankdisputesummary.menu_8118126317") {
        en("Bank Dispute Summary")
        other("fr", "Résumé des litiges des Participants")
    }
    label("disputedetailed.menu_8118126538") {
        en("Dispute Detailed")
        other("fr", "Litige détaillé")
    }
    label("disputedeadline.menu_8118126902") {
        en("Dispute Deadline")
        other("fr", "Délai de Traitement du  Litige")
    }
    label("userspayments.menu_8118127439") {
        en("Users Payments")
        other("fr", "Paiements des utilisateurs")
    }
    label("initialize_8118127660") {
        en("Initialize")
        ar("")
        other("fr", "Initialiser")
    }
    label("create_8118127670") {
        en("Next")
        ar("")
        other("fr", "Suivant")
    }
    label("new_8118127696") {
        en("New")
        ar("New")
        other("fr", "Nouveau")
    }
    label("requestapprovenewdispute_8118127701") {
        en("Request Approval")
        ar("")
        other("fr", "Demander l'approbation")
    }
    label("editnewdispute_8118127717") {
        en("Edit")
        ar("")
        other("fr", "Modifier")
    }
    label("deletenewdispute_8118127727") {
        en("Delete")
        ar("")
        other("fr", "Supprimer")
    }
    label("deletenewdisputeconfirmmsg_8118127732") {
        en("Confirmation")
        other("fr", "Confirmation")
    }
    label("deletenewdisputeconfirmdesc_8118127735") {
        en("Confirm Delete ?")
        other("fr", "Confirmation de suppression ?")
    }
    label("waitingapproval_8118127748") {
        en("Waiting Approval")
        ar("Waiting Approval")
        other("fr", "En attente d'approbation")
    }
    label("approvenewdispute_8118127753") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("rejectnewdispute_8118127785") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("finalapprove_8118127798") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("edited_8118127817") {
        en("Edited")
        ar("Edited")
        other("fr", "Édité")
    }
    label("iips_8118127822") {
        en("Fetch Transaction")
        ar("")
        other("fr", "Récupérer la transaction")
    }
    label("ach_8118127835") {
        en("Fetch Transaction")
        ar("")
        other("fr", "Récupérer la transaction")
    }
    label("save_8118127847") {
        en("Save")
        ar("")
        other("fr", "Sauvegarder")
    }
    label("saveconfirmmsg_8118127852") {
        en("Confirmation")
        other("fr", "Confirmation")
    }
    label("saveconfirmdesc_8118127855") {
        en("This dispute is expired for disputing, You can proceed with good faith process ?")
        other("fr", "Ce Litige est expiré, vous pouvez procéder avec un processus de bonne foi?")
    }
    label("delete_8118127879") {
        en("Cancel")
        ar("")
        other("fr", "Annuler")
    }
    label("deleteconfirmmsg_8118127884") {
        en("Confirmation")
        other("fr", "Confirmation")
    }
    label("deleteconfirmdesc_8118127887") {
        en("Confirm Delete ?")
        other("fr", "Confirmation de la suppression ?")
    }
    label("deleted_8118127897") {
        en("Deleted")
        ar("Deleted")
        other("fr", "Supprimé")
    }
    label("waitingreply_8118127903") {
        en("Waiting Reply")
        ar("Waiting Reply")
        other("fr", "Réponse en attente")
    }
    label("requestmoreinforamtion_8118127908") {
        en("Request More Inforamtion")
        ar("Request More Inforamtion")
        other("fr", "Demander plus d'informations")
    }
    label("requestapprove_8118127942") {
        en("Accept Chargeback")
        ar("")
        other("fr", "Accepter les chargesBack")
    }
    label("requestreject_8118127954") {
        en("Decline Chargeback")
        ar("")
        other("fr", "Refus de recharge")
    }
    label("svc_cancel_8118127975") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_approve_8118127981") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118127992") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("waitingapproval_8118127999") {
        en("Waiting Approval")
        ar("Waiting Approval")
        other("fr", "En attente d'approbation")
    }
    label("approve_8118128004") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118128028") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("requestmoreinforejected_8118128039") {
        en("Request More Info Rejected")
        ar("Request More Info Rejected")
        other("fr", "Demande d'informations rejetée")
    }
    label("modifyrequestinformation_8118128044") {
        en("Modify Request Information")
        ar("")
        other("fr", "Modifier les informations de la demande")
    }
    label("arbitrationcharges_8118128065") {
        en("Arbitration Charges")
        ar("")
        other("fr", "Frais d'arbitrage")
    }
    label("requestapprove_8118128080") {
        en("Accept Chargeback")
        ar("")
        other("fr", "Accepter les chargeback")
    }
    label("requestreject_8118128101") {
        en("Decline Chargeback")
        ar("")
        other("fr", "Refus d'un chargeback")
    }
    label("svc_cancel_8118128119") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_approve_8118128129") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118128140") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("needmoreinfromation_8118128147") {
        en("Need More Infromation")
        ar("Need More Infromation")
        other("fr", "Besoin de plus d'infromations")
    }
    label("responsetomoreinformationrequest_8118128152") {
        en("Response To More Information Request")
        ar("")
        other("fr", "Réponse à une demande d'information")
    }
    label("responsetomoreinformationapproval_8118128171") {
        en("Response To More Information Approval")
        ar("Response To More Information Approval")
        other("fr", "Approbation de la Réponse à une demande d'informations")
    }
    label("approve_8118128176") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118128202") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("rejectedresponcetomoreinfomarion_8118128213") {
        en("Rejected Responce To More Infomarion")
        ar("Rejected Responce To More Infomarion")
        other("fr", "Réponse rejetée pour une demande d'informations")
    }
    label("modifyresponse_8118128218") {
        en("Modify Response")
        ar("")
        other("fr", "Modifier la réponse")
    }
    label("rejected_8118128235") {
        en("Rejected")
        ar("Rejected")
        other("fr", "Rejeté")
    }
    label("repair_8118128240") {
        en("Repair")
        ar("")
        other("fr", "Correction")
    }
    label("repair_8118128251") {
        en("Repair")
        ar("Repair")
        other("fr", "Correction")
    }
    label("edit_8118128256") {
        en("Edit")
        ar("")
        other("fr", "Modifier")
    }
    label("requestapprove_8118128266") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("delete_8118128276") {
        en("Delete")
        ar("")
        other("fr", "Supprimer")
    }
    label("deleteconfirmmsg_8118128281") {
        en("Confirmation")
        other("fr", "Confirmation")
    }
    label("deleteconfirmdesc_8118128284") {
        en("Confirm Delete ?")
        other("fr", "Confirmation de la suppression ?")
    }
    label("repair_8118128294") {
        en("Repair")
        ar("Repair")
        other("fr", "Correction")
    }
    label("save_8118128299") {
        en("Save")
        ar("")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118128313") {
        en("Cancel")
        ar("")
        other("fr", "Annuler")
    }
    label("acceptchargeback_8118128324") {
        en("Accept Chargeback")
        ar("Accept Chargeback")
        other("fr", "Accepter les chargeback")
    }
    label("approvedispute_8118128329") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("rejectdispute_8118128366") {
        en("Reject Dispute")
        ar("")
        other("fr", "Rejeter le Litige")
    }
    label("returntomaker_8118128399") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("svc_auto_approve_8118128409") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118128420") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("declinechargeback_8118128427") {
        en("Decline Chargeback")
        ar("Decline Chargeback")
        other("fr", "Refus du chargeback")
    }
    label("approvedispute_8118128432") {
        en("Approve Dispute")
        ar("")
        other("fr", "Approuver le Litige")
    }
    label("rejectdispute_8118128470") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("returntomaker_8118128507") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("svc_auto_approve_8118128517") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118128528") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("approveddisputeadjustment_8118128535") {
        en("Approved - Dispute Adjustment")
        ar("Approved - Dispute Adjustment")
        other("fr", "Approuvé - ajustement des litiges")
    }
    label("approved_8118128541") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("requestgoodfaith_8118128546") {
        en("Request Good Faith")
        ar("")
        other("fr", "Demander la bonne foi")
    }
    label("rejected_8118128563") {
        en("Rejected")
        ar("Rejected")
        other("fr", "Rejeté")
    }
    label("represent_8118128568") {
        en("Represent")
        ar("")
        other("fr", "Représenter")
    }
    label("arbitrate_8118128591") {
        en("Arbitrate")
        ar("")
        other("fr", "Arbitrer")
    }
    label("edit_8118128611") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("submit_8118128616") {
        en("Submit")
        ar("")
        other("fr", "Soumettre")
    }
    label("cancel_8118128626") {
        en("Cancel")
        ar("")
        other("fr", "Annuler")
    }
    label("waitingapprovalrepresented_8118128637") {
        en("Waiting Approval - Represented")
        ar("Waiting Approval - Represented")
        other("fr", "Approbation en attente - représentée")
    }
    label("approve_8118128642") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118128669") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("waitingreplyrepresented_8118128683") {
        en("Waiting Reply - Represented")
        ar("Waiting Reply - Represented")
        other("fr", "Réponse en attente - représentée")
    }
    label("requestmoreinforamtion_8118128688") {
        en("Request More Inforamtion")
        ar("")
        other("fr", "Demander plus d'informations")
    }
    label("requestapprove_8118128710") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("requestreject_8118128720") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("svc_cancel_8118128730") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_approve_8118128736") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118128747") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("requestacceptance_8118128754") {
        en("Request Acceptance")
        ar("Request Acceptance")
        other("fr", "Acceptation de la Demande")
    }
    label("approvedispute_8118128759") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("rejectdispute_8118128781") {
        en("Reject Dispute")
        ar("")
        other("fr", "Rejeter le Litige")
    }
    label("returntomaker_8118128799") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("svc_auto_approve_8118128809") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118128820") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("requestrejection_8118128827") {
        en("Request Rejection")
        ar("Request Rejection")
        other("fr", "Demander le rejet")
    }
    label("approvedispute_8118128832") {
        en("Approve Dispute")
        ar("")
        other("fr", "Approuver le Litige")
    }
    label("rejectdispute_8118128855") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("returntomaker_8118128872") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("svc_auto_approve_8118128882") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118128896") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("rejectedapprovalrequest_8118128903") {
        en("Rejected Approval Request")
        ar("Rejected Approval Request")
        other("fr", "Demande d'approbation rejetée")
    }
    label("repair_8118128908") {
        en("Repair")
        ar("")
        other("fr", "Correction")
    }
    label("svc_auto_approve_8118128929") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118128943") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("rejectedapprovalrequest_8118128950") {
        en("Rejected Approval Request")
        ar("Rejected Approval Request")
        other("fr", "Demande d'approbation rejetée")
    }
    label("repair_8118128955") {
        en("Repair")
        ar("")
        other("fr", "Correction")
    }
    label("svc_auto_approve_8118128965") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("svc_auto_reject_8118128979") {
        en("SVC_CANCEL")
        ar("")
        other("fr", "Svc_cancel")
    }
    label("approved_8118128986") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("requestgoodfaith_8118128991") {
        en("Request Good Faith")
        ar("")
        other("fr", "Demander la bonne foi")
    }
    label("waitingapprovalcancellationrequest_8118129007") {
        en("Waiting Approval -Cancellation Request")
        ar("Waiting Approval -Cancellation Request")
        other("fr", "Demande d'attente d'approbation de la Cancellation")
    }
    label("svc_approve_8118129012") {
        en("SVC_APPROVE")
        ar("")
        other("fr", "Svc_approve")
    }
    label("svc_reject_8118129024") {
        en("SVC_REJECT")
        ar("")
        other("fr", "Svc_reject")
    }
    label("disputecancelled_8118129040") {
        en("Dispute Cancelled")
        ar("Dispute Cancelled")
        other("fr", "Litige annulé")
    }
    label("represent_8118129045") {
        en("Represent")
        ar("")
        other("fr", "Représenter")
    }
    label("waitingapprovalarbitrateddispute_8118129063") {
        en("Waiting Approval -Arbitrated Dispute")
        ar("Waiting Approval -Arbitrated Dispute")
        other("fr", "Approbation en attente - Clindage arbitré")
    }
    label("approve_8118129068") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118129091") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("waitingreplyarbitrateddispute_8118129105") {
        en("Waiting Reply-Arbitrated Dispute")
        ar("Waiting Reply-Arbitrated Dispute")
        other("fr", "Réponse en attente de réponse arbitrée")
    }
    label("requestmoreinforamtion_8118129110") {
        en("Request More Inforamtion")
        ar("")
        other("fr", "Demander plus d'informations")
    }
    label("arbitrationcharges_8118129135") {
        en("Arbitration Charges")
        ar("")
        other("fr", "Frais d'arbitrage")
    }
    label("requestapprove_8118129147") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("requestreject_8118129159") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("approvedarbitration_8118129176") {
        en("Approved - Arbitration")
        ar("Approved - Arbitration")
        other("fr", "Approuvé - arbitrage")
    }
    label("rejectedarbitration_8118129182") {
        en("Rejected - Arbitration")
        ar("Rejected - Arbitration")
        other("fr", "Rejeté - arbitrage")
    }
    label("requestacceptancearbitration_8118129188") {
        en("Request Acceptance - Arbitration")
        ar("Request Acceptance - Arbitration")
        other("fr", "Demande d'acceptation - arbitrage")
    }
    label("approve_8118129193") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118129217") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("requestrejectionarbitration_8118129234") {
        en("Request Rejection - Arbitration")
        ar("Request Rejection - Arbitration")
        other("fr", "Rejeter le rejet - arbitrage")
    }
    label("approvedispute_8118129239") {
        en("Approve Dispute")
        ar("")
        other("fr", "Approuver le Litige")
    }
    label("rejectdispute_8118129263") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("returntomaker_8118129283") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("rejectedapprovalrequestarbitrated_8118129294") {
        en("Rejected Approval Request- Arbitrated")
        ar("Rejected Approval Request- Arbitrated")
        other("fr", "Demande d'approbation rejetée - arbitrée")
    }
    label("repair_8118129299") {
        en("Repair")
        ar("")
        other("fr", "Correction")
    }
    label("waitingapprovalrepresented_8118129310") {
        en("Waiting Approval - Represented")
        ar("Waiting Approval - Represented")
        other("fr", "Approbation en attente - représentée")
    }
    label("approve_8118129315") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118129329") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("autoaccepted_8118129336") {
        en("Auto Accepted")
        ar("Auto Accepted")
        other("fr", "Auto accepté")
    }
    label("requestgoodfaith_8118129341") {
        en("Request Good Faith")
        ar("")
        other("fr", "Demander la bonne foi")
    }
    label("autorejected_8118129354") {
        en("Auto Rejected")
        ar("Auto Rejected")
        other("fr", "Auto rejeté")
    }
    label("approvedgoodfaithdispute_8118129360") {
        en("Approved - Good Faith Dispute")
        ar("Approved - Good Faith Dispute")
        other("fr", "Approuvé - Litige de bonne foi")
    }
    label("rejectedgoodfaithdispute_8118129366") {
        en("Rejected - Good Faith Dispute")
        ar("Rejected - Good Faith Dispute")
        other("fr", "Rejeté - Dispute de bonne foi")
    }
    label("goodfaithapproval_8118129372") {
        en("Good Faith Approval")
        ar("Good Faith Approval")
        other("fr", "Approbation de bonne foi")
    }
    label("approve_8118129377") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118129392") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("waitingreplygoodfaith_8118129404") {
        en("Waiting Reply - Good Faith")
        ar("Waiting Reply - Good Faith")
        other("fr", "Réponse en attente - Bonne foi")
    }
    label("requestapprove_8118129409") {
        en("Accept Chargeback")
        ar("")
        other("fr", "Accepter les chargeback")
    }
    label("requestreject_8118129418") {
        en("Decline Chargeback")
        ar("")
        other("fr", "Refus de recharge")
    }
    label("approvalgoodfaith_8118129428") {
        en("Approval Good Faith")
        ar("Approval Good Faith")
        other("fr", "Approbation de bonne foi")
    }
    label("approve_8118129433") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118129439") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("rejectiongoodfaith_8118129446") {
        en("Rejection Good Faith")
        ar("Rejection Good Faith")
        other("fr", "Rejet de bonne foi")
    }
    label("approve_8118129451") {
        en("Approve")
        ar("")
        other("fr", "Approuver")
    }
    label("reject_8118129466") {
        en("Reject")
        ar("")
        other("fr", "Rejeter")
    }
    label("approvedgoodfaithdispute_8118129473") {
        en("Approved - Good Faith Dispute")
        ar("Approved - Good Faith Dispute")
        other("fr", "Approuvé - Litige de bonne foi")
    }
    label("rejectedgoodfaithdispute_8118129479") {
        en("Rejected - Good Faith Dispute")
        ar("Rejected - Good Faith Dispute")
        other("fr", "Rejeté - Dispute de bonne foi")
    }
    label("initialize_8118129485") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118129491") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("waitingapproval_8118129501") {
        en("Waiting Approval")
        ar("Waiting Approval")
        other("fr", "En attente d'approbation")
    }
    label("approve_8118129506") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("reject_8118129515") {
        en("Reject")
        ar("Reject")
        other("fr", "Rejeter")
    }
    label("deleted_8118129525") {
        en("Deleted")
        ar("Deleted")
        other("fr", "Supprimé")
    }
    label("disputecanceled_8118129531") {
        en("Dispute Canceled")
        ar("Dispute Canceled")
        other("fr", "Litige annulé")
    }
    label("initialize_8118129537") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118129543") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("active_8118129550") {
        en("Active")
        ar("Active")
        other("fr", "Actif")
    }
    label("edit_8118129555") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("delete_8118129561") {
        en("Delete")
        ar("Delete")
        other("fr", "Supprimer")
    }
    label("deleteconfirmmsg_8118129566") {
        en("Confirmation")
        other("fr", "Confirmation")
    }
    label("deleteconfirmdesc_8118129569") {
        en("Confirm Delete ?")
        other("fr", "Confirmation de la suppression ?")
    }
    label("editted_8118129575") {
        en("Editted")
        ar("Editted")
        other("fr", "Éditant")
    }
    label("save_8118129580") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118129586") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("deleted_8118129593") {
        en("Deleted")
        ar("Deleted")
        other("fr", "Supprimé")
    }
    label("initialize_8118129599") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118129605") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("initialize_8118129613") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118129619") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("new_8118129626") {
        en("New")
        ar("New")
        other("fr", "Nouveau")
    }
    label("requestapprove_8118129631") {
        en("Request Approve")
        ar("Request Approve")
        other("fr", "Demander l'approbation")
    }
    label("edit_8118129637") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("delete_8118129643") {
        en("Delete")
        ar("Delete")
        other("fr", "Supprimer")
    }
    label("deleteconfirmmsg_8118129648") {
        en("Confirmation")
        other("fr", "Confirmation")
    }
    label("deleteconfirmdesc_8118129651") {
        en("Confirm Delete ?")
        other("fr", "Confirmation de la suppression ?")
    }
    label("newentrywaitingapproval_8118129657") {
        en("New Entry - Waiting Approval")
        ar("New Entry - Waiting Approval")
        other("fr", "Nouvelle entrée - Approbation en attente")
    }
    label("approve_8118129662") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("reject_8118129671") {
        en("Reject")
        ar("Reject")
        other("fr", "Rejeter")
    }
    label("edited_8118129678") {
        en("Edited")
        ar("Edited")
        other("fr", "Édité")
    }
    label("save_8118129683") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118129689") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("deleted_8118129696") {
        en("Deleted")
        ar("Deleted")
        other("fr", "Supprimé")
    }
    label("active_8118129702") {
        en("Active")
        ar("Active")
        other("fr", "Actif")
    }
    label("modify_8118129707") {
        en("Modify")
        ar("Modify")
        other("fr", "Modifier")
    }
    label("delete_8118129713") {
        en("Delete")
        ar("Delete")
        other("fr", "Supprimer")
    }
    label("modifiedrejected_8118129720") {
        en("Modified - Rejected")
        ar("Modified - Rejected")
        other("fr", "Modifié - rejeté")
    }
    label("repair_8118129725") {
        en("Repair")
        ar("Repair")
        other("fr", "Correction")
    }
    label("modified_8118129732") {
        en("Modified")
        ar("Modified")
        other("fr", "Modifié")
    }
    label("save_8118129737") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118129743") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("deletionapproval_8118129750") {
        en("Deletion Approval")
        ar("Deletion Approval")
        other("fr", "Approbation de suppression")
    }
    label("approve_8118129755") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("reject_8118129761") {
        en("Reject")
        ar("Reject")
        other("fr", "Rejeter")
    }
    label("modified_8118129768") {
        en("Modified")
        ar("Modified")
        other("fr", "Modifié")
    }
    label("save_8118129773") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118129779") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("newentryrejected_8118129786") {
        en("New Entry - Rejected")
        ar("New Entry - Rejected")
        other("fr", "Nouvelle entrée - Rejeté")
    }
    label("repair_8118129791") {
        en("Repair")
        ar("Repair")
        other("fr", "Correction")
    }
    label("modifiedwaitingapproval_8118129798") {
        en("Modified - Waiting Approval")
        ar("Modified - Waiting Approval")
        other("fr", "Modifié - Approbation en attente")
    }
    label("approve_8118129803") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("reject_8118129809") {
        en("Reject")
        ar("Reject")
        other("fr", "Rejeter")
    }
    label("repair_8118129816") {
        en("Repair")
        ar("Repair")
        other("fr", "Correction")
    }
    label("requestapprove_8118129821") {
        en("Request Approve")
        ar("Request Approve")
        other("fr", "Demander l'approbation")
    }
    label("edit_8118129827") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("delete_8118129833") {
        en("Delete")
        ar("Delete")
        other("fr", "Supprimer")
    }
    label("deleteconfirmmsg_8118129838") {
        en("Confirmation")
        other("fr", "Confirmation")
    }
    label("deleteconfirmdesc_8118129841") {
        en("Confirm Delete ?")
        other("fr", "Confirmation de la suppression ?")
    }
    label("edited_8118129847") {
        en("Edited")
        ar("Edited")
        other("fr", "Édité")
    }
    label("save_8118129852") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118129858") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("initialize_8118129865") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118129871") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("newauthorization_8118129878") {
        en("New Authorization")
        ar("New Authorization")
        other("fr", "Nouvelle autorisation")
    }
    label("approve_8118129883") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("reject_8118129889") {
        en("Reject")
        ar("Reject")
        other("fr", "Rejeter")
    }
    label("repairnew_8118129896") {
        en("Repair New")
        ar("Repair New")
        other("fr", "Réparer les nouveaux")
    }
    label("edit_8118129901") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("delete_8118129907") {
        en("Delete")
        ar("Delete")
        other("fr", "Supprimer")
    }
    label("editnew_8118129914") {
        en("Edit New")
        ar("Edit New")
        other("fr", "Modifier le nouveau")
    }
    label("save_8118129919") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118129925") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("approved_8118129932") {
        en("Approved")
        ar("Approved")
        other("fr", "Approuvé")
    }
    label("edit_8118129937") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("delete_8118129943") {
        en("Delete")
        ar("Delete")
        other("fr", "Supprimer")
    }
    label("modify_8118129950") {
        en("Modify")
        ar("Modify")
        other("fr", "Modifier")
    }
    label("save_8118129955") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118129961") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("authorization_8118129968") {
        en("Authorization")
        ar("Authorization")
        other("fr", "Autorisation")
    }
    label("approve_8118129973") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("reject_8118129979") {
        en("Reject")
        ar("Reject")
        other("fr", "Rejeter")
    }
    label("repairrejected_8118129986") {
        en("Repair Rejected")
        ar("Repair Rejected")
        other("fr", "Correction rejetée")
    }
    label("edit_8118129991") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("reset_8118129997") {
        en("Reset")
        ar("Reset")
        other("fr", "Réinitialiser")
    }
    label("editrejected_8118130004") {
        en("Edit Rejected")
        ar("Edit Rejected")
        other("fr", "Modifier rejeté")
    }
    label("save_8118130009") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118130015") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("deletionauthorization_8118130022") {
        en("Deletion Authorization")
        ar("Deletion Authorization")
        other("fr", "Autorisation de suppression")
    }
    label("approve_8118130027") {
        en("Approve")
        ar("Approve")
        other("fr", "Approuver")
    }
    label("approveconfirmmsg_8118130032") {
        en("Delete Item")
        other("fr", "Effacer l'élément")
    }
    label("approveconfirmdesc_8118130035") {
        en("Are you sure you want to delete this item?")
        other("fr", "Êtes-vous sûr de bien vouloir supprimer cet élément?")
    }
    label("reject_8118130040") {
        en("Reject")
        ar("رفض")
        other("fr", "Rejeter")
    }
    label("deleted_8118130047") {
        en("Deleted")
        ar("Deleted")
        other("fr", "Supprimé")
    }
    label("initialize_8118130053") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118130059") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("inactive_8118130066") {
        en("Inactive")
        ar("Inactive")
        other("fr", "Inactif")
    }
    label("requestactivation_8118130071") {
        en("Request Activation")
        ar("Request Activation")
        other("fr", "Demander l'activation")
    }
    label("edit_8118130077") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("pendingforactivation_8118130084") {
        en("Pending For Activation")
        ar("Pending For Activation")
        other("fr", "En attente d'activation")
    }
    label("approveactivation_8118130089") {
        en("Approve Activation")
        ar("Approve Activation")
        other("fr", "Approuver l'activation")
    }
    label("rejectactivation_8118130095") {
        en("Reject Activation")
        ar("Reject Activation")
        other("fr", "Rejeter l'activation")
    }
    label("active_8118130102") {
        en("Active")
        ar("Active")
        other("fr", "Actif")
    }
    label("repair_8118130107") {
        en("Repair")
        ar("Repair")
        other("fr", "Correction")
    }
    label("disable_8118130113") {
        en("Disable")
        ar("Disable")
        other("fr", "Désactiver")
    }
    label("pendingdisabling_8118130120") {
        en("Pending Disabling")
        ar("Pending Disabling")
        other("fr", "En attente de désactivation")
    }
    label("approvedisabling_8118130125") {
        en("Approve Disabling")
        ar("Approve Disabling")
        other("fr", "Approuver la désactivation")
    }
    label("rejectdisabling_8118130131") {
        en("Reject Disabling")
        ar("Reject Disabling")
        other("fr", "Rejeter la désactivation")
    }
    label("disabled_8118130138") {
        en("Disabled")
        ar("Disabled")
        other("fr", "Désactivé")
    }
    label("reactive_8118130143") {
        en("Reactive")
        ar("Reactive")
        other("fr", "Réactif")
    }
    label("editing_8118130150") {
        en("Editing")
        ar("Editing")
        other("fr", "Editer")
    }
    label("save_8118130155") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118130161") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("reset_8118130167") {
        en("Reset")
        ar("Reset")
        other("fr", "Réinitialiser")
    }
    label("initialize_8118130174") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118130180") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("inactive_8118130187") {
        en("Inactive")
        ar("Inactive")
        other("fr", "Inactif")
    }
    label("requestactivation_8118130192") {
        en("Request Activation")
        ar("Request Activation")
        other("fr", "Demander l'activation")
    }
    label("edit_8118130198") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("pendingforactivation_8118130205") {
        en("Pending For Activation")
        ar("Pending For Activation")
        other("fr", "En attente d'activation")
    }
    label("approveactivation_8118130210") {
        en("Approve Activation")
        ar("Approve Activation")
        other("fr", "Approuver l'activation")
    }
    label("rejectactivation_8118130216") {
        en("Reject Activation")
        ar("Reject Activation")
        other("fr", "Rejeter l'activation")
    }
    label("active_8118130223") {
        en("Active")
        ar("Active")
        other("fr", "Actif")
    }
    label("repair_8118130228") {
        en("Repair")
        ar("Repair")
        other("fr", "Correction")
    }
    label("disable_8118130234") {
        en("Disable")
        ar("Disable")
        other("fr", "Désactiver")
    }
    label("pendingdisabling_8118130241") {
        en("Pending Disabling")
        ar("Pending Disabling")
        other("fr", "En attente de désactivation")
    }
    label("approvedisabling_8118130246") {
        en("Approve Disabling")
        ar("Approve Disabling")
        other("fr", "Approuver la désactivation")
    }
    label("rejectdisabling_8118130252") {
        en("Reject Disabling")
        ar("Reject Disabling")
        other("fr", "Rejeter la désactivation")
    }
    label("disabled_8118130259") {
        en("Disabled")
        ar("Disabled")
        other("fr", "Désactivé")
    }
    label("reactive_8118130264") {
        en("Reactive")
        ar("Reactive")
        other("fr", "Réactif")
    }
    label("editing_8118130271") {
        en("Editing")
        ar("Editing")
        other("fr", "Montage")
    }
    label("save_8118130276") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("delete_8118130282") {
        en("Delete")
        ar("Delete")
        other("fr", "Supprimer")
    }
    label("cancel_8118130288") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("reset_8118130294") {
        en("Reset")
        ar("Reset")
        other("fr", "Réinitialiser")
    }
    label("pendingdeletion_8118130301") {
        en("Pending Deletion")
        ar("Pending Deletion")
        other("fr", "Suppression en attente")
    }
    label("approvedeletion_8118130306") {
        en("Approve Deletion")
        ar("Approve Deletion")
        other("fr", "Approuver la suppression")
    }
    label("rejectdeletion_8118130312") {
        en("Reject Deletion")
        ar("Reject Deletion")
        other("fr", "Rejeter la suppression")
    }
    label("deleted_8118130319") {
        en("Deleted")
        ar("Deleted")
        other("fr", "Supprimé")
    }
    label("initialize_8118130325") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118130331") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("edit_8118130338") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("save_8118130345") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118130351") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("initialize_8118130358") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118130364") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("created_8118130371") {
        en("Created")
        ar("Created")
        other("fr", "Créé")
    }
    label("generate_8118130376") {
        en("Generate")
        ar("Generate")
        other("fr", "Générer")
    }
    label("readyforsubmission_8118130387") {
        en("Ready for Submission")
        ar("Ready for Submission")
        other("fr", "Prêt pour la soumission")
    }
    label("submit_8118130392") {
        en("Submit")
        ar("Submit")
        other("fr", "Soumettre")
    }
    label("regenerate_8118130401") {
        en("Regenerate")
        ar("Regenerate")
        other("fr", "Régénérer")
    }
    label("acceptmanually_8118130410") {
        en("Accept Manually")
        ar("Accept Manually")
        other("fr", "Accepter manuellement")
    }
    label("svc_accept_8118130419") {
        en("SVC_Accept")
        ar("SVC_Accept")
        other("fr", "Svc_accept")
    }
    label("submitted_8118130426") {
        en("Submitted")
        ar("Submitted")
        other("fr", "Soumis")
    }
    label("resubmit_8118130431") {
        en("Resubmit")
        ar("Resubmit")
        other("fr", "Soumettre")
    }
    label("regenerate_8118130439") {
        en("Regenerate")
        ar("Regenerate")
        other("fr", "Régénérer")
    }
    label("acceptmanually_8118130448") {
        en("Accept Manually")
        ar("Accept Manually")
        other("fr", "Accepter manuellement")
    }
    label("svc_accept_8118130456") {
        en("SVC_Accept")
        ar("SVC_Accept")
        other("fr", "Svc_accept")
    }
    label("svc_reject_8118130462") {
        en("SVC_Reject")
        ar("SVC_Reject")
        other("fr", "Svc_reject")
    }
    label("svc_fail_8118130468") {
        en("SVC_Fail")
        ar("SVC_Fail")
        other("fr", "Svc_fail")
    }
    label("rejected_8118130475") {
        en("Rejected")
        ar("Rejected")
        other("fr", "Rejeté")
    }
    label("resubmit_8118130480") {
        en("Resubmit")
        ar("Resubmit")
        other("fr", "Soumettre")
    }
    label("regenerate_8118130488") {
        en("Regenerate")
        ar("Regenerate")
        other("fr", "Régénérer")
    }
    label("acceptmanually_8118130497") {
        en("Accept Manually")
        ar("Accept Manually")
        other("fr", "Accepter manuellement")
    }
    label("failed_8118130506") {
        en("Failed")
        ar("Failed")
        other("fr", "Échec")
    }
    label("resubmit_8118130511") {
        en("Resubmit")
        ar("Resubmit")
        other("fr", "Soumettre")
    }
    label("regenerate_8118130519") {
        en("Regenerate")
        ar("Regenerate")
        other("fr", "Régénérer")
    }
    label("acceptmanually_8118130528") {
        en("Accept Manually")
        ar("Accept Manually")
        other("fr", "Accepter manuellement")
    }
    label("accepted_8118130537") {
        en("Accepted")
        ar("Accepted")
        other("fr", "Accepté")
    }
    label("manuallyaccepted_8118130543") {
        en("Manually Accepted")
        ar("Manually Accepted")
        other("fr", "Accepté manuellement")
    }
    label("initialize_8118130549") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118130555") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("waitingactivation_8118130562") {
        en("Waiting Activation")
        ar("Waiting Activation")
        other("fr", "Activation en attente")
    }
    label("requestactivation_8118130567") {
        en("Request Activation")
        ar("Request Activation")
        other("fr", "Demander l'activation")
    }
    label("edit_8118130573") {
        en("Edit")
        ar("Edit")
        other("fr", "Modifier")
    }
    label("pendingforactivation_8118130580") {
        en("Pending For Activation")
        ar("Pending For Activation")
        other("fr", "En attente d'activation")
    }
    label("approveactivation_8118130585") {
        en("Approve Activation")
        ar("Approve Activation")
        other("fr", "Approuver l'activation")
    }
    label("rejectactivation_8118130594") {
        en("Reject Activation")
        ar("Reject Activation")
        other("fr", "Rejeter l'activation")
    }
    label("active_8118130601") {
        en("Active")
        ar("Active")
        other("fr", "Actif")
    }
    label("repair_8118130606") {
        en("Repair")
        ar("تعديل")
        other("fr", "Correction")
    }
    label("editing_8118130613") {
        en("Editing")
        ar("Editing")
        other("fr", "Montage")
    }
    label("save_8118130618") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("cancel_8118130624") {
        en("Cancel")
        ar("Cancel")
        other("fr", "Annuler")
    }
    label("reset_8118130630") {
        en("Reset")
        ar("Reset")
        other("fr", "Réinitialiser")
    }
    label("initialize_8118130637") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118130643") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("created_8118130650") {
        en("Created")
        ar("Created")
        other("fr", "Créé")
    }
    label("svc_generate_8118130655") {
        en("SVC_Generate")
        ar("SVC_Generate")
        other("fr", "Svc_generate")
    }
    label("generated_8118130665") {
        en("Generated")
        ar("Generated")
        other("fr", "Généré")
    }
    label("initialize_8118130671") {
        en("Initialize")
        ar("Initialize")
        other("fr", "Initialiser")
    }
    label("create_8118130677") {
        en("Create")
        ar("Create")
        other("fr", "Créer")
    }
    label("created_8118130684") {
        en("Created")
        ar("Created")
        other("fr", "Créé")
    }
    label("svc_generate_8118130689") {
        en("SVC_Generate")
        ar("SVC_Generate")
        other("fr", "Svc_generate")
    }
    label("generated_8118130699") {
        en("Generated")
        ar("Generated")
        other("fr", "Généré")
    }
    label("add.new.item") {
        en("Add New Item")
        ar("Add New Item")
        other("fr", "Ajouter un nouvel élément")
    }
    label("dmsncb.menu_8118121172") {
        en("National Central Banks")
        ar("البنك المركزي الوطني")
        other("fr", "Directions Nationales")
    }
    label("dms_ncb.info") {
        en("National Central Bank")
        ar("البنك المركزي الوطني")
        other("fr", "Direction Nationale")
    }
    label("dms_ncb.code") {
        en("Code")
        ar("الرمز")
        other("fr", "Code")
    }
    label("dms_ncb.name") {
        en("Name")
        ar("الاسم")
        other("fr", "Nom")
    }
    label("dms_ncb.description") {
        en("Description")
        ar("الوصف")
        other("fr", "Description")
    }
    label("dms_ncb.country") {
        en("Country")
        ar("الدولة")
        other("fr", "Pays")
    }
    label("dms_nationalcentralbank.view") {
        en("National Central Banks")
        ar("البنوك المركزية الوطنية")
        other("fr", "Directions Nationales")
    }
    label("dms_ncb.country_id") {
        en("Country ID")
        ar("رقم الدولة")
        other("fr", "ID de pays")
    }
    label("dms_ncb.country_code") {
        en("Country Code")
        ar("رمز الدولة")
        other("fr", "Code postal")
    }
    label("dms_ncb.country_name") {
        en("Country Name")
        ar("اسم الدولة")
        other("fr", "Nom du pays")
    }
    label("dms_ncb.country_codeNamePair") {
        en("Country")
        ar("الدولة")
        other("fr", "Pays")
    }
    label("ncb_id") {
        en("National Central Bank")
        ar("البنك المركزي الوطني")
        other("fr", "Direction Nationale")
    }
    label("sys_config_view") {
        en("System Configuration")
        ar("اعدادات النظام")
        other("fr", "Configuration du système")
    }
    label("sys_config_info") {
        en("System Configuration")
        ar("اعدادات النظام")
        other("fr", "Configuration du système")
    }
    label("sys_config_key") {
        en("Key")
        ar("الرمز")
        other("fr", "Clé")
    }
    label("sys_config_value") {
        en("Value")
        ar("القيمة")
        other("fr", "Valeur")
    }
    label("sys_config_menu") {
        en("System Configuration")
        ar("اعدادات النظام")
        other("fr", "Configuration du système")
    }
    label("dispute.menu") {
        en("Disputes")
        ar("النزاعات")
        other("fr", "Litiges")
    }
    label("case_management.menu") {
        en("Case Management")
        other("fr", "Gestions des Litiges")
    }
    label("disputesenttodfnt") {
        en("Submit To Assignee Bank")
        ar("التقديم للبنك المدعى عليه")
        other("fr", "Soumettre à la banque défenderesse")
    }
    label("reply_8118127697") {
        en("Reply")
        ar("رد")
        other("fr", "Répondre")
    }
    label("approveacceptance_8118129799") {
        en("Approve Accepted Dispute")
        ar("موافقة على القضية المقبولة")
        other("fr", "Approuver le litige accepté")
    }
    label("rejectacceptance_8118129800") {
        en("Reject Accepted Dispute")
        ar("رفض القضية المقبولة")
        other("fr", "Rejeter le Litige accepté")
    }
    label("approverejection_8118129800") {
        en("Approve Rejected Dispute")
        ar("موافقة على القضية المرفوضة")
        other("fr", "Approuver le litige rejeté")
    }
    label("declinerejection_8118129800") {
        en("Decline Rejected Dispute")
        ar("عدم الموافقة على القضية المرفوضة")
        other("fr", "Décliner le litige rejeté")
    }
    label("accept_8118129672") {
        en("Accept")
        ar("Accept")
        other("fr", "Accepter")
    }
    label("disputeapproved_8118129801") {
        en("Dispute Approved")
        ar("طلب قضية مقبول")
        other("fr", "Litige approuvé")
    }
    label("disputedeclined_8118129904") {
        en("Dispute Declined")
        ar("طلب قضية مرفوض")
        other("fr", "Le Litige Décliné")
    }
    label("replyrepair_8118129905") {
        en("Reply Repair")
        ar("تعديل الرد")
        other("fr", "Correction de réponse")
    }
    label("accepteddispute_8118129701") {
        en("Accepted Dispute - Waiting Approval")
        ar("قضية مقبولة - بانتظار الموافقة")
        other("fr", "Dispute acceptée - Approbation en attente")
    }
    label("rejecteddispute_8118129702") {
        en("Rejected Dispute - Waiting Approval")
        ar("قضية مرفوضة - بانتظار الموافقة")
        other("fr", "Dispute rejeté - Approbation en attente")
    }
    label("JFW_WF_ACTION.*********") {
        en("SVC_Accepted")
        ar("SVC_Accepted")
        other("fr", "Svc_accepted")
    }
    label("JFW_WF_ACTION.*********") {
        en("SVC_Rejected")
        ar("SVC_Rejected")
        other("fr", "SVC_RECTÉ")
    }
    label("ask_claimant_bank_8118129671") {
        en("Ask Assigner Bank")
        ar("Ask Assigner Bank")
        other("fr", "Demander au participant requérant")
    }
    label("approveadditionalinforequest_8118129800") {
        en("Approve Request")
        ar("موافقة على الطلب")
        other("fr", "Approuver la demande")
    }
    label("declinerequest_8118129801") {
        en("Decline Request")
        ar("رفض الطلب")
        other("fr", "Déclin de la demande")
    }
    label("additionalinforequest_8118129705") {
        en("Request Additional Info - Waiting Approval")
        ar("طلب معلومات إضافية-بانتظار الموافقة")
        other("fr", "Demander des informations supplémentaires - Approbation en attente")
    }
    label("sendrequest_8118129684") {
        en("Send Request")
        ar("إرسال الطلب")
        other("fr", "Envoyer une demande")
    }
    label("JFW_WF_ACTION.*********") {
        en("SVC_MoreInfo")
        ar("SVC_MoreInfo")
        other("fr", "Svc_moreinfo")
    }
    label("claimant.additional_info_required") {
        en("Additional Info Required")
        ar("مطلوب معلومات اضافية")
        other("fr", "Informations supplémentaires requises")
    }
    label("arbitratetodisputecase_8118121000") {
        en("Arbitrate Dispute Case")
        ar("Arbitrate Dispute Case")
        other("fr", "Arbitrage des litiges")
    }
    label("arbitratewaitingapproval_8118121001") {
        en("Arbitrate Dispute - Waiting Approval")
        ar("Arbitrate Dispute - Waiting Approval")
        other("fr", "Litige arbitré - Approbation en attente")
    }
    label("toapprovearbitrate_8118121004") {
        en("Dispute Arbitrated")
        ar("Dispute Arbitrated")
        other("fr", "Litige arbitré")
    }
    label("newdispute_8118129906") {
        en("New Dispute")
        ar("قضية جديدة")
        other("fr", "Nouveau Litige")
    }
    label("JFW_WF_ACTION.185313000") {
        en("SVC_Declined_Dispute")
        ar("SVC_Declined_Dispute")
        other("fr", "Svc_declined_dispute")
    }
    label("JFW_WF_ACTION.185313001") {
        en("SVC_Approved_Dispute")
        ar("SVC_Approved_Dispute")
        other("fr", "Svc_approved_dispute")
    }
    label("JFW_WF_ACTION.185313002") {
        en("SVC_More_Info")
        ar("SVC_More_Info")
        other("fr", "Svc_more_info")
    }
    label("rejecteddispute_8118129902") {
        en("Rejected Dispute - Waiting Approval")
        ar("قضية مرفوضة - بانتظار الموافقة")
        other("fr", "Litige rejeté - Approbation en attente")
    }
    label("disputeapproved_8118129904") {
        en("Dispute Approved")
        ar("طلب قضية مقبول")
        other("fr", "Litige approuvé")
    }
    label("disputedeclined_8118129905") {
        en("Dispute Declined")
        ar("طلب قضية مرفوض")
        other("fr", "Le Litige a diminué")
    }
    label("disputedeclined_8118129906") {
        en("Dispute Declined")
        ar("طلب قضية مرفوض")
        other("fr", "Le Litige Decliné")
    }
    label("closedisputerequest_8118129730") {
        en("Close Dispute Request - Waiting Approval")
        ar("طلب اغلاق النزاع - بانتظار الموافقة")
        other("fr", "Fermer la demande de litige - Approbation en attente")
    }
    label("closedispute_8118129731") {
        en("Close Dispute")
        ar("Close Dispute")
        other("fr", "Clôture")
    }
    label("disputeclosed_8118129732") {
        en("Dispute Closed")
        ar("نزاع مغلق")
        other("fr", "Litige fermé")
    }
    label("JFW_WF_ACTION.185313307") {
        en("SVC_CloseDispute")
        ar("SVC_CloseDispute")
        other("fr", "Svc_closesispute")
    }
    label("JFW_WF_ACTION.1853140210") {
        en("SVC_AdjustedEntries")
        ar("SVC_AdjustedEntries")
        other("fr", "SVC_ADJUSTEDENTRIES")
    }
    label("newdispute_8118199000") {
        en("New Dispute")
        ar("قضية جديدة")
        other("fr", "Nouveau Litige")
    }
    label("decline_8118129801") {
        en("Decline")
        ar("رفض")
        other("fr", "Décliner")
    }
    label("disputeapproved_8118120000") {
        en("Dispute Approved")
        ar("")
        other("fr", "Litige approuvé")
    }
    label("disputedeclined_8118120000") {
        en("Dispute Declined")
        ar("")
        other("fr", "Le Litige Decliné")
    }
    label("arbitrateddispute_8118129105") {
        en("Arbitrated Dispute")
        ar("Arbitrated Dispute")
        other("fr", "Litige arbitré")
    }
    label("ask.defendant") {
        en("Ask Assignee")
        ar("Ask Assignee")
        other("fr", "Demander à l'accusé")
    }
    label("ask.ncb.defendant.waiting.approval") {
        en("Ask Assignee - Waiting Approval")
        ar("Ask Assignee - Waiting Approval")
        other("fr", "Demande au défendeur - approbation en attente")
    }
    label("approved-request") {
        en("Approved Request")
        ar("Approved Request")
        other("fr", "Demande approuvée")
    }
    label("send_8118129683") {
        en("Send")
        ar("Send")
        other("fr", "Envoyer")
    }
    label("repair_8118129662") {
        en("Repair")
        ar("Repair")
        other("fr", "Correction")
    }
    label("proceed_8118129662") {
        en("Proceed")
        ar("Proceed")
        other("fr", "Procéder")
    }
    label("notes_8118121165") {
        en("Note")
        ar("Note")
        other("fr", "Note")
    }
    label("notesaction_8118121165") {
        en("Add Note")
        ar("Add Note")
        other("fr", "Ajouter une note")
    }
    label("sla_config_party") {
        en("Party")
        ar("Party")
        other("fr", "Faire la Partie pénalisée")
    }
    label("sla_config_payment_system") {
        en("Payment System")
        ar("Payment System")
        other("fr", "Système de paiement")
    }
    label("sla_config_stage") {
        en("Stage")
        ar("Stage")
        other("fr", "Etape")
    }
    label("sla_config_max_days") {
        en("Max Days")
        ar("Max Days")
        other("fr", "Jours maximum")
    }
    label("sla_config_automatic_action") {
        en("Automatic Action")
        ar("Automatic Action")
        other("fr", "Action automatique")
    }
    label("sla_config_urgency") {
        en("Urgency")
        ar("Urgency")
        other("fr", "Niveau d'Urgence")
    }
    label("sla_config_view") {
        en("SLA Configuration")
        ar("SLA Configuration")
        other("fr", "Configuration SLA")
    }
    label("sla_config_info") {
        en("SLA Configuration")
        ar("SLA Configuration")
        other("fr", "Configuration SLA")
    }
    label("svc_sla_reject") {
        en("SVC_SLA_Rejected")
        ar("SVC_SLA_Rejected")
        other("fr", "Svc_sla_reject")
    }
    label("svc_sla_accept") {
        en("SVC_SLA_Accepted")
        ar("SVC_SLA_Accepted")
        other("fr", "Svc_sla_accepted")
    }
}

