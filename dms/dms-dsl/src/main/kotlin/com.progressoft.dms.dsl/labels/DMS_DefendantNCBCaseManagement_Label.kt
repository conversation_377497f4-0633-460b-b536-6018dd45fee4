package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

val DMS_DefendantNCBCaseManagement_Label = Labels {
    label("dms_defendantNCBCaseManagement.view") {
        en("Assignee NCB Cases")
        ar("Assignee NCB Cases")
        other("fr", "Cas du défendeur NCB")
    }
    label("dms_defendantNCBCaseManagement.note") {
        en("Note")
        ar("Note")
        other("fr", "Note")
    }
    label("dms_defendantNCBCaseManagement.id") {
        en("Id")
        ar("Id")
        other("fr", "Identifiant")
    }
    label("dms_defendantNCBCaseManagement.disputeCase") {
        en("Dispute Case")
        ar("Dispute Case")
        other("fr", "Litige")
    }
    label("dms_defendantNCBCaseManagement.caseInfo") {
        en("Case Info")
        ar("Case Info")
        other("fr", "Informations sur le cas")
    }
    label("defendant.ncb.moreinforequired") {
        en("Additional Info Required")
        ar("مطلوب معلومات اضافية")
        other("fr", "Informations supplémentaires requises")
    }
    label("newdispute_8118129906") {
        en("New Dispute")
        ar("قضية جديدة")
        other("fr", "Nouveau différend")
    }
    label("ask.defendant.bank") {
        en("Ask Assignee Bank")
        ar("Ask Assignee Bank")
        other("fr", "Demandez à la banque défenderesse")
    }
    label("ask.claimant.ncb") {
        en("Ask Assigner NCB")
        ar("Ask Assigner NCB")
        other("fr", "Demandez au demandeur NCB")
    }
    label("dfndnt_ncb_more_info_provided") {
        en("Arbitrated Dispute - More Info Provided")
        ar("القضية المعروضة - تم تقديم المعلومات الاضافية")
        other("fr", "Dispute arbitré - Plus d'informations fournies")
    }
    label("JFW_WF_ACTION.**********") {
        en("SVC_Approved_RePresent")
        ar("SVC_Approved_RePresent")
        other("fr", "Svc_approved_represent")
    }
    label("defendant.ncb.represented") {
        en("Represented")
        ar("تمت اعادة التمثيل")
        other("fr", "Représentée")
    }
    label("JFW_WF_ACTION.*********") {
        en("SVC_Operator_MoreInfo")
        ar("SVC_Operator_MoreInfo")
        other("fr", "SVC_Operator_moreinfo")
    }
    label("defendant.ncb.approverequest") {
        en("Approve Request")
        ar("موافقة على الطلب")
        other("fr", "Approuver la demande")
    }
    label("JFW_WF_ACTION.185399902") {
        en("SVC_Approve")
        ar("SVC_Approve")
        other("fr", "Svc_approve")
    }
    label("JFW_WF_ACTION.185399903") {
        en("SVC_Decline")
        ar("SVC_Decline")
        other("fr", "Svc_decline")
    }
    label("JFW_WF_ACTION.185399904") {
        en("SVC_Operator_MoreInfo")
        ar("SVC_Operator_MoreInfo")
        other("fr", "SVC_Operator_moreinfo")
    }
    label("defendant.more_info_submitted") {
        en("More Info - Submitted To Operator")
        ar("More Info - Submitted To Operator")
        other("fr", "Plus d'informations - soumis à l'opérateur")
    }
    label("defendant.more_info_waiting_approval") {
        en("More Info - Waiting Approval")
        ar("More Info - Waiting Approval")
        other("fr", "Plus d'informations - Approbation d'attente")
    }
    label("defendant.saveandsubmit") {
        en("Save")
        ar("Save")
        other("fr", "Sauvegarder")
    }
    label("defendant.provide_additional_info") {
        en("Provide Additional Info")
        ar("Provide Additional Info")
        other("fr", "Fournir des informations supplémentaires")
    }
    label("defendant.provide-additional-info") {
        en("Provide More Information")
        ar("Provide More Information")
        other("fr", "Fournir plus d'informations")
    }
    label("dfndnt_more_info_rejected") {
        en("Request More Info - Rejected")
        ar("Request More Info - Rejected")
        other("fr", "Demander plus d'informations - rejeté")
    }
    label("defendant.ncb.approverequest") {
        en("Approve Request")
        ar("موافقة على الطلب")
        other("fr", "Approuver la demande")
    }
    label("defendant.ncb.approverequest") {
        en("Approve Request")
        ar("موافقة على الطلب")
        other("fr", "Approuver la demande")
    }
    label("def_req_add_info_from_claimant") {
        en("Request Additional Info")
        ar("موافقة على الطلب")
        other("fr", "Demander des informations supplémentaires")
    }
    label("req_add_info_submitted") {
        en("Request Additional Info - Waiting Approval")
        ar("موافقة على الطلب")
        other("fr", "Demander des informations supplémentaires - en attente d'approbation")
    }
    label("add_info_request_approved") {
        en("Request Additional Info - Approved")
        ar("موافقة على الطلب")
        other("fr", "Demander des informations supplémentaires - Approuvé")
    }
}
