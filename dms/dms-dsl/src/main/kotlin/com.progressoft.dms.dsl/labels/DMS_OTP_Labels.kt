package com.progressoft.dms.dsl.labels

import com.progressoft.jupiter.kotlin.dsl.Labels

var DMS_OTP_Labels = Labels {
    label("login.inactive.tenant") {
        en("Inactive Tenant")
        other("fr", "Locataire inactif")
    }
    label("otp.not.matched") {
        en("OTP Not Matched")
        other("fr", "OTP ne correspond pas")
    }
    label("otp.expired") {
        en("OTP Expired")
        other("fr", "OTP expiré")
    }
    label("otp.max.validation.attempts.reached") {
        en("OTP Max Validation Attempts Reached")
        other("fr", "Tentatives de validation OTP maximales atteintes")
    }
    label("otp.max.resend.attempts.reached") {
        en("OTP Max Resend Attempts Reached")
        other("fr", "Tentatives de renvoi OTP maximales atteintes")
    }
    label("otp.internal.system.error") {
        en("INTERNAL SYSTEM ERROR")
        other("fr", "ERREUR DU SYSTÈME INTERNE")
    }
    label("otp.form.title") {
        en("Enter The OTP Code")
        other("fr", "Entrez le code OTP")
    }
    label("otp.resend.timer.message") {
        en("Didn't Receive it? You can Resend the OTP after")
        other("fr", "Vous ne l'avez pas reçu ? Vous pouvez renvoyer l'OTP après")
    }
    label("otp.resend.timer.message.seconds") {
        en("seconds")
        other("fr", "secondes")
    }
    label("otp.resend.button") {
        en("Resend")
        other("fr", "Renvoyer")
    }
    label("otp.submit.button") {
        en("Submit")
        other("fr", "Soumettre")
    }
    label("otp.required.message") {
        en("Please Fill The OTP")
        other("fr", "Veuillez remplir l'OTP")
    }
    label("otp.redirect.to.login.message") {
        en("You will be redirected to the login page within 10 seconds")
        other("fr", "Vous serez redirigé vers la page de connexion dans les 10 secondes")
    }
}

