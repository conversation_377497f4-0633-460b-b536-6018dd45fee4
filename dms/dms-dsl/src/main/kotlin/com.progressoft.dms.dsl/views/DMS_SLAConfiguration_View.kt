package com.progressoft.dms.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val DMS_SLAConfiguration_View = View {
    name = "DMS_SLAConfiguration.View"
    workflowName = "WF_DMS_Edit"
    label = "sla_config_view"
    entity = "com.progressoft.dms.entities.DMS_SLAConfiguration"
    idProperty = "id"
    ordering = "id:DESC"
    portal {
        column {
            width = 8
            portlet {
                label = "sla_config_info"
                form {
                    label = "sla_config_info"
                    row {
                        comboBox {
                            label = "sla_config_party"
                            propertyPath = "slaConfigurationParty"
                            filling = Filling.REQUIRED
                        }
                        comboBox {
                            label = "sla_config_payment_system"
                            propertyPath = "paymentSystem"
                            filling = Filling.REQUIRED
                        }
                    }
                    row {
                        text {
                            label = "sla_config_stage"
                            propertyPath = "stage"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 200
                        }
                        number {
                            label = "sla_config_max_days"
                            propertyPath = "maxDays"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 200
                        }
                    }
                    row {
                        radioGroup {
                            label = "sla_config_urgency"
                            propertyPath = "urgency"
                            filling = Filling.READ_ONLY
                            option {
                                label = "dms_disputeCase.urgency.normal"
                                value = "Normal"
                            }
                            option {
                                label = "dms_disputeCase.urgency.urgent"
                                value = "Urgent"
                            }
                        }
                    }
                    row {
                        comboBox {
                            label = "sla_config_automatic_action"
                            propertyPath = "slaConfigAutomaticAction"
                            filling = Filling.REQUIRED
                        }
                    }
                }
            }
        }
    }
}