package com.progressoft.dms.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val DMS_DisputeSummaryReport_View = View {
    name = "DMS_DisputeSummaryReport.View"
    workflowName = "WF_UserReport"
    label = "dispute.summary.view"
    entity = "com.progressoft.dms.entities.DMS_DisputeSummaryReport"
    idProperty = "id"
    ordering = "creationDate:DESC"
    newPortal {
        column {
            width = 8
            portlet {
                label = "dispute.summary.report.filters"
                form {
                    label = "dispute.summary.report.filters"
                    row {
                        comboBox {
                            label = "dispute.summary.report.nature"
                            propertyPath = "nature"
                            filling = Filling.REQUIRED
                        }
                        comboBox {
                            label = "dispute.summary.report.type"
                            propertyPath = "type"
                            filling = Filling.REQUIRED
                        }
                    }
                    row {
                        comboBox {
                            label = "paymentsystem_8118100019"
                            propertyPath = "paymentSystem"
                            filling = Filling.OPTIONAL
                        }
                        comboBox {
                            label = "dms_disputeCase.reason"
                            propertyPath = "reason"
                            filling = Filling.OPTIONAL
                        }
                    }
                    row {
                        dateTime {
                            label = "dispute.summary.report.date_from"
                            propertyPath = "dateFrom"
                            filling = Filling.OPTIONAL
                        }
                        dateTime {
                            label = "dispute.summary.report.date_to"
                            propertyPath = "dateTo"
                            filling = Filling.OPTIONAL
                        }
                    }
                    row {
                        dualList {
                            label = "dispute.summary.report.participants"
                            propertyPath = "participants"
                            filling = Filling.OPTIONAL
                        }
                    }
                }
            }
        }
        column {
            width = 4
            portlet {
                label = "workflowstatus_8118121533"
                form {
                    label = "workflowstatus_8118121533"
                    comboBox {
                        label = "statusid_8118121333"
                        propertyPath = "statusId"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "creationdate_8118121353"
                        propertyPath = "creationDate"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "updatingdate_8118121358"
                        propertyPath = "updatingDate"
                        filling = Filling.READ_ONLY
                    }
                    text {
                        label = "createdby_8118121373"
                        propertyPath = "createdBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                    text {
                        label = "updatedby_8118121378"
                        propertyPath = "updatedBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                }
            }
        }
    }
    portal {
        changeHandlerBean = "hideDisputeSummaryReportsAttPortlet"
        column {
            width = 8
            portlet {
                label = "dispute.summary.report.filters"
                form {
                    label = "dispute.summary.report.filters"
                    row {
                        comboBox {
                            label = "dispute.summary.report.nature"
                            propertyPath = "nature"
                            filling = Filling.READ_ONLY
                        }
                        comboBox {
                            label = "dispute.summary.report.type"
                            propertyPath = "type"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        comboBox {
                            label = "paymentsystem_8118100019"
                            propertyPath = "paymentSystem"
                            filling = Filling.READ_ONLY
                        }
                        comboBox {
                            label = "dms_disputeCase.reason"
                            propertyPath = "reason"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        dateTime {
                            label = "dispute.summary.report.date_from"
                            propertyPath = "dateFrom"
                            filling = Filling.READ_ONLY
                        }
                        dateTime {
                            label = "dispute.summary.report.date_to"
                            propertyPath = "dateTo"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        dualList {
                            label = "dispute.summary.report.participants"
                            propertyPath = "participants"
                            filling = Filling.OPTIONAL
                        }
                    }
                }
            }
            portlet {
                label = "dispute.summary.report.att"
                attachment {
                    label = "dispute.summary.report.att"
                    attachmentFieldUrl = "/api/dms/list-attachments/DMS_DisputeSummaryReport.View/{itemId}"
                }
            }
        }
        column {
            width = 4
            portlet {
                label = "workflowstatus_8118121447"
                form {
                    label = "workflowstatus_8118121447"
                    comboBox {
                        label = "statusid_8118121333"
                        propertyPath = "statusId"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "creationdate_8118121353"
                        propertyPath = "creationDate"
                        filling = Filling.READ_ONLY
                    }
                    text {
                        label = "createdby_8118121373"
                        propertyPath = "createdBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                    dateTime {
                        label = "updatingdate_8118121358"
                        propertyPath = "updatingDate"
                        filling = Filling.READ_ONLY
                    }
                    text {
                        label = "updatedby_8118121378"
                        propertyPath = "updatedBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                }
            }
            portlet {
                label = "dms_disputeCase.latestActivity"
                form {
                    label = "dms_disputeCase.latestActivity"
                    text {
                        label = "dms_disputeCase.lastAction"
                        propertyPath = "lastAction"
                        filling = Filling.READ_ONLY
                        maxLength = 100
                        minLength = 0
                    }
                    text{
                        label = "dms_disputeCase.lastActionBy"
                        propertyPath = "lastActionBy"
                        filling = Filling.READ_ONLY
                        maxLength = 100
                        minLength = 0
                    }
                    textArea {
                        label = "dms_disputeCase.lastNote"
                        propertyPath = "lastNote"
                        filling = Filling.READ_ONLY
                        maxLength = 4000
                        minLength = 0
                    }
                }
            }
        }
    }
}