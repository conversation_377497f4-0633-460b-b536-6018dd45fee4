package com.progressoft.dms.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val DMS_SystemConfiguration_View = View {
    name = "DMS_SystemConfiguration.View"
    workflowName = "WF_DMS_Edit"
    label = "sys_config_view"
    entity = "com.progressoft.dms.entities.DMS_SystemConfiguration"
    idProperty = "id"
    ordering = "id:DESC"
    portal {
        column {
            width = 8
            portlet {
                label = "sys_config_info"
                form {
                    label = "sys_config_info"
                    row {
                        text {
                            propertyPath = "configKey"
                            label = "sys_config_key"
                            filling = Filling.READ_ONLY
                            minLength = 0
                            maxLength = 100
                        }
                        text {
                            propertyPath = "configValue"
                            label = "sys_config_value"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 100
                        }
                    }
                }
            }
        }
    }
}