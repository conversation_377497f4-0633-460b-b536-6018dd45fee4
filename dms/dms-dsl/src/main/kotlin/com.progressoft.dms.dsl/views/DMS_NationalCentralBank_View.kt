package com.progressoft.dms.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val DMS_NationalCentralBank_View = View {
    name = "DMS_NCB.View"
    workflowName = "WF_DMS_NCB"
    label = "dms_nationalcentralbank.view"
    entity = "com.progressoft.dms.entities.DMS_NationalCentralBank"
    idProperty = "id"
    ordering = "creationDate:DESC"
    newPortal {
        column {
            width = 8
            portlet {
                label = "dms_ncb.info"
                form {
                    label = "dms_ncb.info"
                    row {
                        text {
                            label = "dms_ncb.code"
                            propertyPath = "code"
                            filling = Filling.REQUIRED
                            maxLength = 255
                            minLength = 0
                        }
                        text {
                            label = "dms_ncb.name"
                            propertyPath = "name"
                            filling = Filling.REQUIRED
                            maxLength = 255
                            minLength = 0
                        }
                    }
                    row {
                        text {
                            label = "email_8118121200"
                            propertyPath = "email"
                            filling = Filling.REQUIRED
                            regex = "^[\\w.%+-]+@[\\w.-]+\\.[a-zA-Z]{2,}\$"
                            minLength = 3
                            maxLength = 40
                            validationMessage = "email.validation.msg"
                        }
                    }
                    row {
                        text {
                            label = "dms_ncb.description"
                            propertyPath = "description"
                            filling = Filling.REQUIRED
                            maxLength = 255
                            minLength = 0
                            validationMessage = "desc.validation.msg"
                        }
                        comboBox {
                            label = "dms_ncb.country"
                            propertyPath = "country"
                            filling = Filling.REQUIRED
                        }
                    }
                }
            }
        }
        column {
            width = 4
            portlet {
                label = "workflowstatus_8118121533"
                form {
                    label = "workflowstatus_8118121533"
                    row {
                        comboBox {
                            label = "statusid_8118121333"
                            propertyPath = "statusId"
                            filling = Filling.READ_ONLY
                        }
                        dateTime {
                            label = "creationdate_8118121353"
                            propertyPath = "creationDate"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        dateTime {
                            label = "updatingdate_8118121358"
                            propertyPath = "updatingDate"
                            filling = Filling.READ_ONLY
                        }
                        text {
                            label = "createdby_8118121373"
                            propertyPath = "createdBy"
                            filling = Filling.READ_ONLY
                            minLength = 0
                            maxLength = 200
                        }
                    }
                    text {
                        label = "updatedby_8118121378"
                        propertyPath = "updatedBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                }
            }
            portlet {
                label = "rejectedresons_8118121517"
                form {
                    label = "rejectedresons_8118121517"
                    textArea {
                        label = "checkercomments_8118121328"
                        propertyPath = "checkerComments"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 258
                    }
                }
            }
        }
    }
    portal {
        column {
            width = 8
            portlet {
                label = "dms_ncb.info"
                form {
                    label = "dms_ncb.info"
                    row {
                        text {
                            label = "dms_ncb.code"
                            propertyPath = "code"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 255
                        }
                        text {
                            label = "dms_ncb.name"
                            propertyPath = "name"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 255
                        }
                    }
                    row {
                        text {
                            label = "email_8118121200"
                            propertyPath = "email"
                            filling = Filling.REQUIRED
                            regex = "^[\\w.%+-]+@[\\w.-]+\\.[a-zA-Z]{2,}\$"
                            minLength = 3
                            maxLength = 40
                        }
                    }
                    row {
                        text {
                            label = "dms_ncb.description"
                            propertyPath = "description"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 255
                        }
                        comboBox {
                            label = "dms_ncb.country"
                            propertyPath = "country"
                            filling = Filling.REQUIRED
                        }
                    }
                }
            }
        }
        column {
            width = 4
            portlet {
                label = "workflowstatus_8118121447"
                form {
                    label = "workflowstatus_8118121447"
                    comboBox {
                        label = "statusid_8118121333"
                        propertyPath = "statusId"
                        filling = Filling.READ_ONLY
                    }

                    dateTime {
                        label = "creationdate_8118121353"
                        propertyPath = "creationDate"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "updatingdate_8118121358"
                        propertyPath = "updatingDate"
                        filling = Filling.READ_ONLY
                    }
                    text {
                        label = "createdby_8118121373"
                        propertyPath = "createdBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                    text {
                        label = "updatedby_8118121378"
                        propertyPath = "updatedBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                }
            }
            portlet {
                label = "rejectedresons_8118121431"
                form {
                    label = "rejectedresons_8118121431"
                    textArea {
                        label = "checkercomments_8118121328"
                        propertyPath = "checkerComments"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 258
                    }
                }
            }
        }
    }
    actionPortal {
        id = "8118121163"
        editable
        column {
            width = 12
            portlet {
                label = "rejectedresons_8118121597"
                form {
                    label = "rejectedresons_8118121597"
                    textArea {
                        label = "checkercomments_8118121328"
                        propertyPath = "checkerComments"
                        filling = Filling.OPTIONAL
                        minLength = 0
                        maxLength = 258
                    }
                }
            }

        }
    }
}