package com.progressoft.dms.dsl.views

import AMOUNT_REGEX
import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val DMS_ClaimantNCBCaseManagement_View = View {
    name = "DMS_ClaimantNCBCaseManagement.View"
    workflowName = "WF_DMS_ClaimantNCBCaseManagement"
    label = "dms_ClaimantNCBCaseManagement.view"
    entity = "com.progressoft.dms.entities.DMS_ClaimantNCBCaseManagement"
    idProperty = "id"
    ordering = "creationDate:DESC"
    portal {
        column {
            width = 8
            portlet {
                label = "dms_claimantNCBCaseManagement.disputeCase"
                form {
                    label = "dms_claimantNCBCaseManagement.disputeCase"
                    row {
                        text {
                            label = "dms_disputeCase.caseReferenceNumber"
                            propertyPath = "disputeCase.caseReferenceNumber"
                            filling = Filling.READ_ONLY
                            maxLength = 255
                            minLength = 0
                        }
                        dateTime {
                            label = "dms_disputeCase.creationDateTime"
                            propertyPath = "disputeCase.creationDateTime"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        text {
                            label = "dms_disputeCase.paymentSystem"
                            propertyPath = "disputeCase.paymentSystem.code"
                            filling = Filling.READ_ONLY
                        }
                        text {
                            label = "dms_disputeCase.transactionReference"
                            propertyPath = "disputeCase.transactionReference"
                            filling = Filling.READ_ONLY
                            maxLength = 255
                            minLength = 0
                        }
                    }
                    row {
                        date {
                            label = "dms_disputeCase.transactionDate"
                            propertyPath = "disputeCase.transactionDate"
                            filling = Filling.READ_ONLY
                        }
                        text {
                            label = "dms_disputeCase.senderParticipant"
                            propertyPath = "disputeCase.senderParticipant.code"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        text {
                            label = "dms_disputeCase.receiverParticipant"
                            propertyPath = "disputeCase.receiverParticipant.code"
                            filling = Filling.READ_ONLY
                        }
                        text {
                            label = "dms_disputeCase.transactionCurrency"
                            propertyPath = "disputeCase.transactionCurrency.stringISOCode"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        amount {
                            label = "dms_disputeCase.transactionAmount"
                            propertyPath = "disputeCase.transactionAmount"
                            filling = Filling.READ_ONLY
                            regex = AMOUNT_REGEX
                            minLength = 0
                            maxLength = 22
                            showCurrencySymbol = false
                        }
                        amount {
                            label = "dms_disputeCase.disputedAmount"
                            propertyPath = "disputeCase.disputedAmount"
                            filling = Filling.READ_ONLY
                            regex = AMOUNT_REGEX
                            minLength = 0
                            maxLength = 22
                            showCurrencySymbol = false
                        }
                    }
                    row{
                        text {
                            label = "statusid_8118121333"
                            propertyPath = "disputeCase.statusId.description"
                            filling = Filling.READ_ONLY
                        }
                    }
                    row {
                        text {
                            label = "dms_disputeCase.lastAction"
                            propertyPath = "disputeCase.lastAction"
                            filling = Filling.READ_ONLY
                            maxLength = 100
                            minLength = 0
                        }
                        text{
                            label = "dms_disputeCase.lastActionBy"
                            propertyPath = "disputeCase.lastActionBy"
                            filling = Filling.READ_ONLY
                            maxLength = 100
                            minLength = 0
                        }
                    }
                    row {
                        textArea {
                            label = "dms_disputeCase.lastNote"
                            propertyPath = "disputeCase.lastNote"
                            filling = Filling.READ_ONLY
                            maxLength = 4000
                            minLength = 0
                        }
                    }
                    row {
                        text {
                            label = "dms_disputeCase.urgency"
                            propertyPath = "disputeCase.urgency"
                            filling = Filling.READ_ONLY
                        }
                    }
                }
                subView {
                    viewName = "DMS_TransactionAdditionalInfo.View"
                    joinProperty = "disputeCase.claimantNCBCaseManagement.id"
                    idProperty = "id"
                    label = "dms_disputeCase.TransactionAdditionalInfo"
                }
                subView {
                    viewName = "DMS_Correspondence.View"
                    joinProperty = "dispute.claimantNCBCaseManagement.id"
                    idProperty = "id"
                    label = "dms_disputeCase.correspondence"
                }
                attachment {
                    label = "claimant_bank_attachment"
                    attachmentFieldUrl = "/api/dms/list-attachments/dispute-case/DMS_ClaimantNCBCaseManagement.View/{itemId}"
                }
            }
        }
        column {
            width = 4
            portlet {
                label = "workflowstatus_8118121447"
                form {
                    label = "workflowstatus_8118121447"
                    row {
                        comboBox {
                            label = "statusid_8118121333"
                            propertyPath = "statusId"
                            filling = Filling.READ_ONLY
                        }
                    }
                    dateTime {
                            label = "creationdate_8118121353"
                            propertyPath = "creationDate"
                            filling = Filling.READ_ONLY
                        }
                    text {
                        label = "createdby_8118121373"
                        propertyPath = "createdBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                    dateTime {
                            label = "updatingdate_8118121358"
                            propertyPath = "updatingDate"
                            filling = Filling.READ_ONLY
                    }
                    text {
                        label = "updatedby_8118121378"
                        propertyPath = "updatedBy"
                        filling = Filling.READ_ONLY
                        minLength = 0
                        maxLength = 200
                    }
                }
            }
            portlet {
                label = "dms_claimantNCBCaseManagement.caseInfo"
                form {
                    label = "dms_claimantNCBCaseManagement.caseInfo"
                    row {
                        textArea {
                            label = "dms_claimantNCBCaseManagement.note"
                            propertyPath = "note"
                            filling = Filling.OPTIONAL
                            maxLength = 4000
                            minLength = 0
                        }
                    }
                }
            }
        }
    }
//    provide more info portal
    actionPortal {
        id = "8118621167"
        editable
        column {
            width = 12
            portlet {
                label = "notes_8118121165"
                form {
                    label = "notes_8118121165"
                    textArea {
                        label = "notesaction_8118121165"
                        propertyPath = "note"
                        filling = Filling.REQUIRED
                        minLength = 0
                        maxLength = 4000
                    }
                }
            }
        }
    }
//    represent action portal
    actionPortal {
        id = "8118121167"
        editable
        column {
            width = 12
            portlet {
                label = "notes_8118121165"
                form {
                    label = "notes_8118121165"
                    row {
                        textArea {
                            label = "notesaction_8118121165"
                            propertyPath = "note"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 4000
                        }
                        comboBox {
                            label = "correspondence_reason"
                            propertyPath = "representReason"
                            filling = Filling.REQUIRED
                        }
                    }
                }
            }
        }
    }
//    reject action portal
    actionPortal {
        id = "8118128167"
        editable
        column {
            width = 12
            portlet {
                label = "notes_8118121165"
                form {
                    label = "notes_8118121165"
                    row {
                        textArea {
                            label = "notesaction_8118121165"
                            propertyPath = "note"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 4000
                        }
                        comboBox {
                            label = "correspondence_reason"
                            propertyPath = "rejectionReason"//reject reason
                            filling = Filling.REQUIRED
                        }
                    }
                }
            }
        }
    }
//    request more info portal
    actionPortal {
        id = "8118121967"
        editable
        column {
            width = 12
            portlet {
                label = "notes_8118121165"
                form {
                    label = "notes_8118121165"
                    row {
                        textArea {
                            label = "notesaction_8118121165"
                            propertyPath = "note"
                            filling = Filling.REQUIRED
                            minLength = 0
                            maxLength = 4000
                        }
                        comboBox {
                            label = "correspondence_reason"
                            propertyPath = "reqAddInfoReason"
                            filling = Filling.REQUIRED
                        }
                    }
                }
            }
        }
    }
}