package com.progressoft.dms.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val DMS_TransactionAdditionalInfo_View = View {
    name = "DMS_TransactionAdditionalInfo.View"
    workflowName = null
    label = "sys_config_view"
    entity = "com.progressoft.dms.entities.DMS_TransactionAdditionalInfo"
    joinProperty = "disputeCase.id"
    idProperty = "id"
    ordering = "creationDate:DESC"
    portal {
        column {
            width = 8
            portlet {
                label = "sys_config_info"
                form {
                    label = "sys_config_info"
                    row {
                        text {
                            propertyPath = "additionalInfoKey"
                            label = "additional_info_key"
                            filling = Filling.READ_ONLY
                            minLength = 0
                            maxLength = 100
                        }
                        text {
                            propertyPath = "additionalInfoValue"
                            label = "additional_info_value"
                            filling = Filling.READ_ONLY
                            minLength = 0
                            maxLength = 1000
                        }
                    }
                }
            }
        }
    }
}