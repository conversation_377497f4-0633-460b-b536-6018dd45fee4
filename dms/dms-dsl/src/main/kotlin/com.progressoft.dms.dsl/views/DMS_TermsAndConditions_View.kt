package com.progressoft.dms.dsl.views

import com.progressoft.jupiter.kotlin.dsl.View
import com.progressoft.repository.view.dto.jxb.Filling

val DMS_TermsAndConditions_View = View {
    hasMasterDraft
    name = "DMS_TermsAndConditions.View"
    label = "terms.and.conditions.80579032"
    workflowName = "WF_DMS_TermsAndConditions"
    entity = "com.progressoft.dms.entities.DMS_TermsAndConditions"
    idProperty = "id"

    portal {
        column {
            width = 8
            portlet {
                label = "terms.and.conditions.80579032"
                form {
                    label = "terms.and.conditions.80579032"
                    textArea {
                        label = "terms.and.conditions.80579032"
                        propertyPath = "termsAndConditions"
                        filling = Filling.OPTIONAL
                        minLength = 0
                        maxLength = 2000
                    }
                }
            }
        }
        column {
            width = 4
            portlet {
                label = "workflowstatus_8118121533"
                form {
                    label = "workflowstatus_8118121533"
                    comboBox {
                        label = "statusid_8118121333"
                        propertyPath = "statusId"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "creationdate_8118121353"
                        propertyPath = "creationDate"
                        filling = Filling.READ_ONLY
                    }
                    dateTime {
                        label = "updatingdate_8118121358"
                        propertyPath = "updatingDate"
                        filling = Filling.READ_ONLY
                    }
                    text {
                        label = "createdby_8118121373"
                        propertyPath = "createdBy"
                        filling = Filling.READ_ONLY
                    }
                    text {
                        label = "updatedby_8118121378"
                        propertyPath = "updatedBy"
                        filling = Filling.READ_ONLY
                    }
                }
            }
        }
    }
}
