package com.progressoft.dms.dsl

import com.progressoft.dms.dsl.menus.*
import com.progressoft.repository.menu.MenuDslGateway
import com.progressoft.repository.menu.dto.MenuDto
import org.springframework.stereotype.Component
import java.util.*

@Component
class DMSMenusDslGateway : MenuDslGateway {

    override fun allMenus(): MutableList<MenuDto> {
        val result:MutableList<MenuDto> = LinkedList()
        result.addAll(DMS_KUWAIT_MENUS)
        return result
    }

}
