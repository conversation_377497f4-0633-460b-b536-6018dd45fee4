package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_DisputeSummaryReport_Entity = Entity {
    name = "com.progressoft.dms.entities.DMS_DisputeSummaryReport"
    property {
        name = "statusId"
        showAsResultColumn = 1
        label = "statusid_8118121333"
    }
    property {
        name = "id"
        showAsResultColumn = 0
        label = "dms_disputeCase.id"
    }
    property {
        name = "nature"
        showAsResultColumn = 1
        label = "dispute.summary.report.nature"
    }
    property {
        name = "reportType"
        showAsResultColumn = 0
        label = "dispute.summary.report.type"
    }
    property {
        name = "type"
        showAsResultColumn = 0
        label = "dispute.summary.report.type"
    }
    property {
        name = "paymentSystem"
        showAsResultColumn = 0
        label = "paymentsystem_8118100019"
    }
    property {
        name = "participants"
        showAsResultColumn = 1
        label ="dispute.summary.report.participants"
    }
    property {
        name = "participants.id"
        showAsResultColumn = 0
        label ="dispute.summary.report.participants"
    }
    property {
        name = "participants.codeNamePair"
        showAsResultColumn = 0
        label ="dispute.summary.report.participants"
    }
    property {
        name = "participants.code"
        showAsResultColumn = 0
        label ="dispute.summary.report.participants"
    }
    property {
        name = "participants.name"
        showAsResultColumn = 0
        label ="dispute.summary.report.participants"
    }
    property {
        name = "dateFrom"
        showAsResultColumn = 1
        label = "dispute.summary.report.date_from"
    }
    property {
        name = "dateTo"
        showAsResultColumn = 1
        label = "dispute.summary.report.date_to"
    }
    property {
        name = "paymentSystem.codeNamePair"
        showAsResultColumn = 1
        label = "paymentsystem_8118100019"
    }
    property {
        name = "reason"
        showAsResultColumn = 1
        label = "dms_disputeCase.reason"
    }
    property {
        name="attachmentUuid"
        showAsResultColumn=0
        label = "dms_disputeCase.reason"
    }
    property {
        name = "deletedFlag"
        showAsResultColumn = 0
        label = "deletedflag_8118121393"
    }
    property {
        name = "deletedBy"
        showAsResultColumn = 0
        label = "deletedby_8118121388"
    }
    property {
        name = "lockedBy"
        showAsResultColumn = 0
        label = "lockedby_8118121383"
    }
    property {
        name = "updatedBy"
        showAsResultColumn = 0
        label = "updatedby_8118121378"
    }
    property {
        name = "createdBy"
        showAsResultColumn = 0
        label = "createdby_8118121373"
    }
    property {
        name = "deletedOn"
        showAsResultColumn = 0
        label = "deletedon_8118121368"
    }
    property {
        name = "workflowId"
        showAsResultColumn = 0
        label = "workflowId"
    }
    property {
        name = "lockedUntil"
        showAsResultColumn = 0
        label = "lockeduntil_8118121363"
    }
    property {
        name = "updatingDate"
        showAsResultColumn = 0
        label = "updatingdate_8118121358"
    }
    property {
        name = "creationDate"
        showAsResultColumn = 1
        label = "creationdate_8118121353"
    }
    property {
        name = "statusId.description"
        showAsResultColumn = 0
        label = "statusid.description_8118121348"
    }
    property {
        name = "statusId.codeNamePair"
        showAsResultColumn = 0
        label = "statusid.codenamepair_8118121343"
    }
    property {
        name = "statusId.id"
        showAsResultColumn = 0
        label = "statusid.id_8118121338"
    }
    property {
        name = "orgId"
        showAsResultColumn = 0
        label = "orgId"
    }
    property {
        name = "tenantId"
        showAsResultColumn = 0
        label = "tenantId"
    }
    property {
        name = "draftStatus"
        showAsResultColumn = 0
        label = "draftStatus"
    }
    property {
        name = "jfwDraft.id"
        showAsResultColumn = 0
        label = "jfwDraft.id"
    }
    property {
        name = "jfwDraft"
        showAsResultColumn = 0
        label = "jfwDraft.id"
    }
    property {
        name = "jfwDraft.draftData"
        showAsResultColumn = 0
        label = "jfwDraft.draftData"
    }
}