package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_ReasonManagement_Entity = Entity {
    name = "com.progressoft.dms.entities.DMS_ReasonManagement"
    property {
        name = "statusId"
        showAsResultColumn = 1
        label = "workflowstatus_8118121447"
    }
    property {
        name = "code"
        showAsResultColumn = 1
        label = "reason.management.code"
    }
    property {
        name = "name"
        showAsResultColumn = 1
        label = "reason.management.name"
    }
    property {
        name = "description"
        showAsResultColumn = 1
        label = "reason.management.desc"
    }
    property {
        name = "disputeReason"
        showAsResultColumn = 1
        label = "reason.management.isdispute"
    }
    property {
        name = "rejectionReason"
        showAsResultColumn = 1
        label = "reason.management.isrejection"
    }
    property {
        name = "representedReason"
        showAsResultColumn = 1
        label = "reason.management.isrepresentment"
    }
    property {
        name = "requestInfoReason"
        showAsResultColumn = 1
        label = "reason.management.isreqinfo"
    }
    property {
        name = "id"
        showAsResultColumn = 0
        label = "dms_claimantBankCaseManagement.id"
    }
    property {
        name = "deletedFlag"
        showAsResultColumn = 0
        label = "deletedflag_8118121393"
    }
    property {
        name = "deletedBy"
        showAsResultColumn = 0
        label = "deletedby_8118121388"
    }
    property {
        name = "lockedBy"
        showAsResultColumn = 0
        label = "lockedby_8118121383"
    }
    property {
        name = "updatedBy"
        showAsResultColumn = 0
        label = "updatedby_8118121378"
    }
    property {
        name = "createdBy"
        showAsResultColumn = 0
        label = "createdby_8118121373"
    }
    property {
        name = "deletedOn"
        showAsResultColumn = 0
        label = "deletedon_8118121368"
    }
    property {
        name = "lockedUntil"
        showAsResultColumn = 0
        label = "lockeduntil_8118121363"
    }
    property {
        name = "updatingDate"
        showAsResultColumn = 0
        label = "updatingdate_8118121358"
    }
    property {
        name = "creationDate"
        showAsResultColumn = 1
        label = "creationdate_8118121353"
    }
    property {
        name = "statusId.description"
        showAsResultColumn = 0
        label = "statusid.description_8118121348"
    }
    property {
        name = "statusId.codeNamePair"
        showAsResultColumn = 0
        label = "statusid.codenamepair_8118121343"
    }
    property {
        name = "statusId.id"
        showAsResultColumn = 0
        label = "statusid.id_8118121338"
    }
    property {
        name = "orgId"
        showAsResultColumn = 0
        label = "orgId"
    }
    property {
        name = "draftStatus"
        showAsResultColumn = 0
        label = "draftStatus"
    }
    property {
        name = "jfwDraft.id"
        showAsResultColumn = 0
        label = "jfwDraft.id"
    }
    property {
        name = "jfwDraft.draftData"
        showAsResultColumn = 0
        label = "jfwDraft.draftData"
    }
}