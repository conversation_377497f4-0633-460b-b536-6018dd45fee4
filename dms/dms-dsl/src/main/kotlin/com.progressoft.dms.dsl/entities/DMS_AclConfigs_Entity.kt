package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_AclConfigs_Entity = Entity {
	name = "com.progressoft.dms.entities.DMS_AclConfig"
	property { 
		name="statusId"
		showAsResultColumn=1
		label = "statusid_8118121333"
	 }
	property { 
		name="statusId.id"
		showAsResultColumn=0
		label = "statusid.id_8118121338"
	 }
	property { 
		name="statusId.description"
		showAsResultColumn=0
		label = "statusid.description_8118121348"
	 }
	property { 
		name="statusId.codeNamePair"
		showAsResultColumn=0
		label = "statusid.codenamepair_8118121343"
	 }
	property { 
		name="users"
		showAsResultColumn=0
		label = "users.2869601920"
	 }
	property {
		name="users.id"
		showAsResultColumn=0
		label = "users.4269601935"
	}
	property {
		name="users.email"
		showAsResultColumn=0
		label = "users.7669601345"
	}
	property {
		name="maxAmount"
		showAsResultColumn=1
		label = "maxamount.236867754"
	}
	property {
		name = "participants"
		showAsResultColumn = 0
		label ="participants.view_8118121163"
	}
	property {
		name = "participants.id"
		showAsResultColumn = 0
		label ="participants.id.3959601345"
	}
	property {
		name = "participants.codeNamePair"
		showAsResultColumn = 0
		label ="participants.codenamepair.2859601345"
	}
	property {
		name = "participants.code"
		showAsResultColumn = 0
		label ="participants.code.1679601345"
	}
	property {
		name = "participants.name"
		showAsResultColumn = 0
		label ="participants.name.0659601345"
	}
	property { 
		name="creationDate"
		showAsResultColumn=1
		label = "creationdate_8118121353"
	 }
	property { 
		name="updatingDate"
		showAsResultColumn=1
		label = "updatingdate_8118121358"
	 }
	property { 
		name="lockedUntil"
		showAsResultColumn=0
		label = "lockeduntil_8118116197"
	 }
	property { 
		name="deletedOn"
		showAsResultColumn=0
		label = "deletedon_8118116202"
	 }
	property { 
		name="createdBy"
		showAsResultColumn=1
		label = "createdby_8118121373"
	 }
	property { 
		name="updatedBy"
		showAsResultColumn=1
		label = "updatedby_8118121378"
	 }
	property { 
		name="lockedBy"
		showAsResultColumn=0
		label = "lockedby_8118116217"
	 }
	property { 
		name="deletedBy"
		showAsResultColumn=0
		label = "deletedby_8118116222"
	 }
	property { 
		name="deletedFlag"
		showAsResultColumn=1
		label = "deletedflag_8118116227"
	 }
	property { 
		name="id"
		showAsResultColumn=0
		label = "id_8118121180"
	 }
	property { 
		name="orgId"
		showAsResultColumn=0
		label = "orgId"
	 }
	property { 
		name="draftStatus"
		showAsResultColumn=0
		label = "draftStatus"
	 }
	property { 
		name="jfwDraft.id"
		showAsResultColumn=0
		label = "jfwDraft.id"
	 }
	property { 
		name="jfwDraft.draftData"
		showAsResultColumn=0
		label = "jfwDraft.draftData"
	 }
}