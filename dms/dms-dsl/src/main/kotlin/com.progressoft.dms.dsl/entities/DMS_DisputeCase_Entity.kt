package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_DisputeCase_Entity = Entity {
    name = "com.progressoft.dms.entities.DMS_DisputeCase"
    property {
        name = "id"
        showAsResultColumn = 0
        label = "dms_disputeCase.id"
    }
    property {
        name = "claimantBankCaseManagement"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantBankCaseManagement"
    }
    property {
        name = "defendantBankCaseManagement"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantBankCaseManagement"
    }
    property {
        name = "claimantBankCaseManagement.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantBankCaseManagement"
    }
    property {
        name = "defendantBankCaseManagement.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantBankCaseManagement"
    }
    property {
        name = "claimantNCBCaseManagement"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantNCBCaseManagement"
    }
    property {
        name = "claimantNCBCaseManagement.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantNCBCaseManagement"
    }
    property {
        name = "defendantNCBCaseManagement"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantNCBCaseManagement"
    }
    property {
        name = "defendantNCBCaseManagement.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantNCBCaseManagement"
    }
    property {
        name = "operatorCaseManagement"
        showAsResultColumn = 0
        label = "dms_disputeCase.operatorCaseManagement"
    }
    property {
        name = "operatorCaseManagement.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.operatorCaseManagement"
    }
    property {
        name = "correspondence"
        showAsResultColumn = 0
        label = "dms_disputeCase.correspondence"
    }
    property {
        name = "correspondence.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.correspondence"
    }
    property {
        name = "statusId"
        showAsResultColumn = 0
        label = "statusid_8118121333"
    }
    property {
        name = "statusId.description"
        showAsResultColumn = 1
        label = "statusid.description_8118121348"
    }
    property {
        name = "caseReferenceNumber"
        showAsResultColumn = 1
        label = "dms_disputeCase.caseReferenceNumber"
    }
    property {
        name = "disputeOverDispute"
        showAsResultColumn = 1
        label = "dms_disputeCase.dispute.over.dispute"
    }
    property {
        name = "creationDateTime"
        showAsResultColumn = 0
        label = "dms_disputeCase.creationDateTime"
    }
    property {
        name = "paymentSystem"
        showAsResultColumn = 0
        label = "dms_disputeCase.paymentSystem"
    }
    property {
        name = "paymentSystem.code"
        showAsResultColumn = 1
        label = "dms_disputeCase.paymentSystem"
    }
    property {
        name = "lastNote"
        showAsResultColumn = 0
        label = "dms_disputeCase.lastNote"
    }
    property {
        name = "lastAction"
        showAsResultColumn = 0
        label = "dms_disputeCase.lastAction"
    }
    property {
        name = "lastActionBy"
        showAsResultColumn = 0
        label = "dms_disputeCase.lastActionBy"
    }
    property {
        name = "urgency"
        showAsResultColumn = 1
        label = "dms_disputeCase.urgency"
    }
    property {
        name = "claimantBank"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantBank"
    }
    property {
        name = "claimantBank.code"
        showAsResultColumn = 1
        label = "dms_disputeCase.claimantBank"
    }
    property {
        name = "claimantBank.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantBank"
    }
    property {
        name = "defendantBank"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantBank"
    }
    property {
        name = "defendantBank.code"
        showAsResultColumn = 1
        label = "dms_disputeCase.defendantBank"
    }
    property {
        name = "defendantBank.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantBank"
    }
    property {
        name = "reason"
        showAsResultColumn = 0
        label = "dms_disputeCase.reason"
    }
    property {
        name = "reason.name"
        showAsResultColumn = 1
        label = "dms_disputeCase.reason"
    }
    property {
        name = "notes"
        showAsResultColumn = 1
        label = "dms_disputeCase.notes"
    }
    property {
        name = "creationDate"
        showAsResultColumn = 1
        label = "creationdate_8118121353"
    }
    property {
        name = "updatingDate"
        showAsResultColumn = 1
        label = "updatingdate_8118121358"
    }
    property {
        name = "transactionReference"
        showAsResultColumn = 1
        label = "dms_disputeCase.transactionReference"
    }
    property {
        name = "transactionDate"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionDate"
    }
    property {
        name = "refDisputeTransactionAmount"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionAmount"
    }
    property {
        name = "transactionCurrency"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionCurrency"
    }
    property {
        name = "transactionCurrency.code"
        showAsResultColumn = 1
        label = "dms_disputeCase.transactionCurrency"
    }
    property {
        name = "transactionAmount"
        showAsResultColumn = 1
        label = "dms_disputeCase.transactionAmount"
    }
    property {
        name = "disputedAmount"
        showAsResultColumn = 1
        label = "dms_disputeCase.disputedAmount"
    }
    property {
        name = "senderParticipant"
        showAsResultColumn = 0
        label = "dms_disputeCase.tranSenderParticipant"
    }
    property {
        name = "senderParticipant.code"
        showAsResultColumn = 1
        label = "dms_disputeCase.tranSenderParticipant"
    }
    property {
        name = "senderParticipant.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.tranSenderParticipant"
    }
    property {
        name = "receiverParticipant"
        showAsResultColumn = 0
        label = "dms_disputeCase.tranReceiverParticipant"
    }
    property {
        name = "defendantNCBRejectionCount"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantNCBRejectionCount"
    }
    property {
        name = "defendantBankRejectionCount"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantBankRejectionCount"
    }
    property {
        name = "claimantNCBRepresentCount"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantNCBRepresentCount"
    }
    property {
        name = "claimantBankRepresentCount"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantBankRepresentCount"
    }
    property {
        name = "receiverParticipant.code"
        showAsResultColumn = 1
        label = "dms_disputeCase.tranReceiverParticipant"
    }
    property {
        name = "receiverParticipant.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.tranReceiverParticipant"
    }
    property {
        name = "id"
        showAsResultColumn = 0
        label = "dms_disputeCase.id"
    }
    property {
        name = "deletedFlag"
        showAsResultColumn = 0
        label = "deletedflag_8118121393"
    }
    property {
        name = "deletedBy"
        showAsResultColumn = 0
        label = "deletedby_8118121388"
    }
    property {
        name = "lockedBy"
        showAsResultColumn = 0
        label = "lockedby_8118121383"
    }
    property {
        name = "updatedBy"
        showAsResultColumn = 0
        label = "updatedby_8118121378"
    }
    property {
        name = "createdBy"
        showAsResultColumn = 0
        label = "createdby_8118121373"
    }
    property {
        name = "deletedOn"
        showAsResultColumn = 0
        label = "deletedon_8118121368"
    }
    property {
        name = "workflowId"
        showAsResultColumn = 0
        label = "workflowId"
    }
    property {
        name = "lockedUntil"
        showAsResultColumn = 0
        label = "lockeduntil_8118121363"
    }
    property {
        name = "statusId.codeNamePair"
        showAsResultColumn = 0
        label = "statusid.codenamepair_8118121343"
    }
    property {
        name = "statusId.id"
        showAsResultColumn = 0
        label = "statusid.id_8118121338"
    }
    property {
        name = "statusId"
        showAsResultColumn = 0
        label = "statusid.description_8118121348"
    }
    property {
        name = "orgId"
        showAsResultColumn = 0
        label = "orgId"
    }
    property {
        name = "tenantId"
        showAsResultColumn = 0
        label = "tenantId"
    }
    property {
        name = "draftStatus"
        showAsResultColumn = 0
        label = "draftStatus"
    }
    property {
        name = "jfwDraft.id"
        showAsResultColumn = 0
        label = "jfwDraft.id"
    }
    property {
        name = "jfwDraft"
        showAsResultColumn = 0
        label = "jfwDraft.id"
    }
    property {
        name = "jfwDraft.draftData"
        showAsResultColumn = 0
        label = "jfwDraft.draftData"
    }
    property {
        name = "refDisputePaymentSys"
        showAsResultColumn = 0
        label = "dms_disputeCase.paymentSystem"
    }
    property {
        name = "refDisputeClaimantBnk"
        showAsResultColumn = 0
        label = "dms_disputeCase.claimantBank"
    }
    property {
        name = "refDisputeDefendantBnk"
        showAsResultColumn = 0
        label = "dms_disputeCase.defendantBank"
    }
    property {
        name = "refDisputeCaseNumber"
        showAsResultColumn = 0
        label = "dms_disputeCase.caseReferenceNumber"
    }
}