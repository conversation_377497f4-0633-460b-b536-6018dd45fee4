package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_TransactionAdditionalInfo_Entity = Entity {
    name = "com.progressoft.dms.entities.DMS_TransactionAdditionalInfo"

    property {
        name = "id"
        showAsResultColumn = 0
        label = "additional_info_id"
    }

    property {
        name = "additionalInfoKey"
        showAsResultColumn = 1
        label = "additional_info_key"
    }
    property {
        name = "additionalInfoValue"
        showAsResultColumn = 1
        label = "additional_info_value"
    }
    property {
        name = "disputeCase"
        showAsResultColumn = 0
        label = "additional_info_claimant_bank_dispute"
    }
    property {
        name = "disputeCase.id"
        showAsResultColumn = 0
        label = "additional_info_claimant_bank_dispute"
    }
    property {
        name = "disputeCase.caseReferenceNumber"
        showAsResultColumn = 0
        label = "additional_info_claimant_bank_dispute"
    }
    property {
        name = "creationDate"
        showAsResultColumn = 1
        label = "creationdate_8118121353"
    }
}