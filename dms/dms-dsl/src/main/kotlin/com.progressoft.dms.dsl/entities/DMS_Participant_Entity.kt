package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_Participant_Entity = Entity {
	name = "com.progressoft.dms.entities.DMS_Participant"
	property { 
		name="statusId"
		showAsResultColumn=1
		label = "statusid_8118121333"
	 }
	property {
		name="deletedFlag"
		showAsResultColumn=0
		label = "deletedflag_8118121393"
	 }
	property { 
		name="deletedBy"
		showAsResultColumn=0
		label = "deletedby_8118121388"
	 }
	property { 
		name="lockedBy"
		showAsResultColumn=0
		label = "lockedby_8118121383"
	 }
	property { 
		name="updatedBy"
		showAsResultColumn=0
		label = "updatedby_8118121378"
	 }
	property { 
		name="createdBy"
		showAsResultColumn=0
		label = "createdby_8118121373"
	 }
	property { 
		name="deletedOn"
		showAsResultColumn=0
		label = "deletedon_8118121368"
	 }
	property { 
		name="lockedUntil"
		showAsResultColumn=0
		label = "lockeduntil_8118121363"
	 }
	property { 
		name="updatingDate"
		showAsResultColumn=0
		label = "updatingdate_8118121358"
	 }

	property { 
		name="statusId.description"
		showAsResultColumn=0
		label = "statusid.description_8118121348"
	 }
	property { 
		name="statusId.codeNamePair"
		showAsResultColumn=0
		label = "statusid.codenamepair_8118121343"
	 }
	property { 
		name="statusId.id"
		showAsResultColumn=0
		label = "statusid.id_8118121338"
	 }
	property { 
		name="checkerComments"
		showAsResultColumn=0
		label = "checkercomments_8118121328"
	 }
	property {
		name="description"
		showAsResultColumn=0
		label = "description_8118121308"
	 }
	property {
		name="name"
		showAsResultColumn=1
		label = "name_8118121200"
	 }
	property {
		name="email"
		showAsResultColumn=1
		label = "email_8118121200"
	 }
	property { 
		name="fullName"
		showAsResultColumn=1
		label = "fullnameeng_8118121195"
	 }
	property {
		name="code"
		showAsResultColumn=1
		label = "code_8118121190"
	 }
	property { 
		name="id"
		showAsResultColumn=0
		label = "id_8118121180"
	 }
	property {
		name = "country"
		showAsResultColumn = 1
		label = "dms_ncb.country"
	}
	property {
		name = "country.id"
		showAsResultColumn = 0
		label = "dms_ncb.country_id"
	}
	property {
		name = "country.code"
		showAsResultColumn = 0
		label = "dms_ncb.country_code"
	}
	property {
		name = "country.name"
		showAsResultColumn = 0
		label = "dms_ncb.country_name"
	}
	property {
		name = "country.codeNamePair"
		showAsResultColumn = 0
		label = "dms_ncb.country_codeNamePair"
	}
	property {
		name="orgId"
		showAsResultColumn=0
		label = "orgId"
	 }
	property {
		name="nationalCentralBank"
		showAsResultColumn=1
		label = "ncb_id"
	}
	property {
		name="nationalCentralBank.codeNamePair"
		showAsResultColumn=0
		label = "ncb_id"
	}
	property {
		name="draftStatus"
		showAsResultColumn=0
		label = "draftStatus"
	 }
	property { 
		name="jfwDraft.id"
		showAsResultColumn=0
		label = "jfwDraft.id"
	 }
	property { 
		name="jfwDraft.draftData"
		showAsResultColumn=0
		label = "jfwDraft.draftData"
	 }
	property {
		name="creationDate"
		showAsResultColumn=1
		label = "creationdate_8118121353"
	}
}