package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_ClaimantBankCaseManagement_Entity = Entity {
    name = "com.progressoft.dms.entities.DMS_ClaimantBankCaseManagement"
    property {
        name = "disputeCase"
        showAsResultColumn = 1
        label = "dms_claimantBankCaseManagement.disputeCase"
    }
    property {
        name = "disputeCase.caseReferenceNumber"
        showAsResultColumn = 0
        label = "dms_disputeCase.caseReferenceNumber"
    }
    property {
        name = "disputeCase.creationDateTime"
        showAsResultColumn = 0
        label = "dms_disputeCase.creationDateTime"
    }
    property {
        name = "disputeCase.paymentSystem"
        showAsResultColumn = 0
        label = "dms_disputeCase.paymentSystem"
    }
    property {
        name = "disputeCase.paymentSystem.description"
        showAsResultColumn = 0
        label = "dms_disputeCase.paymentSystem"
    }
    property {
        name = "disputeCase.paymentSystem.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.paymentSystem"
    }
    property {
        name = "disputeCase.paymentSystem.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.paymentSystem"
    }
    property {
        name = "rejectionReason.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "rejectionReason.code"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "rejectionReason.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "representReason.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "representReason.code"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "representReason.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "reqAddInfoReason.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "reqAddInfoReason.code"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "reqAddInfoReason.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "disputeCase.paymentSystem.code"
        showAsResultColumn = 0
        label = "dms_disputeCase.paymentSystem"
    }
    property {
        name = "disputeCase.transactionReference"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionReference"
    }
    property {
        name = "disputeCase.transactionDate"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionDate"
    }
    property {
        name = "disputeCase.senderParticipant"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "disputeCase.senderParticipant.description"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "disputeCase.senderParticipant.code"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "disputeCase.senderParticipant.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "disputeCase.senderParticipant.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.senderParticipant"
    }
    property {
        name = "disputeCase.receiverParticipant"
        showAsResultColumn = 0
        label = "dms_disputeCase.receiverParticipant"
    }
    property {
        name = "disputeCase.receiverParticipant.description"
        showAsResultColumn = 0
        label = "dms_disputeCase.receiverParticipant"
    }
    property {
        name = "disputeCase.receiverParticipant.code"
        showAsResultColumn = 0
        label = "dms_disputeCase.receiverParticipant"
    }
    property {
        name = "disputeCase.receiverParticipant.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.receiverParticipant"
    }
    property {
        name = "disputeCase.receiverParticipant.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.receiverParticipant"
    }
    property {
        name = "disputeCase.transactionCurrency"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionCurrency"
    }
    property {
        name = "disputeCase.transactionCurrency.stringISOCode"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionCurrency"
    }
    property {
        name = "disputeCase.transactionCurrency.id"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionCurrency"
    }
    property {
        name = "disputeCase.transactionCurrency.codeNamePair"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionCurrency"
    }
    property {
        name = "disputeCase.transactionAmount"
        showAsResultColumn = 0
        label = "dms_disputeCase.transactionAmount"
    }
    property {
        name = "disputeCase.disputedAmount"
        showAsResultColumn = 0
        label = "dms_disputeCase.disputedAmount"
    }
    property {
        name = "disputeCase.lastAction"
        showAsResultColumn = 0
        label = "dms_disputeCase.lastAction"
    }
    property {
        name = "disputeCase.lastActionBy"
        showAsResultColumn = 0
        label = "dms_disputeCase.lastActionBy"
    }
    property {
        name = "disputeCase.lastNote"
        showAsResultColumn = 0
        label = "dms_disputeCase.lastNote"
    }
    property {
        name = "disputeCase.id"
        showAsResultColumn = 0
        label = "dms_claimantBankCaseManagement.disputeCase"
    }
    property {
        name = "note"
        showAsResultColumn = 1
        label = "dms_claimantBankCaseManagement.note"
    }
    property {
        name = "statusId"
        showAsResultColumn = 1
        label = "management_status"
    }
    property {
        name = "disputeCase.statusId.description"
        showAsResultColumn = 1
        label = "dispute_status"
    }
    property {
        name = "id"
        showAsResultColumn = 0
        label = "dms_claimantBankCaseManagement.id"
    }
    property {
        name = "deletedFlag"
        showAsResultColumn = 0
        label = "deletedflag_8118121393"
    }
    property {
        name = "deletedBy"
        showAsResultColumn = 0
        label = "deletedby_8118121388"
    }
    property {
        name = "lockedBy"
        showAsResultColumn = 0
        label = "lockedby_8118121383"
    }
    property {
        name = "updatedBy"
        showAsResultColumn = 0
        label = "updatedby_8118121378"
    }
    property {
        name = "createdBy"
        showAsResultColumn = 0
        label = "createdby_8118121373"
    }
    property {
        name = "deletedOn"
        showAsResultColumn = 0
        label = "deletedon_8118121368"
    }
    property {
        name = "lockedUntil"
        showAsResultColumn = 0
        label = "lockeduntil_8118121363"
    }
    property {
        name = "updatingDate"
        showAsResultColumn = 0
        label = "updatingdate_8118121358"
    }
    property {
        name = "creationDate"
        showAsResultColumn = 1
        label = "creationdate_8118121353"
    }
    property {
        name = "statusId.description"
        showAsResultColumn = 0
        label = "statusid.description_8118121348"
    }
    property {
        name = "statusId.codeNamePair"
        showAsResultColumn = 0
        label = "statusid.codenamepair_8118121343"
    }
    property {
        name = "statusId.id"
        showAsResultColumn = 0
        label = "statusid.id_8118121338"
    }
    property {
        name = "orgId"
        showAsResultColumn = 0
        label = "orgId"
    }
    property {
        name = "draftStatus"
        showAsResultColumn = 0
        label = "draftStatus"
    }
    property {
        name = "jfwDraft.id"
        showAsResultColumn = 0
        label = "jfwDraft.id"
    }
    property {
        name = "jfwDraft.draftData"
        showAsResultColumn = 0
        label = "jfwDraft.draftData"
    }
    property {
        name = "disputeCase.urgency"
        showAsResultColumn = 1
        label = "dms_disputeCase.urgency"
    }
}