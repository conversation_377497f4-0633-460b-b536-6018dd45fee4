package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_SLAConfiguration_Entity = Entity {
    name = "com.progressoft.dms.entities.DMS_SLAConfiguration"
	property {
		name = "slaConfigurationParty"
		showAsResultColumn = 0
		label = "sla_config_party"
	}
	property {
		name = "slaConfigurationParty.name"
		showAsResultColumn = 1
		label = "sla_config_party"
	}
	property {
		name = "paymentSystem"
		showAsResultColumn = 0
		label = "sla_config_payment_system"
	}
	property {
		name = "paymentSystem.codeNamePair"
		showAsResultColumn = 1
		label = "sla_config_payment_system"
	}
	property {
		name = "stage"
		showAsResultColumn = 1
		label = "sla_config_stage"
	}
	property {
		name = "maxDays"
		showAsResultColumn = 1
		label = "sla_config_max_days"
	}
	property {
		name = "slaConfigAutomaticAction"
		showAsResultColumn = 0
		label = "sla_config_automatic_action"
	}
	property {
		name = "slaConfigAutomaticAction.name"
		showAsResultColumn = 1
		label = "sla_config_automatic_action"
	}
	property {
		name = "urgency"
		showAsResultColumn = 1
		label = "sla_config_urgency"
	}
}