package com.progressoft.dms.dsl.entities

import com.progressoft.jupiter.kotlin.dsl.Entity

val DMS_Correspondence_Entity = Entity {
    name = "com.progressoft.dms.entities.DMS_Correspondence"

	property {
		name = "id"
		showAsResultColumn = 0
		label = "correspondence_id"
	}
	property {
		name = "dispute"
		showAsResultColumn = 0
		label = "correspondence_dispute_case"
	}
	property {
		name = "dispute.Id"
		showAsResultColumn = 0
		label = "correspondence_dispute_case"
	}
	property {
		name = "action"
		showAsResultColumn = 1
		label = "correspondence_action"
	}
	property {
		name = "actionBy"
		showAsResultColumn = 1
		label = "correspondence_action_by"
	}
	property {
		name = "note"
		showAsResultColumn = 1
		label = "correspondence_note"
	}
	property {
		name = "creationDate"
		showAsResultColumn = 1
		label = "creationdate_8118121353"
	}
	property {
		name = "reason"
		showAsResultColumn = 1
		label = "reason_8118121353"
	}
}