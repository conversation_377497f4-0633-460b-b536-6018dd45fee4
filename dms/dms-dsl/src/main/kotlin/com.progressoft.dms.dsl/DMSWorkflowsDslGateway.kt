package com.progressoft.dms.dsl

import com.progressoft.dms.dsl.workflows.*
import com.progressoft.jfw.model.bussinessobject.workflow.JFWWorkflow
import com.progressoft.jupiter.rest.repository.workflow.WorkflowDSLGateway
import org.springframework.stereotype.Component

@Component
class DMSWorkflowsDslGateway : WorkflowDSLGateway {

    override fun allWorkflows(): MutableList<JFWWorkflow> {

        return mutableListOf(
            WF_DMS_Bank_Workflow,
            WF_DMS_MakerChecker_Workflow,
            WF_DMS_ClaimantBankCaseManagement_Workflow,
            WF_DMS_DefendantBankCaseManagement_Workflow,
            WF_DMS_DefendantNCBCaseManagement_Workflow,
            WF_DMS_ClaimantNCBCaseManagement_Workflow,
            WF_DMS_OperatorCaseManagement_Workflow,
            WF_DMS_DisputeCase_Workflow,
            WF_DMS_NCB_Workflow,
            DMS_ReasonManagement_Workflow,
            WF_DMS_Edit,
            WF_DMS_DisputeDetailsReport_Workflow,
            WF_UserReport_Workflow,
            WF_DMS_TermsAndConditions_Workflow
        )
    }
}
