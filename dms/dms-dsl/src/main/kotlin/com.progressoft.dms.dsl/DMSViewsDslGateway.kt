package com.progressoft.dms.dsl


import com.progressoft.dms.dsl.views.*
import com.progressoft.repository.view.ViewDSLGateway

import com.progressoft.repository.view.dto.ViewDto
import org.springframework.stereotype.Component


@Component
class DMSViewsDslGateway : ViewDSLGateway {

    override fun allViews(): List<ViewDto> {
        return mutableListOf(
            DMS_Participants_View,
            DMS_NationalCentralBank_View,
            DMS_SystemConfiguration_View,
            DMS_PaymentSystems_View,
            DMS_DefendantNCBCaseManagement_View,
            DMS_ClaimantBankCaseManagement_View,
            DMS_DefendantBankCaseManagement_View,
            DMS_ClaimantNCBCaseManagement_View,
            DMS_OperatorCaseManagement_View,
            DMS_TransactionAdditionalInfo_View,
            DMS_DisputeCase_View,
            DMS_DisputeDetailsReport_View,
            DMS_Correspondence_View,
            DMS_SLAConfiguration_View,
            DMS_ReasonManagement_View,
            DMS_DisputeSummaryReport_View,
            DMS_TermsAndConditions_View,
            DMS_AclConfigs_View
        )
    }
}
