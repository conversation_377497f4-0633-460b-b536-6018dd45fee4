package com.progressoft.dms.dsl

import com.progressoft.dms.dsl.labels.*
import com.progressoft.jfw.labels.dto.Labels
import com.progressoft.jfw.labels.gateway.LabelsDSLGateway
import org.springframework.stereotype.Component

@Component
class DMSLabelsDslGateway : LabelsDSLGateway {

    override fun allLabels(): MutableList<Labels> {
        return mutableListOf(
            LABELS_ONE,
            LABELS_TWO,
            DMS_DefendantNCBCaseManagement_Label,
            DMS_ClaimantBankCaseManagement_Label,
            DMS_DefendantBankCaseManagement_Label,
            DMS_ClaimantNCBCaseManagement_Label,
            DMS_OperatorCaseManagement_Label,
            DMS_TransactionAdditionalInfo_Label,
            DMS_DisputeCase_Label,
            DMS_DisputeDetailsView_Label,
            DMS_Correspondence_Label,
            DMS_ReasonManagement_Label,
            DMS_JFW_Labels,
            DMS_DisputeSummaryView_Label,
            DMS_ValidationMessages_Label,
            DMS_OTP_Labels
        )
    }
}