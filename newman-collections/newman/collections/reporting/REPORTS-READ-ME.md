***Steps to Generate Reports using the PS Reporting service***


- 1- Uploading the report template to the reporting service.
- 2- Create a new report request entity from the UI.
- 3- Call the reporting service in a JFW post function for generation execution.
- 4- Download the result attachment

************************************************************************************************

# 1- Uploading the report template to the reporting service.

In order to generate reports, you will need to Upload your report template to the reporting service first.

- 1- Use the Postman collection [Reports-Collection.json](Reports-Collection.json) to Upload the template.
- - Prepare your (jrxml) file and attach it to the request, templates are stored in the project repository at : /rsc/reports/.
- - Prepare the request body in a json file and attach it to the Upload request as [disputesSummaryReportRequest.json](..%2F..%2F..%2F..%2Frsc%2Freports%2Ftemplates%2Fdisputes_summary_report%2FdisputesSummaryReportRequest.json).
- - Send the request to the reporting service URL (Define the global variable `REPORTING_SVC_URL`)
- - You can make get the list of uploaded reports using the `list-reports` request in the collection.

# 3- Call the reporting service in a JFW post function for generation execution.

We use the reporting service for `Async` report generation, when you call the async execution api it will return the request uuid in the response,
and then you will update the report request JFW entity with this uuid so the attachments service can link the result of the generation (File) to the request record.

************************************************************************************************

**Uploading Reports Files Via Newman CLI**

To run [Reports-Collection.json](Reports-Collection.json) using newman CLI, we have to provide the following global variables:

- 1- Reporting Service URL (REPORTING_SVC_URL).
- 2- Json File Absolute Path (JSON_FILE_PATH).
- 3- JRXML File Absolute Path (JRXML_FILE_PATH).

************************************************************************************************

**Use the following command to run a report's collection:**

`newman run [Collection Name] --global-var "REPORTING_SVC_URL=VALUE" --global-var "JSON_FILE_PATH=VALUE" --global-var "JRXML_FILE_PATH=VALUE"
`

***Example:***

`newman run Reports-Collection.json --global-var "REPORTING_SVC_URL=http://localhost:8080" --global-var "JRXML_FILE_PATH=/home/<USER>/disputes_summary.jrxml" --global-var "JSON_FILE_PATH=/home/<USER>/disputesSummaryReportRequest.json"`

**Note:** you should run the command for each report, each time you have to change the global variables values (files path).
