{"info": {"_postman_id": "ac183836-e8f6-4795-a361-d5dec23b57ee", "name": "Reports-Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "27667991"}, "item": [{"name": "Upload-Report", "item": [{"name": "upload-report", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "    pm.environment.set(\"report_id\", pm.response.json().uuid);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "report", "type": "file", "src": "{{JSON_FILE_PATH}}"}, {"key": "jrxml", "type": "file", "src": "{{JRXML_FILE_PATH}}"}]}, "url": {"raw": "{{REPORTING_SVC_URL}}/api/v1/reports", "host": ["{{REPORTING_SVC_URL}}"], "path": ["api", "v1", "reports"]}, "description": "create report"}, "response": []}]}, {"name": "List-Reports", "item": [{"name": "list-reports", "request": {"method": "GET", "header": [], "url": {"raw": "{{REPORTING_SVC_URL}}/api/v1/reports", "host": ["{{REPORTING_SVC_URL}}"], "path": ["api", "v1", "reports"]}}, "response": []}]}]}