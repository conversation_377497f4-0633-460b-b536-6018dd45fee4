package com.progressoft.dms.entities

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import jakarta.persistence.*
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement
import java.io.Serializable

@Entity
@Table(name = "DMS_TermsAndConditions")
@XmlRootElement(name = "DMS_TermsAndConditions")
@XmlAccessorType(XmlAccessType.FIELD)
class DMS_TermsAndConditions : JFWEntity(), Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.AUTO)
    @Column(name = "ID", nullable = false, insertable = false)
    var id: Long = 0

    @Column(name = "TERMSANDCONDITIONS", nullable = true, length = 2000)
    var termsAndConditions: String? = null

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        val Id = if ("null" == id.toString() + "") 0 else id.toInt()
        result = prime * result + (Id xor (Id ushr 32))
        result = prime * result + (if ((termsAndConditions == null)) 0 else termsAndConditions.hashCode())
        return result
    }

    override fun equals(obj: Any?): Boolean {
        if (this === obj) return true
        else if (obj == null) return false
        else if (javaClass != obj.javaClass) return false
        else {
            val other: DMS_TermsAndConditions = obj as DMS_TermsAndConditions
            return this.hashCode() == other.hashCode()
        }
    }

    override fun toString(): String {
        return "DMS_TermsAndConditions(id=$id, termsAndConditions=$termsAndConditions)"
    }
}