package com.progressoft.dms.entities

import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity
import java.io.Serializable
import jakarta.persistence.*
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement

@Entity
@Table(
    name = "DMS_SLAConfigAutomaticAction",
    uniqueConstraints = [UniqueConstraint(columnNames = ["NAME", "Z_TENANT_ID"])]
)
@XmlRootElement(name = "SLAConfigAutomaticAction")
@XmlAccessorType(XmlAccessType.FIELD)
open class DMS_SLAConfigAutomaticAction : JFWLookableEntity(), Serializable {
    @Id
    @SequenceGenerator(allocationSize = 1,name = "SEQ_DMS_SLAConfigAutomaticAction", sequenceName = "SEQ_DMS_SLAConfigAutomaticAction")
    @GeneratedValue(generator = "SEQ_DMS_SLAConfigAutomaticAction")
    @Column(name = "ID", nullable = false, insertable = false)
    open var id: Long = 0


    companion object {
        private const val serialVersionUID = 1L
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as DMS_SLAConfigAutomaticAction

        if (id != other.id) return false

        return true
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }

    override fun toString(): String {
        return "DMS_SLAConfigAutomaticAction(id=$id)"
    }


}