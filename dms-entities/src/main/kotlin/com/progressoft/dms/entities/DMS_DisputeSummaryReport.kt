package com.progressoft.dms.entities

import com.progressoft.jfw.model.bussinessobject.core.JfwCountry
import com.progressoft.jupiter.annotation.WithValueProvider
import jakarta.persistence.*
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement
import org.hibernate.annotations.Cascade
import org.hibernate.annotations.CascadeType
import java.sql.Timestamp

@Entity
@Table(name = "DMS_DisputeSummaryReport")
@XmlRootElement(name = "DisputeSummaryReport")
@XmlAccessorType(XmlAccessType.FIELD)
open class DMS_DisputeSummaryReport : DMS_UserReport() {

    @Id
    @SequenceGenerator(allocationSize = 1,name = "SEQ_DMS_DISPUTESUMMARYREPORT", sequenceName = "SEQ_DMS_DISPUTESUMMARYREPORT")
    @GeneratedValue(generator = "SEQ_DMS_DISPUTESUMMARYREPORT")
    @Column(name = "ID", nullable = false, insertable = false)
    open var id: Long = 0

    @ManyToMany(cascade = [jakarta.persistence.CascadeType.PERSIST, jakarta.persistence.CascadeType.DETACH, jakarta.persistence.CascadeType.REMOVE, jakarta.persistence.CascadeType.REFRESH, jakarta.persistence.CascadeType.MERGE])
    @Cascade(CascadeType.REPLICATE)
    @JoinTable(
        name = "DMS_DisputeSummaryRprtParticipants",
        joinColumns = [JoinColumn(name = "report_id", referencedColumnName = "ID", table = "DMS_DisputeSummaryReport")],
        inverseJoinColumns = [JoinColumn(
            name = "participant_id",
            referencedColumnName = "ID",
            table = "DMS_Participants"
        )]
    )
    @WithValueProvider(jupiterValueProviderBean = "reportParticipantProvider", keyProperty = "id")
    open var participants: MutableList<DMS_Participant> = mutableListOf()

    @Column(name = "nature", length = 9)
    @WithValueProvider(jupiterValueProviderBean = "natureProvider")
    open var nature: String? = null

    @ManyToOne(fetch = FetchType.LAZY)
    @Cascade(CascadeType.REPLICATE)
    @JoinColumn(name = "reason_id")
    @WithValueProvider(jupiterValueProviderBean = "reasonValueProvider", keyProperty = "id")
    open var reason: DMS_ReasonManagement? = null

    @ManyToOne(fetch = FetchType.LAZY)
    @Cascade(CascadeType.REPLICATE)
    @JoinColumn(name = "paymentsys_id")
    @WithValueProvider(jupiterValueProviderBean = "paymentSystemProvider", keyProperty = "id")
    open var paymentSystem: DMS_PaymentSystems? = null

    @Column(name = "date_from", nullable = true, length = 20)
    open var dateFrom: Timestamp? = null

    @Column(name = "date_to", nullable = true, length = 20)
    open var dateTo: Timestamp? = null

    @Column(name = "type", length = 5)
    @WithValueProvider(jupiterValueProviderBean = "reportTypesProvider")
    open var type: String? = null

}