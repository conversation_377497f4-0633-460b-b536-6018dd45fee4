package com.progressoft.dms.entities

import com.progressoft.jfw.model.bussinessobject.core.JFWLookableEntity
import com.progressoft.jfw.model.bussinessobject.core.JfwCountry
import com.progressoft.jupiter.annotation.WithValueProvider
import java.io.Serializable
import jakarta.persistence.*
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement

@Entity
@Table(name = "DMS_NCB", uniqueConstraints = [
        UniqueConstraint(columnNames = ["CODE", "Z_TENANT_ID"])
])
@XmlRootElement(name = "NationalBanks")
@XmlAccessorType(XmlAccessType.FIELD)
open class DMS_NationalCentralBank : JFWLookableEntity(), Serializable {

    @Id
    @SequenceGenerator(name = "SEQ_DMS_NationalCentralBanks", sequenceName = "SEQ_DMS_NationalCentralBanks",allocationSize = 1)
    @GeneratedValue(generator = "SEQ_DMS_NationalCentralBanks")
    open var id: Long = 0

    @Column(name = "CHECKERCOMMENTS", nullable = true, length = 258)
    open var checkerComments: String? = null

    @Column(name = "EMAIL", nullable = false, length = 50)
    open var email: String? = null

    @OneToOne
    @JoinColumn(name = "COUNTRYID", nullable=false)
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    @WithValueProvider(jupiterValueProviderBean = "countriesProvider", keyProperty = "id")
    open var country: JfwCountry = JfwCountry()

    @OneToMany(mappedBy = "nationalCentralBank")
    @org.hibernate.annotations.Cascade(org.hibernate.annotations.CascadeType.REPLICATE)
    open var participants = mutableListOf<DMS_Participant>();
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other == null || javaClass != other.javaClass) return false
        val that = other as DMS_NationalCentralBank
        return id != that.id
    }

    override fun hashCode(): Int {
        val prime = 31
        var result = 1
        val Id = if ("null" == id.toString() + "") 0 else id.toInt()
        result = prime * result + (Id xor (Id ushr 32))
        result = prime * result + if (getCode() == null) 0 else getCode().hashCode()
        result = prime * result + if (getName() == null) 0 else getName().hashCode()
        result = prime * result + if (email == null) 0 else email.hashCode()
        return result
    }

}