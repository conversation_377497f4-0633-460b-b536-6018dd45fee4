package com.progressoft.dms.entities

import com.progressoft.jupiter.annotation.WithValueProvider
import org.hibernate.annotations.Cascade
import org.hibernate.annotations.CascadeType
import java.io.Serializable
import jakarta.persistence.*
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement

@Entity
@Table(name = "DMS_DefendantNCBCaseManagement")
@XmlRootElement(name = "DefendantNCBCaseManagement")
@XmlAccessorType(XmlAccessType.FIELD)
open class DMS_DefendantNCBCaseManagement : DMS_BaseCaseManagement(), Serializable {
    @Id
    @SequenceGenerator(allocationSize = 1,name = "SEQ_DMS_DefendantNCBCaseManagement", sequenceName = "SEQ_DMS_DefendantNCBCaseManagement")
    @GeneratedValue(generator = "SEQ_DMS_DefendantNCBCaseManagement")
    @Column(name = "ID", nullable = false, insertable = false)
    open var id: Long = 0

    @OneToOne
    @Cascade(CascadeType.REPLICATE)
    @JoinColumn(name = "REJECTIONREASON")
    @WithValueProvider(jupiterValueProviderBean = "rejectionReasonValueProvider", keyProperty = "id")
    open var rejectionReason: DMS_ReasonManagement? = null

    @OneToOne
    @Cascade(CascadeType.REPLICATE)
    @JoinColumn(name = "REPRESENTREASON")
    @WithValueProvider(jupiterValueProviderBean = "representReasonValueProvider", keyProperty = "id")
    open var representReason: DMS_ReasonManagement? = null

    @OneToOne
    @Cascade(CascadeType.REPLICATE)
    @JoinColumn(name = "REQADDINFOREASON")
    @WithValueProvider(jupiterValueProviderBean = "requestInfoReasonValueProvider", keyProperty = "id")
    open var reqAddInfoReason: DMS_ReasonManagement? = null

    fun resolveReason(): DMS_ReasonManagement? {
        val reason = if (representReason != null){
            representReason
        } else if (rejectionReason != null) {
            rejectionReason
        } else {
            reqAddInfoReason
        }
        representReason = null;
        rejectionReason = null;
        reqAddInfoReason = null;

        return reason
    }
    companion object {
        private const val serialVersionUID = 1L
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is DMS_DefendantNCBCaseManagement) return false

        if (id != other.id) return false
        if (disputeCase != other.disputeCase) return false
        if (note != other.note) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + (disputeCase?.hashCode() ?: 0)
        result = 31 * result + (note?.hashCode() ?: 0)
        return result
    }

    override fun toString(): String {
        return "DMS_DefendantNCBCaseManagement(id=$id, note=$note)"
    }


}