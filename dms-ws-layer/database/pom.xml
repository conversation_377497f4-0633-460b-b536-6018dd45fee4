<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.progressoft.acacia.kuwait.dms</groupId>
        <artifactId>dms-ws-layer</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>database</artifactId>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
            <version>${spring.boot.version}</version>
        </dependency>
        <dependency>
            <groupId>com.progressoft.jupiter</groupId>
            <artifactId>model-core</artifactId>
            <version>${jfw.version}</version>
        </dependency>
        <dependency>
            <groupId>com.progressoft.jupiter</groupId>
            <artifactId>model-country</artifactId>
            <version>${jfw.version}</version>
        </dependency>
        <dependency>
            <groupId>com.progressoft.jupiter</groupId>
            <artifactId>model-attachment</artifactId>
            <version>${jfw.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc11</artifactId>
            <version>23.6.0.24.10</version>
        </dependency>
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-core</artifactId>
            <version>6.4.4.Final</version>
        </dependency>
    </dependencies>
</project>