package com.progressoft.dms.kuwait.database.repositories

import com.progressoft.dms.kuwait.database.entities.DMS_ReasonManagement
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ReasonManagementRepository: JpaRepository<DMS_ReasonManagement, Long> {

    @Query("SELECT r FROM DMS_ReasonManagement r WHERE r.statusCode = 'APPROVED'")
    fun findApprovedReasons(): List<DMS_ReasonManagement>
}
