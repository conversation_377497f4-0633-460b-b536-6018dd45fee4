package com.progressoft.dms.kuwait.database.entities

import com.progressoft.jfw.model.bussinessobject.core.JFWEntity
import com.progressoft.jupiter.annotation.WithValueProvider
import org.hibernate.annotations.Cascade
import org.hibernate.annotations.CascadeType
import java.io.Serializable
import jakarta.persistence.*
import jakarta.xml.bind.annotation.XmlAccessType
import jakarta.xml.bind.annotation.XmlAccessorType
import jakarta.xml.bind.annotation.XmlRootElement


@Entity
@Table(
    name = "DMS_TransactionAdditionalInfos",
    uniqueConstraints = [UniqueConstraint(columnNames = ["ADDITIONALINFOKEY", "Z_TENANT_ID"])]
)
@XmlRootElement(name = "TransactionAdditionalInfos")
@XmlAccessorType(XmlAccessType.FIELD)
open class DMS_TransactionAdditionalInfo : JFWEntity(), Serializable {

    @Id
    @SequenceGenerator(allocationSize = 1,name = "SEQ_DMS_TRN_INFO", sequenceName = "SEQ_DMS_TRN_INFO")
    @GeneratedValue(generator = "SEQ_DMS_TRN_INFO")
    @Column(name = "ID", nullable = false, insertable = false)
    open var id: Long = 0

    @Column(name = "ADDITIONALINFOKEY", nullable = false, length = 100)
    open var additionalInfoKey: String? = null

    @Column(name = "ADDITIONALINFOVALUE", nullable = false, length = 1000)
    open var additionalInfoValue: String? = null

    @ManyToOne(fetch = FetchType.LAZY)
    @Cascade(CascadeType.REPLICATE)
    @JoinColumn(name = "DISPUTE_ID", nullable = true)
    @WithValueProvider(jupiterValueProviderBean = "additionalInfoDisputeProvider", keyProperty = "id")
    open var disputeCase: DMS_DisputeCase? = null


    companion object {
        private const val serialVersionUID = 1L
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as DMS_TransactionAdditionalInfo

        if (id != other.id) return false
        if (additionalInfoKey != other.additionalInfoKey) return false
        if (additionalInfoValue != other.additionalInfoValue) return false
        if (disputeCase != other.disputeCase) return false

        return true
    }

    override fun hashCode(): Int {
        var result = id.hashCode()
        result = 31 * result + (additionalInfoKey?.hashCode() ?: 0)
        result = 31 * result + (additionalInfoValue?.hashCode() ?: 0)
        result = 31 * result + (disputeCase?.hashCode() ?: 0)
        return result
    }
}