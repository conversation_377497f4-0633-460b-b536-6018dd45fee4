<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.progressoft.acacia.kuwait.dms</groupId>
        <artifactId>dms-kuwait</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>dms-ws-layer</artifactId>
    <packaging>pom</packaging>
    <properties>
        <java.version>17</java.version>
    </properties>
    <modules>
        <module>database</module>
        <module>web-services</module>
    </modules>
    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.14.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional> <!-- Optional is necessary for this to work properly -->
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.0</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-webmvc</artifactId>
                <version>${spring.web.mvc.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${spring.web.mvc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <scm>
        <connection>${connection.url}</connection>
        <developerConnection>${connection.url}</developerConnection>
        <url>${base.url}</url>
        <tag>rocca-1.0</tag>
    </scm>
    <distributionManagement>
        <repository>
            <id>RELEASES</id>
            <url>${release.distribution.url}</url>
        </repository>
        <snapshotRepository>
            <id>SNAPSHOTS</id>
            <url>${snapshot.distribution.url}</url>
        </snapshotRepository>
    </distributionManagement>

</project>