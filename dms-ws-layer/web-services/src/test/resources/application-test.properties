# h2 db for test
spring.datasource.url=jdbc:h2:mem:test;Mode=MySQL
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=sa
spring.h2.console.enabled=true
spring.sql.init.mode=always
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# JPA settings
spring.jpa.properties.hibernate.id.new_generator_mappings=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.defer-datasource-initialization=true
spring.jpa.properties.hibernate.enable_lazy_load_no_trans=true
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

###### Cryptography #####
crypto.signature_validation_enabled=${SIGNATURE_VALIDATION_ENABLED:true}
crypto.security_secret=${SECURITY_SECRET:}
crypto.signing_function=${SIGNING_FUNCTION:SHA256withRSA}
crypto.keystore_path=${KEYSTORE_PATH:/private-keystore.jks}
crypto.keystore_password=${KEYSTORE_PASSWORD:P@ssw0rd}
crypto.cert_alias=${CERT_ALIAS:kw-ws-key}