package com.progressoft.dms.kuwait.webservices.service

import com.progressoft.dms.kuwait.webservices.DMSWebServicesApplication
import com.progressoft.dms.kuwait.webservices.config.TestConfig
import org.junit.Assert.assertTrue
import org.junit.Test
import org.junit.runner.RunWith
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.junit4.SpringRunner
import java.time.LocalDateTime
import java.time.LocalDateTime.now

@RunWith(SpringRunner::class)
@SpringBootTest(classes = [DMSWebServicesApplication::class, TestConfig::class])
@ActiveProfiles("test")
class CryptographyImplTest {

    @Autowired
    private lateinit var cryptographer: CryptographyServiceImpl

    @Test
    fun testSignVerifyWithoutDate() {
        val sign = sign(null)
        println(sign)
        assertTrue(verify(sign, null))
    }

    @Test
    fun testSignVerifyWithDate() {
        val sign = sign(DATE)
        println(sign)
        assertTrue(verify(sign, DATE))
    }

    private fun verify(signature: String, date: LocalDateTime?) = if (date == null)
        cryptographer.verify(signature, CONTENT).isSuccess
    else
        cryptographer.verify(signature, CONTENT, date).isSuccess


    private fun sign(date: LocalDateTime?) = if (date == null)
        cryptographer.sign(CONTENT).value()
    else
        cryptographer.sign(CONTENT, date).value()


    companion object {
        private val DATE = now()
        private val CONTENT = this::class.java.getResourceAsStream("/Sample.xml")?.let { String(it.readAllBytes()) }!!
    }
}