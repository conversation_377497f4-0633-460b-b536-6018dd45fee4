package com.progressoft.dms.kuwait.webservices.pacs28

import org.junit.jupiter.api.Assertions.assertEquals
import java.io.BufferedReader
import java.io.IOException
import java.io.InputStream
import java.io.InputStreamReader
import java.nio.charset.StandardCharsets
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import java.util.stream.Collectors

class TestUtil {
    fun convertToOffsetDateTime(date: String?): OffsetDateTime {
        return OffsetDateTime.parse(date, DateTimeFormatter.ISO_OFFSET_DATE_TIME)
    }

    private fun removeWhiteSpaces(expected: String): String {
        return expected.replace("\\s+<".toRegex(), "\r\n<")
            .replace("\\s+&lt;".toRegex(), "\r\n&lt;")
            .trim { it <= ' ' }
    }

    fun removeTags(xmlContent: String, vararg tags: String): String {
        var modifiedContent = xmlContent
        for (tag in tags) {
            modifiedContent = modifiedContent.replace(("<$tag>.*?</$tag>").toRegex(), "")
        }
        return modifiedContent
    }

    fun assertEqualsXMLStringIgnoreWhiteSpacesBetweenTags(expected: String, actual: String, vararg tags: String) {
        assertEquals(
            removeWhiteSpaces(removeTags(expected, *tags)),
            removeWhiteSpaces(removeTags(actual, *tags))
        )
    }

    fun getMessageContent(fileName: String): String {
        try {
            BufferedReader(
                InputStreamReader(
                    Objects.requireNonNull<InputStream>(TestUtil::class.java.getResourceAsStream("/$fileName")),
                StandardCharsets.UTF_8)
            ).use { reader ->
                val text = reader.lines().collect(Collectors.joining("\n"))
                return removeWhiteSpaces(text)
            }
        } catch (e: IOException) {
            throw RuntimeException(e)
        }
    }
}