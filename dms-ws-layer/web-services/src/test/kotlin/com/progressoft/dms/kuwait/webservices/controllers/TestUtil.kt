package com.progressoft.dms.kuwait.webservices.controllers

import com.google.gson.Gson
import com.google.gson.GsonBuilder
import org.apache.commons.io.IOUtils

class TestUtil {
    companion object {
        fun <T> toJSON(obj: T): String {
            val gson = gsonBuilderGenerator()
            return gson.toJson(obj)
        }

        fun <T> fromJSON(jsonString: String, type: Class<T>): T {
            val gson = gsonBuilderGenerator()
            return gson.fromJson(jsonString, type)
        }

        private fun gsonBuilderGenerator(): Gson {
            val builder = GsonBuilder()
            builder.setPrettyPrinting()
            return builder.create()
        }

        fun getJsonDataFromFile(fileName: String): String {
            val testFile = TestUtil::class.java.classLoader.getResource("testdata/$fileName.json")
            return IOUtils.toString(testFile, "UTF-8")
        }
    }
}