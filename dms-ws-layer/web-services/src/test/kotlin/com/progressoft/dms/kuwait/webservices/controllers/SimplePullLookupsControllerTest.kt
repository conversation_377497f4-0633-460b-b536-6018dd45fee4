package com.progressoft.dms.kuwait.webservices.controllers

import com.progressoft.dms.kuwait.webservices.DMSWebServicesApplication
import com.progressoft.dms.kuwait.webservices.config.TestConfig
import org.junit.jupiter.api.Test
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles

@SpringBootTest(classes = [DMSWebServicesApplication::class, TestConfig::class])
@ActiveProfiles("test")
class SimplePullLookupsControllerTest {

    @Test
    fun testBasic() {
        // Basic test to check if context loads
        assert(true)
    }
}
