package com.progressoft.dms.kuwait.webservices.controllers

import com.progressoft.dms.kuwait.webservices.controllers.TestUtil.Companion.fromJSON
import com.progressoft.dms.kuwait.webservices.controllers.TestUtil.Companion.getJsonDataFromFile
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto
import org.hamcrest.CoreMatchers.hasItems
import org.hamcrest.MatcherAssert.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.http.MediaType.APPLICATION_JSON
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status

@AutoConfigureMockMvc
@SpringBootTest
@ActiveProfiles("test")
class CreateDisputeControllerTest {

    @Autowired
    private lateinit var mvc: MockMvc

    @Test
    fun givenValidRequest_whenCreateDispute_thenReturnSuccessResponse() {
        mvc.perform(
            post(END_POINT)
                .contentType(APPLICATION_JSON)
                .content(getJsonDataFromFile(VALID_FILE_PATH))
        )
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.message").value(SUCCESS))
            .andExpect(jsonPath("$.data").exists())
    }

    @Test
    fun givenInvalidRequest_whenCreateDispute_thenReturnBadRequest() {
        val apiResponse = mvc.perform(
            post(END_POINT)
                .contentType(APPLICATION_JSON)
                .content(getJsonDataFromFile(INVALID_FILE_PATH))
        )
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.message").value(ERROR))
            .andExpect(jsonPath("$.errorList").isArray)
            .andReturn().response.contentAsString
        val response = fromJSON(apiResponse, ResponseBuilderDto::class.java)
        val errorListMessage = (response.errorList?.map { it.message }?.toMutableList())
        assertThat(
            errorListMessage, hasItems(
                "Message ID must not be blank",
                "Message date must be exactly 19 characters long",
                "Attachments must not be blank",
                "Urgency should be either of these values: Normal, Urgent, Good-Faith-Collection, Complaint",
                "Message date must follow the pattern yyyy-MM-dd'T'HH:mm:ss",
                "Payment System should be either of these values: ACH, KECCS, KASSIP",
            )
        )
    }

    companion object {
        private const val SUCCESS = "success"
        private const val ERROR = "error"
        private const val END_POINT = "/api/v1/create"
        private const val VALID_FILE_PATH = "createdispute/create_dispute_valid_request"
        private const val INVALID_FILE_PATH = "createdispute/create_dispute_invalid_request"
    }
}
