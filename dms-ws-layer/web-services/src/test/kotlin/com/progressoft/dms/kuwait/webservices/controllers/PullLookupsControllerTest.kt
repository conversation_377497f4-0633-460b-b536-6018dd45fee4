package com.progressoft.dms.kuwait.webservices.controllers

import com.fasterxml.jackson.databind.ObjectMapper
import com.progressoft.dms.kuwait.webservices.DMSWebServicesApplication
import com.progressoft.dms.kuwait.webservices.config.TestConfig
import com.progressoft.dms.kuwait.webservices.controllers.TestUtil.Companion.fromJSON
import com.progressoft.dms.kuwait.webservices.dtos.PullLookupsResponseDto
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.junit.jupiter.api.Assertions.*

@AutoConfigureMockMvc
@SpringBootTest(classes = [DMSWebServicesApplication::class, TestConfig::class])
@ActiveProfiles("test")
class PullLookupsControllerTest {

    @Autowired
    private lateinit var mvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Test
    fun givenValidRequestWithSignature_whenCallPullLookupsApi_thenShouldReturn200Response() {
        val size = 10
        val page = 0
        val validSignature = "Js56xLg/2WGq92jJulx1rijjsiynKxnuoGcYf8VPzeUPaHK6pDWAp+Z1HEw0peetyruRZ2iEqEeWvAfEuKcQQcQzEgK5uud+keQ+6oNvdaxw+WOD0bDMIVc3rfGFYysPOHXCVZeyXRvoL5hWO6oEpUfo1gybKBGWmQO7BNR4d4nEItjbZJI34OHTqZEOrzmzETv4f1TKqLtYbyBtg4E/14GBV5zc4gCrE6JMzmdiLMR8D/w3g3oJknNfftwLTtW6PDv8U3F8JgB1ZDTO0MYIsP2B0yxbhGO+Tl+4v5mNpDmo1agLbWvbnXqbsyBZPUCIn3KUokqjZmP3Y2aYjYdKJQ=="
        
        val apiResponse = mvc.perform(
            get(END_POINT)
                .header("X-Signature", validSignature)
                .param("size", size.toString())
                .param("page", page.toString()))
            .andExpect(status().isOk)
            .andExpect(jsonPath("$.messageHeader").exists())
            .andExpect(jsonPath("$.messageHeader.signature").exists())
            .andExpect(jsonPath("$.messageHeader.message-date").exists())
            .andExpect(jsonPath("$.messageBody").exists())
            .andExpect(jsonPath("$.messageBody.message-id").value("50000001"))
            .andExpect(jsonPath("$.messageBody.reasons").isArray)
            .andExpect(jsonPath("$.messageBody.participants").isArray)
            .andExpect(jsonPath("$.messageBody.payment-systems").isArray)
            .andReturn().response.contentAsString
            
        val response = fromJSON(apiResponse, PullLookupsResponseDto::class.java)
        
        // Verify response structure
        assertNotNull(response.messageHeader)
        assertNotNull(response.messageHeader.signature)
        assertNotNull(response.messageHeader.messageDate)
        assertNotNull(response.messageBody)
        assertEquals("50000001", response.messageBody.messageId)
        
        // Verify reasons
        assertTrue(response.messageBody.reasons.isNotEmpty())
        val reason = response.messageBody.reasons[0]
        assertEquals("1", reason.code)
        assertEquals("General", reason.name)
        assertEquals("General Reason For test", reason.description)
        assertTrue(reason.isDispute)
        assertTrue(reason.isRejection)
        assertTrue(reason.isRepresentment)
        assertTrue(reason.isRequestAdditionalInfo)
        
        // Verify participants
        assertTrue(response.messageBody.participants.isNotEmpty())
        val participant = response.messageBody.participants[0]
        assertEquals("RJHIKWK0", participant.bicCode)
        assertEquals("RJH", participant.shortName)
        assertEquals("Al Rajhi Bank Kuwait", participant.fullName)
        assertEquals("Al Rajhi Bank Kuwait", participant.description)
        
        // Verify payment systems
        assertTrue(response.messageBody.paymentSystems.isNotEmpty())
        val paymentSystem = response.messageBody.paymentSystems[0]
        assertEquals("ACH", paymentSystem.bicCode)
        assertEquals("Automated Clearing House", paymentSystem.fullName)
        assertEquals("Automated Clearing House", paymentSystem.description)
    }

    @Test
    fun givenMissingSignature_whenCallPullLookupsApi_thenShouldReturn400Response() {
        val size = 10
        val page = 0
        
        mvc.perform(
            get(END_POINT)
                .param("size", size.toString())
                .param("page", page.toString()))
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.code").value("auth-0001"))
            .andExpect(jsonPath("$.message").value("Invalid signature"))
    }

    @Test
    fun givenInvalidSignature_whenCallPullLookupsApi_thenShouldReturn400Response() {
        val size = 10
        val page = 0
        val invalidSignature = "aW52YWxpZFNpZ25hdHVyZQ=="
        
        mvc.perform(
            get(END_POINT)
                .header("X-Signature", invalidSignature)
                .param("size", size.toString())
                .param("page", page.toString()))
            .andExpect(status().isBadRequest)
            .andExpect(jsonPath("$.code").value("auth-0001"))
            .andExpect(jsonPath("$.message").value("Invalid signature"))
    }

    companion object {
        private const val END_POINT = "/api/v1/pull-lookups"
    }
}
