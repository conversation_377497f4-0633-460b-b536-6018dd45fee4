package com.progressoft.dms.kuwait.webservices.pacs28

import org.junit.jupiter.api.Test

class Pacs28SerializerImplTest {
    private val pacs28Serializer = Pacs28SerializerImpl()
    private val testUtil = TestUtil()

    @Test
    fun givenPacs28RequestThenReturnsPacs28MessageAsString() {
        val request = Pacs28Dto("string", testUtil.convertToOffsetDateTime("2012-08-02T00:32:22+05:30"), "123")
        val serializedMessage: String = pacs28Serializer.serialize(request)
        testUtil.assertEqualsXMLStringIgnoreWhiteSpacesBetweenTags(
            testUtil.getMessageContent("valid_pacs_028.xml"), serializedMessage,
        )
    }


}