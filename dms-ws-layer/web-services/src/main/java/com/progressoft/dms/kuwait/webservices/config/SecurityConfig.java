package com.progressoft.dms.kuwait.webservices.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
public class SecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(
                                "/actuator/health",
                                "/api/v1/create",
                                "/api/v1/pull",
                                "/api/v1/pull-lookups"
                        ).permitAll()
                        .anyRequest().authenticated()
                )
                .csrf().disable();
        return http.build();
    }
}
