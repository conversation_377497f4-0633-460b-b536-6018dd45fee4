##H2 db for test
#spring.datasource.url=jdbc:h2:mem:ach
#spring.datasource.driverClassName=org.h2.Driver
#spring.datasource.username=sa
#spring.datasource.password=sa
#spring.h2.console.enabled=true
#spring.sql.init.mode=always

#OracleDB connection settings
spring.datasource.url=${db_url:***********************************}
spring.datasource.driver-class-name=oracle.jdbc.OracleDriver
spring.datasource.username=${db_user:dms}
spring.datasource.password=${db_password:12345}
spring.datasource.hikari.connection-test-query=SELECT 1 from dual

# JPA settings
spring.jpa.database-platform=org.hibernate.dialect.OracleDialect
#spring.jpa.database-platform=org.hibernate.dialect.H2Dialect
spring.jpa.properties.hibernate.id.new_generator_mappings=false
spring.jpa.hibernate.ddl-auto=none
spring.jpa.defer-datasource-initialization=true
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl

###### Cryptography #####
crypto.signature_validation_enabled=${SIGNATURE_VALIDATION_ENABLED:true}
crypto.security_secret=${SECURITY_SECRET:}
crypto.signing_function=${SIGNING_FUNCTION:SHA256withRSA}
crypto.keystore_path=${KEYSTORE_PATH:/private-keystore.jks}
crypto.keystore_password=${KEYSTORE_PASSWORD:P@ssw0rd}
crypto.cert_alias=${CERT_ALIAS:kw-ws-key}