<?xml version="1.0" encoding="UTF-8"?>
<!--Generated by Standards Editor on 2024 Feb 29 15:51:14, ISO 20022 version : 2013-->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.028.001.06" xmlns:xs="http://www.w3.org/2001/XMLSchema" elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.028.001.06">
    <xs:element name="Document" type="Document"/>
    <xs:complexType name="AccountIdentification4Choice">
        <xs:choice>
            <xs:element name="IBAN" type="IBAN2007Identifier"/>
            <xs:element name="Othr" type="GenericAccountIdentification1"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="AccountSchemeName1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalAccountIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="ActiveOrHistoricCurrencyAndAmount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="18"/>
            <xs:minInclusive value="0"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActiveOrHistoricCurrencyAndAmount">
        <xs:simpleContent>
            <xs:extension base="ActiveOrHistoricCurrencyAndAmount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="ActiveOrHistoricCurrencyCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AddressType2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADDR"/>
            <xs:enumeration value="PBOX"/>
            <xs:enumeration value="HOME"/>
            <xs:enumeration value="BIZZ"/>
            <xs:enumeration value="MLTO"/>
            <xs:enumeration value="DLVY"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="AddressType3Choice">
        <xs:choice>
            <xs:element name="Cd" type="AddressType2Code"/>
            <xs:element name="Prtry" type="GenericIdentification30"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="AmendmentInformationDetails15">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlMndtId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCdtrSchmeId" type="PartyIdentification272"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCdtrAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCdtrAgtAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlDbtr" type="PartyIdentification272"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlDbtrAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlDbtrAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlDbtrAgtAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlFnlColltnDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlFrqcy" type="Frequency36Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlRsn" type="MandateSetupReason1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTrckgDays" type="Exact2NumericText"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AmountType4Choice">
        <xs:choice>
            <xs:element name="InstdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="EqvtAmt" type="EquivalentAmount2"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="AnyBICDec2014Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BICFIDec2014Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{4,4}[A-Z]{2,2}[A-Z0-9]{2,2}([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification8">
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification23"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BrnchId" type="BranchData5"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BranchData5">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress27"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccount40">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="AccountIdentification4Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CashAccountType2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ccy" type="ActiveOrHistoricCurrencyCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Prxy" type="ProxyAccountIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccountType2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalCashAccountType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CategoryPurpose1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalCategoryPurpose1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="ClearingChannel2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RTGS"/>
            <xs:enumeration value="RTNS"/>
            <xs:enumeration value="MPNS"/>
            <xs:enumeration value="BOOK"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClearingSystemIdentification2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalClearingSystemIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ClearingSystemIdentification3Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalCashClearingSystem1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ClearingSystemMemberIdentification2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysId" type="ClearingSystemIdentification2Choice"/>
            <xs:element name="MmbId" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Contact13">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NmPrfx" type="NamePrefix2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PhneNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MobNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FaxNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="URLAdr" type="Max2048Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="EmailAdr" type="Max256Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="EmailPurp" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="JobTitl" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rspnsblty" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="Max70Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="OtherContact1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrefrdMtd" type="PreferredContactMethod2Code"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CreditDebitCode">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CRDT"/>
            <xs:enumeration value="DBIT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CreditTransferMandateData1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="MndtId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="MandateTypeInformation2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DtOfSgntr" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DtOfVrfctn" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ElctrncSgntr" type="Max10KBinary"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrstPmtDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FnlPmtDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Frqcy" type="Frequency36Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="MandateSetupReason1Choice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceInformation3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CreditorReferenceType3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ref" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceType2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalCreditorReferenceType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceType3">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="CreditorReferenceType2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateAndDateTime2Choice">
        <xs:choice>
            <xs:element name="Dt" type="ISODate"/>
            <xs:element name="DtTm" type="ISODateTime"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="DateAndPlaceOfBirth1">
        <xs:sequence>
            <xs:element name="BirthDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvcOfBirth" type="Max35Text"/>
            <xs:element name="CityOfBirth" type="Max35Text"/>
            <xs:element name="CtryOfBirth" type="CountryCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateAndType1">
        <xs:sequence>
            <xs:element name="Tp" type="DateType2Choice"/>
            <xs:element name="Dt" type="ISODate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DatePeriod2">
        <xs:sequence>
            <xs:element name="FrDt" type="ISODate"/>
            <xs:element name="ToDt" type="ISODate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateType2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalDateType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="DecimalNumber">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="17"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="FIToFIPmtStsReq" type="FIToFIPaymentStatusRequestV06"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentAdjustment1">
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="Max4Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentAmount1">
        <xs:sequence>
            <xs:element name="Tp" type="DocumentAmountType1Choice"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentAmountType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalDocumentAmountType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="DocumentLineIdentification1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="DocumentLineType1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nb" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDt" type="ISODate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineInformation2">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="Id" type="DocumentLineIdentification1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Desc" type="Max2048Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Amt" type="RemittanceAmount4"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineType1">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="DocumentLineType1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentLineType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalDocumentLineType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="DocumentType1">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="DocumentType2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentType2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalDocumentType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="EquivalentAmount2">
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="CcyOfTrf" type="ActiveOrHistoricCurrencyCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Exact2NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Exact4AlphaNumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-zA-Z0-9]{4}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalAccountIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCashAccountType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCashClearingSystem1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="3"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCategoryPurpose1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalClearingSystemIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCreditorReferenceType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalDateType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalDocumentAmountType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalDocumentLineType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalDocumentType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalFinancialInstitutionIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalGarnishmentType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalLocalInstrument1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalMandateSetupReason1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalOrganisationIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPersonIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalProxyAccountType1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPurpose1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalServiceLevel1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FIToFIPaymentStatusRequestV06">
        <xs:sequence>
            <xs:element name="GrpHdr" type="GroupHeader109"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="OrgnlGrpInf" type="OriginalGroupInformation27"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="TxInf" type="PaymentTransaction158"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SplmtryData" type="SupplementaryData1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialIdentificationSchemeName1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalFinancialInstitutionIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification23">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="BICFI" type="BICFIDec2014Identifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress27"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Othr" type="GenericFinancialIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Frequency36Choice">
        <xs:choice>
            <xs:element name="Tp" type="Frequency6Code"/>
            <xs:element name="Prd" type="FrequencyPeriod1"/>
            <xs:element name="PtInTm" type="FrequencyAndMoment1"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="Frequency6Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="YEAR"/>
            <xs:enumeration value="MNTH"/>
            <xs:enumeration value="QURT"/>
            <xs:enumeration value="MIAN"/>
            <xs:enumeration value="WEEK"/>
            <xs:enumeration value="DAIL"/>
            <xs:enumeration value="ADHO"/>
            <xs:enumeration value="INDA"/>
            <xs:enumeration value="FRTN"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FrequencyAndMoment1">
        <xs:sequence>
            <xs:element name="Tp" type="Frequency6Code"/>
            <xs:element name="PtInTm" type="Exact2NumericText"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FrequencyPeriod1">
        <xs:sequence>
            <xs:element name="Tp" type="Frequency6Code"/>
            <xs:element name="CntPerPrd" type="DecimalNumber"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Garnishment4">
        <xs:sequence>
            <xs:element name="Tp" type="GarnishmentType1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Grnshee" type="PartyIdentification272"/>
            <xs:element maxOccurs="1" minOccurs="0" name="GrnshmtAdmstr" type="PartyIdentification272"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RefNb" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FmlyMdclInsrncInd" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MplyeeTermntnInd" type="TrueFalseIndicator"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GarnishmentType1">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="GarnishmentType1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GarnishmentType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalGarnishmentType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="GenericAccountIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max34Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="AccountSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericFinancialIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="FinancialIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericIdentification30">
        <xs:sequence>
            <xs:element name="Id" type="Exact4AlphaNumericText"/>
            <xs:element name="Issr" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericOrganisationIdentification3">
        <xs:sequence>
            <xs:element name="Id" type="Max256Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericPersonIdentification2">
        <xs:sequence>
            <xs:element name="Id" type="Max256Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="PersonIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GroupHeader109">
        <xs:sequence>
            <xs:element name="MsgId" type="Max35Text"/>
            <xs:element name="CreDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAgt" type="BranchAndFinancialInstitutionIdentification8"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="IBAN2007Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ISODate">
        <xs:restriction base="xs:date"/>
    </xs:simpleType>
    <xs:simpleType name="ISODateTime">
        <xs:restriction base="xs:dateTime"/>
    </xs:simpleType>
    <xs:simpleType name="ISOYear">
        <xs:restriction base="xs:gYear"/>
    </xs:simpleType>
    <xs:simpleType name="LEIIdentifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z0-9]{18,18}[0-9]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="LocalInstrument2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalLocalInstrument1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="MandateClassification1Choice">
        <xs:choice>
            <xs:element name="Cd" type="MandateClassification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="MandateClassification1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FIXE"/>
            <xs:enumeration value="USGB"/>
            <xs:enumeration value="VARI"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="MandateRelatedData3Choice">
        <xs:choice>
            <xs:element maxOccurs="1" minOccurs="0" name="DrctDbtMndt" type="MandateRelatedInformation16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtTrfMndt" type="CreditTransferMandateData1"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="MandateRelatedInformation16">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="MndtId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DtOfSgntr" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AmdmntInd" type="TrueFalseIndicator"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AmdmntInfDtls" type="AmendmentInformationDetails15"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ElctrncSgntr" type="Max1025Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrstColltnDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FnlColltnDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Frqcy" type="Frequency36Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="MandateSetupReason1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TrckgDays" type="Exact2NumericText"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="MandateSetupReason1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalMandateSetupReason1Code"/>
            <xs:element name="Prtry" type="Max70Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="MandateTypeInformation2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="SvcLvl" type="ServiceLevel8Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LclInstrm" type="LocalInstrument2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyPurp" type="CategoryPurpose1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Clssfctn" type="MandateClassification1Choice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Max1025Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="1025"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max10KBinary">
        <xs:restriction base="xs:base64Binary">
            <xs:minLength value="1"/>
            <xs:maxLength value="10240"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max128Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="128"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max140Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="140"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max15NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,15}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max16Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max2048Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="2048"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max256Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="256"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max34Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="34"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max350Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="350"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max35Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max4Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max70Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="NamePrefix2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DOCT"/>
            <xs:enumeration value="MADM"/>
            <xs:enumeration value="MISS"/>
            <xs:enumeration value="MIST"/>
            <xs:enumeration value="MIKS"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Number">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="0"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="OrganisationIdentification39">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AnyBIC" type="AnyBICDec2014Identifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LEI" type="LEIIdentifier"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericOrganisationIdentification3"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OrganisationIdentificationSchemeName1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalOrganisationIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="OriginalGroupInformation27">
        <xs:sequence>
            <xs:element name="OrgnlMsgId" type="Max35Text"/>
            <xs:element name="OrgnlMsgNmId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCreDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlNbOfTxs" type="Max15NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCtrlSum" type="DecimalNumber"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalGroupInformation29">
        <xs:sequence>
            <xs:element name="OrgnlMsgId" type="Max35Text"/>
            <xs:element name="OrgnlMsgNmId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlCreDtTm" type="ISODateTime"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OriginalTransactionReference42">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrBkSttlmAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Amt" type="AmountType4Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrBkSttlmDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ReqdColltnDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ReqdExctnDt" type="DateAndDateTime2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrSchmeId" type="PartyIdentification272"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmInf" type="SettlementInstruction15"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtTpInf" type="PaymentTypeInformation27"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtMtd" type="PaymentMethod4Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MndtRltdInf" type="MandateRelatedData3Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtInf" type="RemittanceInformation22"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtDbtr" type="Party50Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dbtr" type="Party50Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgtAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgtAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Cdtr" type="Party50Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtCdtr" type="Party50Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Purp" type="Purpose2Choice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OtherContact1">
        <xs:sequence>
            <xs:element name="ChanlTp" type="Max4Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Max128Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Party50Choice">
        <xs:choice>
            <xs:element name="Pty" type="PartyIdentification272"/>
            <xs:element name="Agt" type="BranchAndFinancialInstitutionIdentification8"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Party52Choice">
        <xs:choice>
            <xs:element name="OrgId" type="OrganisationIdentification39"/>
            <xs:element name="PrvtId" type="PersonIdentification18"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="PartyIdentification272">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress27"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Party52Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtryOfRes" type="CountryCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtctDtls" type="Contact13"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PaymentMethod4Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CHK"/>
            <xs:enumeration value="TRF"/>
            <xs:enumeration value="DD"/>
            <xs:enumeration value="TRA"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PaymentTransaction158">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="StsReqId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlGrpInf" type="OriginalGroupInformation29"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlInstrId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlEndToEndId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlUETR" type="UUIDv4Identifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AccptncDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysRef" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="OrgnlTxRef" type="OriginalTransactionReference42"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SplmtryData" type="SupplementaryData1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentTypeInformation27">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrPrty" type="Priority2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrChanl" type="ClearingChannel2Code"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="SvcLvl" type="ServiceLevel8Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LclInstrm" type="LocalInstrument2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SeqTp" type="SequenceType3Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyPurp" type="CategoryPurpose1Choice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PercentageRate">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="10"/>
            <xs:totalDigits value="11"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PersonIdentification18">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericPersonIdentification2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentificationSchemeName1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalPersonIdentification1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:simpleType name="PhoneNumber">
        <xs:restriction base="xs:string">
            <xs:pattern value="\+[0-9]{1,3}-[0-9()+\-]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PostalAddress27">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AdrTp" type="AddressType3Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CareOf" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SubDept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StrtNm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNb" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Flr" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UnitNb" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstBx" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Room" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstCd" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnNm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnLctnNm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DstrctNm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrySubDvsn" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode"/>
            <xs:element maxOccurs="7" minOccurs="0" name="AdrLine" type="Max70Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PreferredContactMethod2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MAIL"/>
            <xs:enumeration value="FAXX"/>
            <xs:enumeration value="LETT"/>
            <xs:enumeration value="CELL"/>
            <xs:enumeration value="ONLI"/>
            <xs:enumeration value="PHON"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Priority2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="HIGH"/>
            <xs:enumeration value="NORM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ProxyAccountIdentification1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ProxyAccountType1Choice"/>
            <xs:element name="Id" type="Max2048Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ProxyAccountType1Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalProxyAccountType1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="Purpose2Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalPurpose1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentInformation8">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="DocumentType1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nb" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDt" type="DateAndType1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="LineDtls" type="DocumentLineInformation2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceAmount4">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="RmtAmtAndTp" type="DocumentAmount1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AdjstmntAmtAndRsn" type="DocumentAdjustment1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceInformation22">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Ustrd" type="Max140Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Strd" type="StructuredRemittanceInformation18"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="SequenceType3Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FRST"/>
            <xs:enumeration value="RCUR"/>
            <xs:enumeration value="FNAL"/>
            <xs:enumeration value="OOFF"/>
            <xs:enumeration value="RPRE"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ServiceLevel8Choice">
        <xs:choice>
            <xs:element name="Cd" type="ExternalServiceLevel1Code"/>
            <xs:element name="Prtry" type="Max35Text"/>
        </xs:choice>
    </xs:complexType>
    <xs:complexType name="SettlementInstruction15">
        <xs:sequence>
            <xs:element name="SttlmMtd" type="SettlementMethod1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSys" type="ClearingSystemIdentification3Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgRmbrsmntAgtAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdRmbrsmntAgtAcct" type="CashAccount40"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ThrdRmbrsmntAgt" type="BranchAndFinancialInstitutionIdentification8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ThrdRmbrsmntAgtAcct" type="CashAccount40"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="SettlementMethod1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INDA"/>
            <xs:enumeration value="INGA"/>
            <xs:enumeration value="COVE"/>
            <xs:enumeration value="CLRG"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="StructuredRemittanceInformation18">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="RfrdDocInf" type="ReferredDocumentInformation8"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RfrdDocAmt" type="RemittanceAmount4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrRefInf" type="CreditorReferenceInformation3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Invcr" type="PartyIdentification272"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Invcee" type="PartyIdentification272"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxRmt" type="TaxData1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="GrnshmtRmt" type="Garnishment4"/>
            <xs:element maxOccurs="3" minOccurs="0" name="AddtlRmtInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SupplementaryData1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="PlcAndNm" type="Max350Text"/>
            <xs:element name="Envlp" type="SupplementaryDataEnvelope1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SupplementaryDataEnvelope1">
        <xs:sequence>
            <xs:any namespace="##any" processContents="lax"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxAmount3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Rate" type="PercentageRate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Dtls" type="TaxRecordDetails3"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxAuthorisation1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Titl" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxData1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Cdtr" type="TaxParty1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dbtr" type="TaxParty2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtDbtr" type="TaxParty2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AdmstnZone" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RefNb" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Mtd" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlTaxblBaseAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlTaxAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SeqNb" type="Number"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Rcrd" type="TaxRecord3"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxParty1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RegnId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxTp" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxParty2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RegnId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxTp" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Authstn" type="TaxAuthorisation1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxPeriod3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Yr" type="ISOYear"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="TaxRecordPeriod1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrToDt" type="DatePeriod2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxRecord3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctgy" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyDtls" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrSts" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CertId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrmsCd" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Prd" type="TaxPeriod3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxAmt" type="TaxAmount3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="TaxRecordDetails3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Prd" type="TaxPeriod3"/>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="TaxRecordPeriod1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MM01"/>
            <xs:enumeration value="MM02"/>
            <xs:enumeration value="MM03"/>
            <xs:enumeration value="MM04"/>
            <xs:enumeration value="MM05"/>
            <xs:enumeration value="MM06"/>
            <xs:enumeration value="MM07"/>
            <xs:enumeration value="MM08"/>
            <xs:enumeration value="MM09"/>
            <xs:enumeration value="MM10"/>
            <xs:enumeration value="MM11"/>
            <xs:enumeration value="MM12"/>
            <xs:enumeration value="QTR1"/>
            <xs:enumeration value="QTR2"/>
            <xs:enumeration value="QTR3"/>
            <xs:enumeration value="QTR4"/>
            <xs:enumeration value="HLF1"/>
            <xs:enumeration value="HLF2"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="TrueFalseIndicator">
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>
    <xs:simpleType name="UUIDv4Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[a-f0-9]{8}-[a-f0-9]{4}-4[a-f0-9]{3}-[89ab][a-f0-9]{3}-[a-f0-9]{12}"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
