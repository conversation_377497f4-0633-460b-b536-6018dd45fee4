package com.progressoft.dms.kuwait.webservices.service

import com.progressoft.Failure
import com.progressoft.Result
import java.time.LocalDateTime

interface CryptographyService {
    fun verify(signature: String, content: String, date: LocalDateTime): Result<Void, Failure>
    fun verify(signature: String, content: String): Result<Void, Failure>
    fun sign(content: String): Result<String, Failure>
    fun sign(content: String, date: LocalDateTime): Result<String, Failure>
}