package com.progressoft.dms.kuwait.webservices.service

import com.progressoft.Failure
import com.progressoft.Result
import com.progressoft.Result.success
import com.progressoft.dms.kuwait.webservices.crypto.CryptographyImpl
import com.progressoft.dms.kuwait.webservices.crypto.KeyRepositoryLoader
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.time.LocalDateTime

@Component
class CryptographyServiceImpl @Autowired constructor(
    keyRepositoryLoader: KeyRepositoryLoader,
    @Value("\${crypto.signing_function}")
    private val signFunction: String
) : CryptographyService {

    @Value("\${crypto.signature_validation_enabled}")
    private val signatureValidation: Boolean = true

    @Value("\${crypto.cert_alias}")
    private lateinit var certAlias: String

    @Value("\${crypto.security_secret}")
    private lateinit var certSecret: String

    @Value("\${crypto.keystore_password}")
    private lateinit var certPassword: String

    private val cryptography = CryptographyImpl(keyRepositoryLoader.getKeyRepository().value(), signFunction)

    override fun verify(signature: String, content: String, date: LocalDateTime): Result<Void, Failure> {
        return if (signatureValidation) cryptography.verify(
            content,
            signature,
            certAlias,
            certSecret,
            date
        ) else success()
    }

    override fun verify(signature: String, content: String): Result<Void, Failure> {
        return if (signatureValidation) cryptography.verify(
            content,
            signature,
            certAlias,
            certSecret
        ) else success()
    }

    override fun sign(content: String): Result<String, Failure> {
        return if (signatureValidation) cryptography.sign(
            content,
            certAlias,
            certPassword,
            certSecret
        ) else success("")
    }

    override fun sign(content: String, date: LocalDateTime): Result<String, Failure> {
        return if (signatureValidation) cryptography.sign(
            content,
            certAlias,
            certPassword,
            certSecret,
            date
        ) else success("")
    }
}