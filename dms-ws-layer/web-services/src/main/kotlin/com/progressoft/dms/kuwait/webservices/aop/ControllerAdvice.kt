package com.progressoft.dms.kuwait.webservices.aop

import com.progressoft.dms.kuwait.webservices.errorhandling.FieldErrorDto
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto.Companion.errorResponse
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto.Companion.serverErrorResponse
import com.progressoft.dms.kuwait.webservices.exception.InvalidRequestException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus.valueOf
import org.springframework.http.ResponseEntity
import org.springframework.http.ResponseEntity.badRequest
import org.springframework.http.ResponseEntity.status
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice


@RestControllerAdvice
class ControllerAdvice {
    private val logger = LoggerFactory.getLogger(ControllerAdvice::class.java)

    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleBadRequestException(ex: MethodArgumentNotValidException): ResponseEntity<ResponseBuilderDto> {
        val errorList = mutableListOf<FieldErrorDto>()
        ex.bindingResult.fieldErrors.forEach { error ->
            errorList.add(FieldErrorDto(error.defaultMessage!!, error.field, error.rejectedValue))
        }
        return badRequest().body(errorResponse("Bad Request", errorList))
    }

    @ExceptionHandler(InvalidRequestException::class)
    fun handleInvalidRequestException(ex: InvalidRequestException): ResponseEntity<Map<String, String>> {
        logger.warn("Invalid request: {}", ex.message)
        val errorResponse = mapOf(
            "code" to ex.code,
            "message" to ex.message!!
        )
        return badRequest().body(errorResponse)
    }

    @ExceptionHandler(Exception::class)
    fun handleInternalServerError(ex: Exception): ResponseEntity<ResponseBuilderDto> {
        logger.error("unknown error", ex)
        return status(500).body(
            serverErrorResponse(
                mutableListOf(),
                valueOf(500),
                "This request can't be processed at the moment, please try again later"
            )
        )
    }
}