package com.progressoft.dms.kuwait.webservices.controllers

import com.progressoft.dms.kuwait.webservices.dtos.PullDisputesDto
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto.Companion.successResponse
import jakarta.validation.Valid
import lombok.AllArgsConstructor
import lombok.extern.slf4j.Slf4j
import org.slf4j.LoggerFactory.getLogger
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@Slf4j
@EnableAutoConfiguration
@AllArgsConstructor
class PullDisputesController {

    @PostMapping("/api/v1/pull")
    fun pullDisputes(
        @Valid @RequestBody request: PullDisputesDto,
        @RequestParam(value = "size", required = false, defaultValue = "10") size: Int,
        @RequestParam(value = "page", required = false, defaultValue = "0") page: Int
    ): ResponseEntity<ResponseBuilderDto> {
        logger.info("Received DTO: $request")
        return ResponseEntity.ok(successResponse(request))
    }

    companion object {
        private val logger = getLogger(PullDisputesController::class.java.name)
    }
}