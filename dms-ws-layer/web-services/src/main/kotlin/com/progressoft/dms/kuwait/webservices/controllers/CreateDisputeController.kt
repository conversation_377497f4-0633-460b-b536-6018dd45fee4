package com.progressoft.dms.kuwait.webservices.controllers

import com.progressoft.dms.kuwait.webservices.dtos.CreateDisputeRequestDto
import com.progressoft.dms.kuwait.webservices.dtos.CreateDisputeResponseDto
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto.Companion.successResponse
import jakarta.validation.Valid
import org.slf4j.LoggerFactory.getLogger
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
class CreateDisputeController {
    @RequestMapping("/api/v1/create")
    fun createDispute(@Valid @RequestBody dto: CreateDisputeRequestDto): ResponseEntity<ResponseBuilderDto> {
        LOG.info("Received create dispute request for assigner bank: ${dto.assignerBank}")
        return ResponseEntity.ok(successResponse(CreateDisputeResponseDto()))
    }

    companion object {
        private val LOG = getLogger(CreateDisputeController::class.java)
    }
}