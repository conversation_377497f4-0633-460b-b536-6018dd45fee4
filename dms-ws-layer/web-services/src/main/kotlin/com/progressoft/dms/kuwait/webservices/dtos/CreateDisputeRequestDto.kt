package com.progressoft.dms.kuwait.webservices.dtos

import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.Valid
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import java.math.BigDecimal

data class
CreateDisputeRequestDto(
    @field:NotBlank(message = "Message ID must not be blank")
    @JsonProperty("message-id")
    val messageId: String,

    @field:Pattern(
        regexp = "\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}",
        message = "Message date must follow the pattern yyyy-MM-dd'T'HH:mm:ss"
    )
    @field:Size(min = 19, max = 19, message = "Message date must be exactly 19 characters long")
    @JsonProperty("message-date")
    val messageDate: String,

    @field:NotBlank(message = "Payment system must not be blank")
    @field:Pattern(
        regexp = "^(ACH|KECCS|KASSIP)\$",
        message = "Payment System should be either of these values: ACH, KECCS, KASSIP"
    )
    @JsonProperty("payment-system")
    val paymentSystem: String,

    @field:NotBlank(message = "Reason code must not be blank")
    @JsonProperty("reason-code")
    val reasonCode: String,

    @field:NotBlank(message = "Assigner bank code must not be blank")
    @JsonProperty("assigner-bank")
    val assignerBank: String,

    @field:NotBlank(message = "Assignee bank code must not be blank")
    @JsonProperty("assignee-bank")
    val assigneeBank: String,

    @field:NotBlank(message = "Urgency must not be blank")
    @field:Pattern(
        regexp = "^(Normal|Urgent|Good-Faith-Collection|Complaint)\$",
        message = "Urgency should be either of these values: Normal, Urgent, Good-Faith-Collection, Complaint"
    )
    val urgency: String,

    @JsonProperty("dispute-over-dispute")
    val disputeOverDispute: Boolean? = false,

    val note: String? = null,

    @field:Valid
    @JsonProperty("transaction-info")
    val transactionInfo: TransactionInfo,

    @field:DecimalMin(value = "0.0", message = "Disputed amount must be a positive number")
    @JsonProperty("disputed-amount")
    val disputedAmount: BigDecimal,

    @field:NotBlank(message = "Attachments must not be blank")
    val attachments: String
)
