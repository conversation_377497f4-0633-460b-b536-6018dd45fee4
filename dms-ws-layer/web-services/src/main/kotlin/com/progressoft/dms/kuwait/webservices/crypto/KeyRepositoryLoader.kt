package com.progressoft.dms.kuwait.webservices.crypto

import com.google.common.cache.CacheBuilder.newBuilder
import com.google.common.cache.CacheLoader
import com.google.common.cache.LoadingCache
import com.progressoft.Failure
import com.progressoft.Result
import com.progressoft.Result.failure
import com.progressoft.Result.success
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeUnit

@Component
class KeyRepositoryLoader @Autowired constructor(
    @Value("\${crypto.keystore_path}")
    private val keyStorePath: String,
    @Value("\${crypto.keystore_password}")
    private val keyStorePassword: String
) {

    private val cachedKeystores: LoadingCache<String, KeyRepository> = newBuilder()
        .maximumSize(1)
        .expireAfterWrite(EXPIRE_AFTER_MINUTES.toLong(), TimeUnit.MINUTES)
        .build(object : CacheLoader<String, KeyRepository>() {
            @Throws(Exception::class)
            override fun load(key: String): KeyRepository {
                LOG.info("Loading keystore from file..{}", key)
                val repository = KeyRepository(keyStorePath, keyStorePassword)
                repository.loadKeyStore()
                return repository
            }
        })

    fun getKeyRepository(): Result<KeyRepository, Failure> {
        return try {
            return success(cachedKeystores[CACHE_KEY])
        } catch (e: ExecutionException) {
            return failure(Failure.withDetails(e))
        }
    }

    fun invalidate() {
        cachedKeystores.invalidateAll()
    }

    companion object {
        private val LOG: Logger = LoggerFactory.getLogger(KeyRepositoryLoader::class.java)
        private const val CACHE_KEY: String = "KEYSTORE"
        private const val EXPIRE_AFTER_MINUTES: Int = 60
    }
}