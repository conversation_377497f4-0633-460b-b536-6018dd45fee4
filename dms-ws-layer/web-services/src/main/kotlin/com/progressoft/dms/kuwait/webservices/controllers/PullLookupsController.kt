package com.progressoft.dms.kuwait.webservices.controllers

import com.progressoft.dms.kuwait.webservices.dtos.PullLookupsResponseDto
import com.progressoft.dms.kuwait.webservices.services.PullLookupsService
import com.progressoft.dms.kuwait.webservices.crypto.SigUtil
import com.progressoft.dms.kuwait.webservices.service.CryptographyService
import com.progressoft.dms.kuwait.webservices.exception.InvalidRequestException
import jakarta.servlet.http.HttpServletRequest
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDateTime

@RestController
class PullLookupsController(
    private val pullLookupsService: PullLookupsService,
    private val cryptographyService: CryptographyService
) {

    private val sigUtil = SigUtil()

    @GetMapping("/api/v1/pull-lookups")
    fun pullLookups(
        @RequestParam(value = "size", required = false, defaultValue = "10") size: Int,
        @RequestParam(value = "page", required = false, defaultValue = "0") page: Int,
        request: HttpServletRequest
    ): ResponseEntity<PullLookupsResponseDto> {
        logger.info("Received pull lookups request with size: $size, page: $page")

        return try {
            val signature = sigUtil.sigHeader(request)
            val requestContent = "size=$size&page=$page"

            try {
                val verificationResult = cryptographyService.verify(signature, requestContent, LocalDateTime.now())

                if (verificationResult.isFailure()) {
                    logger.warn("Signature verification failed")
                    throw InvalidRequestException(
                        SIGNATURE_ERROR_CODE,
                        SIGNATURE_ERROR_MESSAGE
                    )
                }
            } catch (e: IllegalArgumentException) {
                // Handle Base64 decoding errors
                logger.warn("Invalid signature format: ${e.message}")
                throw InvalidRequestException(
                    SIGNATURE_ERROR_CODE,
                    SIGNATURE_ERROR_MESSAGE
                )
            }

            val response = pullLookupsService.getLookups(size, page)
            ResponseEntity.ok(response)
        } catch (e: InvalidRequestException) {
            throw e
        } catch (e: Exception) {
            logger.error("Error processing pull lookups request", e)
            ResponseEntity.internalServerError().build()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(PullLookupsController::class.java)
        private const val SIGNATURE_ERROR_CODE = "auth-0001"
        private const val SIGNATURE_ERROR_MESSAGE = "Invalid signature"
    }
}