package com.progressoft.dms.kuwait.webservices.controllers

import com.progressoft.dms.kuwait.webservices.dtos.PullLookupsResponseDto
import com.progressoft.dms.kuwait.webservices.services.PullLookupsService
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto
import com.progressoft.dms.kuwait.webservices.errorhandling.ResponseBuilderDto.Companion.successResponse
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
class PullLookupsController(private val pullLookupsService: PullLookupsService) {

    @GetMapping("/api/v1/pull-lookups")
    fun pullLookups(
        @RequestParam(value = "size", required = false, defaultValue = "10") size: Int,
        @RequestParam(value = "page", required = false, defaultValue = "0") page: Int
    ): ResponseEntity<ResponseBuilderDto> {
        logger.info("Received pull lookups request with size: $size, page: $page")
        
        return try {
            val response = pullLookupsService.getLookups(size, page)
            ResponseEntity.ok(successResponse(response))
        } catch (e: Exception) {
            logger.error("Error processing pull lookups request", e)
            ResponseEntity.internalServerError().build()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(PullLookupsController::class.java)
        private const val SIGNATURE_ERROR_CODE = "auth-0001"
        private const val SIGNATURE_ERROR_MESSAGE = "Invalid signature"
    }
}