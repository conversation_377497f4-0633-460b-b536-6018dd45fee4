package com.progressoft.dms.kuwait.webservices.errorhandling

import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatus.BAD_REQUEST
import org.springframework.http.HttpStatus.OK
import java.util.*

data class ResponseBuilderDto(
    val message: String?,
    val status: HttpStatus?,
    val dateTime: Date,
    val data: Any?,
    val error: String?,
    val errorList: MutableList<FieldErrorDto>?
) {
    constructor(message: String, status: HttpStatus, data: Any?) : this(message, status, Date(), data, null, null)

    constructor(message: String, status: HttpStatus, error: String, errorList: MutableList<FieldErrorDto>) : this(
        message, status, Date(), null, error, errorList
    )

    constructor(status: HttpStatus, errorList: MutableList<FieldErrorDto>, reason: String) : this(
        ERROR, status, Date(), null, reason, errorList
    )

    companion object {
        private const val SUCCESS = "success"
        private const val ERROR = "error"
        fun successResponse(data: Any?) = ResponseBuilderDto(SUCCESS, OK, data)
        fun errorResponse(error: String, errorList: MutableList<FieldErrorDto>) =
            ResponseBuilderDto(ERROR, BAD_REQUEST, error, errorList)
        fun serverErrorResponse(errorList: MutableList<FieldErrorDto>, status: HttpStatus, reason: String) =
            ResponseBuilderDto(status, errorList, reason)
    }
}