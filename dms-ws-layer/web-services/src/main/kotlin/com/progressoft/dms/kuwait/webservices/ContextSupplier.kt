package com.progressoft.dms.kuwait.webservices

import jakarta.xml.bind.JAXBContext
import jakarta.xml.bind.JAXBException
import java.util.concurrent.ConcurrentHashMap
import java.util.function.Supplier

class ContextSupplier(private val packageName: String) : Supplier<JAXBContext> {
    override fun get(): JAXBContext {
        return MAP.computeIfAbsent(packageName) { packageName: String -> load(packageName) }
    }

    private class ContextLoadingException(cause: Throwable?) : RuntimeException(cause)


    companion object {
        private val MAP: MutableMap<String, JAXBContext> = ConcurrentHashMap()

        private fun load(packageName: String): JAXBContext {
            try {
                return JAXBContext.newInstance(packageName, ContextSupplier::class.java.classLoader)
            } catch (e: JAXBException) {
                throw ContextLoadingException(e)
            }
        }
    }
}
