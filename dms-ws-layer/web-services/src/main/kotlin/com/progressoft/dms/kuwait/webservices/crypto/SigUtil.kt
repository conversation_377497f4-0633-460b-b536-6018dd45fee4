package com.progressoft.dms.kuwait.webservices.crypto

import com.progressoft.dms.kuwait.webservices.exception.InvalidRequestException
import io.micrometer.common.util.StringUtils
import jakarta.servlet.http.HttpServletRequest

class SigUtil {
    fun sigHeader(servletRequest: HttpServletRequest): String {
        val signature = servletRequest.getHeader(SIG_HEADER)
        if (StringUtils.isEmpty(signature)) throw InvalidRequestException(SIGNATURE_ERROR_CODE, SIGNATURE_ERROR_MESSAGE)
        return signature
    }

    companion object {
        private const val SIG_HEADER: String = "X-Signature"
        const val SIGNATURE_ERROR_CODE: String = "auth-0001"
        const val SIGNATURE_ERROR_MESSAGE: String = "Invalid signature"
    }
}