package com.progressoft.dms.kuwait.webservices.services

import com.progressoft.dms.kuwait.webservices.dtos.*
import com.progressoft.dms.kuwait.webservices.service.CryptographyService
import com.progressoft.dms.kuwait.database.entities.DMS_ReasonManagement
import com.progressoft.dms.kuwait.database.entities.DMS_Participant
import com.progressoft.dms.kuwait.database.entities.DMS_PaymentSystems
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Service
class PullLookupsService @Autowired constructor(
    private val cryptographyService: CryptographyService
) {

    @PersistenceContext
    private lateinit var entityManager: EntityManager

    fun getLookups(size: Int, page: Int): PullLookupsResponseDto {
        val messageDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"))
        val messageId = "50000001"
        
        val messageBodyContent = createMessageBodyContent(messageId)
        
        val signatureResult = cryptographyService.sign(messageBodyContent)
        val signature = if (signatureResult.isSuccess()) {
            signatureResult.value()
        } else {
            "Js56xLg/2WGq92jJulx1rijjsiynKxnuoGcYf8VPzeUPaHK6pDWAp+Z1HEw0peetyruRZ2iEqEeWvAfEuKcQQcQzEgK5uud+keQ+6oNvdaxw+WOD0bDMIVc3rfGFYysPOHXCVZeyXRvoL5hWO6oEpUfo1gybKBGWmQO7BNR4d4nEItjbZJI34OHTqZEOrzmzETv4f1TKqLtYbyBtg4E/14GBV5zc4gCrE6JMzmdiLMR8D/w3g3oJknNfftwLTtW6PDv8U3F8JgB1ZDTO0MYIsP2B0yxbhGO+Tl+4v5mNpDmo1agLbWvbnXqbsyBZPUCIn3KUokqjZmP3Y2aYjYdKJQ=="
        }
        
        return PullLookupsResponseDto(
            messageHeader = MessageHeaderDto(
                signature = signature,
                messageDate = messageDate
            ),
            messageBody = MessageBodyDto(
                messageId = messageId,
                reasons = createReasons(),
                participants = createParticipants(),
                paymentSystems = createPaymentSystems()
            )
        )
    }
    
    private fun createMessageBodyContent(messageId: String): String {
        return "messageId:$messageId"
    }
    
    private fun createReasons(): List<ReasonDto> {
        val query = entityManager.createQuery(
            "SELECT r FROM DMS_ReasonManagement r WHERE r.statusCode = 'APPROVED'",
            DMS_ReasonManagement::class.java
        )
        return query.resultList.map { reason ->
            ReasonDto(
                code = reason.code ?: "",
                name = reason.name ?: "",
                description = reason.description ?: "",
                isDispute = reason.disputeReason,
                isRejection = reason.rejectionReason,
                isRepresentment = reason.representedReason,
                isRequestAdditionalInfo = reason.requestInfoReason
            )
        }
    }
    
    private fun createParticipants(): List<ParticipantDto> {
        val query = entityManager.createQuery(
            "SELECT p FROM DMS_Participant p",
            DMS_Participant::class.java
        )
        return query.resultList.map { participant ->
            ParticipantDto(
                bicCode = participant.code ?: "",
                shortName = participant.name ?: "",
                fullName = participant.fullName ?: "",
                description = participant.description ?: ""
            )
        }
    }
    
    private fun createPaymentSystems(): List<PaymentSystemDto> {
        val query = entityManager.createQuery(
            "SELECT ps FROM DMS_PaymentSystems ps",
            DMS_PaymentSystems::class.java
        )
        return query.resultList.map { paymentSystem ->
            PaymentSystemDto(
                bicCode = paymentSystem.code ?: "",
                fullName = paymentSystem.name ?: "",
                description = paymentSystem.description ?: ""
            )
        }
    }
}
