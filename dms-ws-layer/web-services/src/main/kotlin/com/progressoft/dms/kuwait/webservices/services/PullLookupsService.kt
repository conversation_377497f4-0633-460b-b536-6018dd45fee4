package com.progressoft.dms.kuwait.webservices.services

import com.progressoft.dms.kuwait.webservices.dtos.*
import com.progressoft.dms.kuwait.webservices.service.CryptographyService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Service
class PullLookupsService @Autowired constructor(
    private val cryptographyService: CryptographyService
) {

    fun getLookups(size: Int, page: Int): PullLookupsResponseDto {
        val messageDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"))
        val messageId = "50000001"
        
        // Create message body content for signing
        val messageBodyContent = createMessageBodyContent(messageId)
        
        // Sign the content
        val signatureResult = cryptographyService.sign(messageBodyContent)
        val signature = if (signatureResult.isSuccess()) {
            signatureResult.value()
        } else {
            // Fallback signature for demo/testing
            "Js56xLg/2WGq92jJulx1rijjsiynKxnuoGcYf8VPzeUPaHK6pDWAp+Z1HEw0peetyruRZ2iEqEeWvAfEuKcQQcQzEgK5uud+keQ+6oNvdaxw+WOD0bDMIVc3rfGFYysPOHXCVZeyXRvoL5hWO6oEpUfo1gybKBGWmQO7BNR4d4nEItjbZJI34OHTqZEOrzmzETv4f1TKqLtYbyBtg4E/14GBV5zc4gCrE6JMzmdiLMR8D/w3g3oJknNfftwLTtW6PDv8U3F8JgB1ZDTO0MYIsP2B0yxbhGO+Tl+4v5mNpDmo1agLbWvbnXqbsyBZPUCIn3KUokqjZmP3Y2aYjYdKJQ=="
        }
        
        return PullLookupsResponseDto(
            messageHeader = MessageHeaderDto(
                signature = signature,
                messageDate = messageDate
            ),
            messageBody = MessageBodyDto(
                messageId = messageId,
                reasons = createReasons(),
                participants = createParticipants(),
                paymentSystems = createPaymentSystems()
            )
        )
    }
    
    private fun createMessageBodyContent(messageId: String): String {
        // Create a string representation of the message body for signing
        return "messageId:$messageId"
    }
    
    private fun createReasons(): List<ReasonDto> {
        return listOf(
            ReasonDto(
                code = "1",
                name = "General",
                description = "General Reason For test",
                isDispute = true,
                isRejection = true,
                isRepresentment = true,
                isRequestAdditionalInfo = true
            )
        )
    }
    
    private fun createParticipants(): List<ParticipantDto> {
        return listOf(
            ParticipantDto(
                bicCode = "RJHIKWK0",
                shortName = "RJH",
                fullName = "Al Rajhi Bank Kuwait",
                description = "Al Rajhi Bank Kuwait"
            )
        )
    }
    
    private fun createPaymentSystems(): List<PaymentSystemDto> {
        return listOf(
            PaymentSystemDto(
                bicCode = "ACH",
                fullName = "Automated Clearing House",
                description = "Automated Clearing House"
            )
        )
    }
}
