package com.progressoft.dms.kuwait.messaging.pacs28

import com.progressoft.dms.kuwait.messaging.ContextSupplier
import com.progressoft.dms.kuwait.messaging.JaxbSerializer
import iso.std.iso._20022.tech.xsd.pacs_028_001.*
import org.apache.commons.lang3.SerializationException
import java.time.OffsetDateTime
import java.util.*
import javax.xml.datatype.DatatypeConfigurationException
import javax.xml.datatype.DatatypeConstants
import javax.xml.datatype.DatatypeFactory
import javax.xml.datatype.XMLGregorianCalendar


class Pacs28SerializerImpl {

    private val contextSupplier = ContextSupplier(Document::class.java.getPackage().name)
    private val jaxbSerializer = JaxbSerializer(contextSupplier)

    fun serialize(request: Pacs28Dto): String {
        return jaxbSerializer.serialize(ObjectFactory().createDocument(buildMessageDocument(request)), true)
    }

    private fun buildMessageDocument(request: Pacs28Dto) = Document().apply {
        fiToFIPmtStsReq = buildFIToFIPaymentStatusRequest(request)
    }

    private fun buildFIToFIPaymentStatusRequest(request: Pacs28Dto) = FIToFIPaymentStatusRequestV06().apply {
        grpHdr = buildGroupHeader(request)
        getTxInf().add(buildTxInfo(request))
    }

    private fun buildGroupHeader(request: Pacs28Dto) = GroupHeader109().apply {
        msgId = request.msgId
        creDtTm = toXmlTime(request.creDtTm)
    }

    private fun buildTxInfo(request: Pacs28Dto) = PaymentTransaction158().apply {
        orgnlTxId = request.orgnlTxId
    }

    companion object {
        fun toXmlTime(offsetDateTime: OffsetDateTime): XMLGregorianCalendar {
            val calendar = GregorianCalendar.from(offsetDateTime.toZonedDateTime())
            try {
                val xmlGregorianCalendar = DatatypeFactory.newInstance().newXMLGregorianCalendar(calendar)
                xmlGregorianCalendar.timezone = DatatypeConstants.FIELD_UNDEFINED
                return xmlGregorianCalendar
            } catch (e: DatatypeConfigurationException) {
                throw SerializationException(e)
            }
        }
    }
}
