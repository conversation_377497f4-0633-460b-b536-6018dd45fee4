package com.progressoft.dms.kuwait.messaging

import jakarta.xml.bind.JAXBContext
import jakarta.xml.bind.Marshaller
import org.glassfish.jaxb.core.marshaller.CharacterEscapeHandler
import org.glassfish.jaxb.core.marshaller.DataWriter
import java.io.IOException
import java.io.PrintWriter
import java.io.StringWriter
import java.io.Writer
import java.util.function.Supplier

class JaxbSerializer(private val contextSupplier: Supplier<JAXBContext>) {
    fun <T> serialize(message: T, omitXmlDeclaration: Boolean): String {
        try {
            val stringWriter = StringWriter()
            val printWriter = PrintWriter(stringWriter)
            val dataWriter = DataWriter(printWriter, "UTF-8", JaxbCharacterEscapeHandler())
            val marshaller = contextSupplier.get().createMarshaller()
            marshaller.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, java.lang.Boolean.TRUE)
            if (omitXmlDeclaration) {
                marshaller.setProperty(Marshaller.JAXB_FRAGMENT, java.lang.Boolean.TRUE)
            }
            marshaller.marshal(message, dataWriter)
            return stringWriter.toString().trim { it <= ' ' }
        } catch (e: Exception) {
            throw RuntimeException(e)
        }
    }

    private class JaxbCharacterEscapeHandler : CharacterEscapeHandler {
        @Throws(IOException::class)
        override fun escape(buf: CharArray, start: Int, len: Int, isAttValue: Boolean, out: Writer) {
            for (i in start until start + len) {
                val ch = buf[i]
                out.write(ch.code)
            }
        }
    }
}
