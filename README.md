# DMS-KUWAIT

### User Credentials for DMS-KUWAIT:
1. UserName: **DMSMAKER@DMS** , Password: **a** , Tenant: **DMS**
2. UserName: **DMSCHECKER@DMS** , Password: **a** , Tenant: **DMS**

### Note :
For transaction-simulator app, you have to update the EndPoints in DMS_SYSTEMCONFIGURATION table after finishing the deployment.

### National Level Deployment :
The system work by default in Regional Mode, if you need to deploy the system in National Mode you should do this step :
1. Replace the startup properties file with these properties ("dms/dms-app/src/main/resources/Deployments/National-Deployment/startup.properties")
2. Modify what is inside this file "dms/dms-app/src/main/resources/application.properties"

### Steps to change the database that the system is running on
The system is designed to run on two databases (Oracle and PostgresSQL)

To change between them, you must follow these simple steps:
- Modify what is inside this file "dms/dms-app/src/main/resources/app-camel-settings.properties" by removing the comment from the Oracle scripts and placing a comment on the Postgres scripts
- Modify what is inside this file "cicd/stack/values.yaml" by removing the comment from the Oracle DB Values and placing a comment on the Postgres DB Values

#### Note: The MakeFile has been prepared to Deploy System locally, you can just remove the comment from "ENV variables for ORACLE DB" and put a comment on "ENV variables for POSTGRES DB" (then you can use "make buildRun" or "make run" to deploy the system)