<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.5.final using JasperReports Library version 6.20.5-3efcf2e67f959db3888d79f73dde2dbd7acb4f8e  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="disputes_summary" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="fcea987f-554e-41f8-8b3c-f6c763e997f5" whenNoDataType="AllSectionsNoDetail">
	<property name="com.jaspersoft.studio.data.sql.tables" value=""/>
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="DMS-2"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w1" value="842"/>
	<property name="com.jaspersoft.studio.property.dataset.dialog.DatasetDialog.sash.w2" value="145"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w1" value="253"/>
	<property name="com.jaspersoft.studio.data.sql.SQLQueryDesigner.sash.w2" value="737"/>
	<parameter name="dateTo" class="java.sql.Timestamp"/>
	<parameter name="dateFrom" class="java.sql.Timestamp"/>
	<parameter name="nature" class="java.lang.String"/>
	<parameter name="countryID" class="java.lang.String"/>
	<parameter name="reasonID" class="java.lang.String"/>
	<parameter name="paymentSysID" class="java.lang.String"/>
	<parameter name="reportID" class="java.lang.String"/>
	<parameter name="logo" class="java.lang.String"/>
	<parameter name="countryCode" class="java.lang.String"/>
	<parameter name="reasonCode" class="java.lang.String"/>
	<parameter name="paymentSysCode" class="java.lang.String"/>
	<queryString language="SQL">
		<![CDATA[SELECT
    p.code AS partCode,
    p.name AS partName,
    COALESCE(COUNT(dc.id), 0) AS disputeNo,
    COALESCE(SUM(dc.disputedAmount),0) AS disputeTotalAmount,
    COUNT(CASE WHEN dc.id IN (SELECT c.dispute_id FROM DMS_CORRESPONDENCE c WHERE c.ACTION IN ('Arbitrate Dispute Case')) THEN dc.id END) AS arDisputeNo,
    SUM(CASE WHEN dc.id IN (SELECT c.dispute_id FROM DMS_CORRESPONDENCE c WHERE c.ACTION IN ('Arbitrate Dispute Case')) THEN dc.DISPUTEDAMOUNT ELSE 0 END) AS arDisputeTotalAmount

FROM
    DMS_participants p
INNER JOIN
    DMS_DisputeCases dc
ON
    (
        $P{nature} = 'CLAIMANT'
        AND dc.claimantbank_id = p.id
        AND dc.CLAIMANTBANK_ID IN (
            SELECT m.participant_id 
            FROM DMS_DISPUTESUMMARYRPRTPARTICIPANTS m 
            WHERE m.report_id = $P{reportID}
        )
    )
    OR
    (
          $P{nature} = 'DEFENDANT'
        AND dc.defendantbank_id = p.id
        AND dc.defendantbank_id IN (
            SELECT m.participant_id 
            FROM DMS_DISPUTESUMMARYRPRTPARTICIPANTS m 
            WHERE m.report_id = $P{reportID}
        )
    )
    WHERE (dc.REASON_ID = $P{reasonID} or $P{reasonID} is null)
    AND (dc.PAYMENTSYSTEM_ID = $P{paymentSysID} or  $P{paymentSysID} is null)
    AND (p.COUNTRY_ID = $P{countryID} or $P{countryID} is null)
    AND   (dc.CREATIONDATETIME >=  $P{dateFrom} or    $P{dateFrom} is null ) 
     AND   (dc.CREATIONDATETIME <=  $P{dateTo} or    $P{dateTo} is null ) 
    
GROUP BY
    p.id,
    p.code,
    p.name]]>
	</queryString>
	<field name="partCode" class="java.lang.String"/>
	<field name="partName" class="java.lang.String"/>
	<field name="disputeNo" class="java.lang.String"/>
	<field name="disputeTotalAmount" class="java.lang.String"/>
	<field name="arDisputeNo" class="java.lang.String"/>
	<field name="arDisputeTotalAmount" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="313" splitType="Stretch">
			<staticText>
				<reportElement x="250" y="140" width="303" height="30" forecolor="#0A0A0A" uuid="300b0d3b-1cef-45ab-b1cb-16381be11109"/>
				<textElement textAlignment="Center">
					<font fontName="Arial" size="20" isBold="true"/>
				</textElement>
				<text><![CDATA[Disputes Summary]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="10" y="190" width="550" height="30" forecolor="#030000" backcolor="#0702F7" uuid="8b27383a-4267-4456-b95a-f2e0dbf9aab8">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="14" isBold="true" isUnderline="true"/>
				</textElement>
				<text><![CDATA[Filters]]></text>
			</staticText>
			<textField>
				<reportElement x="168" y="250" width="240" height="30" uuid="d7d9958b-5ee8-4e5f-8421-a901a08bda69">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{paymentSysCode} != null ?  $P{paymentSysCode}  :  "-"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="10" y="250" width="158" height="30" forecolor="#030000" backcolor="#0702F7" uuid="31b33b5b-3b74-4cb2-98ac-ee5a8d06bdfd">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Payment System:]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Transparent" x="10" y="280" width="158" height="30" forecolor="#030000" backcolor="#0702F7" uuid="90d4f74c-3b40-47f1-b7a0-a62a691318c3">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Reason]]></text>
			</staticText>
			<textField>
				<reportElement x="168" y="280" width="240" height="30" uuid="0b96504f-b607-4a61-9dee-93ed7eae0c16">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{reasonCode} != null ? $P{reasonCode}  :  "-"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="470" y="280" width="158" height="30" forecolor="#030000" backcolor="#0702F7" uuid="dde51df3-6c3a-4bb5-8663-897e89b346c3">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Nature:]]></text>
			</staticText>
			<textField>
				<reportElement x="628" y="280" width="240" height="30" uuid="d6fc2240-7c47-4272-9b51-d8bb435e5c2e">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{nature}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="470" y="250" width="158" height="30" forecolor="#030000" backcolor="#0702F7" uuid="b58325eb-f8a7-476f-ada8-25bd5cbcbd0c">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Country:]]></text>
			</staticText>
			<textField>
				<reportElement x="630" y="250" width="240" height="30" uuid="de760ea1-6cef-48d3-9d09-80f8c39b0d0f">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{countryCode}  != null ?  $P{countryCode}  :  "-"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="10" y="220" width="158" height="30" forecolor="#030000" backcolor="#0702F7" uuid="a46829a6-0176-4472-9ef7-8ee008c4d455">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Date From:]]></text>
			</staticText>
			<textField>
				<reportElement x="168" y="220" width="240" height="30" uuid="14c29e81-4732-49bc-876d-adf1cdafaee6">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dateFrom} != null ? $P{dateFrom}  :  "-"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement mode="Transparent" x="470" y="220" width="158" height="30" forecolor="#030000" backcolor="#0702F7" uuid="9e4416e9-f763-4dbb-901c-96466daaf8a4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="9c846d50-e903-4c15-85ad-999661b42f56"/>
				</reportElement>
				<box>
					<pen lineWidth="0.0" lineColor="#030000"/>
				</box>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Date To:]]></text>
			</staticText>
			<textField>
				<reportElement x="630" y="220" width="240" height="30" uuid="e39ee90a-660c-4e65-a6bd-b0cb664bce10">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="Arial" size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{dateTo} != null ? $P{dateTo}  :  "-"]]></textFieldExpression>
			</textField>
			<image hAlign="Center" vAlign="Middle" isUsingCache="true">
				<reportElement x="331" y="0" width="140" height="135" uuid="f98e4b46-1ea1-4000-bd83-908b7bba983d"/>
				<imageExpression><![CDATA[net.sf.jasperreports.util.Base64Util.decode($P{logo})]]></imageExpression>
			</image>
		</band>
	</title>
	<pageHeader>
		<band height="21" splitType="Stretch"/>
	</pageHeader>
	<columnHeader>
		<band height="61" splitType="Stretch">
			<staticText>
				<reportElement mode="Opaque" x="10" y="10" width="130" height="40" forecolor="#FFFFFF" backcolor="#515658" uuid="33494045-54e9-46f5-ac05-1fdd96fea0c5">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Partcicipant 
Code]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="140" y="10" width="130" height="40" forecolor="#FFFFFF" backcolor="#515658" uuid="2e251569-50c0-4f97-81f9-7f3b27ca4275">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Partcicipant
 Name]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="270" y="10" width="130" height="40" forecolor="#FFFFFF" backcolor="#515658" uuid="c3b8ee60-61f9-430a-9b82-63b485b18417">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Disputes
Number]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="400" y="10" width="130" height="40" forecolor="#FFFFFF" backcolor="#515658" uuid="09d4e98d-8197-48c8-901a-34ad0074c8b3">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Disputes
Total Amount]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="530" y="10" width="130" height="40" forecolor="#FFFFFF" backcolor="#515658" uuid="8225e2f3-9ef4-4773-b205-6a5c84e10af7">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Arbitraited Disputes
Number]]></text>
			</staticText>
			<staticText>
				<reportElement mode="Opaque" x="660" y="10" width="130" height="40" forecolor="#FFFFFF" backcolor="#515658" uuid="24f4007d-83e8-403c-ae77-4e602b77b356">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5" lineColor="#0F0908"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Arbitraited Disputes
Total Amount]]></text>
			</staticText>
		</band>
	</columnHeader>
	<detail>
		<band height="31" splitType="Stretch">
			<textField>
				<reportElement x="10" y="-10" width="130" height="30" uuid="7931ca81-eb00-43f1-85b9-dcee072e7671">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partCode}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="140" y="-10" width="130" height="30" uuid="9ee34089-25e7-484c-a82c-f17f96bbaac4">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="270" y="-10" width="130" height="30" uuid="667c547c-3117-4a1d-b5e6-5d9ffe09213d">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{disputeNo}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="400" y="-10" width="130" height="30" uuid="6bd17959-fd20-45ed-9394-d2379e0ef327">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{disputeTotalAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="660" y="-10" width="130" height="30" uuid="b4f352e5-7595-4e91-b074-dacf4a5f40e3">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{arDisputeTotalAmount}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="530" y="-10" width="130" height="30" uuid="03cbd39c-dda2-42ce-881e-7f86da9f565a">
					<property name="com.jaspersoft.studio.spreadsheet.connectionID" value="eafe6db6-b9ff-450d-b3fb-a06c84d0658f"/>
				</reportElement>
				<box>
					<pen lineWidth="1.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="Arial" size="13"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{arDisputeNo}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
	<columnFooter>
		<band height="23" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="46" splitType="Stretch">
			<textField>
				<reportElement x="640" y="16" width="100" height="30" uuid="b0714046-065c-4528-bfce-33aa4460232d"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="740" y="16" width="100" height="30" uuid="b579604f-9abe-4377-9764-7e3f77827afc"/>
				<textElement textAlignment="Left"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField pattern="MMM, dd, yyyy">
				<reportElement x="0" y="20" width="150" height="26" uuid="d7176676-b50e-414e-a835-d45f51a6bc86"/>
				<textFieldExpression><![CDATA[new java.util.Date()]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="20" splitType="Stretch"/>
	</summary>
</jasperReport>
